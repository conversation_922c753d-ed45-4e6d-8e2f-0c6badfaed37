import{_ as a,c as i,o as n,ab as t}from"./chunks/framework.neMYHtQj.js";const E=JSON.parse('{"title":"Markdown Extension Examples","description":"","frontmatter":{},"headers":[],"relativePath":"markdown-examples.md","filePath":"markdown-examples.md"}'),e={name:"markdown-examples.md"};function l(p,s,h,k,r,o){return n(),i("div",null,s[0]||(s[0]=[t("",19)]))}const c=a(e,[["render",l]]);export{E as __pageData,c as default};
