import{U as e}from"./chunks/UserProfile.BuU6AGC-.js";import{c as t,o as r,G as a}from"./chunks/framework.B19ydMwb.js";import"./chunks/theme.CmWpOUCL.js";import"./chunks/TopmeansMarkdownService.HmRBHz74.js";const _=JSON.parse('{"title":"","description":"","frontmatter":{"layout":"page"},"headers":[],"relativePath":"user-center/profile.md","filePath":"user-center/profile.md"}'),o={name:"user-center/profile.md"},f=Object.assign(o,{setup(s){return(c,i)=>(r(),t("div",null,[a(e)]))}});export{_ as __pageData,f as default};
