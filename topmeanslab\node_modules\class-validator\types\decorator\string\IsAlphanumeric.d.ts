import { ValidationOptions } from '../ValidationOptions';
import * as ValidatorJS from 'validator';
export declare const IS_ALPHANUMERIC = "isAlphanumeric";
/**
 * Checks if the string contains only letters and numbers.
 * If given value is not a string, then it returns false.
 */
export declare function isAlphanumeric(value: unknown, locale?: ValidatorJS.AlphanumericLocale): boolean;
/**
 * Checks if the string contains only letters and numbers.
 * If given value is not a string, then it returns false.
 */
export declare function IsAlphanumeric(locale?: ValidatorJS.AlphanumericLocale, validationOptions?: ValidationOptions): PropertyDecorator;
