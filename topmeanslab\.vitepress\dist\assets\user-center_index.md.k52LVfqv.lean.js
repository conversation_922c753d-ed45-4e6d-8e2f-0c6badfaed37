import{u as a,s as n}from"./chunks/theme.Ch1k4S35.js";import{U as i}from"./chunks/UserProfile.CzV3dsgg.js";import{L as c}from"./chunks/UserCenter.Bv8vqyOw.js";import{c as l,o as e,j as t,b as r,k as m}from"./chunks/framework.neMYHtQj.js";import"./chunks/TopmeansMarkdownService.BN2GN_Vw.js";import"./chunks/Valicode.Dl4gpGZx.js";const v=JSON.parse('{"title":"用户中心","description":"","frontmatter":{"layout":"page","title":"用户中心"},"headers":[],"relativePath":"user-center/index.md","filePath":"user-center/index.md"}'),p={name:"user-center/index.md"},y=Object.assign(p,{setup(u){const s=a(),{isLoggedIn:o}=n(s);return(d,f)=>(e(),l("div",null,[t("template",null,[t("div",null,[m(o)?(e(),r(i,{key:1})):(e(),r(c,{key:0}))])])]))}});export{v as __pageData,y as default};
