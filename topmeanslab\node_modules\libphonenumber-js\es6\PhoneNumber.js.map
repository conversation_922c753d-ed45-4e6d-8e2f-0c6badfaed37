{"version": 3, "file": "PhoneNumber.js", "names": ["<PERSON><PERSON><PERSON>", "validateMetadata", "isPossibleNumber", "isValidNumber", "getNumberType", "getPossibleCountriesForNumber", "extractCountryCallingCode", "isObject", "formatNumber", "USE_NON_GEOGRAPHIC_COUNTRY_CODE", "PhoneNumber", "countryOrCountryCallingCode", "nationalNumber", "metadata", "TypeError", "countries", "e164Number", "E164_NUMBER_REGEXP", "test", "Error", "undefined", "countryCallingCode", "number", "getCountryAndCountryCallingCode", "country", "getMetadata", "ext", "v2", "isNonGeographicCallingCode", "phoneNumber", "format", "options", "isCountryCode", "value", "metadataJson", "selectNumberingPlan"], "sources": ["../source/PhoneNumber.js"], "sourcesContent": ["import Metadata, { validateMetadata } from './metadata.js'\r\nimport isPossibleNumber from './isPossible.js'\r\nimport isValidNumber from './isValid.js'\r\n// import checkNumberLength from './helpers/checkNumberLength.js'\r\nimport getNumberType from './helpers/getNumberType.js'\r\nimport getPossibleCountriesForNumber from './helpers/getPossibleCountriesForNumber.js'\r\nimport extractCountryCallingCode from './helpers/extractCountryCallingCode.js'\r\nimport isObject from './helpers/isObject.js'\r\nimport formatNumber from './format.js'\r\n\r\nconst USE_NON_GEOGRAPHIC_COUNTRY_CODE = false\r\n\r\nexport default class PhoneNumber {\r\n\t/**\r\n\t * @param  {string} countryOrCountryCallingCode\r\n\t * @param  {string} nationalNumber\r\n\t * @param  {object} metadata — Metadata JSON\r\n\t * @return {PhoneNumber}\r\n\t */\r\n\tconstructor(countryOrCountryCallingCode, nationalNumber, metadata) {\r\n\t\t// Validate `countryOrCountryCallingCode` argument.\r\n\t\tif (!countryOrCountryCallingCode) {\r\n\t\t\tthrow new TypeError('First argument is required')\r\n\t\t}\r\n\t\tif (typeof countryOrCountryCallingCode !== 'string') {\r\n\t\t\tthrow new TypeError('First argument must be a string')\r\n\t\t}\r\n\r\n\t\t// In case of public API use: `constructor(number, metadata)`.\r\n\t\t// Transform the arguments from `constructor(number, metadata)` to\r\n\t\t// `constructor(countryOrCountryCallingCode, nationalNumber, metadata)`.\r\n\t\tif (typeof countryOrCountryCallingCode === 'string') {\r\n\t\t\tif (countryOrCountryCallingCode[0] === '+' && !nationalNumber) {\r\n\t\t\t\tthrow new TypeError('`metadata` argument not passed')\r\n\t\t\t}\r\n\t\t\tif (isObject(nationalNumber) && isObject(nationalNumber.countries)) {\r\n\t\t\t\tmetadata = nationalNumber\r\n\t\t\t\tconst e164Number = countryOrCountryCallingCode\r\n\t\t\t\tif (!E164_NUMBER_REGEXP.test(e164Number)) {\r\n\t\t\t\t\tthrow new Error('Invalid `number` argument passed: must consist of a \"+\" followed by digits')\r\n\t\t\t\t}\r\n\t\t\t\tconst { countryCallingCode, number } = extractCountryCallingCode(e164Number, undefined, undefined, metadata)\r\n\t\t\t\tnationalNumber = number\r\n\t\t\t\tcountryOrCountryCallingCode = countryCallingCode\r\n\t\t\t\tif (!nationalNumber) {\r\n\t\t\t\t\tthrow new Error('Invalid `number` argument passed: too short')\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\t// Validate `nationalNumber` argument.\r\n\t\tif (!nationalNumber) {\r\n\t\t\tthrow new TypeError('`nationalNumber` argument is required')\r\n\t\t}\r\n\t\tif (typeof nationalNumber !== 'string') {\r\n\t\t\tthrow new TypeError('`nationalNumber` argument must be a string')\r\n\t\t}\r\n\r\n\t\t// Validate `metadata` argument.\r\n\t\tvalidateMetadata(metadata)\r\n\r\n\t\t// Initialize properties.\r\n\t\tconst { country, countryCallingCode } = getCountryAndCountryCallingCode(\r\n\t\t\tcountryOrCountryCallingCode,\r\n\t\t\tmetadata\r\n\t\t)\r\n\t\tthis.country = country\r\n\t\tthis.countryCallingCode = countryCallingCode\r\n\t\tthis.nationalNumber = nationalNumber\r\n\t\tthis.number = '+' + this.countryCallingCode + this.nationalNumber\r\n\t\t// Exclude `metadata` property output from `PhoneNumber.toString()`\r\n\t\t// so that it doesn't clutter the console output of Node.js.\r\n\t\t// Previously, when Node.js did `console.log(new PhoneNumber(...))`,\r\n\t\t// it would output the whole internal structure of the `metadata` object.\r\n\t\tthis.getMetadata = () => metadata\r\n\t}\r\n\r\n\tsetExt(ext) {\r\n\t\tthis.ext = ext\r\n\t}\r\n\r\n\tgetPossibleCountries() {\r\n\t\tif (this.country) {\r\n\t\t\treturn [this.country]\r\n\t\t}\r\n\t\treturn getPossibleCountriesForNumber(\r\n\t\t\tthis.countryCallingCode,\r\n\t\t\tthis.nationalNumber,\r\n\t\t\tthis.getMetadata()\r\n\t\t)\r\n\t}\r\n\r\n\tisPossible() {\r\n\t\treturn isPossibleNumber(this, { v2: true }, this.getMetadata())\r\n\t}\r\n\r\n\tisValid() {\r\n\t\treturn isValidNumber(this, { v2: true }, this.getMetadata())\r\n\t}\r\n\r\n\tisNonGeographic() {\r\n\t\tconst metadata = new Metadata(this.getMetadata())\r\n\t\treturn metadata.isNonGeographicCallingCode(this.countryCallingCode)\r\n\t}\r\n\r\n\tisEqual(phoneNumber) {\r\n\t\treturn this.number === phoneNumber.number && this.ext === phoneNumber.ext\r\n\t}\r\n\r\n\t// This function was originally meant to be an equivalent for `validatePhoneNumberLength()`,\r\n\t// but later it was found out that it doesn't include the possible `TOO_SHORT` result\r\n\t// returned from `parsePhoneNumberWithError()` in the original `validatePhoneNumberLength()`,\r\n\t// so eventually I simply commented out this method from the `PhoneNumber` class\r\n\t// and just left the `validatePhoneNumberLength()` function, even though that one would require\r\n\t// and additional step to also validate the actual country / calling code of the phone number.\r\n\t// validateLength() {\r\n\t// \tconst metadata = new Metadata(this.getMetadata())\r\n\t// \tmetadata.selectNumberingPlan(this.countryCallingCode)\r\n\t// \tconst result = checkNumberLength(this.nationalNumber, metadata)\r\n\t// \tif (result !== 'IS_POSSIBLE') {\r\n\t// \t\treturn result\r\n\t// \t}\r\n\t// }\r\n\r\n\tgetType() {\r\n\t\treturn getNumberType(this, { v2: true }, this.getMetadata())\r\n\t}\r\n\r\n\tformat(format, options) {\r\n\t\treturn formatNumber(\r\n\t\t\tthis,\r\n\t\t\tformat,\r\n\t\t\toptions ? { ...options, v2: true } : { v2: true },\r\n\t\t\tthis.getMetadata()\r\n\t\t)\r\n\t}\r\n\r\n\tformatNational(options) {\r\n\t\treturn this.format('NATIONAL', options)\r\n\t}\r\n\r\n\tformatInternational(options) {\r\n\t\treturn this.format('INTERNATIONAL', options)\r\n\t}\r\n\r\n\tgetURI(options) {\r\n\t\treturn this.format('RFC3966', options)\r\n\t}\r\n}\r\n\r\nconst isCountryCode = (value) => /^[A-Z]{2}$/.test(value)\r\n\r\nfunction getCountryAndCountryCallingCode(countryOrCountryCallingCode, metadataJson) {\r\n\tlet country\r\n\tlet countryCallingCode\r\n\r\n\tconst metadata = new Metadata(metadataJson)\r\n\t// If country code is passed then derive `countryCallingCode` from it.\r\n\t// Also store the country code as `.country`.\r\n\tif (isCountryCode(countryOrCountryCallingCode)) {\r\n\t\tcountry = countryOrCountryCallingCode\r\n\t\tmetadata.selectNumberingPlan(country)\r\n\t\tcountryCallingCode = metadata.countryCallingCode()\r\n\t} else {\r\n\t\tcountryCallingCode = countryOrCountryCallingCode\r\n\t\t/* istanbul ignore if */\r\n\t\tif (USE_NON_GEOGRAPHIC_COUNTRY_CODE) {\r\n\t\t\tif (metadata.isNonGeographicCallingCode(countryCallingCode)) {\r\n\t\t\t\tcountry = '001'\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\r\n\treturn {\r\n\t\tcountry,\r\n\t\tcountryCallingCode\r\n\t}\r\n}\r\n\r\nconst E164_NUMBER_REGEXP = /^\\+\\d+$/"], "mappings": ";;;;;;;;;;;;AAAA,OAAOA,QAAP,IAAmBC,gBAAnB,QAA2C,eAA3C;AACA,OAAOC,gBAAP,MAA6B,iBAA7B;AACA,OAAOC,aAAP,MAA0B,cAA1B,C,CACA;;AACA,OAAOC,aAAP,MAA0B,4BAA1B;AACA,OAAOC,6BAAP,MAA0C,4CAA1C;AACA,OAAOC,yBAAP,MAAsC,wCAAtC;AACA,OAAOC,QAAP,MAAqB,uBAArB;AACA,OAAOC,YAAP,MAAyB,aAAzB;AAEA,IAAMC,+BAA+B,GAAG,KAAxC;;IAEqBC,W;EACpB;AACD;AACA;AACA;AACA;AACA;EACC,qBAAYC,2BAAZ,EAAyCC,cAAzC,EAAyDC,QAAzD,EAAmE;IAAA;;IAClE;IACA,IAAI,CAACF,2BAAL,EAAkC;MACjC,MAAM,IAAIG,SAAJ,CAAc,4BAAd,CAAN;IACA;;IACD,IAAI,OAAOH,2BAAP,KAAuC,QAA3C,EAAqD;MACpD,MAAM,IAAIG,SAAJ,CAAc,iCAAd,CAAN;IACA,CAPiE,CASlE;IACA;IACA;;;IACA,IAAI,OAAOH,2BAAP,KAAuC,QAA3C,EAAqD;MACpD,IAAIA,2BAA2B,CAAC,CAAD,CAA3B,KAAmC,GAAnC,IAA0C,CAACC,cAA/C,EAA+D;QAC9D,MAAM,IAAIE,SAAJ,CAAc,gCAAd,CAAN;MACA;;MACD,IAAIP,QAAQ,CAACK,cAAD,CAAR,IAA4BL,QAAQ,CAACK,cAAc,CAACG,SAAhB,CAAxC,EAAoE;QACnEF,QAAQ,GAAGD,cAAX;QACA,IAAMI,UAAU,GAAGL,2BAAnB;;QACA,IAAI,CAACM,kBAAkB,CAACC,IAAnB,CAAwBF,UAAxB,CAAL,EAA0C;UACzC,MAAM,IAAIG,KAAJ,CAAU,4EAAV,CAAN;QACA;;QACD,4BAAuCb,yBAAyB,CAACU,UAAD,EAAaI,SAAb,EAAwBA,SAAxB,EAAmCP,QAAnC,CAAhE;QAAA,IAAQQ,mBAAR,yBAAQA,kBAAR;QAAA,IAA4BC,MAA5B,yBAA4BA,MAA5B;;QACAV,cAAc,GAAGU,MAAjB;QACAX,2BAA2B,GAAGU,mBAA9B;;QACA,IAAI,CAACT,cAAL,EAAqB;UACpB,MAAM,IAAIO,KAAJ,CAAU,6CAAV,CAAN;QACA;MACD;IACD,CA7BiE,CA+BlE;;;IACA,IAAI,CAACP,cAAL,EAAqB;MACpB,MAAM,IAAIE,SAAJ,CAAc,uCAAd,CAAN;IACA;;IACD,IAAI,OAAOF,cAAP,KAA0B,QAA9B,EAAwC;MACvC,MAAM,IAAIE,SAAJ,CAAc,4CAAd,CAAN;IACA,CArCiE,CAuClE;;;IACAb,gBAAgB,CAACY,QAAD,CAAhB,CAxCkE,CA0ClE;;IACA,4BAAwCU,+BAA+B,CACtEZ,2BADsE,EAEtEE,QAFsE,CAAvE;IAAA,IAAQW,OAAR,yBAAQA,OAAR;IAAA,IAAiBH,kBAAjB,yBAAiBA,kBAAjB;;IAIA,KAAKG,OAAL,GAAeA,OAAf;IACA,KAAKH,kBAAL,GAA0BA,kBAA1B;IACA,KAAKT,cAAL,GAAsBA,cAAtB;IACA,KAAKU,MAAL,GAAc,MAAM,KAAKD,kBAAX,GAAgC,KAAKT,cAAnD,CAlDkE,CAmDlE;IACA;IACA;IACA;;IACA,KAAKa,WAAL,GAAmB;MAAA,OAAMZ,QAAN;IAAA,CAAnB;EACA;;;;WAED,gBAAOa,GAAP,EAAY;MACX,KAAKA,GAAL,GAAWA,GAAX;IACA;;;WAED,gCAAuB;MACtB,IAAI,KAAKF,OAAT,EAAkB;QACjB,OAAO,CAAC,KAAKA,OAAN,CAAP;MACA;;MACD,OAAOnB,6BAA6B,CACnC,KAAKgB,kBAD8B,EAEnC,KAAKT,cAF8B,EAGnC,KAAKa,WAAL,EAHmC,CAApC;IAKA;;;WAED,sBAAa;MACZ,OAAOvB,gBAAgB,CAAC,IAAD,EAAO;QAAEyB,EAAE,EAAE;MAAN,CAAP,EAAqB,KAAKF,WAAL,EAArB,CAAvB;IACA;;;WAED,mBAAU;MACT,OAAOtB,aAAa,CAAC,IAAD,EAAO;QAAEwB,EAAE,EAAE;MAAN,CAAP,EAAqB,KAAKF,WAAL,EAArB,CAApB;IACA;;;WAED,2BAAkB;MACjB,IAAMZ,QAAQ,GAAG,IAAIb,QAAJ,CAAa,KAAKyB,WAAL,EAAb,CAAjB;MACA,OAAOZ,QAAQ,CAACe,0BAAT,CAAoC,KAAKP,kBAAzC,CAAP;IACA;;;WAED,iBAAQQ,WAAR,EAAqB;MACpB,OAAO,KAAKP,MAAL,KAAgBO,WAAW,CAACP,MAA5B,IAAsC,KAAKI,GAAL,KAAaG,WAAW,CAACH,GAAtE;IACA,C,CAED;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;;;;WAEA,mBAAU;MACT,OAAOtB,aAAa,CAAC,IAAD,EAAO;QAAEuB,EAAE,EAAE;MAAN,CAAP,EAAqB,KAAKF,WAAL,EAArB,CAApB;IACA;;;WAED,gBAAOK,OAAP,EAAeC,OAAf,EAAwB;MACvB,OAAOvB,YAAY,CAClB,IADkB,EAElBsB,OAFkB,EAGlBC,OAAO,mCAAQA,OAAR;QAAiBJ,EAAE,EAAE;MAArB,KAA8B;QAAEA,EAAE,EAAE;MAAN,CAHnB,EAIlB,KAAKF,WAAL,EAJkB,CAAnB;IAMA;;;WAED,wBAAeM,OAAf,EAAwB;MACvB,OAAO,KAAKD,MAAL,CAAY,UAAZ,EAAwBC,OAAxB,CAAP;IACA;;;WAED,6BAAoBA,OAApB,EAA6B;MAC5B,OAAO,KAAKD,MAAL,CAAY,eAAZ,EAA6BC,OAA7B,CAAP;IACA;;;WAED,gBAAOA,OAAP,EAAgB;MACf,OAAO,KAAKD,MAAL,CAAY,SAAZ,EAAuBC,OAAvB,CAAP;IACA;;;;;;SAvImBrB,W;;AA0IrB,IAAMsB,aAAa,GAAG,SAAhBA,aAAgB,CAACC,KAAD;EAAA,OAAW,aAAaf,IAAb,CAAkBe,KAAlB,CAAX;AAAA,CAAtB;;AAEA,SAASV,+BAAT,CAAyCZ,2BAAzC,EAAsEuB,YAAtE,EAAoF;EACnF,IAAIV,OAAJ;EACA,IAAIH,kBAAJ;EAEA,IAAMR,QAAQ,GAAG,IAAIb,QAAJ,CAAakC,YAAb,CAAjB,CAJmF,CAKnF;EACA;;EACA,IAAIF,aAAa,CAACrB,2BAAD,CAAjB,EAAgD;IAC/Ca,OAAO,GAAGb,2BAAV;IACAE,QAAQ,CAACsB,mBAAT,CAA6BX,OAA7B;IACAH,kBAAkB,GAAGR,QAAQ,CAACQ,kBAAT,EAArB;EACA,CAJD,MAIO;IACNA,kBAAkB,GAAGV,2BAArB;IACA;;IACA,IAAIF,+BAAJ,EAAqC;MACpC,IAAII,QAAQ,CAACe,0BAAT,CAAoCP,kBAApC,CAAJ,EAA6D;QAC5DG,OAAO,GAAG,KAAV;MACA;IACD;EACD;;EAED,OAAO;IACNA,OAAO,EAAPA,OADM;IAENH,kBAAkB,EAAlBA;EAFM,CAAP;AAIA;;AAED,IAAMJ,kBAAkB,GAAG,SAA3B"}