import{S as e}from"./chunks/ServicePurchase.BnozmWQP.js";import{c as r,o as a,G as t}from"./chunks/framework.B19ydMwb.js";import"./chunks/theme.CmWpOUCL.js";const _=JSON.parse('{"title":"","description":"","frontmatter":{"layout":"page"},"headers":[],"relativePath":"service-purchase/recharge.md","filePath":"service-purchase/recharge.md"}'),c={name:"service-purchase/recharge.md"},h=Object.assign(c,{setup(s){return(o,i)=>(a(),r("div",null,[t(e)]))}});export{_ as __pageData,h as default};
