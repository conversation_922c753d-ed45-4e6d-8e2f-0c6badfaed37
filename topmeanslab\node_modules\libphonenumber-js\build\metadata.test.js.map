{"version": 3, "file": "metadata.test.js", "names": ["describe", "it", "FR", "<PERSON><PERSON><PERSON>", "metadata", "country", "type", "should", "equal", "thrower", "isSupportedCountry", "getExtPrefix", "getNumberingPlanMetadata", "nationalPrefixForParsing", "chooseCountryByCountryCallingCode", "meta", "numberingPlan", "formats", "nationalPrefixIsMandatoryWhenFormattingInNationalFormat", "validateMetadata", "a", "b", "countries", "country_calling_codes", "metadataV4", "selectNumberingPlan", "nonGeographic", "metadataV3", "metadataV2", "possibleLengths", "deep", "length", "nationalPrefix", "pattern", "leadingDigits", "expect", "nationalPrefixTransformRule", "to", "be", "nationalPrefixFormattingRule", "nationalPrefixIsOptionalWhenFormattingInNationalFormat", "metadataV1", "ext", "metaNew", "defaultIDDPrefix", "something"], "sources": ["../source/metadata.test.js"], "sourcesContent": ["import metadata from '../metadata.min.json' assert { type: 'json' }\r\nimport metadataV1 from '../test/metadata/1.0.0/metadata.min.json' assert { type: 'json' }\r\nimport metadataV2 from '../test/metadata/1.1.11/metadata.min.json' assert { type: 'json' }\r\nimport metadataV3 from '../test/metadata/1.7.34/metadata.min.json' assert { type: 'json' }\r\nimport metadataV4 from '../test/metadata/1.7.37/metadata.min.json' assert { type: 'json' }\r\n\r\nimport Metadata, { validateMetadata, getExtPrefix, isSupportedCountry } from './metadata.js'\r\n\r\ndescribe('metadata', () => {\r\n\tit('should return undefined for non-defined types', () => {\r\n\t\tconst FR = new Metadata(metadata).country('FR')\r\n\t\ttype(FR.type('FIXED_LINE')).should.equal('undefined')\r\n\t})\r\n\r\n\tit('should validate country', () => {\r\n\t\tconst thrower = () => new Metadata(metadata).country('RUS')\r\n\t\tthrower.should.throw('Unknown country')\r\n\t})\r\n\r\n\tit('should tell if a country is supported', () => {\r\n\t\tisSupportedCountry('RU', metadata).should.equal(true)\r\n\t\tisSupportedCountry('XX', metadata).should.equal(false)\r\n\t})\r\n\r\n\tit('should return ext prefix for a country', () => {\r\n\t\tgetExtPrefix('US', metadata).should.equal(' ext. ')\r\n\t\tgetExtPrefix('CA', metadata).should.equal(' ext. ')\r\n\t\tgetExtPrefix('GB', metadata).should.equal(' x')\r\n\t\t// expect(getExtPrefix('XX', metadata)).to.equal(undefined)\r\n\t\tgetExtPrefix('XX', metadata).should.equal(' ext. ')\r\n\t})\r\n\r\n\tit('should cover non-occuring edge cases', () => {\r\n\t\tnew Metadata(metadata).getNumberingPlanMetadata('999')\r\n\t})\r\n\r\n\tit('should support deprecated methods', () => {\r\n\t\tnew Metadata(metadata).country('US').nationalPrefixForParsing().should.equal('1')\r\n\t\tnew Metadata(metadata).chooseCountryByCountryCallingCode('1').nationalPrefixForParsing().should.equal('1')\r\n\t})\r\n\r\n\tit('should tell if a national prefix is mandatory when formatting a national number', () => {\r\n\t\tconst meta = new Metadata(metadata)\r\n\t\t// No \"national_prefix_formatting_rule\".\r\n\t\t// \"national_prefix_is_optional_when_formatting\": true\r\n\t\tmeta.country('US')\r\n\t\tmeta.numberingPlan.formats()[0].nationalPrefixIsMandatoryWhenFormattingInNationalFormat().should.equal(false)\r\n\t\t// \"national_prefix_formatting_rule\": \"8 ($1)\"\r\n\t\t// \"national_prefix_is_optional_when_formatting\": true\r\n\t\tmeta.country('RU')\r\n\t\tmeta.numberingPlan.formats()[0].nationalPrefixIsMandatoryWhenFormattingInNationalFormat().should.equal(false)\r\n\t\t// \"national_prefix\": \"0\"\r\n\t\t// \"national_prefix_formatting_rule\": \"0 $1\"\r\n\t\tmeta.country('FR')\r\n\t\tmeta.numberingPlan.formats()[0].nationalPrefixIsMandatoryWhenFormattingInNationalFormat().should.equal(true)\r\n\t})\r\n\r\n\tit('should validate metadata', () => {\r\n\t\tlet thrower = () => validateMetadata()\r\n\t\tthrower.should.throw('`metadata` argument not passed')\r\n\r\n\t\tthrower = () => validateMetadata(123)\r\n\t\tthrower.should.throw('Got a number: 123.')\r\n\r\n\t\tthrower = () => validateMetadata('abc')\r\n\t\tthrower.should.throw('Got a string: abc.')\r\n\r\n\t\tthrower = () => validateMetadata({ a: true, b: 2 })\r\n\t\tthrower.should.throw('Got an object of shape: { a, b }.')\r\n\r\n\t\tthrower = () => validateMetadata({ a: true, countries: 2 })\r\n\t\tthrower.should.throw('Got an object of shape: { a, countries }.')\r\n\r\n\t\tthrower = () => validateMetadata({ country_calling_codes: true, countries: 2 })\r\n\t\tthrower.should.throw('Got an object of shape')\r\n\r\n\t\tthrower = () => validateMetadata({ country_calling_codes: {}, countries: 2 })\r\n\t\tthrower.should.throw('Got an object of shape')\r\n\r\n\t\tvalidateMetadata({ country_calling_codes: {}, countries: {}, b: 3 })\r\n\t})\r\n\r\n\tit('should work around `nonGeographical` typo in metadata generated from `1.7.35` to `1.7.37`', function() {\r\n\t\tconst meta = new Metadata(metadataV4)\r\n\t\tmeta.selectNumberingPlan('888')\r\n\t\ttype(meta.nonGeographic()).should.equal('object')\r\n\t})\r\n\r\n\tit('should work around `nonGeographic` metadata not existing before `1.7.35`', function() {\r\n\t\tconst meta = new Metadata(metadataV3)\r\n\t\ttype(meta.getNumberingPlanMetadata('800')).should.equal('object')\r\n\t\ttype(meta.getNumberingPlanMetadata('000')).should.equal('undefined')\r\n\t})\r\n\r\n\tit('should work with metadata from version `1.1.11`', function() {\r\n\t\tconst meta = new Metadata(metadataV2)\r\n\r\n\t\tmeta.selectNumberingPlan('US')\r\n\t\tmeta.numberingPlan.possibleLengths().should.deep.equal([10])\r\n\t\tmeta.numberingPlan.formats().length.should.equal(1)\r\n\t\tmeta.numberingPlan.nationalPrefix().should.equal('1')\r\n\t\tmeta.numberingPlan.nationalPrefixForParsing().should.equal('1')\r\n\t\tmeta.numberingPlan.type('MOBILE').pattern().should.equal('')\r\n\r\n\t\tmeta.selectNumberingPlan('AG')\r\n\t\tmeta.numberingPlan.leadingDigits().should.equal('268')\r\n\t\t// Should've been \"268$1\" but apparently there was a bug in metadata generator\r\n\t\t// and no national prefix transform rules were written.\r\n\t\texpect(meta.numberingPlan.nationalPrefixTransformRule()).to.be.null\r\n\r\n\t\tmeta.selectNumberingPlan('AF')\r\n\t\tmeta.numberingPlan.formats()[0].nationalPrefixFormattingRule().should.equal('0$1')\r\n\r\n\t\tmeta.selectNumberingPlan('RU')\r\n\t\tmeta.numberingPlan.formats()[0].nationalPrefixIsOptionalWhenFormattingInNationalFormat().should.equal(true)\r\n\t})\r\n\r\n\tit('should work with metadata from version `1.0.0`', function() {\r\n\t\tconst meta = new Metadata(metadataV1)\r\n\r\n\t\tmeta.selectNumberingPlan('US')\r\n\t\tmeta.numberingPlan.formats().length.should.equal(1)\r\n\t\tmeta.numberingPlan.nationalPrefix().should.equal('1')\r\n\t\tmeta.numberingPlan.nationalPrefixForParsing().should.equal('1')\r\n\t\ttype(meta.numberingPlan.type('MOBILE')).should.equal('undefined')\r\n\r\n\t\tmeta.selectNumberingPlan('AG')\r\n\t\tmeta.numberingPlan.leadingDigits().should.equal('268')\r\n\t\t// Should've been \"268$1\" but apparently there was a bug in metadata generator\r\n\t\t// and no national prefix transform rules were written.\r\n\t\texpect(meta.numberingPlan.nationalPrefixTransformRule()).to.be.null\r\n\r\n\t\tmeta.selectNumberingPlan('AF')\r\n\t\tmeta.numberingPlan.formats()[0].nationalPrefixFormattingRule().should.equal('0$1')\r\n\r\n\t\tmeta.selectNumberingPlan('RU')\r\n\t\tmeta.numberingPlan.formats()[0].nationalPrefixIsOptionalWhenFormattingInNationalFormat().should.equal(true)\r\n\t})\r\n\r\n\tit('should work around \"ext\" data not present in metadata from version `1.0.0`', function() {\r\n\t\tconst meta = new Metadata(metadataV1)\r\n\t\tmeta.selectNumberingPlan('GB')\r\n\t\tmeta.ext().should.equal(' ext. ')\r\n\r\n\t\tconst metaNew = new Metadata(metadata)\r\n\t\tmetaNew.selectNumberingPlan('GB')\r\n\t\tmetaNew.ext().should.equal(' x')\r\n\t})\r\n\r\n\tit('should work around \"default IDD prefix\" data not present in metadata from version `1.0.0`', function() {\r\n\t\tconst meta = new Metadata(metadataV1)\r\n\t\tmeta.selectNumberingPlan('AU')\r\n\t\ttype(meta.defaultIDDPrefix()).should.equal('undefined')\r\n\r\n\t\tconst metaNew = new Metadata(metadata)\r\n\t\tmetaNew.selectNumberingPlan('AU')\r\n\t\tmetaNew.defaultIDDPrefix().should.equal('0011')\r\n\t})\r\n})\r\n\r\nfunction type(something) {\r\n\treturn typeof something\r\n}"], "mappings": ";;AAAA;;AACA;;AACA;;AACA;;AACA;;AAEA;;;;;;;;;;AAEAA,QAAQ,CAAC,UAAD,EAAa,YAAM;EAC1BC,EAAE,CAAC,+CAAD,EAAkD,YAAM;IACzD,IAAMC,EAAE,GAAG,IAAIC,oBAAJ,CAAaC,uBAAb,EAAuBC,OAAvB,CAA+B,IAA/B,CAAX;IACAC,IAAI,CAACJ,EAAE,CAACI,IAAH,CAAQ,YAAR,CAAD,CAAJ,CAA4BC,MAA5B,CAAmCC,KAAnC,CAAyC,WAAzC;EACA,CAHC,CAAF;EAKAP,EAAE,CAAC,yBAAD,EAA4B,YAAM;IACnC,IAAMQ,OAAO,GAAG,SAAVA,OAAU;MAAA,OAAM,IAAIN,oBAAJ,CAAaC,uBAAb,EAAuBC,OAAvB,CAA+B,KAA/B,CAAN;IAAA,CAAhB;;IACAI,OAAO,CAACF,MAAR,UAAqB,iBAArB;EACA,CAHC,CAAF;EAKAN,EAAE,CAAC,uCAAD,EAA0C,YAAM;IACjD,IAAAS,4BAAA,EAAmB,IAAnB,EAAyBN,uBAAzB,EAAmCG,MAAnC,CAA0CC,KAA1C,CAAgD,IAAhD;IACA,IAAAE,4BAAA,EAAmB,IAAnB,EAAyBN,uBAAzB,EAAmCG,MAAnC,CAA0CC,KAA1C,CAAgD,KAAhD;EACA,CAHC,CAAF;EAKAP,EAAE,CAAC,wCAAD,EAA2C,YAAM;IAClD,IAAAU,sBAAA,EAAa,IAAb,EAAmBP,uBAAnB,EAA6BG,MAA7B,CAAoCC,KAApC,CAA0C,QAA1C;IACA,IAAAG,sBAAA,EAAa,IAAb,EAAmBP,uBAAnB,EAA6BG,MAA7B,CAAoCC,KAApC,CAA0C,QAA1C;IACA,IAAAG,sBAAA,EAAa,IAAb,EAAmBP,uBAAnB,EAA6BG,MAA7B,CAAoCC,KAApC,CAA0C,IAA1C,EAHkD,CAIlD;;IACA,IAAAG,sBAAA,EAAa,IAAb,EAAmBP,uBAAnB,EAA6BG,MAA7B,CAAoCC,KAApC,CAA0C,QAA1C;EACA,CANC,CAAF;EAQAP,EAAE,CAAC,sCAAD,EAAyC,YAAM;IAChD,IAAIE,oBAAJ,CAAaC,uBAAb,EAAuBQ,wBAAvB,CAAgD,KAAhD;EACA,CAFC,CAAF;EAIAX,EAAE,CAAC,mCAAD,EAAsC,YAAM;IAC7C,IAAIE,oBAAJ,CAAaC,uBAAb,EAAuBC,OAAvB,CAA+B,IAA/B,EAAqCQ,wBAArC,GAAgEN,MAAhE,CAAuEC,KAAvE,CAA6E,GAA7E;IACA,IAAIL,oBAAJ,CAAaC,uBAAb,EAAuBU,iCAAvB,CAAyD,GAAzD,EAA8DD,wBAA9D,GAAyFN,MAAzF,CAAgGC,KAAhG,CAAsG,GAAtG;EACA,CAHC,CAAF;EAKAP,EAAE,CAAC,iFAAD,EAAoF,YAAM;IAC3F,IAAMc,IAAI,GAAG,IAAIZ,oBAAJ,CAAaC,uBAAb,CAAb,CAD2F,CAE3F;IACA;;IACAW,IAAI,CAACV,OAAL,CAAa,IAAb;IACAU,IAAI,CAACC,aAAL,CAAmBC,OAAnB,GAA6B,CAA7B,EAAgCC,uDAAhC,GAA0FX,MAA1F,CAAiGC,KAAjG,CAAuG,KAAvG,EAL2F,CAM3F;IACA;;IACAO,IAAI,CAACV,OAAL,CAAa,IAAb;IACAU,IAAI,CAACC,aAAL,CAAmBC,OAAnB,GAA6B,CAA7B,EAAgCC,uDAAhC,GAA0FX,MAA1F,CAAiGC,KAAjG,CAAuG,KAAvG,EAT2F,CAU3F;IACA;;IACAO,IAAI,CAACV,OAAL,CAAa,IAAb;IACAU,IAAI,CAACC,aAAL,CAAmBC,OAAnB,GAA6B,CAA7B,EAAgCC,uDAAhC,GAA0FX,MAA1F,CAAiGC,KAAjG,CAAuG,IAAvG;EACA,CAdC,CAAF;EAgBAP,EAAE,CAAC,0BAAD,EAA6B,YAAM;IACpC,IAAIQ,OAAO,GAAG;MAAA,OAAM,IAAAU,0BAAA,GAAN;IAAA,CAAd;;IACAV,OAAO,CAACF,MAAR,UAAqB,gCAArB;;IAEAE,OAAO,GAAG;MAAA,OAAM,IAAAU,0BAAA,EAAiB,GAAjB,CAAN;IAAA,CAAV;;IACAV,OAAO,CAACF,MAAR,UAAqB,oBAArB;;IAEAE,OAAO,GAAG;MAAA,OAAM,IAAAU,0BAAA,EAAiB,KAAjB,CAAN;IAAA,CAAV;;IACAV,OAAO,CAACF,MAAR,UAAqB,oBAArB;;IAEAE,OAAO,GAAG;MAAA,OAAM,IAAAU,0BAAA,EAAiB;QAAEC,CAAC,EAAE,IAAL;QAAWC,CAAC,EAAE;MAAd,CAAjB,CAAN;IAAA,CAAV;;IACAZ,OAAO,CAACF,MAAR,UAAqB,mCAArB;;IAEAE,OAAO,GAAG;MAAA,OAAM,IAAAU,0BAAA,EAAiB;QAAEC,CAAC,EAAE,IAAL;QAAWE,SAAS,EAAE;MAAtB,CAAjB,CAAN;IAAA,CAAV;;IACAb,OAAO,CAACF,MAAR,UAAqB,2CAArB;;IAEAE,OAAO,GAAG;MAAA,OAAM,IAAAU,0BAAA,EAAiB;QAAEI,qBAAqB,EAAE,IAAzB;QAA+BD,SAAS,EAAE;MAA1C,CAAjB,CAAN;IAAA,CAAV;;IACAb,OAAO,CAACF,MAAR,UAAqB,wBAArB;;IAEAE,OAAO,GAAG;MAAA,OAAM,IAAAU,0BAAA,EAAiB;QAAEI,qBAAqB,EAAE,EAAzB;QAA6BD,SAAS,EAAE;MAAxC,CAAjB,CAAN;IAAA,CAAV;;IACAb,OAAO,CAACF,MAAR,UAAqB,wBAArB;IAEA,IAAAY,0BAAA,EAAiB;MAAEI,qBAAqB,EAAE,EAAzB;MAA6BD,SAAS,EAAE,EAAxC;MAA4CD,CAAC,EAAE;IAA/C,CAAjB;EACA,CAvBC,CAAF;EAyBApB,EAAE,CAAC,2FAAD,EAA8F,YAAW;IAC1G,IAAMc,IAAI,GAAG,IAAIZ,oBAAJ,CAAaqB,wBAAb,CAAb;IACAT,IAAI,CAACU,mBAAL,CAAyB,KAAzB;IACAnB,IAAI,CAACS,IAAI,CAACW,aAAL,EAAD,CAAJ,CAA2BnB,MAA3B,CAAkCC,KAAlC,CAAwC,QAAxC;EACA,CAJC,CAAF;EAMAP,EAAE,CAAC,0EAAD,EAA6E,YAAW;IACzF,IAAMc,IAAI,GAAG,IAAIZ,oBAAJ,CAAawB,wBAAb,CAAb;IACArB,IAAI,CAACS,IAAI,CAACH,wBAAL,CAA8B,KAA9B,CAAD,CAAJ,CAA2CL,MAA3C,CAAkDC,KAAlD,CAAwD,QAAxD;IACAF,IAAI,CAACS,IAAI,CAACH,wBAAL,CAA8B,KAA9B,CAAD,CAAJ,CAA2CL,MAA3C,CAAkDC,KAAlD,CAAwD,WAAxD;EACA,CAJC,CAAF;EAMAP,EAAE,CAAC,iDAAD,EAAoD,YAAW;IAChE,IAAMc,IAAI,GAAG,IAAIZ,oBAAJ,CAAayB,wBAAb,CAAb;IAEAb,IAAI,CAACU,mBAAL,CAAyB,IAAzB;IACAV,IAAI,CAACC,aAAL,CAAmBa,eAAnB,GAAqCtB,MAArC,CAA4CuB,IAA5C,CAAiDtB,KAAjD,CAAuD,CAAC,EAAD,CAAvD;IACAO,IAAI,CAACC,aAAL,CAAmBC,OAAnB,GAA6Bc,MAA7B,CAAoCxB,MAApC,CAA2CC,KAA3C,CAAiD,CAAjD;IACAO,IAAI,CAACC,aAAL,CAAmBgB,cAAnB,GAAoCzB,MAApC,CAA2CC,KAA3C,CAAiD,GAAjD;IACAO,IAAI,CAACC,aAAL,CAAmBH,wBAAnB,GAA8CN,MAA9C,CAAqDC,KAArD,CAA2D,GAA3D;IACAO,IAAI,CAACC,aAAL,CAAmBV,IAAnB,CAAwB,QAAxB,EAAkC2B,OAAlC,GAA4C1B,MAA5C,CAAmDC,KAAnD,CAAyD,EAAzD;IAEAO,IAAI,CAACU,mBAAL,CAAyB,IAAzB;IACAV,IAAI,CAACC,aAAL,CAAmBkB,aAAnB,GAAmC3B,MAAnC,CAA0CC,KAA1C,CAAgD,KAAhD,EAXgE,CAYhE;IACA;;IACA2B,MAAM,CAACpB,IAAI,CAACC,aAAL,CAAmBoB,2BAAnB,EAAD,CAAN,CAAyDC,EAAzD,CAA4DC,EAA5D;IAEAvB,IAAI,CAACU,mBAAL,CAAyB,IAAzB;IACAV,IAAI,CAACC,aAAL,CAAmBC,OAAnB,GAA6B,CAA7B,EAAgCsB,4BAAhC,GAA+DhC,MAA/D,CAAsEC,KAAtE,CAA4E,KAA5E;IAEAO,IAAI,CAACU,mBAAL,CAAyB,IAAzB;IACAV,IAAI,CAACC,aAAL,CAAmBC,OAAnB,GAA6B,CAA7B,EAAgCuB,sDAAhC,GAAyFjC,MAAzF,CAAgGC,KAAhG,CAAsG,IAAtG;EACA,CArBC,CAAF;EAuBAP,EAAE,CAAC,gDAAD,EAAmD,YAAW;IAC/D,IAAMc,IAAI,GAAG,IAAIZ,oBAAJ,CAAasC,wBAAb,CAAb;IAEA1B,IAAI,CAACU,mBAAL,CAAyB,IAAzB;IACAV,IAAI,CAACC,aAAL,CAAmBC,OAAnB,GAA6Bc,MAA7B,CAAoCxB,MAApC,CAA2CC,KAA3C,CAAiD,CAAjD;IACAO,IAAI,CAACC,aAAL,CAAmBgB,cAAnB,GAAoCzB,MAApC,CAA2CC,KAA3C,CAAiD,GAAjD;IACAO,IAAI,CAACC,aAAL,CAAmBH,wBAAnB,GAA8CN,MAA9C,CAAqDC,KAArD,CAA2D,GAA3D;IACAF,IAAI,CAACS,IAAI,CAACC,aAAL,CAAmBV,IAAnB,CAAwB,QAAxB,CAAD,CAAJ,CAAwCC,MAAxC,CAA+CC,KAA/C,CAAqD,WAArD;IAEAO,IAAI,CAACU,mBAAL,CAAyB,IAAzB;IACAV,IAAI,CAACC,aAAL,CAAmBkB,aAAnB,GAAmC3B,MAAnC,CAA0CC,KAA1C,CAAgD,KAAhD,EAV+D,CAW/D;IACA;;IACA2B,MAAM,CAACpB,IAAI,CAACC,aAAL,CAAmBoB,2BAAnB,EAAD,CAAN,CAAyDC,EAAzD,CAA4DC,EAA5D;IAEAvB,IAAI,CAACU,mBAAL,CAAyB,IAAzB;IACAV,IAAI,CAACC,aAAL,CAAmBC,OAAnB,GAA6B,CAA7B,EAAgCsB,4BAAhC,GAA+DhC,MAA/D,CAAsEC,KAAtE,CAA4E,KAA5E;IAEAO,IAAI,CAACU,mBAAL,CAAyB,IAAzB;IACAV,IAAI,CAACC,aAAL,CAAmBC,OAAnB,GAA6B,CAA7B,EAAgCuB,sDAAhC,GAAyFjC,MAAzF,CAAgGC,KAAhG,CAAsG,IAAtG;EACA,CApBC,CAAF;EAsBAP,EAAE,CAAC,4EAAD,EAA+E,YAAW;IAC3F,IAAMc,IAAI,GAAG,IAAIZ,oBAAJ,CAAasC,wBAAb,CAAb;IACA1B,IAAI,CAACU,mBAAL,CAAyB,IAAzB;IACAV,IAAI,CAAC2B,GAAL,GAAWnC,MAAX,CAAkBC,KAAlB,CAAwB,QAAxB;IAEA,IAAMmC,OAAO,GAAG,IAAIxC,oBAAJ,CAAaC,uBAAb,CAAhB;IACAuC,OAAO,CAAClB,mBAAR,CAA4B,IAA5B;IACAkB,OAAO,CAACD,GAAR,GAAcnC,MAAd,CAAqBC,KAArB,CAA2B,IAA3B;EACA,CARC,CAAF;EAUAP,EAAE,CAAC,2FAAD,EAA8F,YAAW;IAC1G,IAAMc,IAAI,GAAG,IAAIZ,oBAAJ,CAAasC,wBAAb,CAAb;IACA1B,IAAI,CAACU,mBAAL,CAAyB,IAAzB;IACAnB,IAAI,CAACS,IAAI,CAAC6B,gBAAL,EAAD,CAAJ,CAA8BrC,MAA9B,CAAqCC,KAArC,CAA2C,WAA3C;IAEA,IAAMmC,OAAO,GAAG,IAAIxC,oBAAJ,CAAaC,uBAAb,CAAhB;IACAuC,OAAO,CAAClB,mBAAR,CAA4B,IAA5B;IACAkB,OAAO,CAACC,gBAAR,GAA2BrC,MAA3B,CAAkCC,KAAlC,CAAwC,MAAxC;EACA,CARC,CAAF;AASA,CAtJO,CAAR;;AAwJA,SAASF,IAAT,CAAcuC,SAAd,EAAyB;EACxB,eAAcA,SAAd;AACA"}