{"name": "@fidm/asn1", "description": "ASN.1/DER, PEM for Node.js", "authors": ["<PERSON> <<EMAIL>>"], "version": "1.0.4", "main": "build/index.js", "types": "build/index.d.ts", "license": "MIT", "repository": {"type": "git", "url": "**************:fidm/asn1.git"}, "homepage": "https://github.com/fidm/asn1", "engines": {"node": ">= 8"}, "keywords": ["asn1", "ASN.1", "pem", "JavaScript", "node"], "dependencies": {}, "devDependencies": {"@types/node": "^11.10.4", "istanbul": "^1.1.0-alpha.1", "tman": "^1.9.0", "ts-node": "^8.0.2", "tslint": "^5.13.1", "tslint-eslint-rules": "^5.4.0", "typedoc": "^0.14.2", "typescript": "^3.3.3333"}, "scripts": {"build": "rm -rf build && tsc", "docs": "rm -rf docs && typedoc --out docs && touch docs/.nojekyll", "test": "tman -r ts-node/register 'test/**/*.ts'", "test-cov": "istanbul cover _tman -- 'test/**/*.ts' -r ts-node/register", "lint": "tslint -p tsconfig.json -t stylish 'src/**/*.ts' 'test/**/*.ts'"}, "files": ["build", "LICENSE", "README.md", "CHANGELOG.md"]}