{"name": "sse-decoder", "version": "1.0.0", "publishConfig": {"tag": "latest"}, "description": "Server-Sent Events implementation in Typescript. Used by the Gin Framework. ", "keywords": ["ai", "streaming", "sse", "server sent events"], "repository": {"type": "git", "url": "https://github.com/node-modules/sse-decoder.git"}, "engines": {"node": ">= 14.19.3"}, "license": "MIT", "scripts": {"lint": "eslint src test --ext .ts --cache --fix", "build": "tsc --version && tshy && tshy-after", "build:version": "node ./scripts/replace_version.js", "prepublishOnly": "npm run build", "test": "npm run lint && vitest run", "test:run": "vitest run --reporter=basic --disable-console-intercept", "ci": "npm run lint && npm run cov", "cov": "vitest run --coverage"}, "devDependencies": {"@types/node": "^20.2.1", "@tsconfig/node18": "^18.2.1", "@tsconfig/strictest": "^2.0.2", "@vitest/coverage-v8": "^1.3.1", "eslint": "^8.25.0", "eslint-config-egg": "^12.1.0", "msw": "^2.2.14", "typescript": "^5.0.4", "tshy": "^1.0.0", "tshy-after": "^1.0.0", "undici": "^6.14.1", "vitest": "^1.3.1"}, "type": "module", "tshy": {"exports": {".": "./src/index.ts", "./package.json": "./package.json"}}, "exports": {".": {"import": {"types": "./dist/esm/index.d.ts", "default": "./dist/esm/index.js"}, "require": {"types": "./dist/commonjs/index.d.ts", "default": "./dist/commonjs/index.js"}}, "./package.json": "./package.json"}, "files": ["dist", "src"], "types": "./dist/commonjs/index.d.ts", "main": "./dist/commonjs/index.js"}