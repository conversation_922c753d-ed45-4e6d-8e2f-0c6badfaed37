{"version": 3, "file": "planner.js", "sourceRoot": "", "sources": ["../../src/planning/planner.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,YAAY,EAAE,MAAM,2BAA2B,CAAC;AACzD,OAAO,KAAK,UAAU,MAAM,yBAAyB,CAAC;AACtD,OAAO,EAAE,eAAe,EAAE,cAAc,EAAE,MAAM,4BAA4B,CAAC;AAC7E,OAAO,KAAK,YAAY,MAAM,4BAA4B,CAAC;AAE3D,OAAO,EAAE,uBAAuB,EAAE,MAAM,qBAAqB,CAAC;AAC9D,OAAO,EACL,6BAA6B,EAC7B,4BAA4B,EAC5B,qBAAqB,EACrB,0CAA0C,EAC3C,MAAM,wBAAwB,CAAC;AAChC,OAAO,EAAE,OAAO,EAAE,MAAM,WAAW,CAAC;AACpC,OAAO,EAAE,QAAQ,EAAE,MAAM,YAAY,CAAC;AACtC,OAAO,EAAE,IAAI,EAAE,MAAM,QAAQ,CAAC;AAC9B,OAAO,EAAE,2BAA2B,EAAE,eAAe,EAAE,eAAe,EAAE,MAAM,oBAAoB,CAAC;AACnG,OAAO,EAAE,OAAO,EAAE,MAAM,WAAW,CAAC;AACpC,OAAO,EAAE,MAAM,EAAE,MAAM,UAAU,CAAC;AAElC,SAAS,oBAAoB,CAAC,KAA2B;IACvD,OAAQ,KAA2F,CAAC,kBAAkB,CAAC;AACzH,CAAC;AAED,SAAS,aAAa,CACpB,aAAsB,EACtB,UAAiC,EACjC,iBAA+C,EAC/C,IAAY,EACZ,GAA8B,EAC9B,KAAe;IAGf,IAAM,WAAW,GAAG,aAAa,CAAC,CAAC,CAAC,YAAY,CAAC,gBAAgB,CAAC,CAAC,CAAC,YAAY,CAAC,UAAU,CAAC;IAC5F,IAAM,cAAc,GAAG,IAAI,QAAQ,CAAC,WAAW,EAAE,iBAAiB,CAAC,CAAC;IACpE,IAAM,MAAM,GAAG,IAAI,MAAM,CAAC,UAAU,EAAE,IAAI,EAAE,iBAAiB,EAAE,cAAc,CAAC,CAAC;IAE/E,IAAI,GAAG,KAAK,SAAS,EAAE;QACrB,IAAM,WAAW,GAAG,IAAI,QAAQ,CAAC,GAAG,EAAE,KAAK,CAAC,CAAC;QAC7C,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;KACnC;IAED,OAAO,MAAM,CAAC;AAEhB,CAAC;AAED,SAAS,kBAAkB,CACzB,cAAyC,EACzC,gBAAyB,EACzB,OAA2B,EAC3B,aAAwC,EACxC,MAAyB;IAGzB,IAAI,QAAQ,GAAG,WAAW,CAAC,OAAO,CAAC,SAAS,EAAE,MAAM,CAAC,iBAAiB,CAAC,CAAC;IACxE,IAAI,cAAc,GAAkC,EAAE,CAAC;IAGvD,IAAI,QAAQ,CAAC,MAAM,KAAK,YAAY,CAAC,mBAAmB;QACtD,OAAO,CAAC,SAAS,CAAC,OAAO,CAAC,kBAAkB;QAC5C,OAAO,MAAM,CAAC,iBAAiB,KAAK,UAAU;QAC9C,cAAc,CAAC,sBAAsB,CAAC,MAAM,CAAC,iBAAiB,CAAC,CAAC,yBAAyB,EACzF;QACA,OAAO,CAAC,SAAS,CAAC,IAAI,CAAC,MAAM,CAAC,iBAAiB,CAAC,CAAC,MAAM,EAAE,CAAC;QAC1D,QAAQ,GAAG,WAAW,CAAC,OAAO,CAAC,SAAS,EAAE,MAAM,CAAC,iBAAiB,CAAC,CAAC;KACrE;IAGD,IAAI,CAAC,gBAAgB,EAAE;QAGrB,cAAc,GAAG,QAAQ,CAAC,MAAM,CAAC,UAAC,OAAO;YAEvC,IAAM,OAAO,GAAG,IAAI,OAAO,CACzB,OAAO,CAAC,iBAAiB,EACzB,OAAO,EACP,aAAa,EACb,OAAO,EACP,MAAM,CACP,CAAC;YAEF,OAAO,OAAO,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC;QAErC,CAAC,CAAC,CAAC;KAEJ;SAAM;QAEL,cAAc,GAAG,QAAQ,CAAC;KAC3B;IAGD,2BAA2B,CAAC,MAAM,CAAC,iBAAiB,EAAE,cAAc,EAAE,MAAM,EAAE,OAAO,CAAC,SAAS,CAAC,CAAC;IAEjG,OAAO,cAAc,CAAC;AACxB,CAAC;AAED,SAAS,2BAA2B,CAClC,iBAA+C,EAC/C,QAAuC,EACvC,MAAyB,EACzB,SAA+B;IAG/B,QAAQ,QAAQ,CAAC,MAAM,EAAE;QAEvB,KAAK,YAAY,CAAC,mBAAmB;YACnC,IAAI,MAAM,CAAC,UAAU,EAAE,EAAE;gBACvB,OAAO,QAAQ,CAAC;aACjB;iBAAM;gBACL,IAAM,uBAAuB,GAAG,4BAA4B,CAAC,iBAAiB,CAAC,CAAC;gBAChF,IAAI,GAAG,GAAG,UAAU,CAAC,cAAc,CAAC;gBACpC,GAAG,IAAI,qBAAqB,CAAC,uBAAuB,EAAE,MAAM,CAAC,CAAC;gBAC9D,GAAG,IAAI,0CAA0C,CAAC,SAAS,EAAE,uBAAuB,EAAE,WAAW,CAAC,CAAC;gBACnG,MAAM,IAAI,KAAK,CAAC,GAAG,CAAC,CAAC;aACtB;QAEH,KAAK,YAAY,CAAC,uBAAuB;YACvC,OAAO,QAAQ,CAAC;QAClB,KAAK,YAAY,CAAC,yBAAyB,CAAC;QAC5C;YACE,IAAI,CAAC,MAAM,CAAC,OAAO,EAAE,EAAE;gBACrB,IAAM,uBAAuB,GAAG,4BAA4B,CAAC,iBAAiB,CAAC,CAAC;gBAChF,IAAI,GAAG,GAAM,UAAU,CAAC,eAAe,SAAI,uBAAyB,CAAC;gBACrE,GAAG,IAAI,0CAA0C,CAAC,SAAS,EAAE,uBAAuB,EAAE,WAAW,CAAC,CAAC;gBACnG,MAAM,IAAI,KAAK,CAAC,GAAG,CAAC,CAAC;aACtB;iBAAM;gBACL,OAAO,QAAQ,CAAC;aACjB;KACJ;AAEH,CAAC;AAED,SAAS,kBAAkB,CACzB,cAAyC,EACzC,gBAAyB,EACzB,iBAA+C,EAC/C,OAA2B,EAC3B,aAAwC,EACxC,MAAyB;IAGzB,IAAI,cAAyC,CAAC;IAC9C,IAAI,YAAgC,CAAC;IAErC,IAAI,aAAa,KAAK,IAAI,EAAE;QAE1B,cAAc,GAAG,kBAAkB,CAAC,cAAc,EAAE,gBAAgB,EAAE,OAAO,EAAE,IAAI,EAAE,MAAM,CAAC,CAAC;QAE7F,YAAY,GAAG,IAAI,OAAO,CACxB,iBAAiB,EACjB,OAAO,EACP,IAAI,EACJ,cAAc,EACd,MAAM,CACP,CAAC;QAEF,IAAM,OAAO,GAAG,IAAI,IAAI,CAAC,OAAO,EAAE,YAAY,CAAC,CAAC;QAChD,OAAO,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;KAE1B;SAAM;QACL,cAAc,GAAG,kBAAkB,CAAC,cAAc,EAAE,gBAAgB,EAAE,OAAO,EAAE,aAAa,EAAE,MAAM,CAAC,CAAC;QACtG,YAAY,GAAG,aAAa,CAAC,eAAe,CAAC,MAAM,CAAC,iBAAiB,EAAE,cAAc,EAAE,MAAM,CAAC,CAAC;KAChG;IAED,cAAc,CAAC,OAAO,CAAC,UAAC,OAAO;QAE7B,IAAI,eAAe,GAA8B,IAAI,CAAC;QAEtD,IAAI,MAAM,CAAC,OAAO,EAAE,EAAE;YACpB,eAAe,GAAG,YAAY,CAAC,eAAe,CAAC,OAAO,CAAC,iBAAiB,EAAE,OAAO,EAAE,MAAM,CAAC,CAAC;SAC5F;aAAM;YACL,IAAI,OAAO,CAAC,KAAK,EAAE;gBACjB,OAAO;aACR;YACD,eAAe,GAAG,YAAY,CAAC;SAChC;QAED,IAAI,OAAO,CAAC,IAAI,KAAK,eAAe,CAAC,QAAQ,IAAI,OAAO,CAAC,kBAAkB,KAAK,IAAI,EAAE;YAEpF,IAAM,YAAY,GAAG,eAAe,CAAC,cAAc,EAAE,OAAO,CAAC,kBAAkB,CAAC,CAAC;YAEjF,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC,OAAO,CAAC,mBAAmB,EAAE;gBAIlD,IAAM,wBAAwB,GAAG,2BAA2B,CAAC,cAAc,EAAE,OAAO,CAAC,kBAAkB,CAAC,CAAC;gBAEzG,IAAI,YAAY,CAAC,MAAM,GAAG,wBAAwB,EAAE;oBAClD,IAAM,KAAK,GAAG,UAAU,CAAC,yBAAyB,CAAC,eAAe,CAAC,OAAO,CAAC,kBAAkB,CAAC,CAAC,CAAC;oBAChG,MAAM,IAAI,KAAK,CAAC,KAAK,CAAC,CAAC;iBACxB;aACF;YAED,YAAY,CAAC,OAAO,CAAC,UAAC,UAA6B;gBACjD,kBAAkB,CAAC,cAAc,EAAE,KAAK,EAAE,UAAU,CAAC,iBAAiB,EAAE,OAAO,EAAE,eAAe,EAAE,UAAU,CAAC,CAAC;YAChH,CAAC,CAAC,CAAC;SAEJ;IAEH,CAAC,CAAC,CAAC;AAEL,CAAC;AAED,SAAS,WAAW,CAClB,SAA+B,EAC/B,iBAAkD;IAGlD,IAAI,QAAQ,GAA4B,EAAE,CAAC;IAC3C,IAAM,iBAAiB,GAAG,oBAAoB,CAAC,SAAS,CAAC,CAAC;IAE1D,IAAI,iBAAiB,CAAC,MAAM,CAAC,iBAAiB,CAAC,EAAE;QAE/C,QAAQ,GAAG,iBAAiB,CAAC,GAAG,CAAC,iBAAiB,CAA4B,CAAC;KAEhF;SAAM,IAAI,SAAS,CAAC,MAAM,KAAK,IAAI,EAAE;QAGpC,QAAQ,GAAG,WAAW,CAAI,SAAS,CAAC,MAAM,EAAE,iBAAiB,CAAC,CAAC;KAEhE;IAED,OAAO,QAAQ,CAAC;AAClB,CAAC;AAED,SAAS,IAAI,CACX,cAAyC,EACzC,SAA+B,EAC/B,aAAsB,EACtB,UAAiC,EACjC,iBAA+C,EAC/C,GAA8B,EAC9B,KAAe,EACf,gBAAwB;IAAxB,iCAAA,EAAA,wBAAwB;IAGxB,IAAM,OAAO,GAAG,IAAI,OAAO,CAAC,SAAS,CAAC,CAAC;IACvC,IAAM,MAAM,GAAG,aAAa,CAAC,aAAa,EAAE,UAAU,EAAE,iBAAiB,EAAE,EAAE,EAAE,GAAG,EAAE,KAAK,CAAC,CAAC;IAE3F,IAAI;QACF,kBAAkB,CAAC,cAAc,EAAE,gBAAgB,EAAE,iBAAiB,EAAE,OAAO,EAAE,IAAI,EAAE,MAAM,CAAC,CAAC;QAC/F,OAAO,OAAO,CAAC;KAChB;IAAC,OAAO,KAAK,EAAE;QACd,IACE,uBAAuB,CAAC,KAAK,CAAC,EAC9B;YACA,6BAA6B,CAAC,OAAO,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;SACzD;QACD,MAAM,KAAK,CAAC;KACb;AAEH,CAAC;AAED,SAAS,iBAAiB,CACxB,SAA+B,EAC/B,iBAA+C,EAC/C,GAA6B,EAC7B,KAAc;IAGd,IAAM,MAAM,GAAG,IAAI,MAAM,CAAC,cAAc,CAAC,QAAQ,EAAE,EAAE,EAAE,iBAAiB,EAAE,IAAI,QAAQ,CAAC,GAAG,EAAE,KAAK,CAAC,CAAC,CAAC;IACpG,IAAM,OAAO,GAAG,IAAI,OAAO,CAAC,SAAS,CAAC,CAAC;IACvC,IAAM,OAAO,GAAG,IAAI,OAAO,CAAC,iBAAiB,EAAE,OAAO,EAAE,IAAI,EAAE,EAAE,EAAE,MAAM,CAAC,CAAC;IAC1E,OAAO,OAAO,CAAC;AACjB,CAAC;AAED,OAAO,EAAE,IAAI,EAAE,iBAAiB,EAAE,oBAAoB,EAAE,CAAC"}