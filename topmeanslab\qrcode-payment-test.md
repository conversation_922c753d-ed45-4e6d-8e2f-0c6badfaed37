# 二维码扫码支付功能测试

<script setup>
import { ref, onMounted } from 'vue'
import PaymentMethods from './.vitepress/theme/components/Payment/PaymentMethods.vue'
import { useUserStore } from './.vitepress/theme/components/UserCenter/userStore'

const userStore = useUserStore()
const amount = ref(0.01)
const orderInfo = ref({
  subject: '二维码扫码支付测试',
  serviceType: 'qrcode_test'
})

const testResults = ref([])
const isLoggedIn = ref(false)
const currentPaymentData = ref(null)
const showPaymentDialog = ref(false)

const addTestResult = (test, result, details = '') => {
  testResults.value.push({
    test,
    result,
    details,
    timestamp: new Date().toLocaleTimeString()
  })
}

const handlePaymentSuccess = (paymentData) => {
  console.log('支付成功:', paymentData)
  addTestResult('支付成功', '✅ 通过', `订单号: ${paymentData.orderId}`)
  showPaymentDialog.value = false
  currentPaymentData.value = null
}

const handlePaymentCancel = () => {
  console.log('支付取消')
  addTestResult('支付取消', '✅ 通过', '用户主动取消支付')
  showPaymentDialog.value = false
  currentPaymentData.value = null
}

const handlePaymentError = (error) => {
  console.error('支付错误:', error)
  addTestResult('支付错误', '❌ 失败', error.message)
  showPaymentDialog.value = false
  currentPaymentData.value = null
}

const testQRCodeGeneration = async () => {
  try {
    addTestResult('二维码生成测试', '🚀 开始', '正在创建支付订单...')
    
    const response = await fetch(`${import.meta.env.VITE_BACKEND_SRV_URL}/api/payment/create`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${userStore.token}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        amount: amount.value,
        subject: '二维码测试订单',
        serviceType: 'qrcode_test'
      })
    })
    
    const result = await response.json()
    
    if (response.ok && result.success) {
      addTestResult('二维码生成', '✅ 成功', `订单号: ${result.data.outTradeNo}`)
      addTestResult('二维码数据', '✅ 获取', `类型: ${result.data.qrCode.startsWith('data:') ? 'Base64 SVG' : 'URL'}`)
      
      currentPaymentData.value = result.data
      return result.data
    } else {
      addTestResult('二维码生成', '❌ 失败', result.message || '未知错误')
      return null
    }
  } catch (error) {
    addTestResult('二维码生成', '❌ 失败', error.message)
    return null
  }
}

const testPaymentStatus = async (outTradeNo) => {
  try {
    const response = await fetch(`${import.meta.env.VITE_BACKEND_SRV_URL}/api/payment/status/${outTradeNo}`, {
      headers: {
        'Authorization': `Bearer ${userStore.token}`,
        'Content-Type': 'application/json'
      }
    })
    
    const result = await response.json()
    
    if (response.ok && result.success) {
      addTestResult('支付状态查询', '✅ 成功', `状态: ${result.data.status}`)
      return result.data
    } else {
      addTestResult('支付状态查询', '❌ 失败', result.message || '未知错误')
      return null
    }
  } catch (error) {
    addTestResult('支付状态查询', '❌ 失败', error.message)
    return null
  }
}

const simulatePaymentSuccess = async () => {
  if (!currentPaymentData.value) {
    addTestResult('模拟支付', '❌ 失败', '没有当前支付订单')
    return
  }
  
  addTestResult('模拟支付', '🚀 开始', '模拟用户扫码支付成功...')
  
  // 等待30秒后查询状态（后端会模拟支付成功）
  setTimeout(async () => {
    const statusData = await testPaymentStatus(currentPaymentData.value.outTradeNo)
    if (statusData && statusData.status === 'success') {
      addTestResult('模拟支付', '✅ 成功', '支付状态已更新为成功')
      handlePaymentSuccess({ orderId: currentPaymentData.value.outTradeNo })
    }
  }, 31000) // 31秒后查询（后端设置30秒后模拟成功）
  
  addTestResult('模拟支付', '⏳ 等待', '请等待30秒后查看支付状态变化')
}

const runCompleteTest = async () => {
  testResults.value = []
  addTestResult('完整测试', '🚀 开始', '开始二维码扫码支付完整测试')
  
  // 1. 测试二维码生成
  const paymentData = await testQRCodeGeneration()
  
  if (paymentData) {
    // 2. 测试支付状态查询
    await testPaymentStatus(paymentData.outTradeNo)
    
    // 3. 提示用户可以模拟支付
    addTestResult('测试提示', '💡 说明', '您可以点击"模拟扫码支付"按钮测试支付成功流程')
  }
}

onMounted(() => {
  isLoggedIn.value = userStore.isLoggedIn
})
</script>

## 二维码扫码支付功能测试

这是一个专门测试二维码扫码支付功能的页面，包含完整的支付流程验证。

### 用户状态

<div v-if="!isLoggedIn" style="background: #fff2f0; border: 1px solid #ffccc7; padding: 16px; border-radius: 6px; margin: 16px 0;">
  <h4 style="color: #cf1322; margin: 0 0 8px 0;">⚠️ 用户未登录</h4>
  <p style="margin: 0;">请先登录后再进行支付测试。<a href="/">点击这里登录</a></p>
</div>

<div v-else style="background: #f6ffed; border: 1px solid #b7eb8f; padding: 16px; border-radius: 6px; margin: 16px 0;">
  <h4 style="color: #389e0d; margin: 0 0 8px 0;">✅ 用户已登录</h4>
  <p style="margin: 0;">当前用户: {{ userStore.userInfo.nickname }} ({{ userStore.userInfo.account }})</p>
</div>

### 测试控制面板

<div v-if="isLoggedIn" style="background: #fafafa; border: 1px solid #d9d9d9; border-radius: 6px; padding: 16px; margin: 16px 0;">
  <h4 style="margin: 0 0 16px 0;">测试操作</h4>
  
  <div style="display: flex; gap: 12px; flex-wrap: wrap; margin-bottom: 16px;">
    <button @click="runCompleteTest" style="padding: 8px 16px; background: #1677ff; color: white; border: none; border-radius: 4px; cursor: pointer;">
      🧪 完整测试
    </button>
    
    <button @click="testQRCodeGeneration" style="padding: 8px 16px; background: #52c41a; color: white; border: none; border-radius: 4px; cursor: pointer;">
      📱 生成二维码
    </button>
    
    <button @click="simulatePaymentSuccess" :disabled="!currentPaymentData" style="padding: 8px 16px; background: #fa8c16; color: white; border: none; border-radius: 4px; cursor: pointer; opacity: currentPaymentData ? 1 : 0.5;">
      💰 模拟扫码支付
    </button>
  </div>
  
  <div style="font-size: 12px; color: #666;">
    <p><strong>测试说明:</strong></p>
    <ul style="margin: 8px 0; padding-left: 20px;">
      <li><strong>完整测试</strong>: 执行完整的二维码生成和状态查询测试</li>
      <li><strong>生成二维码</strong>: 单独测试二维码生成功能</li>
      <li><strong>模拟扫码支付</strong>: 模拟用户扫码支付成功（需要先生成二维码）</li>
    </ul>
  </div>
</div>

### 测试结果

<div style="background: #fafafa; border: 1px solid #d9d9d9; border-radius: 6px; padding: 16px; margin: 16px 0;">
  <h4 style="margin: 0 0 16px 0;">测试日志</h4>
  
  <div v-if="testResults.length === 0" style="color: #666; font-style: italic; text-align: center; padding: 20px;">
    暂无测试结果，请点击上方按钮开始测试
  </div>
  
  <div v-for="(result, index) in testResults" :key="index" style="margin-bottom: 8px; padding: 12px; background: white; border-radius: 4px; border-left: 3px solid #1677ff;">
    <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 4px;">
      <span style="font-weight: bold;">{{ result.test }}: {{ result.result }}</span>
      <span style="color: #666; font-size: 12px;">{{ result.timestamp }}</span>
    </div>
    <div v-if="result.details" style="color: #666; font-size: 12px;">
      {{ result.details }}
    </div>
  </div>
</div>

### 实际支付测试

<div v-if="isLoggedIn">
  <h4>真实支付界面测试</h4>
  <p style="color: #666; font-size: 14px; margin-bottom: 16px;">
    下面是真实的支付界面，您可以测试完整的用户体验流程：
  </p>
  
  <PaymentMethods
    :amount="amount"
    :order-info="orderInfo"
    @payment-success="handlePaymentSuccess"
    @payment-cancel="handlePaymentCancel"
    @payment-error="handlePaymentError"
  />
</div>

### 功能验证清单

#### 二维码生成功能
- [ ] 支付订单创建成功
- [ ] 二维码数据正确返回
- [ ] 二维码图片正常显示
- [ ] 订单信息准确显示

#### 支付状态管理
- [ ] 支付状态查询接口正常
- [ ] 状态轮询机制工作
- [ ] 支付成功状态更新
- [ ] 支付超时处理

#### 用户体验
- [ ] 二维码弹窗正常显示
- [ ] 倒计时功能正常
- [ ] 取消支付功能正常
- [ ] 错误提示友好

#### 扫码支付模拟
- [ ] 模拟支付成功流程
- [ ] 支付状态实时更新
- [ ] 支付完成回调触发
- [ ] 界面状态正确更新

### 技术说明

#### 二维码生成
- **类型**: SVG 格式，Base64 编码
- **尺寸**: 200x200 像素
- **内容**: 包含订单信息和金额
- **显示**: 直接在浏览器中渲染

#### 支付状态模拟
- **初始状态**: pending (等待支付)
- **模拟时间**: 30秒后自动变为 success
- **查询频率**: 每3秒查询一次
- **超时时间**: 30分钟自动过期

#### 测试模式说明
当前使用测试模式，所有支付都是模拟的：
- 二维码是 SVG 模拟图案，不是真实的支付宝二维码
- 支付状态通过时间模拟，不需要真实扫码
- 所有金额都是测试金额，不会产生真实交易
