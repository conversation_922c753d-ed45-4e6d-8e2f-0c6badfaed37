{"version": 3, "file": "IsOptional.js", "sourceRoot": "", "sources": ["../../../../src/decorator/common/IsOptional.ts"], "names": [], "mappings": "AAEA,OAAO,EAAE,eAAe,EAAE,MAAM,kCAAkC,CAAC;AACnE,OAAO,EAAE,kBAAkB,EAAE,MAAM,mCAAmC,CAAC;AACvE,OAAO,EAAE,kBAAkB,EAAE,MAAM,gCAAgC,CAAC;AAEpE;;GAEG;AACH,MAAM,UAAU,UAAU,CAAC,iBAAqC;IAC9D,OAAO,UAAU,MAAc,EAAE,YAAoB;QACnD,IAAM,IAAI,GAA2B;YACnC,IAAI,EAAE,eAAe,CAAC,sBAAsB;YAC5C,MAAM,EAAE,MAAM,CAAC,WAAW;YAC1B,YAAY,EAAE,YAAY;YAC1B,WAAW,EAAE;gBACX,UAAC,MAAW,EAAE,KAAU;oBACtB,OAAO,MAAM,CAAC,YAAY,CAAC,KAAK,IAAI,IAAI,MAAM,CAAC,YAAY,CAAC,KAAK,SAAS,CAAC;gBAC7E,CAAC;aACF;YACD,iBAAiB,EAAE,iBAAiB;SACrC,CAAC;QACF,kBAAkB,EAAE,CAAC,qBAAqB,CAAC,IAAI,kBAAkB,CAAC,IAAI,CAAC,CAAC,CAAC;IAC3E,CAAC,CAAC;AACJ,CAAC", "sourcesContent": ["import { ValidationOptions } from '../ValidationOptions';\nimport { ValidationMetadataArgs } from '../../metadata/ValidationMetadataArgs';\nimport { ValidationTypes } from '../../validation/ValidationTypes';\nimport { ValidationMetadata } from '../../metadata/ValidationMetadata';\nimport { getMetadataStorage } from '../../metadata/MetadataStorage';\n\n/**\n * Checks if value is missing and if so, ignores all validators.\n */\nexport function IsOptional(validationOptions?: ValidationOptions): PropertyDecorator {\n  return function (object: object, propertyName: string): void {\n    const args: ValidationMetadataArgs = {\n      type: ValidationTypes.CONDITIONAL_VALIDATION,\n      target: object.constructor,\n      propertyName: propertyName,\n      constraints: [\n        (object: any, value: any): boolean => {\n          return object[propertyName] !== null && object[propertyName] !== undefined;\n        },\n      ],\n      validationOptions: validationOptions,\n    };\n    getMetadataStorage().addValidationMetadata(new ValidationMetadata(args));\n  };\n}\n"]}