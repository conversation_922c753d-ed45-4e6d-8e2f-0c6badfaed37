import{S as e}from"./chunks/ServicePurchase.C-uqiPmC.js";import{c as a,o as t,G as r}from"./chunks/framework.neMYHtQj.js";import"./chunks/theme.Ch1k4S35.js";const _=JSON.parse('{"title":"","description":"","frontmatter":{"layout":"page"},"headers":[],"relativePath":"service-purchase/vip.md","filePath":"service-purchase/vip.md"}'),s={name:"service-purchase/vip.md"},l=Object.assign(s,{setup(c){return(i,o)=>(t(),a("div",null,[r(e)]))}});export{_ as __pageData,l as default};
