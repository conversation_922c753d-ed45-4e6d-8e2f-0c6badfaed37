import { ValidationOptions } from '../ValidationOptions';
export declare const IS_ISO31661_ALPHA_3 = "isISO31661Alpha3";
/**
 * Check if the string is a valid [ISO 3166-1 alpha-3](https://en.wikipedia.org/wiki/ISO_3166-1_alpha-3) officially assigned country code.
 */
export declare function isISO31661Alpha3(value: unknown): boolean;
/**
 * Check if the string is a valid [ISO 3166-1 alpha-3](https://en.wikipedia.org/wiki/ISO_3166-1_alpha-3) officially assigned country code.
 */
export declare function IsISO31661Alpha3(validationOptions?: ValidationOptions): PropertyDecorator;
