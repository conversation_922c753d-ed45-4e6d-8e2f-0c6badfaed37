<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>登录功能测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .test-container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .test-section {
            margin-bottom: 30px;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .test-section h3 {
            margin-top: 0;
            color: #333;
        }
        .test-case {
            margin: 10px 0;
            padding: 10px;
            background: #f9f9f9;
            border-left: 4px solid #007bff;
        }
        .test-case.error {
            border-left-color: #dc3545;
        }
        .test-case.success {
            border-left-color: #28a745;
        }
        .test-case.warning {
            border-left-color: #ffc107;
        }
        .test-input {
            margin: 5px 0;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            width: 200px;
        }
        .test-button {
            padding: 8px 16px;
            margin: 5px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            background: #007bff;
            color: white;
        }
        .test-button:hover {
            background: #0056b3;
        }
        .result {
            margin-top: 10px;
            padding: 10px;
            border-radius: 4px;
            display: none;
        }
        .result.show {
            display: block;
        }
        .result.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .result.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🧪 登录功能测试页面</h1>
        <p>此页面用于测试登录页面的各种错误提示功能</p>
        
        <div class="test-section">
            <h3>📋 测试清单</h3>
            
            <div class="test-case error">
                <h4>❌ 错误情况测试</h4>
                <ul>
                    <li>空账号密码提交</li>
                    <li>空验证码提交</li>
                    <li>错误验证码提交</li>
                    <li>不存在的账号登录</li>
                    <li>错误密码登录</li>
                    <li>账号格式错误注册</li>
                    <li>密码强度不足注册</li>
                    <li>密码确认不一致注册</li>
                    <li>重复账号注册</li>
                </ul>
            </div>
            
            <div class="test-case success">
                <h4>✅ 成功情况测试</h4>
                <ul>
                    <li>正确账号密码登录</li>
                    <li>新账号注册成功</li>
                </ul>
            </div>
            
            <div class="test-case warning">
                <h4>⚠️ 警告情况测试</h4>
                <ul>
                    <li>网络超时重试</li>
                    <li>忘记密码功能提示</li>
                </ul>
            </div>
        </div>
        
        <div class="test-section">
            <h3>🔧 手动测试步骤</h3>
            <ol>
                <li>打开登录页面 (访问 <code>/login</code> 或首页点击登录)</li>
                <li>尝试以下测试用例：</li>
                <ul>
                    <li><strong>空表单提交</strong>: 不填写任何信息直接点击登录/注册</li>
                    <li><strong>部分信息缺失</strong>: 只填写账号不填密码，或不填验证码</li>
                    <li><strong>格式错误</strong>: 账号使用特殊字符，密码少于6位</li>
                    <li><strong>验证码错误</strong>: 故意输入错误的验证码</li>
                    <li><strong>密码不一致</strong>: 注册时两次密码输入不同</li>
                </ul>
                <li>观察是否有清晰的错误提示显示</li>
                <li>检查错误提示是否准确描述了问题</li>
                <li>验证错误提示是否足够明显（颜色、位置、持续时间）</li>
            </ol>
        </div>
        
        <div class="test-section">
            <h3>📝 预期结果</h3>
            <p>每个错误情况都应该显示：</p>
            <ul>
                <li>🔴 <strong>错误提示消息</strong>: 使用 ElMessage.error 显示红色错误信息</li>
                <li>🔔 <strong>通知提示</strong>: 使用 ElNotification.error 显示通知</li>
                <li>⚠️ <strong>备用提示</strong>: 如果前两者失败，使用原生 alert</li>
                <li>📍 <strong>具体错误描述</strong>: 明确告知用户哪里出错了</li>
                <li>💡 <strong>解决建议</strong>: 提示用户如何修正错误</li>
            </ul>
        </div>
        
        <div class="test-section">
            <h3>🚨 如果没有错误提示</h3>
            <p>请检查以下问题：</p>
            <ol>
                <li>浏览器控制台是否有 JavaScript 错误</li>
                <li>Element Plus 是否正确加载</li>
                <li>网络请求是否正常发送</li>
                <li>后端是否返回了正确的错误信息</li>
            </ol>
        </div>
    </div>
    
    <div class="test-container">
        <h2>🔗 快速链接</h2>
        <button class="test-button" onclick="window.open('/login', '_blank')">打开登录页面</button>
        <button class="test-button" onclick="window.open('/', '_blank')">打开首页</button>
        <button class="test-button" onclick="window.location.reload()">刷新测试页面</button>
    </div>
    
    <script>
        console.log('🧪 登录功能测试页面已加载');
        console.log('请按照测试步骤进行手动测试');
    </script>
</body>
</html>
