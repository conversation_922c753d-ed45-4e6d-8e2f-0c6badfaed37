import axios from 'axios'
import { useUserStore } from '../UserCenter/userStore'

const API_BASE = `${import.meta.env.VITE_BACKEND_SRV_URL}/api`

class PaymentService {
  constructor() {
    this.userStore = useUserStore()
  }

  // 获取认证头
  getAuthHeaders() {
    const token = this.userStore.token
    return token ? {
      'Authorization': `Bearer ${token}`,
      'Content-Type': 'application/json'
    } : {
      'Content-Type': 'application/json'
    }
  }

  // 创建支付订单
  async createPayment(paymentData) {
    try {
      const { amount, subject, serviceType } = paymentData

      if (!amount || amount <= 0) {
        throw new Error('支付金额无效')
      }

      if (!this.userStore.isLoggedIn) {
        throw new Error('请先登录')
      }

      const response = await axios.post(`${API_BASE}/payment/create`, {
        amount: parseFloat(amount),
        subject: subject || 'TopMeansLab 服务购买',
        serviceType: serviceType || 'premium_service'
      }, {
        headers: this.getAuthHeaders()
      })

      if (response.data.success) {
        console.log('支付订单创建成功:', response.data.data)
        return {
          success: true,
          data: response.data.data
        }
      } else {
        throw new Error(response.data.message || '创建支付订单失败')
      }
    } catch (error) {
      console.error('创建支付订单失败:', error)
      return {
        success: false,
        message: error.response?.data?.message || error.message || '创建支付订单失败'
      }
    }
  }

  // 查询支付状态
  async queryPaymentStatus(outTradeNo) {
    try {
      if (!outTradeNo) {
        throw new Error('订单号不能为空')
      }

      const response = await axios.get(`${API_BASE}/payment/status/${outTradeNo}`, {
        headers: this.getAuthHeaders()
      })

      return response.data
    } catch (error) {
      console.error('查询支付状态失败:', error)
      return {
        success: false,
        message: error.response?.data?.message || error.message || '查询支付状态失败'
      }
    }
  }

  // 获取支付记录
  async getPaymentRecord(outTradeNo) {
    try {
      if (!outTradeNo) {
        throw new Error('订单号不能为空')
      }

      const response = await axios.get(`${API_BASE}/payment/record/${outTradeNo}`, {
        headers: this.getAuthHeaders()
      })

      return response.data
    } catch (error) {
      console.error('获取支付记录失败:', error)
      return {
        success: false,
        message: error.response?.data?.message || error.message || '获取支付记录失败'
      }
    }
  }

  // 取消支付
  async cancelPayment(outTradeNo) {
    try {
      if (!outTradeNo) {
        throw new Error('订单号不能为空')
      }

      const response = await axios.post(`${API_BASE}/payment/cancel/${outTradeNo}`, {}, {
        headers: this.getAuthHeaders()
      })

      return response.data
    } catch (error) {
      console.error('取消支付失败:', error)
      return {
        success: false,
        message: error.response?.data?.message || error.message || '取消支付失败'
      }
    }
  }

  // 轮询支付状态
  async pollPaymentStatus(outTradeNo, options = {}) {
    const {
      maxAttempts = 60,        // 最大轮询次数
      interval = 2000,         // 轮询间隔（毫秒）
      onStatusChange = null,   // 状态变化回调
      onTimeout = null         // 超时回调
    } = options

    let attempts = 0
    let lastStatus = null

    const poll = async () => {
      try {
        attempts++
        
        const result = await this.queryPaymentStatus(outTradeNo)
        
        if (result.success) {
          const currentStatus = result.data.status
          
          // 状态发生变化时调用回调
          if (currentStatus !== lastStatus && onStatusChange) {
            onStatusChange(currentStatus, result.data)
          }
          
          lastStatus = currentStatus
          
          // 支付成功或失败时停止轮询
          if (currentStatus === 'success' || currentStatus === 'closed' || currentStatus === 'cancelled') {
            return {
              success: true,
              status: currentStatus,
              data: result.data
            }
          }
          
          // 达到最大轮询次数
          if (attempts >= maxAttempts) {
            if (onTimeout) {
              onTimeout()
            }
            return {
              success: false,
              message: '支付状态查询超时',
              status: 'timeout'
            }
          }
          
          // 继续轮询
          setTimeout(poll, interval)
        } else {
          // 查询失败，停止轮询
          return {
            success: false,
            message: result.message,
            status: 'error'
          }
        }
      } catch (error) {
        console.error('轮询支付状态失败:', error)
        return {
          success: false,
          message: error.message,
          status: 'error'
        }
      }
    }

    return poll()
  }

  // 生成二维码数据URL（简化版本）
  generateQRCodeDataURL(qrCodeText) {
    try {
      // 直接返回 SVG 二维码
      return `data:image/svg+xml;base64,${btoa(this.generateQRCodeSVG(qrCodeText))}`
    } catch (error) {
      console.error('生成二维码失败:', error)
      return null
    }
  }

  // 生成简单的二维码 SVG（降级方案）
  generateQRCodeSVG(text) {
    return `
      <svg width="200" height="200" xmlns="http://www.w3.org/2000/svg">
        <rect width="200" height="200" fill="white" stroke="#ccc"/>
        <text x="100" y="90" text-anchor="middle" font-size="14" fill="black" font-weight="bold">
          支付二维码
        </text>
        <text x="100" y="110" text-anchor="middle" font-size="10" fill="#666">
          订单号: ${text.substring(0, 15)}...
        </text>
        <text x="100" y="130" text-anchor="middle" font-size="10" fill="#999">
          请使用支付宝扫码支付
        </text>
      </svg>
    `
  }

  // 验证支付金额
  validateAmount(amount) {
    const numAmount = parseFloat(amount)
    
    if (isNaN(numAmount) || numAmount <= 0) {
      return {
        valid: false,
        message: '支付金额必须大于0'
      }
    }
    
    if (numAmount > 10000) {
      return {
        valid: false,
        message: '单次支付金额不能超过10000元'
      }
    }
    
    if (numAmount < 0.01) {
      return {
        valid: false,
        message: '支付金额不能少于0.01元'
      }
    }
    
    return {
      valid: true,
      amount: numAmount
    }
  }

  // 格式化金额显示
  formatAmount(amount) {
    return `¥${parseFloat(amount).toFixed(2)}`
  }

  // 格式化支付状态
  formatPaymentStatus(status) {
    const statusMap = {
      'pending': '待支付',
      'success': '支付成功',
      'closed': '已关闭',
      'cancelled': '已取消',
      'timeout': '支付超时',
      'error': '支付异常'
    }
    
    return statusMap[status] || '未知状态'
  }
}

// 创建单例实例
const paymentService = new PaymentService()

export default paymentService
