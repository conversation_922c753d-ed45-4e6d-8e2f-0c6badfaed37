import e from"./chunks/LoginPage.BrdIiakU.js";import{c as t,o as a,G as o}from"./chunks/framework.B19ydMwb.js";import"./chunks/Valicode.C71W2eNO.js";import"./chunks/theme.CmWpOUCL.js";const _=JSON.parse('{"title":"登录 - TopMeansLab","description":"","frontmatter":{"layout":"page","title":"登录 - TopMeansLab"},"headers":[],"relativePath":"login.md","filePath":"login.md"}'),r={name:"login.md"},d=Object.assign(r,{setup(n){return(i,s)=>(a(),t("div",null,[o(e)]))}});export{_ as __pageData,d as default};
