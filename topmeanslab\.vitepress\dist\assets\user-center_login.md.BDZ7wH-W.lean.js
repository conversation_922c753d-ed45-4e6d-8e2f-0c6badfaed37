import{L as e}from"./chunks/UserCenter.Bv8vqyOw.js";import{c as t,o as r,G as a}from"./chunks/framework.neMYHtQj.js";import"./chunks/Valicode.Dl4gpGZx.js";import"./chunks/theme.Ch1k4S35.js";const _=JSON.parse('{"title":"","description":"","frontmatter":{"layout":"page"},"headers":[],"relativePath":"user-center/login.md","filePath":"user-center/login.md"}'),o={name:"user-center/login.md"},u=Object.assign(o,{setup(n){return(s,c)=>(r(),t("div",null,[a(e)]))}});export{_ as __pageData,u as default};
