<template>
    <div class="login-page">
      <!-- 背景装饰 -->
      <div class="background-decoration">
        <div class="floating-shapes">
          <div class="shape shape-1"></div>
          <div class="shape shape-2"></div>
          <div class="shape shape-3"></div>
          <div class="shape shape-4"></div>
          <div class="shape shape-5"></div>
        </div>
      </div>
  
      <!-- 主要内容区域 -->
      <div class="login-container">
        <!-- 左侧品牌区域 -->
        <div class="brand-section">
          <div class="brand-content">
            <h1 class="brand-title">旅美</h1>
            <p class="brand-subtitle">一段旅途，一段人生，智慧出行，美好常伴</p>
            <div class="brand-features">
              <div class="feature-item">
                <div class="feature-icon">🗺️</div>
                <span>智能路线规划</span>
              </div>
              <div class="feature-item">
                <div class="feature-icon">🏨</div>
                <span>酒店推荐</span>
              </div>
              <div class="feature-item">
                <div class="feature-icon">🍽️</div>
                <span>美食发现</span>
              </div>
              <div class="feature-item">
                <div class="feature-icon">📸</div>
                <span>景点攻略</span>
              </div>
            </div>
          </div>
        </div>
  
        <!-- 右侧登录区域 -->
        <div class="auth-section">
          <div class="auth-container">
            <div class="auth-header">
              <h2>欢迎使用 TopMeansLab</h2>
              <p>请登录您的账户开始旅程规划</p>
            </div>
  
            <!-- 登录/注册切换标签 -->
            <div class="auth-tabs">
              <button
                :class="['tab-btn', { active: activeTab === 'login' }]"
                @click="activeTab = 'login'"
              >
                登录
              </button>
              <button
                :class="['tab-btn', { active: activeTab === 'register' }]"
                @click="activeTab = 'register'"
              >
                注册
              </button>
            </div>
  
            <!-- 登录表单 -->
            <div v-if="activeTab === 'login'" class="auth-form">
              <div class="form-group">
                <label>账号</label>
                <input
                  v-model="loginForm.account"
                  type="text"
                  placeholder="请输入手机号或邮箱"
                  class="form-input"
                  required
                >
              </div>
              <div class="form-group">
                <label>密码</label>
                <input
                  v-model="loginForm.password"
                  type="password"
                  placeholder="请输入密码"
                  class="form-input"
                  required
                >
              </div>
              <div class="form-group">
                <label>验证码</label>
                <div class="captcha-container">
                  <input
                    v-model="loginForm.valicode"
                    type="text"
                    placeholder="请输入验证码"
                    class="form-input captcha-input"
                    required
                  >
                  <div class="captcha-wrapper">
                    <Valicode ref="valicode" @getCode="handleGetCode" />
                  </div>
                </div>
              </div>
              <div class="form-options">
                <label class="remember-me">
                  <input type="checkbox" v-model="loginForm.remember">
                  <span class="checkmark"></span>
                  记住密码
                </label>
                <a href="#" class="forgot-password" @click="handleForgotPassword">忘记密码？</a>
              </div>
              <button @click="handleLogin" class="submit-btn" :disabled="isLoading">
                <span v-if="isLoading" class="loading-spinner"></span>
                {{ isLoading ? '登录中...' : '登录' }}
              </button>

              <!-- 测试按钮 - 仅在开发环境显示 -->
              <div v-if="isDevelopment" class="test-buttons">
                <button @click="testErrorMessage" class="test-btn error">测试错误提示</button>
                <button @click="testSuccessMessage" class="test-btn success">测试成功提示</button>
                <button @click="testWarningMessage" class="test-btn warning">测试警告提示</button>
              </div>
            </div>
  
            <!-- 注册表单 -->
            <div v-if="activeTab === 'register'" class="auth-form">
              <div class="form-group">
                <label>账号</label>
                <input
                  v-model="registerForm.account"
                  type="text"
                  placeholder="请输入你的账号名称"
                  class="form-input"
                  required
                >
              </div>
              <div class="form-group">
                <label>密码</label>
                <input
                  v-model="registerForm.password"
                  type="password"
                  placeholder="请输入密码，由6位以上的英文和数字组成"
                  class="form-input"
                  required
                >
              </div>
              <div class="form-group">
                <label>确认密码</label>
                <input
                  v-model="registerForm.confirmPassword"
                  type="password"
                  placeholder="请再次输入密码，确保两次密码一致"
                  class="form-input"
                  required
                >
              </div>
              <div class="form-group">
                <label>验证码</label>
                <div class="captcha-container">
                  <input
                    v-model="registerForm.valicode"
                    type="text"
                    placeholder="请输入验证码"
                    class="form-input captcha-input"
                    required
                  >
                  <div class="captcha-wrapper">
                    <Valicode ref="valicode" @getCode="handleGetCode" />
                  </div>
                </div>
              </div>
              <button @click="handleRegister" class="submit-btn" :disabled="isLoading">
                <span v-if="isLoading" class="loading-spinner"></span>
                {{ isLoading ? '注册中...' : '注册' }}
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </template>
  
  <script setup>
  import { ref, onMounted, nextTick } from 'vue';
  import 'vue-toastification/dist/index.css'
  import { useUserStore } from './UserCenter/userStore'
  import { ElMessage } from 'element-plus'
  import Valicode from './UserCenter/Valicode.vue';
  import log from 'loglevel';

  const API_BASE = `${import.meta.env.VITE_BACKEND_SRV_URL}/api`;
  log.setLevel('info');

  // 错误提示函数 - 确保消息能够显示
  const showError = (message, title = '错误') => {
    console.error('显示错误信息:', message);

    // 使用多种方式确保用户能看到错误信息
    try {
      // 方式1: ElMessage
      ElMessage.error({
        appendTo: document.body,
        message: message,
        duration: 4000,
        showClose: true,
        center: true
      });
      console.log('ElMessage 成功:', message);
    } catch (e) {
      console.error('ElMessage 失败:', e);
    }

    // try {
    //   // 方式2: ElNotification 作为备选
    //   ElNotification.error({
    //     title: title,
    //     message: message,
    //     duration: 4000,
    //     position: 'top-right'
    //   });
    // } catch (e) {
    //   console.error('ElNotification 失败:', e);
    // }

    // 方式3: 原生 alert 作为最后备选
    // setTimeout(() => {
    //   if (confirm(`${title}: ${message}\n\n点击确定继续`)) {
    //     // 用户确认后继续
    //   }
    // }, 100);
  };

  // 成功提示函数
  const showSuccess = (message, title = '成功') => {
    console.log('显示成功信息:', message);

    try {
      ElMessage.success({
        message: '1111111111111111111',
        duration: 3000,
        showClose: true,
        center: true
      });
      // alert(`${title}: ${message}`);
    } catch (e) {
      console.error('ElMessage 成功提示失败:', e);
      alert(`${title}: ${message}`);
    }
  };

  // 警告提示函数
  const showWarning = (message, title = '警告') => {
    console.warn('显示警告信息:', message);

    try {
      ElMessage.warning({
        message: message,
        duration: 3000,
        showClose: true,
        center: true
      });
    } catch (e) {
      console.error('ElMessage 警告提示失败:', e);
      alert(`${title}: ${message}`);
    }
  };
  
  // 状态管理
  const activeTab = ref('login');
  const userStore = useUserStore()
  const isLoading = ref(false);

  // 开发环境检测
  const isDevelopment = ref(import.meta.env.DEV || import.meta.env.MODE === 'development');
  
  const handleGetCode = (code) => {
    generatedCode.value = code;
  };
  
  // 表单数据
  const loginForm = ref({
    account: '',
    password: '',
    valicode: '',
    remember: false
  });
  
  const registerForm = ref({
    account: '',
    password: '',
    confirmPassword: '',
    valicode: '',
    nickname: '',
    avatar: '/images/default-avatar.jpg',
    signature: '这个人很懒，什么都没写~',
  });
  
  // 存储生成的验证码
  const valicode = ref(null);
  const generatedCode = ref('');
  
  // 登录处理
  const handleLogin = async () => {
    console.log('🚀 开始登录处理');

    // 基础验证
    if (!loginForm.value.account || !loginForm.value.password) {
      showError('请输入账号和密码', '登录验证失败');
      valicode.value?.refresh()
      return;
    }

    if (!loginForm.value.valicode) {
      showError('请输入验证码', '登录验证失败');
      return;
    }

    // 验证码不区分大小写比较
    if (loginForm.value.valicode.toLowerCase() !== generatedCode.value.toLowerCase()) {
      showError('验证码错误，请重新输入', '验证码验证失败');
      valicode.value?.refresh()
      return;
    }

    isLoading.value = true;
    let retryCount = 0;
    const maxRetries = 2;

    const attemptLogin = async () => {
      try {
        log.info('尝试登录:', { account: loginForm.value.account, apiBase: API_BASE });

        const response = await fetch(`${API_BASE}/user/login`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Accept': 'application/json'
          },
          body: JSON.stringify({
            account: loginForm.value.account,
            password: loginForm.value.password
          }),
          signal: AbortSignal.timeout(10000)
        });

        log.info('登录响应状态:', response.status);

        if (!response.ok) {
          const errorText = await response.text();
          log.error('登录响应错误:', errorText);
          throw new Error(`服务器响应错误 (${response.status}): ${response.statusText}`);
        }

        const result = await response.json();
        log.info('登录响应结果:', result);

        if (result.success) {
          console.log('✅ 登录成功');

          // 根据记住密码选项处理密码存储
          if (loginForm.value.remember) {
            userStore.setStoredPassword(loginForm.value.password)
          } else {
            userStore.clearStoredPassword()
          }

          // 保存登录状态到 store
          userStore.setToken(result.token)
          userStore.setUserInfo(result.user)

          // 自动填充账号逻辑
          if (loginForm.value.remember) {
            localStorage.setItem('rememberedAccount', loginForm.value.account)
          } else {
            localStorage.removeItem('rememberedAccount')
          }

          showSuccess('登录成功，正在跳转...', '登录成功');

          setTimeout(() => {
            window.location.reload()
          }, 1000);
        } else {
          console.log('❌ 登录失败:', result);

          // 处理登录失败的具体错误
          let errorMessage = '登录失败';
          let errorTitle = '登录失败';

          if (result.message) {
            if (result.message.includes('账号') || result.message.includes('用户')) {
              errorMessage = '账号不存在或格式错误，请检查您输入的账号是否正确';
              errorTitle = '账号验证失败';
            } else if (result.message.includes('密码')) {
              errorMessage = '密码错误，请检查后重试。如果忘记密码请联系管理员';
              errorTitle = '密码验证失败';
            } else if (result.message.includes('验证码')) {
              errorMessage = '验证码错误，请重新输入验证码';
              errorTitle = '验证码验证失败';
            } else {
              errorMessage = result.message;
              errorTitle = '登录失败';
            }
          }

          showError(errorMessage, errorTitle);
          valicode.value?.refresh();
        }
      } catch (error) {
        retryCount++;
        log.error(`登录尝试 ${retryCount} 失败:`, error);
        console.error('🔥 登录请求异常:', error);

        let errorMessage = '登录失败，请稍后重试';
        let errorTitle = '网络错误';

        if (error.name === 'AbortError' || error.message.includes('timeout')) {
          errorMessage = '请求超时，请检查网络连接后重试';
          errorTitle = '请求超时';
        } else if (error.message.includes('Failed to fetch')) {
          errorMessage = '网络连接失败，请检查网络设置或稍后重试';
          errorTitle = '网络连接失败';
        } else if (error.message.includes('Mixed Content')) {
          errorMessage = '网络安全限制，请联系管理员解决';
          errorTitle = '安全限制';
        } else if (error.message.includes('服务器响应错误')) {
          errorMessage = error.message;
          errorTitle = '服务器错误';
        }

        if (retryCount <= maxRetries) {
          showWarning(`${errorMessage}，正在重试... (${retryCount}/${maxRetries})`, '重试中');
          await new Promise(resolve => setTimeout(resolve, 1000));
          return attemptLogin();
        } else {
          showError(errorMessage, errorTitle);
          valicode.value?.refresh();
        }
      }
    };

    try {
      await attemptLogin();
    } finally {
      isLoading.value = false;
    }
  };
  
  // 注册处理
  const handleRegister = async () => {
    console.log('🚀 开始注册处理');

    // 基础验证
    if (!registerForm.value.account || !registerForm.value.password) {
      showError('请输入账号和密码', '注册验证失败');
      valicode.value?.refresh();
      return;
    }

    // 账号格式验证
    if (registerForm.value.account.length < 3) {
      showError('账号长度至少需要3个字符，请重新输入', '账号格式错误');
      return;
    }

    // 账号字符验证
    const accountRegex = /^[\u4e00-\u9fa5a-zA-Z0-9]+$/;
    if (!accountRegex.test(registerForm.value.account)) {
      showError('账号只能包含中文、英文字母和数字，请重新输入', '账号格式错误');
      return;
    }

    // 密码强度验证
    if (registerForm.value.password.length < 6) {
      showError('密码长度至少需要6个字符，请重新输入', '密码格式错误');
      return;
    }

    // 密码复杂度验证
    const passwordRegex = /^(?=.*[a-zA-Z])(?=.*\d)[a-zA-Z\d@$!%*?&]{6,}$/;
    if (!passwordRegex.test(registerForm.value.password)) {
      showError('密码必须包含至少一个字母和一个数字，长度不少于6位', '密码强度不足');
      return;
    }

    // 密码一致性校验
    if (registerForm.value.password !== registerForm.value.confirmPassword) {
      showError('两次输入的密码不一致，请重新输入', '密码确认失败');
      valicode.value?.refresh();
      return;
    }

    if (!registerForm.value.valicode) {
      showError('请输入验证码', '注册验证失败');
      return;
    }

    // 验证码不区分大小写比较
    if (registerForm.value.valicode.toLowerCase() !== generatedCode.value.toLowerCase()) {
      showError('验证码错误，请重新输入', '验证码验证失败');
      valicode.value?.refresh()
      return;
    }

    isLoading.value = true;

    try {
      log.info('尝试注册:', { account: registerForm.value.account, apiBase: API_BASE });

      const response = await fetch(`${API_BASE}/user/register`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json'
        },
        body: JSON.stringify(registerForm.value),
        signal: AbortSignal.timeout(10000)
      });

      log.info('注册响应状态:', response.status);

      if (!response.ok) {
        const errorText = await response.text();
        log.error('注册响应错误:', errorText);
        throw new Error(`服务器响应错误 (${response.status}): ${response.statusText}`);
      }

      const result = await response.json();
      log.info('注册响应结果:', result);

      if (result.success) {
        console.log('✅ 注册成功');

        showSuccess('注册成功！请使用新账号登录', '注册成功');

        // 切换到登录标签页并预填账号
        activeTab.value = 'login';
        loginForm.value.account = registerForm.value.account;

        // 清空注册表单
        registerForm.value = {
          account: '',
          password: '',
          confirmPassword: '',
          valicode: '',
          nickname: '',
          avatar: '/images/default-avatar.jpg',
          signature: '这个人很懒，什么都没写~'
        };
      } else {
        console.log('❌ 注册失败:', result);

        // 处理注册失败的具体错误
        let errorMessage = '注册失败';
        let errorTitle = '注册失败';

        if (result.message) {
          if (result.message.includes('账号') && result.message.includes('存在')) {
            errorMessage = '该账号已存在，请更换账号或直接登录';
            errorTitle = '账号已存在';
          } else if (result.message.includes('格式')) {
            errorMessage = '账号格式不正确，请使用中文、英文字母或数字';
            errorTitle = '账号格式错误';
          } else if (result.message.includes('密码')) {
            errorMessage = '密码格式不符合要求，请确保包含字母和数字且长度不少于6位';
            errorTitle = '密码格式错误';
          } else if (result.message.includes('重复')) {
            errorMessage = '该账号已被注册，请更换其他账号';
            errorTitle = '账号重复';
          } else {
            errorMessage = result.message;
            errorTitle = '注册失败';
          }
        }

        showError(errorMessage, errorTitle);
        valicode.value?.refresh();
      }
    } catch (error) {
      log.error('注册错误:', error);
      console.error('🔥 注册请求异常:', error);

      let errorMessage = '注册失败，请稍后重试';
      let errorTitle = '网络错误';

      if (error.name === 'AbortError' || error.message.includes('timeout')) {
        errorMessage = '请求超时，请检查网络连接后重试';
        errorTitle = '请求超时';
      } else if (error.message.includes('Failed to fetch')) {
        errorMessage = '网络连接失败，请检查网络设置或稍后重试';
        errorTitle = '网络连接失败';
      } else if (error.message.includes('Mixed Content')) {
        errorMessage = '网络安全限制，请联系管理员解决';
        errorTitle = '安全限制';
      } else if (error.message.includes('服务器响应错误')) {
        errorMessage = error.message;
        errorTitle = '服务器错误';
      }

      showError(errorMessage, errorTitle);
      valicode.value?.refresh();
    } finally {
      isLoading.value = false;
    }
  };
  
  const handleForgotPassword = (e) => {
    e.preventDefault();
    showWarning('忘记密码功能暂未开放，请联系管理员重置密码', '功能提示');
  };

  // 测试函数
  const testErrorMessage = () => {
    showError('这是一个测试错误消息，用于验证错误提示功能是否正常工作', '测试错误');
  };

  const testSuccessMessage = () => {
    showSuccess('这是一个测试成功消息，用于验证成功提示功能是否正常工作', '测试成功');
  };

  const testWarningMessage = () => {
    showWarning('这是一个测试警告消息，用于验证警告提示功能是否正常工作', '测试警告');
  };
  
  onMounted(() => {
    // 自动填充密码
    if (userStore.storedPassword && localStorage.getItem('rememberedAccount')) {
      const remembered = localStorage.getItem('rememberedAccount');
      loginForm.value.account = remembered;
      loginForm.value.password = userStore.storedPassword;
      loginForm.value.remember = true;
    }
  });
  </script>
  
  <style scoped>
  .login-page {
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    overflow-x: hidden;
    overflow-y: auto;
    z-index: 99999;
    -webkit-overflow-scrolling: touch; /* iOS平滑滚动 */
  }
  
  /* 背景装饰 */
  .background-decoration {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    overflow: hidden;
  }
  
  .floating-shapes {
    position: relative;
    width: 100%;
    height: 100%;
  }
  
  .shape {
    position: absolute;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.1);
    animation: float 6s ease-in-out infinite;
  }
  
  .shape-1 {
    width: 80px;
    height: 80px;
    top: 10%;
    left: 10%;
    animation-delay: 0s;
  }
  
  .shape-2 {
    width: 120px;
    height: 120px;
    top: 20%;
    right: 15%;
    animation-delay: 1s;
  }
  
  .shape-3 {
    width: 60px;
    height: 60px;
    bottom: 30%;
    left: 20%;
    animation-delay: 2s;
  }
  
  .shape-4 {
    width: 100px;
    height: 100px;
    bottom: 20%;
    right: 25%;
    animation-delay: 3s;
  }
  
  .shape-5 {
    width: 140px;
    height: 140px;
    top: 50%;
    left: 5%;
    animation-delay: 4s;
  }
  
  @keyframes float {
    0%, 100% {
      transform: translateY(0px) rotate(0deg);
      opacity: 0.7;
    }
    50% {
      transform: translateY(-20px) rotate(180deg);
      opacity: 1;
    }
  }
  
  /* 主容器 */
  .login-container {
    display: flex;
    width: 100%;
    height: 100%;
    position: relative;
    z-index: 1;
    align-items: center;
    justify-content: center;
    gap: 15rem;
    padding: 2rem;
  }
  
  /* 左侧品牌区域 */
  .brand-section {
    flex: 0 0 auto;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    max-width: 500px;
  }
  
  .brand-content {
    text-align: center;
    max-width: 500px;
  }
  
  .brand-title {
    font-size: 3.5rem;
    font-weight: 1000;
    margin-bottom: 1rem;
    text-shadow: 2px 2px 8px rgba(0, 0, 0, 0.4);
    background: linear-gradient(267deg, #ffffff 0%, #e3f2fd 25%, #bbdefb 50%, #90caf9 75%, #ffffff 100%);
    background-position-x: 5rem;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    filter: brightness(1.1) contrast(1.2);
    padding: 0.5em 0;  /* 上下间距变大 */
    line-height: 1.5;  /* 行高增加，避免文字重叠 */
    display: inline-block; /* 确保 padding 生效 */
  }
  
  .brand-subtitle {
    font-size: 1.5rem;
    margin-bottom: 3rem;
    opacity: 0.95;
    font-weight: 400;
    text-shadow: 1px 1px 3px rgba(0, 0, 0, 0.3);
    letter-spacing: 0.5px;
  }
  
  .brand-features {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 1.5rem;
    margin-top: 2.5rem;
    margin-left: 2rem;
    max-width: 400px;
  }
  
  .feature-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 0.8rem;
    padding: 1.8rem 1.2rem;
    background: rgba(255, 255, 255, 0.15);
    border-radius: 16px;
    backdrop-filter: blur(15px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    transition: all 0.3s ease;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  }
  
  .feature-item:hover {
    transform: translateY(-8px) scale(1.02);
    background: rgba(255, 255, 255, 0.2);
    box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
  }
  
  .feature-icon {
    font-size: 2.2rem;
    margin-bottom: 0.5rem;
    filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.2));
    transition: transform 0.3s ease;
  }
  
  .feature-item:hover .feature-icon {
    transform: scale(1.1);
  }
  
  /* 右侧认证区域 */
  .auth-section {
    flex: 0 0 auto;
    display: flex;
    align-items: center;
    justify-content: center;
  }
  
  .auth-container {
    width: 100%;
    max-width: 800px;
    background: rgba(255, 255, 255, 0.98);
    border-radius: 24px;
    padding: 3rem 2.5rem;
    box-shadow: 0 25px 50px rgba(0, 0, 0, 0.15);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.3);
    position: relative;
    overflow: hidden;
  }
  
  .auth-container::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 1px;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.8), transparent);
  }
  
  .auth-header {
    text-align: center;
    margin-bottom: 2rem;
  }
  
  .auth-header h2 {
    font-size: 1.8rem;
    color: #333;
    margin-bottom: 0.5rem;
    font-weight: 600;
  }
  
  .auth-header p {
    color: #666;
    font-size: 0.9rem;
  }
  
  /* 标签切换 */
  .auth-tabs {
    display: flex;
    margin-bottom: 2rem;
    background: #f5f5f5;
    border-radius: 10px;
    padding: 4px;
  }
  
  .tab-btn {
    flex: 1;
    padding: 0.8rem;
    border: none;
    background: none;
    font-size: 1rem;
    color: #666;
    cursor: pointer;
    border-radius: 8px;
    transition: all 0.3s ease;
    font-weight: 500;
  }
  
  .tab-btn.active {
    background: white;
    color: #667eea;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  }
  
  /* 表单样式 */
  .auth-form {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
  }
  
  .form-group {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
  }
  
  .form-group label {
    font-size: 0.9rem;
    color: #555;
    font-weight: 500;
  }
  
  .form-input {
    padding: 1rem;
    border: 2px solid #e1e5e9;
    border-radius: 10px;
    font-size: 1rem;
    transition: all 0.3s ease;
    background: white;
  }
  
  .form-input:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
  }
  
  .captcha-container {
    display: flex;
    gap: 1rem;
    align-items: center;
  }
  
  .captcha-input {
    flex: 1;
  }
  
  .captcha-wrapper {
    display: flex;
    align-items: center;
  }
  
  /* 表单选项 */
  .form-options {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 0.5rem;
  }
  
  .remember-me {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    color: #666;
    font-size: 0.9rem;
    cursor: pointer;
  }
  
  .remember-me input[type="checkbox"] {
    display: none;
  }
  
  .checkmark {
    width: 18px;
    height: 18px;
    border: 2px solid #ddd;
    border-radius: 4px;
    position: relative;
    transition: all 0.3s ease;
  }
  
  .remember-me input[type="checkbox"]:checked + .checkmark {
    background: #667eea;
    border-color: #667eea;
  }
  
  .remember-me input[type="checkbox"]:checked + .checkmark::after {
    content: '✓';
    position: absolute;
    top: -2px;
    left: 2px;
    color: white;
    font-size: 12px;
    font-weight: bold;
  }
  
  .forgot-password {
    color: #667eea;
    text-decoration: none;
    font-size: 0.9rem;
    transition: color 0.3s ease;
  }
  
  .forgot-password:hover {
    color: #5a6fd8;
    text-decoration: underline;
  }
  
  /* 提交按钮 */
  .submit-btn {
    padding: 1rem;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: none;
    border-radius: 10px;
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
  }
  
  .submit-btn:hover:not(:disabled) {
    transform: translateY(-2px);
    box-shadow: 0 10px 20px rgba(102, 126, 234, 0.3);
  }
  
  .submit-btn:disabled {
    opacity: 0.7;
    cursor: not-allowed;
  }
  
  .loading-spinner {
    display: inline-block;
    width: 16px;
    height: 16px;
    border: 2px solid rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    border-top-color: white;
    animation: spin 1s ease-in-out infinite;
    margin-right: 0.5rem;
  }
  
  @keyframes spin {
    to {
      transform: rotate(360deg);
    }
  }

  /* 测试按钮样式 */
  .test-buttons {
    margin-top: 1rem;
    display: flex;
    gap: 0.5rem;
    flex-wrap: wrap;
    justify-content: center;
  }

  .test-btn {
    padding: 0.5rem 1rem;
    border: none;
    border-radius: 6px;
    font-size: 0.8rem;
    cursor: pointer;
    transition: all 0.3s ease;
    opacity: 0.8;
  }

  .test-btn.error {
    background: #f56565;
    color: white;
  }

  .test-btn.success {
    background: #48bb78;
    color: white;
  }

  .test-btn.warning {
    background: #ed8936;
    color: white;
  }

  .test-btn:hover {
    opacity: 1;
    transform: translateY(-1px);
  }
  
  /* 响应式设计 */
  @media (max-width: 1024px) {
    .login-container {
      gap: 2rem;
      padding: 1.5rem;
    }
  
    .brand-title {
      font-size: 3rem;
    }
  
    .auth-container {
      max-width: 380px;
      padding: 2.5rem 2rem;
    }
  }
  
  @media (max-width: 768px) {
    .login-page {
      position: fixed !important;
      top: 0 !important;
      left: 0 !important;
      width: 100vw !important;
      height: 100vh !important;
      min-height: 100vh;
      overflow-y: auto;
      overflow-x: hidden;
      z-index: 99999 !important;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
    }
  
    .login-container {
      flex-direction: column;
      gap: 1.5rem;
      padding: 1rem;
      justify-content: flex-start;
      padding-top: 1rem;
      padding-bottom: 3rem;
      min-height: 100vh;
      height: auto;
      align-items: stretch;
      width: 100%;
      position: relative;
    }
  
    .brand-section {
      order: 1;
      flex: 0 0 auto;
      padding: 1rem 0;
    }
  
    .brand-content {
      text-align: center;
    }
  
    .auth-section {
      order: 2;
      flex: 1;
      display: flex;
      align-items: flex-start;
      justify-content: center;
      padding-bottom: 3rem;
      min-height: 400px;
    }
  
    .brand-title {
      font-size: 2.2rem;
      margin-bottom: 0.5rem;
    }
  
    .brand-subtitle {
      font-size: 1.1rem;
      margin-bottom: 1.5rem;
    }
  
    .brand-features {
      grid-template-columns: repeat(4, 1fr);
      gap: 0.8rem;
      margin-top: 1rem;
      max-width: 100%;
    }
  
    .feature-item {
      padding: 1rem 0.5rem;
      font-size: 0.85rem;
      border-radius: 12px;
    }
  
    .feature-icon {
      font-size: 1.5rem;
      margin-bottom: 0.3rem;
    }
  
    .auth-container {
      max-width: 100%;
      padding: 1.5rem 1.2rem;
      margin: 0 auto;
      width: 100%;
      max-width: 400px;
    }
  
    .captcha-container {
      flex-direction: column;
      align-items: stretch;
      gap: 0.8rem;
    }
  
    .captcha-input {
      width: 100%;
    }
  
    .captcha-wrapper {
      justify-content: center;
      width: 100%;
    }
  }
  
  /* 确保在所有移动设备上完全全屏 */
  @media (max-width: 768px) {
    body:has(.login-page) {
      overflow: hidden !important;
    }
  
    .login-page * {
      box-sizing: border-box;
    }
  }
  
  @media (max-width: 480px) {
    .login-container {
      padding: 0.5rem;
      gap: 1rem;
    }
  
    .brand-section {
      padding: 0.5rem 0;
    }
  
    .brand-title {
      font-size: 1.8rem;
      margin-bottom: 0.3rem;
    }
  
    .brand-subtitle {
      font-size: 1rem;
      margin-bottom: 1rem;
    }
  
    .brand-features {
      grid-template-columns: repeat(4, 1fr);
      gap: 0.5rem;
      margin-top: 0.8rem;
    }
  
    .feature-item {
      padding: 0.8rem 0.3rem;
      font-size: 0.75rem;
      border-radius: 10px;
    }
  
    .feature-icon {
      font-size: 1.2rem;
      margin-bottom: 0.2rem;
    }
  
    .auth-container {
      padding: 1.2rem 1rem;
      border-radius: 20px;
    }
  
    .auth-header h2 {
      font-size: 1.5rem;
    }
  
    .auth-header p {
      font-size: 0.85rem;
    }
  }
  </style>
  