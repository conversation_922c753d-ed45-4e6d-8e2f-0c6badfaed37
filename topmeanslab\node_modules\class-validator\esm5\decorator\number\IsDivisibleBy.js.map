{"version": 3, "file": "IsDivisibleBy.js", "sourceRoot": "", "sources": ["../../../../src/decorator/number/IsDivisibleBy.ts"], "names": [], "mappings": "AACA,OAAO,EAAE,YAAY,EAAE,UAAU,EAAE,MAAM,sBAAsB,CAAC;AAChE,OAAO,sBAAsB,MAAM,6BAA6B,CAAC;AAEjE,MAAM,CAAC,IAAM,eAAe,GAAG,eAAe,CAAC;AAE/C;;GAEG;AACH,MAAM,UAAU,aAAa,CAAC,KAAc,EAAE,GAAW;IACvD,OAAO,OAAO,KAAK,KAAK,QAAQ,IAAI,OAAO,GAAG,KAAK,QAAQ,IAAI,sBAAsB,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE,GAAG,CAAC,CAAC;AAC5G,CAAC;AAED;;GAEG;AACH,MAAM,UAAU,aAAa,CAAC,GAAW,EAAE,iBAAqC;IAC9E,OAAO,UAAU,CACf;QACE,IAAI,EAAE,eAAe;QACrB,WAAW,EAAE,CAAC,GAAG,CAAC;QAClB,SAAS,EAAE;YACT,QAAQ,EAAE,UAAC,KAAK,EAAE,IAAI,IAAc,OAAA,aAAa,CAAC,KAAK,EAAE,IAAI,aAAJ,IAAI,uBAAJ,IAAI,CAAE,WAAW,CAAC,CAAC,CAAC,CAAC,EAA1C,CAA0C;YAC9E,cAAc,EAAE,YAAY,CAC1B,UAAA,UAAU,IAAI,OAAA,UAAU,GAAG,6CAA6C,EAA1D,CAA0D,EACxE,iBAAiB,CAClB;SACF;KACF,EACD,iBAAiB,CAClB,CAAC;AACJ,CAAC", "sourcesContent": ["import { ValidationOptions } from '../ValidationOptions';\nimport { buildMessage, ValidateBy } from '../common/ValidateBy';\nimport isDivisibleByValidator from 'validator/lib/isDivisibleBy';\n\nexport const IS_DIVISIBLE_BY = 'isDivisibleBy';\n\n/**\n * Checks if value is a number that's divisible by another.\n */\nexport function isDivisibleBy(value: unknown, num: number): boolean {\n  return typeof value === 'number' && typeof num === 'number' && isDivisibleByValidator(String(value), num);\n}\n\n/**\n * Checks if value is a number that's divisible by another.\n */\nexport function IsDivisibleBy(num: number, validationOptions?: ValidationOptions): PropertyDecorator {\n  return ValidateBy(\n    {\n      name: IS_DIVISIBLE_BY,\n      constraints: [num],\n      validator: {\n        validate: (value, args): boolean => isDivisibleBy(value, args?.constraints[0]),\n        defaultMessage: buildMessage(\n          eachPrefix => eachPrefix + '$property must be divisible by $constraint1',\n          validationOptions\n        ),\n      },\n    },\n    validationOptions\n  );\n}\n"]}