# 认证状态调试页面

<script setup>
import { ref, onMounted } from 'vue'
import { useUserStore } from './.vitepress/theme/components/UserCenter/userStore'

const userStore = useUserStore()
const debugInfo = ref({})

const refreshDebugInfo = () => {
  debugInfo.value = {
    isLoggedIn: userStore.isLoggedIn,
    token: userStore.token ? `${userStore.token.substring(0, 20)}...` : null,
    tokenExists: !!userStore.token,
    userInfo: userStore.userInfo,
    lastLoginTime: userStore.lastLoginTime,
    rememberPassword: userStore.rememberPassword,
    localStorage_token: typeof window !== 'undefined' ? localStorage.getItem('userToken') : null,
    sessionStorage_token: typeof window !== 'undefined' ? sessionStorage.getItem('userToken') : null,
    isLoginExpired: userStore.isLoginExpired
  }
}

onMounted(() => {
  refreshDebugInfo()
})

const testAPI = async () => {
  try {
    const response = await fetch(`${import.meta.env.VITE_BACKEND_SRV_URL}/api/user/info`, {
      headers: {
        'Authorization': `Bearer ${userStore.token}`,
        'Content-Type': 'application/json'
      }
    })
    
    const result = await response.json()
    console.log('API 测试结果:', result)
    alert(`API 测试结果: ${response.status} - ${JSON.stringify(result)}`)
  } catch (error) {
    console.error('API 测试失败:', error)
    alert(`API 测试失败: ${error.message}`)
  }
}

const clearAuth = () => {
  userStore.logout()
  refreshDebugInfo()
}
</script>

## 用户认证状态调试

这个页面用于调试用户认证状态和 token 管理问题。

### 当前认证状态

<div style="background: #f5f5f5; padding: 20px; border-radius: 8px; margin: 20px 0;">
  <h4>用户状态信息</h4>
  <pre>{{ JSON.stringify(debugInfo, null, 2) }}</pre>
</div>

### 调试操作

<div style="margin: 20px 0;">
  <button @click="refreshDebugInfo" style="margin-right: 10px; padding: 8px 16px; background: #1677ff; color: white; border: none; border-radius: 4px; cursor: pointer;">
    刷新状态
  </button>
  
  <button @click="testAPI" style="margin-right: 10px; padding: 8px 16px; background: #52c41a; color: white; border: none; border-radius: 4px; cursor: pointer;">
    测试 API 调用
  </button>
  
  <button @click="clearAuth" style="padding: 8px 16px; background: #ff4d4f; color: white; border: none; border-radius: 4px; cursor: pointer;">
    清除认证状态
  </button>
</div>

### 问题排查清单

1. **检查 token 是否存在**: `tokenExists` 应该为 `true`
2. **检查登录状态**: `isLoggedIn` 应该为 `true`
3. **检查存储位置**: token 应该在 `localStorage` 或 `sessionStorage` 中
4. **检查 token 格式**: token 应该是有效的 JWT 格式
5. **检查过期时间**: `isLoginExpired` 应该为 `false`

### 常见问题

- **401 Unauthorized**: token 不存在或无效
- **Token 为 null**: 登录时没有正确保存 token
- **存储问题**: token 保存在错误的存储位置
- **过期问题**: token 已过期需要重新登录

### 修复步骤

1. 如果 token 不存在，请重新登录
2. 如果 token 存在但 API 调用失败，检查 token 格式
3. 如果登录状态为 false，检查过期逻辑
4. 清除认证状态后重新登录测试
