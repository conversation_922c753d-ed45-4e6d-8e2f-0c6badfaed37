{"version": 3, "file": "parse.js", "names": ["_parseNumber", "normalizeArguments", "parseNumber", "arguments", "text", "options", "metadata"], "sources": ["../../source/legacy/parse.js"], "sourcesContent": ["import _parseNumber from '../parse.js'\r\nimport normalizeArguments from '../normalizeArguments.js'\r\n\r\nexport default function parseNumber() {\r\n\tconst { text, options, metadata } = normalizeArguments(arguments)\r\n\treturn _parseNumber(text, options, metadata)\r\n}"], "mappings": "AAAA,OAAOA,YAAP,MAAyB,aAAzB;AACA,OAAOC,kBAAP,MAA+B,0BAA/B;AAEA,eAAe,SAASC,WAAT,GAAuB;EACrC,0BAAoCD,kBAAkB,CAACE,SAAD,CAAtD;EAAA,IAAQC,IAAR,uBAAQA,IAAR;EAAA,IAAcC,OAAd,uBAAcA,OAAd;EAAA,IAAuBC,QAAvB,uBAAuBA,QAAvB;;EACA,OAAON,YAAY,CAACI,IAAD,EAAOC,OAAP,EAAgBC,QAAhB,CAAnB;AACA"}