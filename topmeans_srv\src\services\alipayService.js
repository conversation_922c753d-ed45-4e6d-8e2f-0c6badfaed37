const { AlipaySdk } = require('alipay-sdk');
const fs = require('fs');
const path = require('path');
const crypto = require('crypto');
const logger = require('../log/logger');

class AlipayService {
  constructor() {
    this.initializeSDK();
    this.paymentRecords = new Map(); // 存储支付记录
  }

  initializeSDK() {
    try {
      // 读取环境变量
      const appId = process.env.alipayAppid;
      if (!appId) {
        throw new Error('支付宝 AppId 未配置');
      }

      // 读取私钥
      const privateKeyPath = path.join(__dirname, 'privateKey.txt');
      const privateKey = fs.readFileSync(privateKeyPath, 'utf8').trim();

      // 读取支付宝公钥
      const publicKeyPath = path.join(__dirname, 'publicKey.txt');
      const alipayPublicKey = fs.readFileSync(publicKeyPath, 'utf8').trim();

      // 初始化支付宝 SDK
      this.alipaySdk = new AlipaySdk({
        appId: appId,
        privateKey: privateKey,
        alipayPublicKey: alipayPublicKey,
        // gateway: 'https://openapi.alipay.com/gateway.do', // 正式环境
        gateway: 'https://openapi.alipaydev.com/gateway.do', // 沙箱环境
      });

      logger.info('支付宝 SDK 初始化成功');
    } catch (error) {
      logger.error('支付宝 SDK 初始化失败:', error);
      throw error;
    }
  }

  // 生成订单号
  generateOrderNo() {
    const timestamp = Date.now();
    const random = Math.floor(Math.random() * 1000).toString().padStart(3, '0');
    return `TML${timestamp}${random}`;
  }

  // 生成模拟二维码 SVG
  generateMockQRCode(outTradeNo, amount) {
    const svg = `
      <svg width="200" height="200" xmlns="http://www.w3.org/2000/svg">
        <!-- 白色背景 -->
        <rect width="200" height="200" fill="white" stroke="#e8e8e8" stroke-width="2"/>

        <!-- 模拟二维码图案 -->
        <!-- 左上角定位点 -->
        <rect x="10" y="10" width="30" height="30" fill="black"/>
        <rect x="15" y="15" width="20" height="20" fill="white"/>
        <rect x="20" y="20" width="10" height="10" fill="black"/>

        <!-- 右上角定位点 -->
        <rect x="160" y="10" width="30" height="30" fill="black"/>
        <rect x="165" y="15" width="20" height="20" fill="white"/>
        <rect x="170" y="20" width="10" height="10" fill="black"/>

        <!-- 左下角定位点 -->
        <rect x="10" y="160" width="30" height="30" fill="black"/>
        <rect x="15" y="165" width="20" height="20" fill="white"/>
        <rect x="20" y="170" width="10" height="10" fill="black"/>

        <!-- 模拟数据点 -->
        <rect x="50" y="20" width="5" height="5" fill="black"/>
        <rect x="60" y="20" width="5" height="5" fill="black"/>
        <rect x="70" y="25" width="5" height="5" fill="black"/>
        <rect x="80" y="20" width="5" height="5" fill="black"/>
        <rect x="90" y="25" width="5" height="5" fill="black"/>
        <rect x="100" y="20" width="5" height="5" fill="black"/>
        <rect x="110" y="25" width="5" height="5" fill="black"/>
        <rect x="120" y="20" width="5" height="5" fill="black"/>
        <rect x="130" y="25" width="5" height="5" fill="black"/>
        <rect x="140" y="20" width="5" height="5" fill="black"/>

        <rect x="20" y="50" width="5" height="5" fill="black"/>
        <rect x="30" y="55" width="5" height="5" fill="black"/>
        <rect x="40" y="50" width="5" height="5" fill="black"/>
        <rect x="50" y="55" width="5" height="5" fill="black"/>
        <rect x="60" y="50" width="5" height="5" fill="black"/>
        <rect x="70" y="55" width="5" height="5" fill="black"/>
        <rect x="80" y="50" width="5" height="5" fill="black"/>
        <rect x="90" y="55" width="5" height="5" fill="black"/>
        <rect x="100" y="50" width="5" height="5" fill="black"/>
        <rect x="110" y="55" width="5" height="5" fill="black"/>
        <rect x="120" y="50" width="5" height="5" fill="black"/>
        <rect x="130" y="55" width="5" height="5" fill="black"/>
        <rect x="140" y="50" width="5" height="5" fill="black"/>
        <rect x="150" y="55" width="5" height="5" fill="black"/>
        <rect x="160" y="50" width="5" height="5" fill="black"/>
        <rect x="170" y="55" width="5" height="5" fill="black"/>
        <rect x="180" y="50" width="5" height="5" fill="black"/>

        <!-- 更多模拟数据点 -->
        <rect x="25" y="80" width="5" height="5" fill="black"/>
        <rect x="35" y="85" width="5" height="5" fill="black"/>
        <rect x="45" y="80" width="5" height="5" fill="black"/>
        <rect x="55" y="85" width="5" height="5" fill="black"/>
        <rect x="65" y="80" width="5" height="5" fill="black"/>
        <rect x="75" y="85" width="5" height="5" fill="black"/>
        <rect x="85" y="80" width="5" height="5" fill="black"/>
        <rect x="95" y="85" width="5" height="5" fill="black"/>
        <rect x="105" y="80" width="5" height="5" fill="black"/>
        <rect x="115" y="85" width="5" height="5" fill="black"/>
        <rect x="125" y="80" width="5" height="5" fill="black"/>
        <rect x="135" y="85" width="5" height="5" fill="black"/>
        <rect x="145" y="80" width="5" height="5" fill="black"/>
        <rect x="155" y="85" width="5" height="5" fill="black"/>
        <rect x="165" y="80" width="5" height="5" fill="black"/>
        <rect x="175" y="85" width="5" height="5" fill="black"/>

        <!-- 中心区域 -->
        <rect x="90" y="90" width="20" height="20" fill="black"/>
        <rect x="95" y="95" width="10" height="10" fill="white"/>
        <rect x="98" y="98" width="4" height="4" fill="black"/>

        <!-- 底部信息 -->
        <text x="100" y="140" text-anchor="middle" font-size="10" fill="#666" font-weight="bold">
          支付宝扫码支付
        </text>
        <text x="100" y="155" text-anchor="middle" font-size="8" fill="#999">
          金额: ¥${amount}
        </text>
        <text x="100" y="170" text-anchor="middle" font-size="6" fill="#ccc">
          ${outTradeNo.substring(0, 20)}
        </text>
      </svg>
    `;

    // 返回 base64 编码的 SVG
    return `data:image/svg+xml;base64,${Buffer.from(svg).toString('base64')}`;
  }

  // 创建支付订单
  async createPayment(orderData) {
    try {
      const {
        amount,
        subject = 'TopMeansLab 服务购买',
        userId,
        serviceType = 'premium_service'
      } = orderData;

      // 验证金额
      if (!amount || amount <= 0) {
        throw new Error('支付金额无效');
      }

      // 生成订单号
      const outTradeNo = this.generateOrderNo();

      logger.info('创建支付订单 - 开始处理:', {
        outTradeNo,
        amount,
        subject,
        userId,
        serviceType
      });

      // 临时使用模拟响应来测试整个流程
      // TODO: 在生产环境中需要配置正确的支付宝密钥
      const isTestMode = true; // 设置为测试模式

      if (isTestMode) {
        // 生成模拟二维码 SVG
        const mockQRCodeSVG = this.generateMockQRCode(outTradeNo, amount);

        // 模拟支付宝响应
        const mockResult = {
          code: '10000',
          msg: 'Success',
          qrCode: mockQRCodeSVG,
          outTradeNo: outTradeNo
        };

        logger.info('使用模拟支付宝响应 (测试模式):', {
          ...mockResult,
          qrCode: '已生成 SVG 二维码'
        });

        // 存储支付记录
        const paymentRecord = {
          outTradeNo,
          amount,
          subject,
          userId,
          serviceType,
          status: 'pending',
          qrCode: mockResult.qrCode,
          createTime: new Date(),
          expireTime: new Date(Date.now() + 30 * 60 * 1000) // 30分钟后过期
        };

        this.paymentRecords.set(outTradeNo, paymentRecord);

        logger.info('支付订单创建成功 (测试模式):', {
          outTradeNo,
          amount,
          userId,
          qrCode: '已生成 (模拟)'
        });

        return {
          success: true,
          data: {
            outTradeNo,
            qrCode: mockResult.qrCode,
            amount,
            expireTime: paymentRecord.expireTime
          }
        };
      }

      // 真实支付宝接口调用 (当前被禁用)
      const bizContent = {
        out_trade_no: outTradeNo,
        total_amount: amount.toString(),
        subject: subject,
        product_code: 'FAST_INSTANT_TRADE_PAY',
        qr_pay_mode: '1',
        qrcode_width: 200,
        timeout_express: '30m',
      };

      logger.info('调用支付宝接口 - 请求参数:', {
        method: 'alipay.trade.precreate',
        bizContent: bizContent,
        notifyUrl: `${process.env.VITE_BACKEND_SRV_URL}/api/payment/alipay/notify`,
        appId: process.env.alipayAppid,
        gateway: 'https://openapi.alipaydev.com/gateway.do'
      });

      const result = await this.alipaySdk.exec('alipay.trade.precreate', {
        bizContent: bizContent,
        notifyUrl: `${process.env.VITE_BACKEND_SRV_URL}/api/payment/alipay/notify`
      });

      logger.info('支付宝接口响应:', {
        code: result.code,
        msg: result.msg,
        subCode: result.subCode,
        subMsg: result.subMsg,
        hasQrCode: !!result.qrCode
      });

      // 解析返回结果
      if (result.code === '10000') {
        // 存储支付记录
        const paymentRecord = {
          outTradeNo,
          amount,
          subject,
          userId,
          serviceType,
          status: 'pending',
          qrCode: result.qrCode,
          createTime: new Date(),
          expireTime: new Date(Date.now() + 30 * 60 * 1000) // 30分钟后过期
        };

        this.paymentRecords.set(outTradeNo, paymentRecord);

        logger.info('支付订单创建成功:', {
          outTradeNo,
          amount,
          userId,
          qrCode: result.qrCode ? '已生成' : '未生成'
        });

        return {
          success: true,
          data: {
            outTradeNo,
            qrCode: result.qrCode,
            amount,
            expireTime: paymentRecord.expireTime
          }
        };
      } else {
        const errorMsg = `支付宝接口调用失败: ${result.msg || result.subMsg || 'Unknown Error'}`;
        logger.error('支付宝接口错误:', {
          code: result.code,
          msg: result.msg,
          subCode: result.subCode,
          subMsg: result.subMsg,
          bizContent: bizContent
        });
        throw new Error(errorMsg);
      }
    } catch (error) {
      logger.error('创建支付订单失败:', {
        error: error.message,
        stack: error.stack,
        orderData: orderData
      });
      return {
        success: false,
        message: error.message || '创建支付订单失败'
      };
    }
  }

  // 查询支付状态
  async queryPaymentStatus(outTradeNo) {
    try {
      // 检查本地记录
      const record = this.paymentRecords.get(outTradeNo);
      if (!record) {
        return {
          success: false,
          message: '订单不存在'
        };
      }

      // 测试模式：模拟支付状态
      const isTestMode = true;

      if (isTestMode) {
        // 模拟支付状态变化：前5次查询返回pending，之后可以手动设置为success
        const currentTime = new Date();
        const createTime = record.createTime;
        const timeDiff = currentTime - createTime;

        // 如果订单创建超过30秒，模拟支付成功（用于测试）
        let mockStatus = 'pending';
        let mockTradeStatus = 'WAIT_BUYER_PAY';

        if (timeDiff > 30000) { // 30秒后模拟支付成功
          mockStatus = 'success';
          mockTradeStatus = 'TRADE_SUCCESS';
          record.status = 'success';
          record.tradeNo = `MOCK_TRADE_${outTradeNo}`;
          record.updateTime = currentTime;
        }

        logger.info('查询支付状态 (测试模式):', {
          outTradeNo,
          status: mockStatus,
          tradeStatus: mockTradeStatus,
          timeDiff: Math.round(timeDiff / 1000) + 's'
        });

        return {
          success: true,
          data: {
            outTradeNo,
            tradeNo: record.tradeNo || `MOCK_TRADE_${outTradeNo}`,
            tradeStatus: mockTradeStatus,
            status: mockStatus,
            totalAmount: record.amount.toString(),
            buyerPayAmount: record.amount.toString()
          }
        };
      }

      // 真实支付宝接口调用（当前被禁用）
      const result = await this.alipaySdk.exec('alipay.trade.query', {
        bizContent: {
          out_trade_no: outTradeNo
        }
      });

      if (result.code === '10000') {
        const tradeStatus = result.tradeStatus;

        // 更新本地记录
        if (record) {
          record.status = this.mapTradeStatus(tradeStatus);
          record.tradeNo = result.tradeNo;
          record.updateTime = new Date();
        }

        return {
          success: true,
          data: {
            outTradeNo,
            tradeNo: result.tradeNo,
            tradeStatus,
            status: this.mapTradeStatus(tradeStatus),
            totalAmount: result.totalAmount,
            buyerPayAmount: result.buyerPayAmount
          }
        };
      } else {
        return {
          success: false,
          message: result.msg || result.subMsg || '查询支付状态失败'
        };
      }
    } catch (error) {
      logger.error('查询支付状态失败:', error);
      return {
        success: false,
        message: error.message || '查询支付状态失败'
      };
    }
  }

  // 映射支付宝交易状态到内部状态
  mapTradeStatus(tradeStatus) {
    const statusMap = {
      'WAIT_BUYER_PAY': 'pending',    // 等待买家付款
      'TRADE_SUCCESS': 'success',     // 交易成功
      'TRADE_FINISHED': 'success',    // 交易完成
      'TRADE_CLOSED': 'closed'        // 交易关闭
    };
    return statusMap[tradeStatus] || 'unknown';
  }

  // 处理支付宝异步通知
  async handleNotify(notifyData) {
    try {
      // 验证签名
      const isValid = this.verifyNotifySignature(notifyData);
      if (!isValid) {
        logger.error('支付宝通知签名验证失败');
        return { success: false, message: '签名验证失败' };
      }

      const {
        out_trade_no: outTradeNo,
        trade_status: tradeStatus,
        trade_no: tradeNo,
        total_amount: totalAmount
      } = notifyData;

      // 更新支付记录
      const record = this.paymentRecords.get(outTradeNo);
      if (record) {
        record.status = this.mapTradeStatus(tradeStatus);
        record.tradeNo = tradeNo;
        record.notifyTime = new Date();
        
        logger.info('支付状态更新:', {
          outTradeNo,
          tradeStatus,
          tradeNo,
          totalAmount
        });
      }

      return {
        success: true,
        data: {
          outTradeNo,
          tradeStatus,
          status: this.mapTradeStatus(tradeStatus)
        }
      };
    } catch (error) {
      logger.error('处理支付宝通知失败:', error);
      return {
        success: false,
        message: error.message || '处理通知失败'
      };
    }
  }

  // 验证支付宝通知签名
  verifyNotifySignature(notifyData) {
    try {
      // 这里应该使用支付宝提供的签名验证方法
      // 简化实现，实际项目中需要严格验证
      return true;
    } catch (error) {
      logger.error('签名验证失败:', error);
      return false;
    }
  }

  // 获取支付记录
  getPaymentRecord(outTradeNo) {
    return this.paymentRecords.get(outTradeNo);
  }

  // 清理过期记录
  cleanExpiredRecords() {
    const now = new Date();
    for (const [outTradeNo, record] of this.paymentRecords.entries()) {
      if (record.expireTime < now && record.status === 'pending') {
        this.paymentRecords.delete(outTradeNo);
        logger.info('清理过期支付记录:', outTradeNo);
      }
    }
  }
}

// 创建单例实例
const alipayService = new AlipayService();

// 定期清理过期记录
setInterval(() => {
  alipayService.cleanExpiredRecords();
}, 5 * 60 * 1000); // 每5分钟清理一次

module.exports = alipayService;
