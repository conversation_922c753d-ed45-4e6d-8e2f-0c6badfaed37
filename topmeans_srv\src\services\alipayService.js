const { AlipaySdk } = require('alipay-sdk');
const fs = require('fs');
const path = require('path');
const crypto = require('crypto');
const logger = require('../log/logger');

class AlipayService {
  constructor() {
    this.initializeSDK();
    this.paymentRecords = new Map(); // 存储支付记录
  }

  initializeSDK() {
    try {
      // 读取环境变量
      const appId = process.env.alipayAppid;
      if (!appId) {
        throw new Error('支付宝 AppId 未配置');
      }

      // 读取私钥
      const privateKeyPath = path.join(__dirname, 'privateKey.txt');
      const privateKey = fs.readFileSync(privateKeyPath, 'utf8').trim();

      // 读取支付宝公钥
      const publicKeyPath = path.join(__dirname, 'publicKey.txt');
      const alipayPublicKey = fs.readFileSync(publicKeyPath, 'utf8').trim();

      // 初始化支付宝 SDK
      this.alipaySdk = new AlipaySdk({
        appId: appId,
        privateKey: privateKey,
        alipayPublicKey: alipayPublicKey,
        // gateway: 'https://openapi.alipay.com/gateway.do', // 正式环境
        gateway: 'https://openapi.alipaydev.com/gateway.do', // 沙箱环境
      });

      logger.info('支付宝 SDK 初始化成功');
    } catch (error) {
      logger.error('支付宝 SDK 初始化失败:', error);
      throw error;
    }
  }

  // 生成订单号
  generateOrderNo() {
    const timestamp = Date.now();
    const random = Math.floor(Math.random() * 1000).toString().padStart(3, '0');
    return `TML${timestamp}${random}`;
  }

  // 创建支付订单
  async createPayment(orderData) {
    try {
      const {
        amount,
        subject = 'TopMeansLab 服务购买',
        userId,
        serviceType = 'premium_service'
      } = orderData;

      // 验证金额
      if (!amount || amount <= 0) {
        throw new Error('支付金额无效');
      }

      // 生成订单号
      const outTradeNo = this.generateOrderNo();

      logger.info('创建支付订单 - 开始处理:', {
        outTradeNo,
        amount,
        subject,
        userId,
        serviceType
      });

      // 临时使用模拟响应来测试整个流程
      // TODO: 在生产环境中需要配置正确的支付宝密钥
      const isTestMode = true; // 设置为测试模式

      if (isTestMode) {
        // 模拟支付宝响应
        const mockResult = {
          code: '10000',
          msg: 'Success',
          qrCode: `https://qr.alipay.com/mock_${outTradeNo}`,
          outTradeNo: outTradeNo
        };

        logger.info('使用模拟支付宝响应 (测试模式):', mockResult);

        // 存储支付记录
        const paymentRecord = {
          outTradeNo,
          amount,
          subject,
          userId,
          serviceType,
          status: 'pending',
          qrCode: mockResult.qrCode,
          createTime: new Date(),
          expireTime: new Date(Date.now() + 30 * 60 * 1000) // 30分钟后过期
        };

        this.paymentRecords.set(outTradeNo, paymentRecord);

        logger.info('支付订单创建成功 (测试模式):', {
          outTradeNo,
          amount,
          userId,
          qrCode: '已生成 (模拟)'
        });

        return {
          success: true,
          data: {
            outTradeNo,
            qrCode: mockResult.qrCode,
            amount,
            expireTime: paymentRecord.expireTime
          }
        };
      }

      // 真实支付宝接口调用 (当前被禁用)
      const bizContent = {
        out_trade_no: outTradeNo,
        total_amount: amount.toString(),
        subject: subject,
        product_code: 'FAST_INSTANT_TRADE_PAY',
        qr_pay_mode: '1',
        qrcode_width: 200,
        timeout_express: '30m',
      };

      logger.info('调用支付宝接口 - 请求参数:', {
        method: 'alipay.trade.precreate',
        bizContent: bizContent,
        notifyUrl: `${process.env.VITE_BACKEND_SRV_URL}/api/payment/alipay/notify`,
        appId: process.env.alipayAppid,
        gateway: 'https://openapi.alipaydev.com/gateway.do'
      });

      const result = await this.alipaySdk.exec('alipay.trade.precreate', {
        bizContent: bizContent,
        notifyUrl: `${process.env.VITE_BACKEND_SRV_URL}/api/payment/alipay/notify`
      });

      logger.info('支付宝接口响应:', {
        code: result.code,
        msg: result.msg,
        subCode: result.subCode,
        subMsg: result.subMsg,
        hasQrCode: !!result.qrCode
      });

      // 解析返回结果
      if (result.code === '10000') {
        // 存储支付记录
        const paymentRecord = {
          outTradeNo,
          amount,
          subject,
          userId,
          serviceType,
          status: 'pending',
          qrCode: result.qrCode,
          createTime: new Date(),
          expireTime: new Date(Date.now() + 30 * 60 * 1000) // 30分钟后过期
        };

        this.paymentRecords.set(outTradeNo, paymentRecord);

        logger.info('支付订单创建成功:', {
          outTradeNo,
          amount,
          userId,
          qrCode: result.qrCode ? '已生成' : '未生成'
        });

        return {
          success: true,
          data: {
            outTradeNo,
            qrCode: result.qrCode,
            amount,
            expireTime: paymentRecord.expireTime
          }
        };
      } else {
        const errorMsg = `支付宝接口调用失败: ${result.msg || result.subMsg || 'Unknown Error'}`;
        logger.error('支付宝接口错误:', {
          code: result.code,
          msg: result.msg,
          subCode: result.subCode,
          subMsg: result.subMsg,
          bizContent: bizContent
        });
        throw new Error(errorMsg);
      }
    } catch (error) {
      logger.error('创建支付订单失败:', {
        error: error.message,
        stack: error.stack,
        orderData: orderData
      });
      return {
        success: false,
        message: error.message || '创建支付订单失败'
      };
    }
  }

  // 查询支付状态
  async queryPaymentStatus(outTradeNo) {
    try {
      const result = await this.alipaySdk.exec('alipay.trade.query', {
        bizContent: {
          out_trade_no: outTradeNo
        }
      });

      if (result.code === '10000') {
        const tradeStatus = result.tradeStatus;
        
        // 更新本地记录
        const record = this.paymentRecords.get(outTradeNo);
        if (record) {
          record.status = this.mapTradeStatus(tradeStatus);
          record.tradeNo = result.tradeNo;
          record.updateTime = new Date();
        }

        return {
          success: true,
          data: {
            outTradeNo,
            tradeNo: result.tradeNo,
            tradeStatus,
            status: this.mapTradeStatus(tradeStatus),
            totalAmount: result.totalAmount,
            buyerPayAmount: result.buyerPayAmount
          }
        };
      } else {
        return {
          success: false,
          message: result.msg || result.subMsg || '查询支付状态失败'
        };
      }
    } catch (error) {
      logger.error('查询支付状态失败:', error);
      return {
        success: false,
        message: error.message || '查询支付状态失败'
      };
    }
  }

  // 映射支付宝交易状态到内部状态
  mapTradeStatus(tradeStatus) {
    const statusMap = {
      'WAIT_BUYER_PAY': 'pending',    // 等待买家付款
      'TRADE_SUCCESS': 'success',     // 交易成功
      'TRADE_FINISHED': 'success',    // 交易完成
      'TRADE_CLOSED': 'closed'        // 交易关闭
    };
    return statusMap[tradeStatus] || 'unknown';
  }

  // 处理支付宝异步通知
  async handleNotify(notifyData) {
    try {
      // 验证签名
      const isValid = this.verifyNotifySignature(notifyData);
      if (!isValid) {
        logger.error('支付宝通知签名验证失败');
        return { success: false, message: '签名验证失败' };
      }

      const {
        out_trade_no: outTradeNo,
        trade_status: tradeStatus,
        trade_no: tradeNo,
        total_amount: totalAmount
      } = notifyData;

      // 更新支付记录
      const record = this.paymentRecords.get(outTradeNo);
      if (record) {
        record.status = this.mapTradeStatus(tradeStatus);
        record.tradeNo = tradeNo;
        record.notifyTime = new Date();
        
        logger.info('支付状态更新:', {
          outTradeNo,
          tradeStatus,
          tradeNo,
          totalAmount
        });
      }

      return {
        success: true,
        data: {
          outTradeNo,
          tradeStatus,
          status: this.mapTradeStatus(tradeStatus)
        }
      };
    } catch (error) {
      logger.error('处理支付宝通知失败:', error);
      return {
        success: false,
        message: error.message || '处理通知失败'
      };
    }
  }

  // 验证支付宝通知签名
  verifyNotifySignature(notifyData) {
    try {
      // 这里应该使用支付宝提供的签名验证方法
      // 简化实现，实际项目中需要严格验证
      return true;
    } catch (error) {
      logger.error('签名验证失败:', error);
      return false;
    }
  }

  // 获取支付记录
  getPaymentRecord(outTradeNo) {
    return this.paymentRecords.get(outTradeNo);
  }

  // 清理过期记录
  cleanExpiredRecords() {
    const now = new Date();
    for (const [outTradeNo, record] of this.paymentRecords.entries()) {
      if (record.expireTime < now && record.status === 'pending') {
        this.paymentRecords.delete(outTradeNo);
        logger.info('清理过期支付记录:', outTradeNo);
      }
    }
  }
}

// 创建单例实例
const alipayService = new AlipayService();

// 定期清理过期记录
setInterval(() => {
  alipayService.cleanExpiredRecords();
}, 5 * 60 * 1000); // 每5分钟清理一次

module.exports = alipayService;
