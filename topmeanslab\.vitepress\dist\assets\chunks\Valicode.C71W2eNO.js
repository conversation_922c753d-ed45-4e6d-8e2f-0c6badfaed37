import{d as C,p as d,v as g,C as M,c as y,o as V,j as h,G as k,w,a as T,_ as B}from"./framework.B19ydMwb.js";const N=l;function l(c,s){const r=_();return l=function(o,n){return o=o-0,r[o]},l(c,s)}const S={class:"captcha",style:{display:N(0)}},A={ref:"canvas",width:"100",height:"40"},j={class:"valicode-btn"},m="ABCDEFGHIJKLMNPQRSTUVWXYZabcdefghijmnopqrstuvwxyz023456789";function _(){const c=["flex","font","#ccc","random","stroke"];return _=function(){return c},_()}const q=C({__name:"Valicode",emits:["getCode"],setup(c,{expose:s,emit:r}){const o=r,n=d(null),e=d(null),u=d(""),b=m.length;function p(){return m.charAt(Math.floor(Math.random()*b))}function f(){const t=l;if(!e.value)return;e.value.clearRect(0,0,n.value.width,n.value.height);let x=10;u.value="";for(let a=0;a<4;a++){const v=p();u.value+=v,e.value[t(1)]="bold 20px Arial",e.value.fillStyle="#333",e.value.fillText(v,x,25),x+=20}for(let a=0;a<10;a++)e.value.strokeStyle=t(2),e.value.beginPath(),e.value.moveTo(Math.random()*100,Math[t(3)]()*40),e.value.lineTo(Math.random()*100,Math[t(3)]()*40),e.value[t(4)]();o("getCode",u.value.toLowerCase())}function i(){f()}return s({refresh:i}),g(()=>{var t;n.value=document.querySelector("canvas"),e.value=(t=n.value)==null?void 0:t.getContext("2d"),f()}),(t,x)=>{const a=M("el-button");return V(),y("div",S,[h("canvas",A,null,512),h("div",j,[k(a,{type:"text",class:"link-button",onClick:i},{default:w(()=>x[0]||(x[0]=[T("看不清，换一张")])),_:1})])])}}}),G=B(q,[["__scopeId","data-v-aa6c3bb5"]]);export{G as V};
