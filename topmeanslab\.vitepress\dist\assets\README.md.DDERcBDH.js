import{_ as n,c as s,o,j as e,a as t}from"./chunks/framework.neMYHtQj.js";const E=JSON.parse('{"title":"topmeanslab","description":"","frontmatter":{},"headers":[],"relativePath":"README.md","filePath":"README.md"}'),r={name:"README.md"};function l(p,a,c,i,m,d){return o(),s("div",null,a[0]||(a[0]=[e("h1",{id:"topmeanslab",tabindex:"-1"},[t("topmeanslab "),e("a",{class:"header-anchor",href:"#topmeanslab","aria-label":'Permalink to "topmeanslab"'},"​")],-1),e("p",null,[e("strong",null,"test"),t(" 服务启动：npm run start nginx服务配置文件：/etc/nginx/nginx.conf")],-1)]))}const _=n(r,[["render",l]]);export{E as __pageData,_ as default};
