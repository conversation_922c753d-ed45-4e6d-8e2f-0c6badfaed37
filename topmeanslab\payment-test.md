# 支付功能测试页面

<script setup>
import PaymentMethods from './.vitepress/theme/components/Payment/PaymentMethods.vue'
import { ref } from 'vue'

const amount = ref(0.01)
const orderInfo = ref({
  subject: '测试支付订单',
  serviceType: 'test_service'
})

const handlePaymentSuccess = (paymentData) => {
  console.log('支付成功:', paymentData)
  alert(`支付成功！订单号: ${paymentData.orderId}`)
}

const handlePaymentCancel = () => {
  console.log('支付取消')
  alert('支付已取消')
}
</script>

## 支付宝支付测试

这是一个支付功能测试页面，用于验证支付宝支付集成是否正常工作。

### 测试说明

1. **测试金额**: 0.01 元（最小测试金额）
2. **支付方式**: 支付宝扫码支付
3. **测试流程**: 
   - 点击"确认支付"按钮
   - 查看二维码弹窗是否正常显示
   - 观察支付状态轮询是否正常
   - 测试取消支付功能

### 支付组件

<PaymentMethods
  :amount="amount"
  :order-info="orderInfo"
  @payment-success="handlePaymentSuccess"
  @payment-cancel="handlePaymentCancel"
/>

### 功能验证清单

- [ ] 支付方式选择界面正常显示
- [ ] 支付金额正确显示
- [ ] 点击支付按钮无报错
- [ ] 二维码弹窗正常显示
- [ ] 二维码图片正常生成
- [ ] 支付状态轮询正常工作
- [ ] 取消支付功能正常
- [ ] 支付超时处理正常

### 预期结果

1. **界面显示**: 支付方式选择界面应该正常显示，包含支付宝选项
2. **二维码生成**: 点击支付后应该显示二维码弹窗，包含模拟的二维码图片
3. **状态轮询**: 应该每3秒查询一次支付状态
4. **错误处理**: 网络错误或其他异常应该有友好的提示

### 技术说明

- 使用 SVG 生成模拟二维码（避免 qrcode 库导入问题）
- 支付状态通过轮询后端 API 获取
- 所有支付操作都需要用户登录认证
- 支付金额验证范围：0.01-10000元
