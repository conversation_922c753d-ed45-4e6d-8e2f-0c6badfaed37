{"version": 3, "file": "parsePhoneNumberWithError_.js", "names": ["parsePhoneNumberWithError", "text", "options", "metadata", "parse", "v2"], "sources": ["../source/parsePhoneNumberWithError_.js"], "sourcesContent": ["import parse from './parse.js'\r\n\r\nexport default function parsePhoneNumberWithError(text, options, metadata) {\r\n\treturn parse(text, { ...options, v2: true }, metadata)\r\n}"], "mappings": ";;;;;;;AAAA;;;;;;;;;;AAEe,SAASA,yBAAT,CAAmCC,IAAnC,EAAyCC,OAAzC,EAAkDC,QAAlD,EAA4D;EAC1E,OAAO,IAAAC,iBAAA,EAAMH,IAAN,kCAAiBC,OAAjB;IAA0BG,EAAE,EAAE;EAA9B,IAAsCF,QAAtC,CAAP;AACA"}