<!DOCTYPE html>
<html lang="en-US" dir="ltr">
  <head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width,initial-scale=1">
    <title>Runtime API Examples | TopMeansLab</title>
    <meta name="description" content="TopMeansLab Documentation">
    <meta name="generator" content="VitePress v1.6.3">
    <link rel="preload stylesheet" href="/assets/style.LEQmhhGX.css" as="style">
    <link rel="preload stylesheet" href="/vp-icons.css" as="style">
    
    <script type="module" src="/assets/app.26IQWFXf.js"></script>
    <link rel="preload" href="/assets/inter-roman-latin.Di8DUHzh.woff2" as="font" type="font/woff2" crossorigin="">
    <link rel="modulepreload" href="/assets/chunks/theme.CmWpOUCL.js">
    <link rel="modulepreload" href="/assets/chunks/framework.B19ydMwb.js">
    <link rel="modulepreload" href="/assets/api-examples.md.OicIAmCk.lean.js">
    <link rel="icon" href="/favicon.ico">
    <link rel="shortcut icon" href="/favicon.ico">
    <link rel="apple-touch-icon" href="/favicon.ico">
    <script id="check-dark-mode">(()=>{const e=localStorage.getItem("vitepress-theme-appearance")||"auto",a=window.matchMedia("(prefers-color-scheme: dark)").matches;(!e||e==="auto"?a:e==="dark")&&document.documentElement.classList.add("dark")})();</script>
    <script id="check-mac-os">document.documentElement.classList.toggle("mac",/Mac|iPhone|iPod|iPad/i.test(navigator.platform));</script>
  </head>
  <body>
    <div id="app"><div class="Layout" data-v-d8b57b2d><!--[--><!--]--><!--[--><span tabindex="-1" data-v-fcbfc0e0></span><a href="#VPContent" class="VPSkipLink visually-hidden" data-v-fcbfc0e0>Skip to content</a><!--]--><!----><header class="VPNav" data-v-d8b57b2d data-v-7ad780c2><div class="VPNavBar" data-v-7ad780c2 data-v-9fd4d1dd><div class="wrapper" data-v-9fd4d1dd><div class="container" data-v-9fd4d1dd><div class="title" data-v-9fd4d1dd><div class="VPNavBarTitle" data-v-9fd4d1dd data-v-9f43907a><a class="title" href="/" data-v-9f43907a><!--[--><!--]--><!----><span data-v-9f43907a>TopMeansLab</span><!--[--><!--]--></a></div></div><div class="content" data-v-9fd4d1dd><div class="content-body" data-v-9fd4d1dd><!--[--><!--]--><div class="VPNavBarSearch search" data-v-9fd4d1dd><!----></div><nav aria-labelledby="main-nav-aria-label" class="VPNavBarMenu menu" data-v-9fd4d1dd data-v-afb2845e><span id="main-nav-aria-label" class="visually-hidden" data-v-afb2845e> Main Navigation </span><!--[--><!--[--><a class="VPLink link VPNavBarMenuLink" href="/" tabindex="0" data-v-afb2845e data-v-815115f5><!--[--><span data-v-815115f5>首页</span><!--]--></a><!--]--><!--[--><a class="VPLink link VPNavBarMenuLink" href="/user-center/profile.html" tabindex="0" data-v-afb2845e data-v-815115f5><!--[--><span data-v-815115f5>用户中心</span><!--]--></a><!--]--><!--[--><a class="VPLink link VPNavBarMenuLink" href="/service-purchase/" tabindex="0" data-v-afb2845e data-v-815115f5><!--[--><span data-v-815115f5>服务购买</span><!--]--></a><!--]--><!--[--><a class="VPLink link VPNavBarMenuLink" href="/showcase/" tabindex="0" data-v-afb2845e data-v-815115f5><!--[--><span data-v-815115f5>ShowCase</span><!--]--></a><!--]--><!--]--></nav><!----><div class="VPNavBarAppearance appearance" data-v-9fd4d1dd data-v-3f90c1a5><button class="VPSwitch VPSwitchAppearance" type="button" role="switch" title aria-checked="false" data-v-3f90c1a5 data-v-be9742d9 data-v-b4ccac88><span class="check" data-v-b4ccac88><span class="icon" data-v-b4ccac88><!--[--><span class="vpi-sun sun" data-v-be9742d9></span><span class="vpi-moon moon" data-v-be9742d9></span><!--]--></span></span></button></div><!----><div class="VPFlyout VPNavBarExtra extra" data-v-9fd4d1dd data-v-f953d92f data-v-bfe7971f><button type="button" class="button" aria-haspopup="true" aria-expanded="false" aria-label="extra navigation" data-v-bfe7971f><span class="vpi-more-horizontal icon" data-v-bfe7971f></span></button><div class="menu" data-v-bfe7971f><div class="VPMenu" data-v-bfe7971f data-v-20ed86d6><!----><!--[--><!--[--><!----><div class="group" data-v-f953d92f><div class="item appearance" data-v-f953d92f><p class="label" data-v-f953d92f>Appearance</p><div class="appearance-action" data-v-f953d92f><button class="VPSwitch VPSwitchAppearance" type="button" role="switch" title aria-checked="false" data-v-f953d92f data-v-be9742d9 data-v-b4ccac88><span class="check" data-v-b4ccac88><span class="icon" data-v-b4ccac88><!--[--><span class="vpi-sun sun" data-v-be9742d9></span><span class="vpi-moon moon" data-v-be9742d9></span><!--]--></span></span></button></div></div></div><!----><!--]--><!--]--></div></div></div><!--[--><!--]--><button type="button" class="VPNavBarHamburger hamburger" aria-label="mobile navigation" aria-expanded="false" aria-controls="VPNavScreen" data-v-9fd4d1dd data-v-6bee1efd><span class="container" data-v-6bee1efd><span class="top" data-v-6bee1efd></span><span class="middle" data-v-6bee1efd></span><span class="bottom" data-v-6bee1efd></span></span></button></div></div></div></div><div class="divider" data-v-9fd4d1dd><div class="divider-line" data-v-9fd4d1dd></div></div></div><!----></header><div class="VPLocalNav empty fixed" data-v-d8b57b2d data-v-2488c25a><div class="container" data-v-2488c25a><!----><div class="VPLocalNavOutlineDropdown" style="--vp-vh:0px;" data-v-2488c25a data-v-6b867909><button data-v-6b867909>Return to top</button><!----></div></div></div><!----><div class="VPContent" id="VPContent" data-v-d8b57b2d data-v-9a6c75ad><div class="VPDoc has-aside" data-v-9a6c75ad data-v-e6f2a212><!--[--><!--]--><div class="container" data-v-e6f2a212><div class="aside" data-v-e6f2a212><div class="aside-curtain" data-v-e6f2a212></div><div class="aside-container" data-v-e6f2a212><div class="aside-content" data-v-e6f2a212><div class="VPDocAside" data-v-e6f2a212 data-v-cb998dce><!--[--><!--]--><!--[--><!--]--><nav aria-labelledby="doc-outline-aria-label" class="VPDocAsideOutline" data-v-cb998dce data-v-f610f197><div class="content" data-v-f610f197><div class="outline-marker" data-v-f610f197></div><div aria-level="2" class="outline-title" id="doc-outline-aria-label" role="heading" data-v-f610f197>On this page</div><ul class="VPDocOutlineItem root" data-v-f610f197 data-v-53c99d69><!--[--><!--]--></ul></div></nav><!--[--><!--]--><div class="spacer" data-v-cb998dce></div><!--[--><!--]--><!----><!--[--><!--]--><!--[--><!--]--></div></div></div></div><div class="content" data-v-e6f2a212><div class="content-container" data-v-e6f2a212><!--[--><!--]--><main class="main" data-v-e6f2a212><div style="position:relative;" class="vp-doc _api-examples" data-v-e6f2a212><div><h1 id="runtime-api-examples" tabindex="-1">Runtime API Examples <a class="header-anchor" href="#runtime-api-examples" aria-label="Permalink to &quot;Runtime API Examples&quot;">​</a></h1><p>This page demonstrates usage of some of the runtime APIs provided by VitePress.</p><p>The main <code>useData()</code> API can be used to access site, theme, and page data for the current page. It works in both <code>.md</code> and <code>.vue</code> files:</p><div class="language-md vp-adaptive-theme"><button title="Copy Code" class="copy"></button><span class="lang">md</span><pre class="shiki shiki-themes github-light github-dark vp-code" tabindex="0"><code><span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">&lt;script setup&gt;</span></span>
<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">import { useData } from &#39;vitepress&#39;</span></span>
<span class="line"></span>
<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">const { theme, page, frontmatter } = useData()</span></span>
<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">&lt;/script&gt;</span></span>
<span class="line"></span>
<span class="line"><span style="--shiki-light:#005CC5;--shiki-light-font-weight:bold;--shiki-dark:#79B8FF;--shiki-dark-font-weight:bold;">## Results</span></span>
<span class="line"></span>
<span class="line"><span style="--shiki-light:#005CC5;--shiki-light-font-weight:bold;--shiki-dark:#79B8FF;--shiki-dark-font-weight:bold;">### Theme Data</span></span>
<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">&lt;pre&gt;{{ theme }}&lt;/pre&gt;</span></span>
<span class="line"></span>
<span class="line"><span style="--shiki-light:#005CC5;--shiki-light-font-weight:bold;--shiki-dark:#79B8FF;--shiki-dark-font-weight:bold;">### Page Data</span></span>
<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">&lt;pre&gt;{{ page }}&lt;/pre&gt;</span></span>
<span class="line"></span>
<span class="line"><span style="--shiki-light:#005CC5;--shiki-light-font-weight:bold;--shiki-dark:#79B8FF;--shiki-dark-font-weight:bold;">### Page Frontmatter</span></span>
<span class="line"><span style="--shiki-light:#24292E;--shiki-dark:#E1E4E8;">&lt;pre&gt;{{ frontmatter }}&lt;/pre&gt;</span></span></code></pre></div><h2 id="results" tabindex="-1">Results <a class="header-anchor" href="#results" aria-label="Permalink to &quot;Results&quot;">​</a></h2><h3 id="theme-data" tabindex="-1">Theme Data <a class="header-anchor" href="#theme-data" aria-label="Permalink to &quot;Theme Data&quot;">​</a></h3><pre>{
  &quot;nav&quot;: [
    {
      &quot;text&quot;: &quot;首页&quot;,
      &quot;link&quot;: &quot;/&quot;
    },
    {
      &quot;text&quot;: &quot;用户中心&quot;,
      &quot;link&quot;: &quot;/user-center/profile&quot;
    },
    {
      &quot;text&quot;: &quot;服务购买&quot;,
      &quot;link&quot;: &quot;/service-purchase/&quot;
    },
    {
      &quot;text&quot;: &quot;ShowCase&quot;,
      &quot;link&quot;: &quot;/showcase/&quot;
    }
  ],
  &quot;sidebar&quot;: {
    &quot;/user-center/&quot;: [
      {
        &quot;text&quot;: &quot;用户中心&quot;,
        &quot;items&quot;: [
          {
            &quot;text&quot;: &quot;登录/注册&quot;,
            &quot;link&quot;: &quot;/user-center/login&quot;
          },
          {
            &quot;text&quot;: &quot;个人主页&quot;,
            &quot;link&quot;: &quot;/user-center/profile&quot;
          }
        ]
      }
    ],
    &quot;/service-purchase/&quot;: [
      {
        &quot;text&quot;: &quot;服务购买&quot;,
        &quot;items&quot;: [
          {
            &quot;text&quot;: &quot;套餐选择&quot;,
            &quot;link&quot;: &quot;/service-purchase/&quot;
          }
        ]
      }
    ],
    &quot;/showcase/&quot;: [
      {
        &quot;text&quot;: &quot;ShowCase&quot;,
        &quot;items&quot;: [
          {
            &quot;text&quot;: &quot;Tour Planning Examples&quot;,
            &quot;link&quot;: &quot;/showcase/&quot;
          }
        ]
      }
    ]
  }
}</pre><h3 id="page-data" tabindex="-1">Page Data <a class="header-anchor" href="#page-data" aria-label="Permalink to &quot;Page Data&quot;">​</a></h3><pre>{
  &quot;title&quot;: &quot;Runtime API Examples&quot;,
  &quot;description&quot;: &quot;&quot;,
  &quot;frontmatter&quot;: {
    &quot;outline&quot;: &quot;deep&quot;
  },
  &quot;headers&quot;: [],
  &quot;relativePath&quot;: &quot;api-examples.md&quot;,
  &quot;filePath&quot;: &quot;api-examples.md&quot;
}</pre><h3 id="page-frontmatter" tabindex="-1">Page Frontmatter <a class="header-anchor" href="#page-frontmatter" aria-label="Permalink to &quot;Page Frontmatter&quot;">​</a></h3><pre>{
  &quot;outline&quot;: &quot;deep&quot;
}</pre><h2 id="more" tabindex="-1">More <a class="header-anchor" href="#more" aria-label="Permalink to &quot;More&quot;">​</a></h2><p>Check out the documentation for the <a href="https://vitepress.dev/reference/runtime-api#usedata" target="_blank" rel="noreferrer">full list of runtime APIs</a>.</p></div></div></main><footer class="VPDocFooter" data-v-e6f2a212 data-v-1bcd8184><!--[--><!--]--><!----><!----></footer><!--[--><!--]--></div></div></div><!--[--><!--]--></div></div><!----><!--[--><!--]--></div></div>
    <script>window.__VP_HASH_MAP__=JSON.parse("{\"api-examples.md\":\"OicIAmCk\",\"index.md\":\"C5DNIyzZ\",\"login.md\":\"D5fsBjkA\",\"markdown-examples.md\":\"BPvl5Bap\",\"readme.md\":\"D346Sb61\",\"service-purchase_index.md\":\"BAYbupTv\",\"service-purchase_recharge.md\":\"Cei0qMz7\",\"service-purchase_single.md\":\"7B5mH1l5\",\"service-purchase_vip.md\":\"DmUN3ghI\",\"showcase_index.md\":\"xRZtXJDk\",\"user-center_index.md\":\"CKmDINYY\",\"user-center_login.md\":\"D9njvq2P\",\"user-center_profile.md\":\"ByO9M1zg\"}");window.__VP_SITE_DATA__=JSON.parse("{\"lang\":\"en-US\",\"dir\":\"ltr\",\"title\":\"TopMeansLab\",\"description\":\"TopMeansLab Documentation\",\"base\":\"/\",\"head\":[],\"router\":{\"prefetchLinks\":true},\"appearance\":true,\"themeConfig\":{\"nav\":[{\"text\":\"首页\",\"link\":\"/\"},{\"text\":\"用户中心\",\"link\":\"/user-center/profile\"},{\"text\":\"服务购买\",\"link\":\"/service-purchase/\"},{\"text\":\"ShowCase\",\"link\":\"/showcase/\"}],\"sidebar\":{\"/user-center/\":[{\"text\":\"用户中心\",\"items\":[{\"text\":\"登录/注册\",\"link\":\"/user-center/login\"},{\"text\":\"个人主页\",\"link\":\"/user-center/profile\"}]}],\"/service-purchase/\":[{\"text\":\"服务购买\",\"items\":[{\"text\":\"套餐选择\",\"link\":\"/service-purchase/\"}]}],\"/showcase/\":[{\"text\":\"ShowCase\",\"items\":[{\"text\":\"Tour Planning Examples\",\"link\":\"/showcase/\"}]}]}},\"locales\":{},\"scrollOffset\":134,\"cleanUrls\":false}");</script>
    
  </body>
</html>