import{u as p,c as h,o,ab as d,j as a,t,k as i,a as s}from"./chunks/framework.neMYHtQj.js";const f=JSON.parse('{"title":"Runtime API Examples","description":"","frontmatter":{"outline":"deep"},"headers":[],"relativePath":"api-examples.md","filePath":"api-examples.md"}'),m={name:"api-examples.md"},E=Object.assign(m,{setup(k){const{site:g,theme:n,page:l,frontmatter:r}=p();return(c,e)=>(o(),h("div",null,[e[0]||(e[0]=d("",6)),a("pre",null,t(i(n)),1),e[1]||(e[1]=a("h3",{id:"page-data",tabindex:"-1"},[s("Page Data "),a("a",{class:"header-anchor",href:"#page-data","aria-label":'Permalink to "Page Data"'},"​")],-1)),a("pre",null,t(i(l)),1),e[2]||(e[2]=a("h3",{id:"page-frontmatter",tabindex:"-1"},[s("Page Frontmatter "),a("a",{class:"header-anchor",href:"#page-frontmatter","aria-label":'Permalink to "Page Frontmatter"'},"​")],-1)),a("pre",null,t(i(r)),1),e[3]||(e[3]=a("h2",{id:"more",tabindex:"-1"},[s("More "),a("a",{class:"header-anchor",href:"#more","aria-label":'Permalink to "More"'},"​")],-1)),e[4]||(e[4]=a("p",null,[s("Check out the documentation for the "),a("a",{href:"https://vitepress.dev/reference/runtime-api#usedata",target:"_blank",rel:"noreferrer"},"full list of runtime APIs"),s(".")],-1))]))}});export{f as __pageData,E as default};
