import{d as C,p as _,v as g,C as M,c as y,o as V,j as h,G as k,w,a as T,_ as B}from"./framework.neMYHtQj.js";function c(l,r){const u=i();return c=function(n,x){return n=n-0,u[n]},c(l,r)}const N={class:"captcha",style:{display:"flex"}},S={ref:"canvas",width:"100",height:"40"},A={class:"valicode-btn"};function i(){const l=["value","#ccc","lineTo","random","stroke","div"];return i=function(){return l},i()}const b="ABCDEFGHIJKLMNPQRSTUVWXYZabcdefghijmnopqrstuvwxyz023456789",j=C({__name:"Valicode",emits:["getCode"],setup(l,{expose:r,emit:u}){const n=u,x=_(null),e=_(null),d=_(""),m=b.length;function p(){return b.charAt(Math.floor(Math.random()*m))}function f(){const t=c;if(!e.value)return;e.value.clearRect(0,0,x.value.width,x.value.height);let a=10;d.value="";for(let o=0;o<4;o++){const s=p();d.value+=s,e.value.font="bold 20px Arial",e[t(0)].fillStyle="#333",e[t(0)].fillText(s,a,25),a+=20}for(let o=0;o<10;o++)e.value.strokeStyle=t(1),e.value.beginPath(),e.value.moveTo(Math.random()*100,Math.random()*40),e.value[t(2)](Math[t(3)]()*100,Math.random()*40),e.value[t(4)]();n("getCode",d.value.toLowerCase())}function v(){f()}return r({refresh:v}),g(()=>{var a;const t=c;x.value=document.querySelector("canvas"),e[t(0)]=(a=x.value)==null?void 0:a.getContext("2d"),f()}),(t,a)=>{const o=c,s=M("el-button");return V(),y("div",N,[h("canvas",S,null,512),h(o(5),A,[k(s,{type:"text",class:"link-button",onClick:v},{default:w(()=>a[0]||(a[0]=[T("看不清，换一张")])),_:1})])])}}}),E=B(j,[["__scopeId","data-v-aa6c3bb5"]]);export{E as V};
