<template>
  <div v-if="visible" class="qr-dialog-overlay" @click="handleOverlayClick">
    <div class="qr-dialog" @click.stop>
      <!-- 对话框头部 -->
      <div class="qr-dialog-header">
        <h3 class="dialog-title">
          <i class="alipay-icon">💰</i>
          支付宝扫码支付
        </h3>
        <button class="close-btn" @click="closeDialog">
          <i class="close-icon">✕</i>
        </button>
      </div>

      <!-- 对话框内容 -->
      <div class="qr-dialog-content">
        <!-- 支付信息 -->
        <div class="payment-info">
          <div class="amount-display">
            <span class="amount-label">支付金额</span>
            <span class="amount-value">{{ formatAmount(paymentData.amount) }}</span>
          </div>
          <div class="order-info">
            <span class="order-label">订单号：</span>
            <span class="order-no">{{ paymentData.outTradeNo }}</span>
          </div>
        </div>

        <!-- 二维码区域 -->
        <div class="qr-code-section">
          <div v-if="loading" class="qr-loading">
            <div class="loading-spinner"></div>
            <p>正在生成支付二维码...</p>
          </div>
          
          <div v-else-if="qrCodeUrl" class="qr-code-container">
            <img :src="qrCodeUrl" alt="支付宝支付二维码" class="qr-code-image" />
            <p class="qr-code-tip">请使用支付宝扫描二维码完成支付</p>
          </div>
          
          <div v-else class="qr-error">
            <p class="error-message">二维码生成失败，请重试</p>
            <button class="retry-btn" @click="regenerateQRCode">重新生成</button>
          </div>
        </div>

        <!-- 支付状态 -->
        <div class="payment-status">
          <div class="status-indicator" :class="statusClass">
            <i class="status-icon">{{ statusIcon }}</i>
            <span class="status-text">{{ statusText }}</span>
          </div>
          
          <div v-if="paymentStatus === 'pending'" class="countdown">
            <span class="countdown-text">支付剩余时间：</span>
            <span class="countdown-time">{{ formatCountdown(remainingTime) }}</span>
          </div>
        </div>

        <!-- 操作按钮 -->
        <div class="dialog-actions">
          <button 
            class="action-btn secondary" 
            @click="cancelPayment"
            :disabled="paymentStatus === 'success'"
          >
            取消支付
          </button>
          <button 
            class="action-btn primary" 
            @click="checkPaymentStatus"
            :disabled="loading || paymentStatus === 'success'"
          >
            {{ paymentStatus === 'success' ? '支付成功' : '我已支付' }}
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted, watch } from 'vue'
import paymentService from '../services/paymentService'

// Props
const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  paymentData: {
    type: Object,
    required: true
  }
})

// Emits
const emit = defineEmits(['close', 'success', 'cancel', 'error'])

// 响应式数据
const loading = ref(false)
const qrCodeUrl = ref('')
const paymentStatus = ref('pending')
const remainingTime = ref(30 * 60) // 30分钟倒计时
const pollingTimer = ref(null)
const countdownTimer = ref(null)

// 计算属性
const statusClass = computed(() => {
  return {
    'status-pending': paymentStatus.value === 'pending',
    'status-success': paymentStatus.value === 'success',
    'status-error': paymentStatus.value === 'error' || paymentStatus.value === 'closed'
  }
})

const statusIcon = computed(() => {
  const iconMap = {
    'pending': '⏳',
    'success': '✅',
    'error': '❌',
    'closed': '❌'
  }
  return iconMap[paymentStatus.value] || '⏳'
})

const statusText = computed(() => {
  const textMap = {
    'pending': '等待支付',
    'success': '支付成功',
    'error': '支付失败',
    'closed': '支付已关闭'
  }
  return textMap[paymentStatus.value] || '未知状态'
})

// 方法
const formatAmount = (amount) => {
  return `¥${parseFloat(amount).toFixed(2)}`
}

const formatCountdown = (seconds) => {
  const minutes = Math.floor(seconds / 60)
  const secs = seconds % 60
  return `${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`
}

const handleOverlayClick = () => {
  if (paymentStatus.value !== 'success') {
    closeDialog()
  }
}

const closeDialog = () => {
  stopPolling()
  stopCountdown()
  emit('close')
}

const generateQRCode = async () => {
  try {
    loading.value = true

    if (props.paymentData.qrCode) {
      // 如果支付宝返回了二维码数据，直接使用
      if (props.paymentData.qrCode.startsWith('data:')) {
        qrCodeUrl.value = props.paymentData.qrCode
      } else if (props.paymentData.qrCode.startsWith('http')) {
        // 如果是 URL，直接使用
        qrCodeUrl.value = props.paymentData.qrCode
      } else {
        // 如果是 base64 数据，添加前缀
        qrCodeUrl.value = `data:image/png;base64,${props.paymentData.qrCode}`
      }
    } else {
      // 生成简单的二维码占位符
      qrCodeUrl.value = generateSimpleQRCode(props.paymentData.outTradeNo)
    }
  } catch (error) {
    console.error('生成二维码失败:', error)
    qrCodeUrl.value = generateSimpleQRCode(props.paymentData.outTradeNo || '订单号')
  } finally {
    loading.value = false
  }
}

// 生成简单的二维码 SVG
const generateSimpleQRCode = (text) => {
  const svg = `
    <svg width="200" height="200" xmlns="http://www.w3.org/2000/svg">
      <rect width="200" height="200" fill="white" stroke="#e8e8e8" stroke-width="2"/>
      <rect x="20" y="20" width="20" height="20" fill="#1677ff"/>
      <rect x="60" y="20" width="20" height="20" fill="#1677ff"/>
      <rect x="100" y="20" width="20" height="20" fill="#1677ff"/>
      <rect x="140" y="20" width="20" height="20" fill="#1677ff"/>
      <rect x="160" y="20" width="20" height="20" fill="#1677ff"/>

      <rect x="20" y="60" width="20" height="20" fill="#1677ff"/>
      <rect x="160" y="60" width="20" height="20" fill="#1677ff"/>

      <rect x="20" y="100" width="20" height="20" fill="#1677ff"/>
      <rect x="60" y="100" width="20" height="20" fill="#1677ff"/>
      <rect x="100" y="100" width="20" height="20" fill="#1677ff"/>
      <rect x="140" y="100" width="20" height="20" fill="#1677ff"/>
      <rect x="160" y="100" width="20" height="20" fill="#1677ff"/>

      <rect x="20" y="140" width="20" height="20" fill="#1677ff"/>
      <rect x="160" y="140" width="20" height="20" fill="#1677ff"/>

      <rect x="20" y="160" width="20" height="20" fill="#1677ff"/>
      <rect x="60" y="160" width="20" height="20" fill="#1677ff"/>
      <rect x="100" y="160" width="20" height="20" fill="#1677ff"/>
      <rect x="140" y="160" width="20" height="20" fill="#1677ff"/>
      <rect x="160" y="160" width="20" height="20" fill="#1677ff"/>

      <text x="100" y="110" text-anchor="middle" font-size="12" fill="#666" font-weight="bold">
        支付二维码
      </text>
      <text x="100" y="130" text-anchor="middle" font-size="10" fill="#999">
        ${text.substring(0, 15)}...
      </text>
    </svg>
  `
  return `data:image/svg+xml;base64,${btoa(svg)}`
}

const regenerateQRCode = () => {
  generateQRCode()
}

const startPolling = () => {
  if (pollingTimer.value) return
  
  pollingTimer.value = setInterval(async () => {
    await checkPaymentStatus(false)
  }, 3000) // 每3秒查询一次
}

const stopPolling = () => {
  if (pollingTimer.value) {
    clearInterval(pollingTimer.value)
    pollingTimer.value = null
  }
}

const startCountdown = () => {
  if (countdownTimer.value) return
  
  countdownTimer.value = setInterval(() => {
    if (remainingTime.value > 0) {
      remainingTime.value--
    } else {
      // 倒计时结束，支付超时
      paymentStatus.value = 'closed'
      stopPolling()
      stopCountdown()
      emit('error', { message: '支付超时' })
    }
  }, 1000)
}

const stopCountdown = () => {
  if (countdownTimer.value) {
    clearInterval(countdownTimer.value)
    countdownTimer.value = null
  }
}

const checkPaymentStatus = async (showLoading = true) => {
  try {
    if (showLoading) {
      loading.value = true
    }
    
    const result = await paymentService.queryPaymentStatus(props.paymentData.outTradeNo)
    
    if (result.success) {
      const newStatus = result.data.status
      
      if (newStatus !== paymentStatus.value) {
        paymentStatus.value = newStatus
        
        if (newStatus === 'success') {
          stopPolling()
          stopCountdown()
          emit('success', result.data)
        } else if (newStatus === 'closed' || newStatus === 'cancelled') {
          stopPolling()
          stopCountdown()
          emit('error', { message: '支付已关闭' })
        }
      }
    }
  } catch (error) {
    console.error('查询支付状态失败:', error)
  } finally {
    if (showLoading) {
      loading.value = false
    }
  }
}

const cancelPayment = async () => {
  try {
    loading.value = true
    
    const result = await paymentService.cancelPayment(props.paymentData.outTradeNo)
    
    if (result.success) {
      paymentStatus.value = 'cancelled'
      stopPolling()
      stopCountdown()
      emit('cancel')
    } else {
      console.error('取消支付失败:', result.message)
    }
  } catch (error) {
    console.error('取消支付失败:', error)
  } finally {
    loading.value = false
  }
}

// 监听弹窗显示状态
watch(() => props.visible, (newVisible) => {
  if (newVisible) {
    // 弹窗打开时初始化
    paymentStatus.value = 'pending'
    remainingTime.value = 30 * 60
    generateQRCode()
    startPolling()
    startCountdown()
  } else {
    // 弹窗关闭时清理
    stopPolling()
    stopCountdown()
  }
})

// 组件卸载时清理
onUnmounted(() => {
  stopPolling()
  stopCountdown()
})
</script>

<style scoped>
.qr-dialog-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.6);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
  backdrop-filter: blur(4px);
}

.qr-dialog {
  background: white;
  border-radius: 12px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
  max-width: 400px;
  width: 90%;
  max-height: 90vh;
  overflow: hidden;
  animation: dialogSlideIn 0.3s ease-out;
}

@keyframes dialogSlideIn {
  from {
    opacity: 0;
    transform: scale(0.9) translateY(-20px);
  }
  to {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}

.qr-dialog-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20px 24px;
  border-bottom: 1px solid #f0f0f0;
  background: linear-gradient(135deg, #1677ff 0%, #69c0ff 100%);
  color: white;
}

.dialog-title {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 8px;
}

.alipay-icon {
  font-size: 20px;
}

.close-btn {
  background: none;
  border: none;
  color: white;
  font-size: 20px;
  cursor: pointer;
  padding: 4px;
  border-radius: 4px;
  transition: background-color 0.2s;
}

.close-btn:hover {
  background: rgba(255, 255, 255, 0.1);
}

.qr-dialog-content {
  padding: 24px;
}

.payment-info {
  text-align: center;
  margin-bottom: 24px;
}

.amount-display {
  margin-bottom: 12px;
}

.amount-label {
  display: block;
  font-size: 14px;
  color: #666;
  margin-bottom: 4px;
}

.amount-value {
  font-size: 28px;
  font-weight: bold;
  color: #1677ff;
}

.order-info {
  font-size: 12px;
  color: #999;
}

.order-no {
  font-family: monospace;
}

.qr-code-section {
  text-align: center;
  margin-bottom: 24px;
}

.qr-loading {
  padding: 40px 20px;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 3px solid #f3f3f3;
  border-top: 3px solid #1677ff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto 16px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.qr-code-container {
  padding: 20px;
}

.qr-code-image {
  width: 200px;
  height: 200px;
  border: 1px solid #e8e8e8;
  border-radius: 8px;
  margin-bottom: 16px;
}

.qr-code-tip {
  font-size: 14px;
  color: #666;
  margin: 0;
}

.qr-error {
  padding: 40px 20px;
}

.error-message {
  color: #ff4d4f;
  margin-bottom: 16px;
}

.retry-btn {
  background: #1677ff;
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
}

.payment-status {
  text-align: center;
  margin-bottom: 24px;
  padding: 16px;
  background: #f8f9fa;
  border-radius: 8px;
}

.status-indicator {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  margin-bottom: 12px;
}

.status-pending .status-text {
  color: #faad14;
}

.status-success .status-text {
  color: #52c41a;
}

.status-error .status-text {
  color: #ff4d4f;
}

.countdown {
  font-size: 14px;
  color: #666;
}

.countdown-time {
  font-weight: bold;
  color: #faad14;
}

.dialog-actions {
  display: flex;
  gap: 12px;
}

.action-btn {
  flex: 1;
  padding: 12px 24px;
  border: none;
  border-radius: 6px;
  font-size: 16px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
}

.action-btn.secondary {
  background: #f5f5f5;
  color: #666;
}

.action-btn.secondary:hover:not(:disabled) {
  background: #e8e8e8;
}

.action-btn.primary {
  background: #1677ff;
  color: white;
}

.action-btn.primary:hover:not(:disabled) {
  background: #0958d9;
}

.action-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}
</style>
