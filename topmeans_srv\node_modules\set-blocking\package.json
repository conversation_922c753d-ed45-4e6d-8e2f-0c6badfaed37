{"name": "set-blocking", "version": "2.0.0", "description": "set blocking stdio and stderr ensuring that terminal output does not truncate", "main": "index.js", "scripts": {"pretest": "standard", "test": "nyc mocha ./test/*.js", "coverage": "nyc report --reporter=text-lcov | coveralls", "version": "standard-version"}, "repository": {"type": "git", "url": "git+https://github.com/yargs/set-blocking.git"}, "keywords": ["flush", "terminal", "blocking", "shim", "stdio", "stderr"], "author": "<PERSON> <<EMAIL>>", "license": "ISC", "bugs": {"url": "https://github.com/yargs/set-blocking/issues"}, "homepage": "https://github.com/yargs/set-blocking#readme", "devDependencies": {"chai": "^3.5.0", "coveralls": "^2.11.9", "mocha": "^2.4.5", "nyc": "^6.4.4", "standard": "^7.0.1", "standard-version": "^2.2.1"}, "files": ["index.js", "LICENSE.txt"]}