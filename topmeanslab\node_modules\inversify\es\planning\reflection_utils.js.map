{"version": 3, "file": "reflection_utils.js", "sourceRoot": "", "sources": ["../../src/planning/reflection_utils.ts"], "names": [], "mappings": ";;;;;;;;;AAAA,OAAO,EAAE,oBAAoB,EAAE,MAAM,uCAAuC,CAAC;AAC7E,OAAO,KAAK,UAAU,MAAM,yBAAyB,CAAC;AACtD,OAAO,EAAE,cAAc,EAAE,MAAM,4BAA4B,CAAC;AAC5D,OAAO,KAAK,YAAY,MAAM,4BAA4B,CAAC;AAE3D,OAAO,EAAE,eAAe,EAAE,MAAM,wBAAwB,CAAC;AAEzD,OAAO,EAAE,MAAM,EAAE,MAAM,UAAU,CAAC;AAElC,SAAS,eAAe,CACtB,cAAyC,EAAE,IAAqB;IAEhE,IAAM,eAAe,GAAG,eAAe,CAAC,IAAI,CAAC,CAAC;IAC9C,OAAO,UAAU,CAAC,cAAc,EAAE,eAAe,EAAE,IAAI,EAAE,KAAK,CAAC,CAAC;AAClE,CAAC;AAED,SAAS,UAAU,CACjB,cAAyC,EACzC,eAAuB,EACvB,IAAqB,EACrB,WAAoB;IAGpB,IAAM,QAAQ,GAAG,cAAc,CAAC,sBAAsB,CAAC,IAAI,CAAC,CAAC;IAG7D,IAAM,kBAAkB,GAAG,QAAQ,CAAC,yBAAyB,CAAC;IAG9D,IAAI,kBAAkB,KAAK,SAAS,EAAE;QACpC,IAAM,GAAG,GAAM,UAAU,CAAC,6BAA6B,SAAI,eAAe,MAAG,CAAC;QAC9E,MAAM,IAAI,KAAK,CAAC,GAAG,CAAC,CAAC;KACtB;IAGD,IAAM,uBAAuB,GAAG,QAAQ,CAAC,qBAAqB,CAAC;IAE/D,IAAM,IAAI,GAAG,MAAM,CAAC,IAAI,CAAC,uBAAuB,CAAC,CAAC;IAClD,IAAM,gCAAgC,GAAG,CAAC,IAAI,CAAC,MAAM,KAAK,CAAC,IAAI,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;IAChF,IAAM,qBAAqB,GAAG,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC;IAExD,IAAM,UAAU,GAAG,CAAC,gCAAgC,IAAI,qBAAqB,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC;IAG3G,IAAM,kBAAkB,GAAG,2BAA2B,CACpD,WAAW,EACX,eAAe,EACf,kBAAkB,EAClB,uBAAuB,EACvB,UAAU,CACX,CAAC;IAGF,IAAM,eAAe,GAAG,sBAAsB,CAAC,cAAc,EAAE,IAAI,EAAE,eAAe,CAAC,CAAC;IAEtF,IAAM,OAAO,mCACR,kBAAkB,SAClB,eAAe,OACnB,CAAC;IAEF,OAAO,OAAO,CAAC;AAEjB,CAAC;AACD,SAAS,0BAA0B,CACjC,KAAa,EACb,WAAoB,EACpB,eAAuB,EACvB,kBAAkD,EAClD,uBAA+C;IAG/C,IAAM,cAAc,GAAG,uBAAuB,CAAC,KAAK,CAAC,QAAQ,EAAE,CAAC,IAAI,EAAE,CAAC;IACvE,IAAM,QAAQ,GAAG,oBAAoB,CAAC,cAAc,CAAC,CAAC;IACtD,IAAM,SAAS,GAAG,QAAQ,CAAC,SAAS,KAAK,IAAI,CAAC;IAI9C,IAAI,iBAAiB,GAAG,kBAAkB,CAAC,KAAK,CAAC,CAAC;IAClD,IAAM,gBAAgB,GAAG,CAAC,QAAQ,CAAC,MAAM,IAAI,QAAQ,CAAC,WAAW,CAAC,CAAC;IACnE,iBAAiB,GAAG,CAAC,gBAAgB,CAAC,CAAC,CAAC,CAAC,CAAC,gBAAgB,CAAC,CAAC,CAAC,CAAC,iBAAiB,CAAC;IAGhF,IAAI,iBAAiB,YAAY,oBAAoB,EAAE;QACrD,iBAAiB,GAAG,iBAAiB,CAAC,MAAM,EAAE,CAAC;KAChD;IAID,IAAI,SAAS,EAAE;QAEb,IAAM,QAAQ,GAAG,iBAAiB,KAAK,MAAM,CAAC;QAC9C,IAAM,UAAU,GAAG,iBAAiB,KAAK,QAAQ,CAAC;QAClD,IAAM,WAAW,GAAG,iBAAiB,KAAK,SAAS,CAAC;QACpD,IAAM,aAAa,GAAG,CAAC,QAAQ,IAAI,UAAU,IAAI,WAAW,CAAC,CAAC;QAE9D,IAAI,CAAC,WAAW,IAAI,aAAa,EAAE;YACjC,IAAM,GAAG,GAAM,UAAU,CAAC,yBAAyB,kBAAa,KAAK,kBAAa,eAAe,MAAG,CAAC;YACrG,MAAM,IAAI,KAAK,CAAC,GAAG,CAAC,CAAC;SACtB;QAED,IAAM,MAAM,GAAG,IAAI,MAAM,CAAC,cAAc,CAAC,mBAAmB,EAAE,QAAQ,CAAC,UAAU,EAAE,iBAAiD,CAAC,CAAC;QACtI,MAAM,CAAC,QAAQ,GAAG,cAAc,CAAC;QACjC,OAAO,MAAM,CAAC;KACf;IAED,OAAO,IAAI,CAAC;AAEd,CAAC;AAED,SAAS,2BAA2B,CAClC,WAAoB,EACpB,eAAuB,EACvB,kBAAkD,EAClD,uBAA+C,EAC/C,UAAkB;IAGlB,IAAM,OAAO,GAAwB,EAAE,CAAC;IACxC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,UAAU,EAAE,CAAC,EAAE,EAAE;QACnC,IAAM,KAAK,GAAG,CAAC,CAAC;QAChB,IAAM,MAAM,GAAG,0BAA0B,CACvC,KAAK,EACL,WAAW,EACX,eAAe,EACf,kBAAkB,EAClB,uBAAuB,CACxB,CAAC;QACF,IAAI,MAAM,KAAK,IAAI,EAAE;YACnB,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;SACtB;KACF;IAED,OAAO,OAAO,CAAC;AACjB,CAAC;AAED,SAAS,gCAAgC,CACvC,MAAW,EACX,WAAgB,EAChB,YAA6B,EAC7B,SAAiB;IAEjB,IAAM,iBAAiB,GAAG,CAAC,MAAM,IAAI,WAAW,CAAC,CAAC;IAClD,IAAI,iBAAiB,KAAK,SAAS,EAAE;QACnC,IAAM,GAAG,GAAM,UAAU,CAAC,6BAA6B,sBAAiB,MAAM,CAAC,YAAY,CAAC,kBAAa,SAAS,MAAG,CAAC;QACtH,MAAM,IAAI,KAAK,CAAC,GAAG,CAAC,CAAC;KACtB;IACD,OAAO,iBAAiB,CAAC;AAC3B,CAAC;AAED,SAAS,sBAAsB,CAC7B,cAAyC,EACzC,eAAgC,EAChC,eAAuB;IAGvB,IAAM,kBAAkB,GAAG,cAAc,CAAC,qBAAqB,CAAC,eAAe,CAAC,CAAC;IACjF,IAAI,OAAO,GAAwB,EAAE,CAAC;IACtC,IAAM,UAAU,GAAG,MAAM,CAAC,qBAAqB,CAAC,kBAAkB,CAAC,CAAC;IACpE,IAAM,UAAU,GAAwB,MAAM,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAC;IACxE,IAAM,IAAI,GAAwB,UAAU,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC;IAEhE,KAAkB,UAAI,EAAJ,aAAI,EAAJ,kBAAI,EAAJ,IAAI,EAAE;QAAnB,IAAM,GAAG,aAAA;QAGZ,IAAM,cAAc,GAAG,kBAAkB,CAAC,GAAG,CAA0B,CAAC;QAGxE,IAAM,QAAQ,GAAG,oBAAoB,CAAC,cAAc,CAAC,CAAC;QAEtD,IAAM,UAAU,GAAG,QAAQ,CAAC,UAAU,IAAI,GAAG,CAAC;QAG9C,IAAM,iBAAiB,GAAG,gCAAgC,CAAC,QAAQ,CAAC,MAAM,EAAE,QAAQ,CAAC,WAAW,EAAE,GAAG,EAAE,eAAe,CAAC,CAAC;QAGxH,IAAM,MAAM,GAAG,IAAI,MAAM,CAAC,cAAc,CAAC,aAAa,EAAE,UAAU,EAAE,iBAAiB,CAAC,CAAC;QACvF,MAAM,CAAC,QAAQ,GAAG,cAAc,CAAC;QACjC,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;KACtB;IAGD,IAAM,eAAe,GAAG,MAAM,CAAC,cAAc,CAAC,eAAe,CAAC,SAAS,CAAC,CAAC,WAAW,CAAC;IAErF,IAAI,eAAe,KAAK,MAAM,EAAE;QAE9B,IAAM,WAAW,GAAG,sBAAsB,CAAC,cAAc,EAAE,eAAe,EAAE,eAAe,CAAC,CAAC;QAE7F,OAAO,mCACF,OAAO,SACP,WAAW,OACf,CAAC;KAEH;IAED,OAAO,OAAO,CAAC;AACjB,CAAC;AAED,SAAS,2BAA2B,CAClC,cAAyC,EACzC,IAAqB;IAGrB,IAAM,eAAe,GAAG,MAAM,CAAC,cAAc,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,WAAW,CAAC;IAE1E,IAAI,eAAe,KAAK,MAAM,EAAE;QAG9B,IAAM,mBAAmB,GAAG,eAAe,CAAC,eAAe,CAAC,CAAC;QAE7D,IAAM,OAAO,GAAG,UAAU,CAAC,cAAc,EAAE,mBAAmB,EAAE,eAAe,EAAE,IAAI,CAAC,CAAC;QAGvF,IAAM,QAAQ,GAAG,OAAO,CAAC,GAAG,CAAC,UAAC,CAAC,IAAK,OAAA,CAAC,CAAC,QAAQ,CAAC,MAAM,CAAC,UAAA,CAAC,IAAI,OAAA,CAAC,CAAC,GAAG,KAAK,YAAY,CAAC,aAAa,EAApC,CAAoC,CAAC,EAA5D,CAA4D,CAAC,CAAC;QAIlG,IAAM,cAAc,GAAI,EAAiB,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE,EAAE,QAAQ,CAAC,CAAC,MAAM,CAAC;QAC5E,IAAM,eAAe,GAAG,OAAO,CAAC,MAAM,GAAG,cAAc,CAAC;QAExD,IAAI,eAAe,GAAG,CAAC,EAAE;YACvB,OAAO,eAAe,CAAC;SACxB;aAAM;YACL,OAAO,2BAA2B,CAAC,cAAc,EAAE,eAAe,CAAC,CAAC;SACrE;KAEF;SAAM;QACL,OAAO,CAAC,CAAC;KACV;AAEH,CAAC;AAED,SAAS,oBAAoB,CAAC,cAAqC;IAGjE,IAAM,iBAAiB,GAAQ,EAAE,CAAC;IAClC,cAAc,CAAC,OAAO,CAAC,UAAC,CAAsB;QAC5C,iBAAiB,CAAC,CAAC,CAAC,GAAG,CAAC,QAAQ,EAAE,CAAC,GAAG,CAAC,CAAC,KAAK,CAAC;IAChD,CAAC,CAAC,CAAC;IAGH,OAAO;QACL,MAAM,EAAE,iBAAiB,CAAC,YAAY,CAAC,UAAU,CAAC;QAClD,WAAW,EAAE,iBAAiB,CAAC,YAAY,CAAC,gBAAgB,CAAC;QAC7D,UAAU,EAAE,iBAAiB,CAAC,YAAY,CAAC,QAAQ,CAAC;QACpD,SAAS,EAAE,iBAAiB,CAAC,YAAY,CAAC,aAAa,CAAC;KACzD,CAAC;AAEJ,CAAC;AAED,OAAO,EAAE,eAAe,EAAE,2BAA2B,EAAE,eAAe,EAAE,CAAC"}