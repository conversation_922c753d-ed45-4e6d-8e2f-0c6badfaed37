import{_ as re,b3 as ie,p as f,a3 as F,h as de,v as ue,q as ce,C as m,c as d,o as i,G as o,k as c,w as a,a as y,F as G,j as s,b as N,ao as H,t as u,ap as A,e as W,B as Z,n as pe,P as me}from"./framework.B19ydMwb.js";import{u as ve,s as fe,e as J,c as ge,E as C,a as we}from"./theme.CmWpOUCL.js";import{m as ye}from"./TopmeansMarkdownService.HmRBHz74.js";const _e={class:"user-profile"},ke={key:0,class:"login-prompt"},Pe={class:"profile-card"},be={class:"profile-header"},$e={class:"profile-info"},he={class:"avatar-wrapper"},Ce=["src","alt"],xe={class:"user-details"},Ie={class:"user-name"},Ve={class:"editable-field",style:{position:"relative",display:"flex","align-items":"center"}},Te={class:"user-signature"},Ue={class:"editable-field",style:{position:"relative",display:"flex","align-items":"center"}},Le={class:"profile-content"},Be={class:"guides-container"},Fe={class:"guides-sidebar"},Ee={class:"sidebar-header"},ze={class:"guide-count"},Se={class:"guides-list"},De=["onClick"],je={class:"guide-item-header"},Ge={class:"guide-days"},Me={class:"guide-item-meta"},qe={class:"guide-date"},Re={key:0,class:"empty-guides"},Ne={class:"guides-content"},He={key:0,class:"guide-detail"},Ae={class:"guide-detail-header"},We={class:"guide-detail-meta"},Ze={class:"scroll-area guide-scroll-container"},Je={key:0},Ke={class:"answer-area-container"},Oe=["innerHTML"],Qe={key:0,class:"vitepress-divider"},Xe={key:1,class:"no-plans"},Ye={key:1,class:"no-guide-selected"},es={class:"security-settings"},ss={class:"card-header",style:{display:"flex","justify-content":"space-between","align-items":"center",width:"100%"}},ts={class:"card-header",style:{display:"flex","justify-content":"space-between","align-items":"center",width:"100%"}},ns={class:"card-header",style:{display:"flex","justify-content":"space-between","align-items":"center",width:"100%"}},os={class:"card-header",style:{display:"flex","justify-content":"space-between","align-items":"center",width:"100%"}},as={class:"dialog-footer",style:{display:"flex","justify-content":"flex-end",gap:"12px"}},ls={__name:"UserProfile",setup(rs){const E=ie(),r=ve(),{isLoggedIn:is}=fe(r),U=f("guides"),x=f(!1),K=f(!1),O=f(!1);F({nickname:!1,signature:!1});const b=F({nickname:!1,signature:!1}),v=F({nickname:"",signature:""});(()=>{v.nickname=r.userInfo.nickname||"",v.signature=r.userInfo.signature||""})();const L=async t=>{b[t]=!0,await me(),t==="nickname"?document.querySelector(".nickname-input .el-input__inner").focus():document.querySelector(".signature-input .el-textarea__inner").focus()},M=async t=>{if(b[t]=!1,v[t]!==r.userInfo[t])try{await r.updateProfile({[t]:v[t]}),C.success({message:"更新成功",duration:1e3}),r.userInfo[t]=v[t]}catch{C.error({message:"更新失败",duration:1e3}),v[t]=r.userInfo[t]}};f({avatar:"",isVerified:!1,guideCount:0,followers:0,following:0,phone:"",email:""});const p=f({currentPassword:"",newPassword:"",confirmPassword:""}),k=f(!1),I=f(null),z=t=>{k.value||(I.value.validateField(t),t==="newPassword"&&I.value.validateField("confirmPassword"))},Q=F({currentPassword:[{required:!0,message:"当前密码不能为空",trigger:["blur","change"]}],newPassword:[{required:!0,pattern:/^(?=.*[a-zA-Z])(?=.*\d)[\x21-\x7e]{6,20}$/,message:"需6-20位, 必须包含字母和数字",trigger:["blur","input"]},{validator:(t,e,l)=>{e===p.value.currentPassword?l(new Error("新密码不能与当前密码相同")):l()},trigger:["input","blur"]}],confirmPassword:[{validator:(t,e,l)=>{if(!e||!p.value.newPassword)return l();e!==p.value.newPassword?l(new Error("两次输入密码不一致")):l()},trigger:["input","blur"]}]}),$=f([]),V=f(-1),g=de(()=>V.value>=0?$.value[V.value]:null),q=async()=>{var t;try{if(!((t=r.userInfo)!=null&&t.account))return;const e=await r.getUserGuides();$.value=e,e.length>0&&(V.value=0)}catch(e){C.error({message:"获取攻略失败: "+e.message,duration:2e3})}},X=t=>{V.value=t},R=t=>t.startLocation&&t.endLocation&&t.totalDays?`从${t.startLocation}到${t.endLocation}的${t.totalDays}天攻略`:`攻略 - ${S(t.createTime)}`,Y=(t,e)=>{var B;if(!t)return"";let l=t;return l=l.replace(/^# Smart Travel Plan\s*\n*/m,""),l=l.replace(/!\[([^\]]*)\]\((https?:\/\/[^)]+)\)/g,(w,P,_)=>_.match(/\.(png|jpe?g|gif|webp|svg)(\?.*)?$/i)?w:`[${P}](${_})`),(B=r.userInfo)!=null&&B.account&&(l=l.replace(/!\[([^\]]*)\]\(\.?\/?([^)]+)\)/g,(w,P,_)=>{if(_.startsWith("http"))return w;const h=`http://localhost:3999/api/public/${r.userInfo.account}/${_}`;return`![${P}](${h})`})),ye.parse(l)},ee=()=>{E.go("/")};ue(()=>{r.isLoggedIn&&U.value==="guides"&&q()}),ce(U,t=>{t==="guides"&&r.isLoggedIn&&$.value.length===0&&q()});const se=async()=>{var t,e;try{await I.value.validate(),(await r.changePassword({currentPassword:p.value.currentPassword,newPassword:p.value.newPassword})).success&&(C.success({message:"密码更新成功",duration:1e3}),I.value.resetFields(),x.value=!1)}catch(l){console.error("密码修改失败:",l),C.error({message:((e=(t=l.response)==null?void 0:t.data)==null?void 0:e.message)||"密码修改失败",duration:1500})}},S=t=>new Date(t).toLocaleDateString(),te=()=>{E.go("/user-center/login")},ne=()=>{we.confirm("确定要注销当前登录吗？","警告",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(()=>{r.logout(),r.clearStoredPassword(),E.go("/user-center/login"),C.success({message:"已成功注销",duration:2e3})}).catch(()=>{})};return(t,e)=>{const l=m("el-button"),B=m("el-alert"),w=m("el-input"),P=m("el-icon"),_=m("el-empty"),D=m("el-tab-pane"),h=m("el-card"),oe=m("el-tabs"),j=m("el-form-item"),ae=m("el-form"),le=m("el-dialog");return i(),d("div",_e,[c(r).isLoggedIn?(i(),d(G,{key:1},[s("div",Pe,[s("div",be,[e[27]||(e[27]=s("div",{class:"profile-cover"},[s("div",{class:"cover-overlay"})],-1)),s("div",$e,[s("div",he,[s("img",{src:c(r).userInfo.avatar||"/images/default-avatar.jpg",alt:c(r).userInfo.nickname,class:"avatar"},null,8,Ce)]),s("div",xe,[s("div",Ie,[s("div",Ve,[b.nickname?(i(),N(w,{key:0,ref:"nicknameInput",modelValue:v.nickname,"onUpdate:modelValue":e[0]||(e[0]=n=>v.nickname=n),size:"small",style:{width:"300px"},onBlur:e[1]||(e[1]=n=>M("nickname"))},null,8,["modelValue"])):(i(),d("h1",{key:1,style:{flex:"1",cursor:"pointer",margin:"0","white-space":"nowrap"},onClick:e[2]||(e[2]=n=>L("nickname"))},u(c(r).userInfo.nickname||"未设置昵称"),1)),H(o(P,{class:"edit-icon",style:{"margin-left":"12px",color:"#666"},onClick:e[3]||(e[3]=n=>L("nickname"))},{default:a(()=>[o(c(J))]),_:1},512),[[A,!b.nickname]])])]),s("div",Te,[s("div",Ue,[b.signature?(i(),N(w,{key:0,ref:"signatureInput",modelValue:v.signature,"onUpdate:modelValue":e[4]||(e[4]=n=>v.signature=n),size:"small",style:{width:"300px"},onBlur:e[5]||(e[5]=n=>M("signature"))},null,8,["modelValue"])):(i(),d("p",{key:1,style:{flex:"1",cursor:"pointer",margin:"0","white-space":"nowrap"},onClick:e[6]||(e[6]=n=>L("signature"))},u(c(r).userInfo.signature||"这个人很懒，什么都没写~"),1)),H(o(P,{class:"edit-icon",style:{"margin-left":"12px",color:"#666"},onClick:e[7]||(e[7]=n=>L("signature"))},{default:a(()=>[o(c(J))]),_:1},512),[[A,!b.signature]])])])])])])]),s("div",Le,[o(oe,{modelValue:U.value,"onUpdate:modelValue":e[11]||(e[11]=n=>U.value=n),class:"profile-tabs"},{default:a(()=>[o(D,{label:"我的攻略",name:"guides"},{default:a(()=>[s("div",Be,[s("div",Fe,[s("div",Ee,[e[28]||(e[28]=s("h3",null,"我的攻略",-1)),s("span",ze,u($.value.length)+" 个攻略",1)]),s("div",Se,[(i(!0),d(G,null,Z($.value,(n,T)=>(i(),d("div",{key:n.createTime,class:pe(["guide-item",{active:V.value===T}]),onClick:us=>X(T)},[s("div",je,[s("h4",null,u(R(n)),1),s("span",Ge,u(n.totalDays)+"天",1)]),s("div",Me,[s("span",qe,[o(P,null,{default:a(()=>[o(c(ge))]),_:1}),y(" "+u(S(n.createTime)),1)])])],10,De))),128)),$.value.length===0?(i(),d("div",Re,[o(_,{description:"暂无攻略记录"},{default:a(()=>[o(l,{type:"primary",onClick:ee},{default:a(()=>e[29]||(e[29]=[y("去制作攻略")])),_:1})]),_:1})])):W("",!0)])]),s("div",Ne,[g.value?(i(),d("div",He,[s("div",Ae,[s("h2",null,u(R(g.value)),1),s("div",We,[s("span",null,"创建时间："+u(S(g.value.createTime)),1),s("span",null,"总天数："+u(g.value.totalDays)+"天",1)])]),s("div",Ze,[g.value.plans&&g.value.plans.length>0?(i(),d("div",Je,[(i(!0),d(G,null,Z(g.value.plans,(n,T)=>(i(),d("div",{key:T},[s("div",Ke,[s("div",{class:"answer-area",innerHTML:Y(n.content,g.value)},null,8,Oe)]),T<g.value.plans.length-1?(i(),d("hr",Qe)):W("",!0)]))),128))])):(i(),d("div",Xe,e[30]||(e[30]=[s("p",null,"该攻略暂无内容",-1)])))])])):(i(),d("div",Ye,[o(_,{description:"请选择一个攻略查看详情"})]))])])]),_:1}),o(D,{label:"账号安全",name:"security"},{default:a(()=>[s("div",es,[o(h,{class:"security-card"},{header:a(()=>[s("div",ss,[e[32]||(e[32]=s("span",null,"修改密码",-1)),o(l,{type:"primary",link:"",size:"small",onClick:e[8]||(e[8]=n=>x.value=!0)},{default:a(()=>e[31]||(e[31]=[y(" 修改 ")])),_:1})])]),default:a(()=>[e[33]||(e[33]=s("p",null,"定期修改密码可以保护账号安全",-1))]),_:1}),o(h,{class:"security-card"},{header:a(()=>[s("div",ts,[e[34]||(e[34]=s("span",null,"手机绑定",-1)),o(l,{type:"primary",link:"",size:"small",onClick:e[9]||(e[9]=n=>K.value=!0)},{default:a(()=>[y(u(c(r).userInfo.phone?"修改":"绑定"),1)]),_:1})])]),default:a(()=>[s("p",null,u(c(r).userInfo.phone||"未绑定手机号"),1)]),_:1}),o(h,{class:"security-card"},{header:a(()=>[s("div",ns,[e[35]||(e[35]=s("span",null,"邮箱绑定",-1)),o(l,{type:"primary",link:"",size:"small",onClick:e[10]||(e[10]=n=>O.value=!0),class:"action-btn"},{default:a(()=>[y(u(c(r).userInfo.email?"修改":"绑定"),1)]),_:1})])]),default:a(()=>[s("p",null,u(c(r).userInfo.email||"未绑定邮箱"),1)]),_:1}),o(h,{class:"security-card"},{header:a(()=>[s("div",os,[e[37]||(e[37]=s("span",null,"登录状态",-1)),o(l,{type:"danger",link:"",size:"small",class:"action-btn",onClick:ne},{default:a(()=>e[36]||(e[36]=[y(" 立即注销 ")])),_:1})])]),default:a(()=>[s("p",null,"当前登录账号："+u(c(r).userInfo.account),1)]),_:1})])]),_:1})]),_:1},8,["modelValue"])])],64)):(i(),d("div",ke,[o(B,{title:"请先登录账号",type:"warning",closable:!1,"show-icon":!1},{default:a(()=>[o(l,{type:"primary",onClick:te},{default:a(()=>e[26]||(e[26]=[y("去登录")])),_:1})]),_:1})])),o(le,{modelValue:x.value,"onUpdate:modelValue":e[25]||(e[25]=n=>x.value=n),title:"修改密码",width:"400px"},{footer:a(()=>[s("span",as,[o(l,{size:"small",onClick:e[24]||(e[24]=n=>x.value=!1)},{default:a(()=>e[38]||(e[38]=[y("取消")])),_:1}),o(l,{type:"primary",size:"small",onClick:se},{default:a(()=>e[39]||(e[39]=[y("确认修改")])),_:1})])]),default:a(()=>[o(ae,{ref_key:"passwordFormRef",ref:I,model:p.value,rules:Q,"label-width":"100px"},{default:a(()=>[o(j,{label:"当前密码",prop:"currentPassword"},{default:a(()=>[o(w,{modelValue:p.value.currentPassword,"onUpdate:modelValue":e[12]||(e[12]=n=>p.value.currentPassword=n),type:"password","show-password":"",onInput:e[13]||(e[13]=n=>z("currentPassword")),onCompositionstart:e[14]||(e[14]=n=>k.value=!0),onCompositionend:e[15]||(e[15]=n=>k.value=!1)},null,8,["modelValue"])]),_:1}),o(j,{label:"新密码",prop:"newPassword"},{default:a(()=>[o(w,{modelValue:p.value.newPassword,"onUpdate:modelValue":e[16]||(e[16]=n=>p.value.newPassword=n),type:"password","show-password":"",onInput:e[17]||(e[17]=n=>z("newPassword")),onCompositionstart:e[18]||(e[18]=n=>k.value=!0),onCompositionend:e[19]||(e[19]=n=>k.value=!1)},null,8,["modelValue"])]),_:1}),o(j,{label:"确认密码",prop:"confirmPassword"},{default:a(()=>[o(w,{modelValue:p.value.confirmPassword,"onUpdate:modelValue":e[20]||(e[20]=n=>p.value.confirmPassword=n),modelModifiers:{trim:!0},type:"password","show-password":"",onInput:e[21]||(e[21]=n=>z("confirmPassword")),onCompositionstart:e[22]||(e[22]=n=>k.value=!0),onCompositionend:e[23]||(e[23]=n=>k.value=!1)},null,8,["modelValue"])]),_:1})]),_:1},8,["model","rules"])]),_:1},8,["modelValue"])])}}},vs=re(ls,[["__scopeId","data-v-74b8612c"]]);export{vs as U};
