{"version": 3, "file": "Allow.js", "sourceRoot": "", "sources": ["../../../../src/decorator/common/Allow.ts"], "names": [], "mappings": ";;;AAEA,sEAAmE;AACnE,0EAAuE;AACvE,oEAAoE;AAEpE;;GAEG;AACH,SAAgB,KAAK,CAAC,iBAAqC;IACzD,OAAO,UAAU,MAAc,EAAE,YAAoB;QACnD,MAAM,IAAI,GAA2B;YACnC,IAAI,EAAE,iCAAe,CAAC,SAAS;YAC/B,MAAM,EAAE,MAAM,CAAC,WAAW;YAC1B,YAAY,EAAE,YAAY;YAC1B,iBAAiB,EAAE,iBAAiB;SACrC,CAAC;QACF,IAAA,oCAAkB,GAAE,CAAC,qBAAqB,CAAC,IAAI,uCAAkB,CAAC,IAAI,CAAC,CAAC,CAAC;IAC3E,CAAC,CAAC;AACJ,CAAC;AAVD,sBAUC", "sourcesContent": ["import { ValidationOptions } from '../ValidationOptions';\nimport { ValidationMetadataArgs } from '../../metadata/ValidationMetadataArgs';\nimport { ValidationTypes } from '../../validation/ValidationTypes';\nimport { ValidationMetadata } from '../../metadata/ValidationMetadata';\nimport { getMetadataStorage } from '../../metadata/MetadataStorage';\n\n/**\n * If object has both allowed and not allowed properties a validation error will be thrown.\n */\nexport function Allow(validationOptions?: ValidationOptions): PropertyDecorator {\n  return function (object: object, propertyName: string): void {\n    const args: ValidationMetadataArgs = {\n      type: ValidationTypes.WHITELIST,\n      target: object.constructor,\n      propertyName: propertyName,\n      validationOptions: validationOptions,\n    };\n    getMetadataStorage().addValidationMetadata(new ValidationMetadata(args));\n  };\n}\n"]}