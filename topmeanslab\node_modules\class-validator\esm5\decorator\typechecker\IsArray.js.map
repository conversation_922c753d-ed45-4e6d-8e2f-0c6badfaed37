{"version": 3, "file": "IsArray.js", "sourceRoot": "", "sources": ["../../../../src/decorator/typechecker/IsArray.ts"], "names": [], "mappings": "AACA,OAAO,EAAE,YAAY,EAAE,UAAU,EAAE,MAAM,sBAAsB,CAAC;AAEhE,MAAM,CAAC,IAAM,QAAQ,GAAG,SAAS,CAAC;AAElC;;GAEG;AACH,MAAM,UAAU,OAAO,CAAU,KAAc;IAC7C,OAAO,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;AAC9B,CAAC;AAED;;GAEG;AACH,MAAM,UAAU,OAAO,CAAC,iBAAqC;IAC3D,OAAO,UAAU,CACf;QACE,IAAI,EAAE,QAAQ;QACd,SAAS,EAAE;YACT,QAAQ,EAAE,UAAC,KAAK,EAAE,IAAI,IAAc,OAAA,OAAO,CAAC,KAAK,CAAC,EAAd,CAAc;YAClD,cAAc,EAAE,YAAY,CAAC,UAAA,UAAU,IAAI,OAAA,UAAU,GAAG,4BAA4B,EAAzC,CAAyC,EAAE,iBAAiB,CAAC;SACzG;KACF,EACD,iBAAiB,CAClB,CAAC;AACJ,CAAC", "sourcesContent": ["import { ValidationOptions } from '../ValidationOptions';\nimport { buildMessage, ValidateBy } from '../common/ValidateBy';\n\nexport const IS_ARRAY = 'isArray';\n\n/**\n * Checks if a given value is an array\n */\nexport function isArray<T = any>(value: unknown): value is Array<T> {\n  return Array.isArray(value);\n}\n\n/**\n * Checks if a given value is an array\n */\nexport function IsArray(validationOptions?: ValidationOptions): PropertyDecorator {\n  return ValidateBy(\n    {\n      name: IS_ARRAY,\n      validator: {\n        validate: (value, args): boolean => isArray(value),\n        defaultMessage: buildMessage(eachPrefix => eachPrefix + '$property must be an array', validationOptions),\n      },\n    },\n    validationOptions\n  );\n}\n"]}