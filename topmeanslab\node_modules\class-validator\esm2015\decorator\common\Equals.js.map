{"version": 3, "file": "Equals.js", "sourceRoot": "", "sources": ["../../../../src/decorator/common/Equals.ts"], "names": [], "mappings": "AACA,OAAO,EAAE,YAAY,EAAE,UAAU,EAAE,MAAM,sBAAsB,CAAC;AAEhE,MAAM,CAAC,MAAM,MAAM,GAAG,QAAQ,CAAC;AAE/B;;GAEG;AACH,MAAM,UAAU,MAAM,CAAC,KAAc,EAAE,UAAmB;IACxD,OAAO,KAAK,KAAK,UAAU,CAAC;AAC9B,CAAC;AAED;;GAEG;AACH,MAAM,UAAU,MAAM,CAAC,UAAe,EAAE,iBAAqC;IAC3E,OAAO,UAAU,CACf;QACE,IAAI,EAAE,MAAM;QACZ,WAAW,EAAE,CAAC,UAAU,CAAC;QACzB,SAAS,EAAE;YACT,QAAQ,EAAE,CAAC,KAAK,EAAE,IAAI,EAAW,EAAE,CAAC,MAAM,CAAC,KAAK,EAAE,IAAI,aAAJ,IAAI,uBAAJ,IAAI,CAAE,WAAW,CAAC,CAAC,CAAC,CAAC;YACvE,cAAc,EAAE,YAAY,CAC1B,UAAU,CAAC,EAAE,CAAC,UAAU,GAAG,yCAAyC,EACpE,iBAAiB,CAClB;SACF;KACF,EACD,iBAAiB,CAClB,CAAC;AACJ,CAAC", "sourcesContent": ["import { ValidationOptions } from '../ValidationOptions';\nimport { buildMessage, ValidateBy } from '../common/ValidateBy';\n\nexport const EQUALS = 'equals';\n\n/**\n * Checks if value matches (\"===\") the comparison.\n */\nexport function equals(value: unknown, comparison: unknown): boolean {\n  return value === comparison;\n}\n\n/**\n * Checks if value matches (\"===\") the comparison.\n */\nexport function Equals(comparison: any, validationOptions?: ValidationOptions): PropertyDecorator {\n  return ValidateBy(\n    {\n      name: EQUALS,\n      constraints: [comparison],\n      validator: {\n        validate: (value, args): boolean => equals(value, args?.constraints[0]),\n        defaultMessage: buildMessage(\n          eachPrefix => eachPrefix + '$property must be equal to $constraint1',\n          validationOptions\n        ),\n      },\n    },\n    validationOptions\n  );\n}\n"]}