{"name": "@fidm/x509", "description": "Pure JavaScript X509 certificate tools for Node.js", "authors": ["<PERSON> <<EMAIL>>"], "version": "1.2.1", "main": "build/index.js", "types": "build/index.d.ts", "license": "MIT", "repository": {"type": "git", "url": "**************:fidm/x509.git"}, "homepage": "https://github.com/fidm/x509", "engines": {"node": ">= 8"}, "keywords": ["x509", "certificate", "tls", "asn1", "pem", "pki", "ed25519", "JavaScript", "node"], "dependencies": {"@fidm/asn1": "^1.0.4", "tweetnacl": "^1.0.1"}, "devDependencies": {"@types/node": "^11.10.4", "istanbul": "^1.1.0-alpha.1", "tman": "^1.9.0", "ts-node": "^8.0.2", "tslint": "^5.13.1", "tslint-eslint-rules": "^5.4.0", "typedoc": "^0.14.2", "typescript": "^3.3.3333"}, "scripts": {"build": "rm -rf build && tsc", "docs": "rm -rf docs && typedoc --out docs && touch docs/.nojekyll", "test": "tman -r ts-node/register 'test/**/*.ts'", "test-cov": "istanbul cover _tman -- 'test/**/*.ts' -r ts-node/register", "lint": "tslint -p tsconfig.json -t stylish 'src/**/*.ts' 'test/**/*.ts'"}, "files": ["build", "LICENSE", "README.md", "CHANGELOG.md"]}