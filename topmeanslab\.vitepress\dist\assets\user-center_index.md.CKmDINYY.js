import{u as a,s as n}from"./chunks/theme.CmWpOUCL.js";import{U as i}from"./chunks/UserProfile.BuU6AGC-.js";import{L as c}from"./chunks/UserCenter.BiAzlCkY.js";import{c as l,o as e,j as t,b as r,k as m}from"./chunks/framework.B19ydMwb.js";import"./chunks/TopmeansMarkdownService.HmRBHz74.js";import"./chunks/Valicode.C71W2eNO.js";const v=JSON.parse('{"title":"用户中心","description":"","frontmatter":{"layout":"page","title":"用户中心"},"headers":[],"relativePath":"user-center/index.md","filePath":"user-center/index.md"}'),p={name:"user-center/index.md"},y=Object.assign(p,{setup(u){const s=a(),{isLoggedIn:o}=n(s);return(d,f)=>(e(),l("div",null,[t("template",null,[t("div",null,[m(o)?(e(),r(i,{key:1})):(e(),r(c,{key:0}))])])]))}});export{v as __pageData,y as default};
