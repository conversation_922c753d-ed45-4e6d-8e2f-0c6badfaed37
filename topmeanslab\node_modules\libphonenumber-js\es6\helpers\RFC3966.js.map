{"version": 3, "file": "RFC3966.js", "names": ["isViablePhoneNumber", "parseRFC3966", "text", "number", "ext", "replace", "split", "part", "name", "value", "result", "formatRFC3966", "Error"], "sources": ["../../source/helpers/RFC3966.js"], "sourcesContent": ["import isViablePhoneNumber from './isViablePhoneNumber.js'\r\n\r\n// https://www.ietf.org/rfc/rfc3966.txt\r\n\r\n/**\r\n * @param  {string} text - Phone URI (RFC 3966).\r\n * @return {object} `{ ?number, ?ext }`.\r\n */\r\nexport function parseRFC3966(text) {\r\n\tlet number\r\n\tlet ext\r\n\r\n\t// Replace \"tel:\" with \"tel=\" for parsing convenience.\r\n\ttext = text.replace(/^tel:/, 'tel=')\r\n\r\n\tfor (const part of text.split(';')) {\r\n\t\tconst [name, value] = part.split('=')\r\n\t\tswitch (name) {\r\n\t\t\tcase 'tel':\r\n\t\t\t\tnumber = value\r\n\t\t\t\tbreak\r\n\t\t\tcase 'ext':\r\n\t\t\t\text = value\r\n\t\t\t\tbreak\r\n\t\t\tcase 'phone-context':\r\n\t\t\t\t// Only \"country contexts\" are supported.\r\n\t\t\t\t// \"Domain contexts\" are ignored.\r\n\t\t\t\tif (value[0] === '+') {\r\n\t\t\t\t\tnumber = value + number\r\n\t\t\t\t}\r\n\t\t\t\tbreak\r\n\t\t}\r\n\t}\r\n\r\n\t// If the phone number is not viable, then abort.\r\n\tif (!isViablePhoneNumber(number)) {\r\n\t\treturn {}\r\n\t}\r\n\r\n\tconst result = { number }\r\n\tif (ext) {\r\n\t\tresult.ext = ext\r\n\t}\r\n\treturn result\r\n}\r\n\r\n/**\r\n * @param  {object} - `{ ?number, ?extension }`.\r\n * @return {string} Phone URI (RFC 3966).\r\n */\r\nexport function formatRFC3966({ number, ext }) {\r\n\tif (!number) {\r\n\t\treturn ''\r\n\t}\r\n\tif (number[0] !== '+') {\r\n\t\tthrow new Error(`\"formatRFC3966()\" expects \"number\" to be in E.164 format.`)\r\n\t}\r\n\treturn `tel:${number}${ext ? ';ext=' + ext : ''}`\r\n}"], "mappings": ";;;;;;;;;;;;;;AAAA,OAAOA,mBAAP,MAAgC,0BAAhC,C,CAEA;;AAEA;AACA;AACA;AACA;;AACA,OAAO,SAASC,YAAT,CAAsBC,IAAtB,EAA4B;EAClC,IAAIC,MAAJ;EACA,IAAIC,GAAJ,CAFkC,CAIlC;;EACAF,IAAI,GAAGA,IAAI,CAACG,OAAL,CAAa,OAAb,EAAsB,MAAtB,CAAP;;EAEA,qDAAmBH,IAAI,CAACI,KAAL,CAAW,GAAX,CAAnB,wCAAoC;IAAA,IAAzBC,IAAyB;;IACnC,kBAAsBA,IAAI,CAACD,KAAL,CAAW,GAAX,CAAtB;IAAA;IAAA,IAAOE,IAAP;IAAA,IAAaC,KAAb;;IACA,QAAQD,IAAR;MACC,KAAK,KAAL;QACCL,MAAM,GAAGM,KAAT;QACA;;MACD,KAAK,KAAL;QACCL,GAAG,GAAGK,KAAN;QACA;;MACD,KAAK,eAAL;QACC;QACA;QACA,IAAIA,KAAK,CAAC,CAAD,CAAL,KAAa,GAAjB,EAAsB;UACrBN,MAAM,GAAGM,KAAK,GAAGN,MAAjB;QACA;;QACD;IAbF;EAeA,CAxBiC,CA0BlC;;;EACA,IAAI,CAACH,mBAAmB,CAACG,MAAD,CAAxB,EAAkC;IACjC,OAAO,EAAP;EACA;;EAED,IAAMO,MAAM,GAAG;IAAEP,MAAM,EAANA;EAAF,CAAf;;EACA,IAAIC,GAAJ,EAAS;IACRM,MAAM,CAACN,GAAP,GAAaA,GAAb;EACA;;EACD,OAAOM,MAAP;AACA;AAED;AACA;AACA;AACA;;AACA,OAAO,SAASC,aAAT,OAAwC;EAAA,IAAfR,MAAe,QAAfA,MAAe;EAAA,IAAPC,GAAO,QAAPA,GAAO;;EAC9C,IAAI,CAACD,MAAL,EAAa;IACZ,OAAO,EAAP;EACA;;EACD,IAAIA,MAAM,CAAC,CAAD,CAAN,KAAc,GAAlB,EAAuB;IACtB,MAAM,IAAIS,KAAJ,iEAAN;EACA;;EACD,qBAAcT,MAAd,SAAuBC,GAAG,GAAG,UAAUA,GAAb,GAAmB,EAA7C;AACA"}