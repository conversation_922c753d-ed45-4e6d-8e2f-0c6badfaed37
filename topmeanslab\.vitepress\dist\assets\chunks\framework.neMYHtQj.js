/**
* @vue/shared v3.5.13
* (c) 2018-present <PERSON><PERSON> (<PERSON>) You and Vue contributors
* @license MIT
**//*! #__NO_SIDE_EFFECTS__ */function Js(e){const t=Object.create(null);for(const n of e.split(","))t[n]=1;return n=>n in t}const te={},Lt=[],De=()=>{},vl=()=>!1,un=e=>e.charCodeAt(0)===111&&e.charCodeAt(1)===110&&(e.charCodeAt(2)>122||e.charCodeAt(2)<97),zs=e=>e.startsWith("onUpdate:"),he=Object.assign,Qs=(e,t)=>{const n=e.indexOf(t);n>-1&&e.splice(n,1)},bl=Object.prototype.hasOwnProperty,Q=(e,t)=>bl.call(e,t),B=Array.isArra<PERSON>,Nt=e=>dn(e)==="[object Map]",kt=e=>dn(e)==="[object Set]",Tr=e=>dn(e)==="[object Date]",q=e=>typeof e=="function",oe=e=>typeof e=="string",je=e=>typeof e=="symbol",ee=e=>e!==null&&typeof e=="object",Si=e=>(ee(e)||q(e))&&q(e.then)&&q(e.catch),xi=Object.prototype.toString,dn=e=>xi.call(e),_l=e=>dn(e).slice(8,-1),Ti=e=>dn(e)==="[object Object]",Zs=e=>oe(e)&&e!=="NaN"&&e[0]!=="-"&&""+parseInt(e,10)===e,Ft=Js(",key,ref,ref_for,ref_key,onVnodeBeforeMount,onVnodeMounted,onVnodeBeforeUpdate,onVnodeUpdated,onVnodeBeforeUnmount,onVnodeUnmounted"),Xn=e=>{const t=Object.create(null);return n=>t[n]||(t[n]=e(n))},wl=/-(\w)/g,Fe=Xn(e=>e.replace(wl,(t,n)=>n?n.toUpperCase():"")),Sl=/\B([A-Z])/g,at=Xn(e=>e.replace(Sl,"-$1").toLowerCase()),Yn=Xn(e=>e.charAt(0).toUpperCase()+e.slice(1)),Pn=Xn(e=>e?`on${Yn(e)}`:""),ot=(e,t)=>!Object.is(e,t),In=(e,...t)=>{for(let n=0;n<e.length;n++)e[n](...t)},Ei=(e,t,n,s=!1)=>{Object.defineProperty(e,t,{configurable:!0,enumerable:!1,writable:s,value:n})},$n=e=>{const t=parseFloat(e);return isNaN(t)?e:t},xl=e=>{const t=oe(e)?Number(e):NaN;return isNaN(t)?e:t};let Er;const Jn=()=>Er||(Er=typeof globalThis<"u"?globalThis:typeof self<"u"?self:typeof window<"u"?window:typeof global<"u"?global:{});function zn(e){if(B(e)){const t={};for(let n=0;n<e.length;n++){const s=e[n],r=oe(s)?Al(s):zn(s);if(r)for(const i in r)t[i]=r[i]}return t}else if(oe(e)||ee(e))return e}const Tl=/;(?![^(]*\))/g,El=/:([^]+)/,Cl=/\/\*[^]*?\*\//g;function Al(e){const t={};return e.replace(Cl,"").split(Tl).forEach(n=>{if(n){const s=n.split(El);s.length>1&&(t[s[0].trim()]=s[1].trim())}}),t}function Qn(e){let t="";if(oe(e))t=e;else if(B(e))for(let n=0;n<e.length;n++){const s=Qn(e[n]);s&&(t+=s+" ")}else if(ee(e))for(const n in e)e[n]&&(t+=n+" ");return t.trim()}function Zf(e){if(!e)return null;let{class:t,style:n}=e;return t&&!oe(t)&&(e.class=Qn(t)),n&&(e.style=zn(n)),e}const Rl="itemscope,allowfullscreen,formnovalidate,ismap,nomodule,novalidate,readonly",Ml=Js(Rl);function Ci(e){return!!e||e===""}function Ol(e,t){if(e.length!==t.length)return!1;let n=!0;for(let s=0;n&&s<e.length;s++)n=Tt(e[s],t[s]);return n}function Tt(e,t){if(e===t)return!0;let n=Tr(e),s=Tr(t);if(n||s)return n&&s?e.getTime()===t.getTime():!1;if(n=je(e),s=je(t),n||s)return e===t;if(n=B(e),s=B(t),n||s)return n&&s?Ol(e,t):!1;if(n=ee(e),s=ee(t),n||s){if(!n||!s)return!1;const r=Object.keys(e).length,i=Object.keys(t).length;if(r!==i)return!1;for(const o in e){const l=e.hasOwnProperty(o),c=t.hasOwnProperty(o);if(l&&!c||!l&&c||!Tt(e[o],t[o]))return!1}}return String(e)===String(t)}function er(e,t){return e.findIndex(n=>Tt(n,t))}const Ai=e=>!!(e&&e.__v_isRef===!0),Pl=e=>oe(e)?e:e==null?"":B(e)||ee(e)&&(e.toString===xi||!q(e.toString))?Ai(e)?Pl(e.value):JSON.stringify(e,Ri,2):String(e),Ri=(e,t)=>Ai(t)?Ri(e,t.value):Nt(t)?{[`Map(${t.size})`]:[...t.entries()].reduce((n,[s,r],i)=>(n[us(s,i)+" =>"]=r,n),{})}:kt(t)?{[`Set(${t.size})`]:[...t.values()].map(n=>us(n))}:je(t)?us(t):ee(t)&&!B(t)&&!Ti(t)?String(t):t,us=(e,t="")=>{var n;return je(e)?`Symbol(${(n=e.description)!=null?n:t})`:e};/**
* @vue/reactivity v3.5.13
* (c) 2018-present Yuxi (Evan) You and Vue contributors
* @license MIT
**/let Se;class Mi{constructor(t=!1){this.detached=t,this._active=!0,this.effects=[],this.cleanups=[],this._isPaused=!1,this.parent=Se,!t&&Se&&(this.index=(Se.scopes||(Se.scopes=[])).push(this)-1)}get active(){return this._active}pause(){if(this._active){this._isPaused=!0;let t,n;if(this.scopes)for(t=0,n=this.scopes.length;t<n;t++)this.scopes[t].pause();for(t=0,n=this.effects.length;t<n;t++)this.effects[t].pause()}}resume(){if(this._active&&this._isPaused){this._isPaused=!1;let t,n;if(this.scopes)for(t=0,n=this.scopes.length;t<n;t++)this.scopes[t].resume();for(t=0,n=this.effects.length;t<n;t++)this.effects[t].resume()}}run(t){if(this._active){const n=Se;try{return Se=this,t()}finally{Se=n}}}on(){Se=this}off(){Se=this.parent}stop(t){if(this._active){this._active=!1;let n,s;for(n=0,s=this.effects.length;n<s;n++)this.effects[n].stop();for(this.effects.length=0,n=0,s=this.cleanups.length;n<s;n++)this.cleanups[n]();if(this.cleanups.length=0,this.scopes){for(n=0,s=this.scopes.length;n<s;n++)this.scopes[n].stop(!0);this.scopes.length=0}if(!this.detached&&this.parent&&!t){const r=this.parent.scopes.pop();r&&r!==this&&(this.parent.scopes[this.index]=r,r.index=this.index)}this.parent=void 0}}}function eu(e){return new Mi(e)}function Oi(){return Se}function Il(e,t=!1){Se&&Se.cleanups.push(e)}let se;const ds=new WeakSet;class Pi{constructor(t){this.fn=t,this.deps=void 0,this.depsTail=void 0,this.flags=5,this.next=void 0,this.cleanup=void 0,this.scheduler=void 0,Se&&Se.active&&Se.effects.push(this)}pause(){this.flags|=64}resume(){this.flags&64&&(this.flags&=-65,ds.has(this)&&(ds.delete(this),this.trigger()))}notify(){this.flags&2&&!(this.flags&32)||this.flags&8||Li(this)}run(){if(!(this.flags&1))return this.fn();this.flags|=2,Cr(this),Ni(this);const t=se,n=$e;se=this,$e=!0;try{return this.fn()}finally{Fi(this),se=t,$e=n,this.flags&=-3}}stop(){if(this.flags&1){for(let t=this.deps;t;t=t.nextDep)sr(t);this.deps=this.depsTail=void 0,Cr(this),this.onStop&&this.onStop(),this.flags&=-2}}trigger(){this.flags&64?ds.add(this):this.scheduler?this.scheduler():this.runIfDirty()}runIfDirty(){Ls(this)&&this.run()}get dirty(){return Ls(this)}}let Ii=0,Jt,zt;function Li(e,t=!1){if(e.flags|=8,t){e.next=zt,zt=e;return}e.next=Jt,Jt=e}function tr(){Ii++}function nr(){if(--Ii>0)return;if(zt){let t=zt;for(zt=void 0;t;){const n=t.next;t.next=void 0,t.flags&=-9,t=n}}let e;for(;Jt;){let t=Jt;for(Jt=void 0;t;){const n=t.next;if(t.next=void 0,t.flags&=-9,t.flags&1)try{t.trigger()}catch(s){e||(e=s)}t=n}}if(e)throw e}function Ni(e){for(let t=e.deps;t;t=t.nextDep)t.version=-1,t.prevActiveLink=t.dep.activeLink,t.dep.activeLink=t}function Fi(e){let t,n=e.depsTail,s=n;for(;s;){const r=s.prevDep;s.version===-1?(s===n&&(n=r),sr(s),Ll(s)):t=s,s.dep.activeLink=s.prevActiveLink,s.prevActiveLink=void 0,s=r}e.deps=t,e.depsTail=n}function Ls(e){for(let t=e.deps;t;t=t.nextDep)if(t.dep.version!==t.version||t.dep.computed&&(Hi(t.dep.computed)||t.dep.version!==t.version))return!0;return!!e._dirty}function Hi(e){if(e.flags&4&&!(e.flags&16)||(e.flags&=-17,e.globalVersion===sn))return;e.globalVersion=sn;const t=e.dep;if(e.flags|=2,t.version>0&&!e.isSSR&&e.deps&&!Ls(e)){e.flags&=-3;return}const n=se,s=$e;se=e,$e=!0;try{Ni(e);const r=e.fn(e._value);(t.version===0||ot(r,e._value))&&(e._value=r,t.version++)}catch(r){throw t.version++,r}finally{se=n,$e=s,Fi(e),e.flags&=-3}}function sr(e,t=!1){const{dep:n,prevSub:s,nextSub:r}=e;if(s&&(s.nextSub=r,e.prevSub=void 0),r&&(r.prevSub=s,e.nextSub=void 0),n.subs===e&&(n.subs=s,!s&&n.computed)){n.computed.flags&=-5;for(let i=n.computed.deps;i;i=i.nextDep)sr(i,!0)}!t&&!--n.sc&&n.map&&n.map.delete(n.key)}function Ll(e){const{prevDep:t,nextDep:n}=e;t&&(t.nextDep=n,e.prevDep=void 0),n&&(n.prevDep=t,e.nextDep=void 0)}let $e=!0;const Di=[];function ft(){Di.push($e),$e=!1}function ut(){const e=Di.pop();$e=e===void 0?!0:e}function Cr(e){const{cleanup:t}=e;if(e.cleanup=void 0,t){const n=se;se=void 0;try{t()}finally{se=n}}}let sn=0;class Nl{constructor(t,n){this.sub=t,this.dep=n,this.version=n.version,this.nextDep=this.prevDep=this.nextSub=this.prevSub=this.prevActiveLink=void 0}}class Zn{constructor(t){this.computed=t,this.version=0,this.activeLink=void 0,this.subs=void 0,this.map=void 0,this.key=void 0,this.sc=0}track(t){if(!se||!$e||se===this.computed)return;let n=this.activeLink;if(n===void 0||n.sub!==se)n=this.activeLink=new Nl(se,this),se.deps?(n.prevDep=se.depsTail,se.depsTail.nextDep=n,se.depsTail=n):se.deps=se.depsTail=n,$i(n);else if(n.version===-1&&(n.version=this.version,n.nextDep)){const s=n.nextDep;s.prevDep=n.prevDep,n.prevDep&&(n.prevDep.nextDep=s),n.prevDep=se.depsTail,n.nextDep=void 0,se.depsTail.nextDep=n,se.depsTail=n,se.deps===n&&(se.deps=s)}return n}trigger(t){this.version++,sn++,this.notify(t)}notify(t){tr();try{for(let n=this.subs;n;n=n.prevSub)n.sub.notify()&&n.sub.dep.notify()}finally{nr()}}}function $i(e){if(e.dep.sc++,e.sub.flags&4){const t=e.dep.computed;if(t&&!e.dep.subs){t.flags|=20;for(let s=t.deps;s;s=s.nextDep)$i(s)}const n=e.dep.subs;n!==e&&(e.prevSub=n,n&&(n.nextSub=e)),e.dep.subs=e}}const jn=new WeakMap,yt=Symbol(""),Ns=Symbol(""),rn=Symbol("");function me(e,t,n){if($e&&se){let s=jn.get(e);s||jn.set(e,s=new Map);let r=s.get(n);r||(s.set(n,r=new Zn),r.map=s,r.key=n),r.track()}}function Ye(e,t,n,s,r,i){const o=jn.get(e);if(!o){sn++;return}const l=c=>{c&&c.trigger()};if(tr(),t==="clear")o.forEach(l);else{const c=B(e),f=c&&Zs(n);if(c&&n==="length"){const a=Number(s);o.forEach((d,g)=>{(g==="length"||g===rn||!je(g)&&g>=a)&&l(d)})}else switch((n!==void 0||o.has(void 0))&&l(o.get(n)),f&&l(o.get(rn)),t){case"add":c?f&&l(o.get("length")):(l(o.get(yt)),Nt(e)&&l(o.get(Ns)));break;case"delete":c||(l(o.get(yt)),Nt(e)&&l(o.get(Ns)));break;case"set":Nt(e)&&l(o.get(yt));break}}nr()}function Fl(e,t){const n=jn.get(e);return n&&n.get(t)}function Mt(e){const t=z(e);return t===e?t:(me(t,"iterate",rn),Pe(e)?t:t.map(ye))}function es(e){return me(e=z(e),"iterate",rn),e}const Hl={__proto__:null,[Symbol.iterator](){return hs(this,Symbol.iterator,ye)},concat(...e){return Mt(this).concat(...e.map(t=>B(t)?Mt(t):t))},entries(){return hs(this,"entries",e=>(e[1]=ye(e[1]),e))},every(e,t){return Ge(this,"every",e,t,void 0,arguments)},filter(e,t){return Ge(this,"filter",e,t,n=>n.map(ye),arguments)},find(e,t){return Ge(this,"find",e,t,ye,arguments)},findIndex(e,t){return Ge(this,"findIndex",e,t,void 0,arguments)},findLast(e,t){return Ge(this,"findLast",e,t,ye,arguments)},findLastIndex(e,t){return Ge(this,"findLastIndex",e,t,void 0,arguments)},forEach(e,t){return Ge(this,"forEach",e,t,void 0,arguments)},includes(...e){return ps(this,"includes",e)},indexOf(...e){return ps(this,"indexOf",e)},join(e){return Mt(this).join(e)},lastIndexOf(...e){return ps(this,"lastIndexOf",e)},map(e,t){return Ge(this,"map",e,t,void 0,arguments)},pop(){return qt(this,"pop")},push(...e){return qt(this,"push",e)},reduce(e,...t){return Ar(this,"reduce",e,t)},reduceRight(e,...t){return Ar(this,"reduceRight",e,t)},shift(){return qt(this,"shift")},some(e,t){return Ge(this,"some",e,t,void 0,arguments)},splice(...e){return qt(this,"splice",e)},toReversed(){return Mt(this).toReversed()},toSorted(e){return Mt(this).toSorted(e)},toSpliced(...e){return Mt(this).toSpliced(...e)},unshift(...e){return qt(this,"unshift",e)},values(){return hs(this,"values",ye)}};function hs(e,t,n){const s=es(e),r=s[t]();return s!==e&&!Pe(e)&&(r._next=r.next,r.next=()=>{const i=r._next();return i.value&&(i.value=n(i.value)),i}),r}const Dl=Array.prototype;function Ge(e,t,n,s,r,i){const o=es(e),l=o!==e&&!Pe(e),c=o[t];if(c!==Dl[t]){const d=c.apply(e,i);return l?ye(d):d}let f=n;o!==e&&(l?f=function(d,g){return n.call(this,ye(d),g,e)}:n.length>2&&(f=function(d,g){return n.call(this,d,g,e)}));const a=c.call(o,f,s);return l&&r?r(a):a}function Ar(e,t,n,s){const r=es(e);let i=n;return r!==e&&(Pe(e)?n.length>3&&(i=function(o,l,c){return n.call(this,o,l,c,e)}):i=function(o,l,c){return n.call(this,o,ye(l),c,e)}),r[t](i,...s)}function ps(e,t,n){const s=z(e);me(s,"iterate",rn);const r=s[t](...n);return(r===-1||r===!1)&&or(n[0])?(n[0]=z(n[0]),s[t](...n)):r}function qt(e,t,n=[]){ft(),tr();const s=z(e)[t].apply(e,n);return nr(),ut(),s}const $l=Js("__proto__,__v_isRef,__isVue"),ji=new Set(Object.getOwnPropertyNames(Symbol).filter(e=>e!=="arguments"&&e!=="caller").map(e=>Symbol[e]).filter(je));function jl(e){je(e)||(e=String(e));const t=z(this);return me(t,"has",e),t.hasOwnProperty(e)}class Vi{constructor(t=!1,n=!1){this._isReadonly=t,this._isShallow=n}get(t,n,s){if(n==="__v_skip")return t.__v_skip;const r=this._isReadonly,i=this._isShallow;if(n==="__v_isReactive")return!r;if(n==="__v_isReadonly")return r;if(n==="__v_isShallow")return i;if(n==="__v_raw")return s===(r?i?Yl:Bi:i?Wi:Ui).get(t)||Object.getPrototypeOf(t)===Object.getPrototypeOf(s)?t:void 0;const o=B(t);if(!r){let c;if(o&&(c=Hl[n]))return c;if(n==="hasOwnProperty")return jl}const l=Reflect.get(t,n,de(t)?t:s);return(je(n)?ji.has(n):$l(n))||(r||me(t,"get",n),i)?l:de(l)?o&&Zs(n)?l:l.value:ee(l)?r?ts(l):Dt(l):l}}class ki extends Vi{constructor(t=!1){super(!1,t)}set(t,n,s,r){let i=t[n];if(!this._isShallow){const c=Et(i);if(!Pe(s)&&!Et(s)&&(i=z(i),s=z(s)),!B(t)&&de(i)&&!de(s))return c?!1:(i.value=s,!0)}const o=B(t)&&Zs(n)?Number(n)<t.length:Q(t,n),l=Reflect.set(t,n,s,de(t)?t:r);return t===z(r)&&(o?ot(s,i)&&Ye(t,"set",n,s):Ye(t,"add",n,s)),l}deleteProperty(t,n){const s=Q(t,n);t[n];const r=Reflect.deleteProperty(t,n);return r&&s&&Ye(t,"delete",n,void 0),r}has(t,n){const s=Reflect.has(t,n);return(!je(n)||!ji.has(n))&&me(t,"has",n),s}ownKeys(t){return me(t,"iterate",B(t)?"length":yt),Reflect.ownKeys(t)}}class Vl extends Vi{constructor(t=!1){super(!0,t)}set(t,n){return!0}deleteProperty(t,n){return!0}}const kl=new ki,Ul=new Vl,Wl=new ki(!0);const Fs=e=>e,_n=e=>Reflect.getPrototypeOf(e);function Bl(e,t,n){return function(...s){const r=this.__v_raw,i=z(r),o=Nt(i),l=e==="entries"||e===Symbol.iterator&&o,c=e==="keys"&&o,f=r[e](...s),a=n?Fs:t?Hs:ye;return!t&&me(i,"iterate",c?Ns:yt),{next(){const{value:d,done:g}=f.next();return g?{value:d,done:g}:{value:l?[a(d[0]),a(d[1])]:a(d),done:g}},[Symbol.iterator](){return this}}}}function wn(e){return function(...t){return e==="delete"?!1:e==="clear"?void 0:this}}function Kl(e,t){const n={get(r){const i=this.__v_raw,o=z(i),l=z(r);e||(ot(r,l)&&me(o,"get",r),me(o,"get",l));const{has:c}=_n(o),f=t?Fs:e?Hs:ye;if(c.call(o,r))return f(i.get(r));if(c.call(o,l))return f(i.get(l));i!==o&&i.get(r)},get size(){const r=this.__v_raw;return!e&&me(z(r),"iterate",yt),Reflect.get(r,"size",r)},has(r){const i=this.__v_raw,o=z(i),l=z(r);return e||(ot(r,l)&&me(o,"has",r),me(o,"has",l)),r===l?i.has(r):i.has(r)||i.has(l)},forEach(r,i){const o=this,l=o.__v_raw,c=z(l),f=t?Fs:e?Hs:ye;return!e&&me(c,"iterate",yt),l.forEach((a,d)=>r.call(i,f(a),f(d),o))}};return he(n,e?{add:wn("add"),set:wn("set"),delete:wn("delete"),clear:wn("clear")}:{add(r){!t&&!Pe(r)&&!Et(r)&&(r=z(r));const i=z(this);return _n(i).has.call(i,r)||(i.add(r),Ye(i,"add",r,r)),this},set(r,i){!t&&!Pe(i)&&!Et(i)&&(i=z(i));const o=z(this),{has:l,get:c}=_n(o);let f=l.call(o,r);f||(r=z(r),f=l.call(o,r));const a=c.call(o,r);return o.set(r,i),f?ot(i,a)&&Ye(o,"set",r,i):Ye(o,"add",r,i),this},delete(r){const i=z(this),{has:o,get:l}=_n(i);let c=o.call(i,r);c||(r=z(r),c=o.call(i,r)),l&&l.call(i,r);const f=i.delete(r);return c&&Ye(i,"delete",r,void 0),f},clear(){const r=z(this),i=r.size!==0,o=r.clear();return i&&Ye(r,"clear",void 0,void 0),o}}),["keys","values","entries",Symbol.iterator].forEach(r=>{n[r]=Bl(r,e,t)}),n}function rr(e,t){const n=Kl(e,t);return(s,r,i)=>r==="__v_isReactive"?!e:r==="__v_isReadonly"?e:r==="__v_raw"?s:Reflect.get(Q(n,r)&&r in s?n:s,r,i)}const ql={get:rr(!1,!1)},Gl={get:rr(!1,!0)},Xl={get:rr(!0,!1)};const Ui=new WeakMap,Wi=new WeakMap,Bi=new WeakMap,Yl=new WeakMap;function Jl(e){switch(e){case"Object":case"Array":return 1;case"Map":case"Set":case"WeakMap":case"WeakSet":return 2;default:return 0}}function zl(e){return e.__v_skip||!Object.isExtensible(e)?0:Jl(_l(e))}function Dt(e){return Et(e)?e:ir(e,!1,kl,ql,Ui)}function Ql(e){return ir(e,!1,Wl,Gl,Wi)}function ts(e){return ir(e,!0,Ul,Xl,Bi)}function ir(e,t,n,s,r){if(!ee(e)||e.__v_raw&&!(t&&e.__v_isReactive))return e;const i=r.get(e);if(i)return i;const o=zl(e);if(o===0)return e;const l=new Proxy(e,o===2?s:n);return r.set(e,l),l}function vt(e){return Et(e)?vt(e.__v_raw):!!(e&&e.__v_isReactive)}function Et(e){return!!(e&&e.__v_isReadonly)}function Pe(e){return!!(e&&e.__v_isShallow)}function or(e){return e?!!e.__v_raw:!1}function z(e){const t=e&&e.__v_raw;return t?z(t):e}function Ln(e){return!Q(e,"__v_skip")&&Object.isExtensible(e)&&Ei(e,"__v_skip",!0),e}const ye=e=>ee(e)?Dt(e):e,Hs=e=>ee(e)?ts(e):e;function de(e){return e?e.__v_isRef===!0:!1}function qe(e){return Ki(e,!1)}function Ie(e){return Ki(e,!0)}function Ki(e,t){return de(e)?e:new Zl(e,t)}class Zl{constructor(t,n){this.dep=new Zn,this.__v_isRef=!0,this.__v_isShallow=!1,this._rawValue=n?t:z(t),this._value=n?t:ye(t),this.__v_isShallow=n}get value(){return this.dep.track(),this._value}set value(t){const n=this._rawValue,s=this.__v_isShallow||Pe(t)||Et(t);t=s?t:z(t),ot(t,n)&&(this._rawValue=t,this._value=s?t:ye(t),this.dep.trigger())}}function lr(e){return de(e)?e.value:e}function ce(e){return q(e)?e():lr(e)}const ec={get:(e,t,n)=>t==="__v_raw"?e:lr(Reflect.get(e,t,n)),set:(e,t,n,s)=>{const r=e[t];return de(r)&&!de(n)?(r.value=n,!0):Reflect.set(e,t,n,s)}};function qi(e){return vt(e)?e:new Proxy(e,ec)}class tc{constructor(t){this.__v_isRef=!0,this._value=void 0;const n=this.dep=new Zn,{get:s,set:r}=t(n.track.bind(n),n.trigger.bind(n));this._get=s,this._set=r}get value(){return this._value=this._get()}set value(t){this._set(t)}}function nc(e){return new tc(e)}function tu(e){const t=B(e)?new Array(e.length):{};for(const n in e)t[n]=Gi(e,n);return t}class sc{constructor(t,n,s){this._object=t,this._key=n,this._defaultValue=s,this.__v_isRef=!0,this._value=void 0}get value(){const t=this._object[this._key];return this._value=t===void 0?this._defaultValue:t}set value(t){this._object[this._key]=t}get dep(){return Fl(z(this._object),this._key)}}class rc{constructor(t){this._getter=t,this.__v_isRef=!0,this.__v_isReadonly=!0,this._value=void 0}get value(){return this._value=this._getter()}}function ic(e,t,n){return de(e)?e:q(e)?new rc(e):ee(e)&&arguments.length>1?Gi(e,t,n):qe(e)}function Gi(e,t,n){const s=e[t];return de(s)?s:new sc(e,t,n)}class oc{constructor(t,n,s){this.fn=t,this.setter=n,this._value=void 0,this.dep=new Zn(this),this.__v_isRef=!0,this.deps=void 0,this.depsTail=void 0,this.flags=16,this.globalVersion=sn-1,this.next=void 0,this.effect=this,this.__v_isReadonly=!n,this.isSSR=s}notify(){if(this.flags|=16,!(this.flags&8)&&se!==this)return Li(this,!0),!0}get value(){const t=this.dep.track();return Hi(this),t&&(t.version=this.dep.version),this._value}set value(t){this.setter&&this.setter(t)}}function lc(e,t,n=!1){let s,r;return q(e)?s=e:(s=e.get,r=e.set),new oc(s,r,n)}const Sn={},Vn=new WeakMap;let gt;function cc(e,t=!1,n=gt){if(n){let s=Vn.get(n);s||Vn.set(n,s=[]),s.push(e)}}function ac(e,t,n=te){const{immediate:s,deep:r,once:i,scheduler:o,augmentJob:l,call:c}=n,f=m=>r?m:Pe(m)||r===!1||r===0?Je(m,1):Je(m);let a,d,g,v,_=!1,b=!1;if(de(e)?(d=()=>e.value,_=Pe(e)):vt(e)?(d=()=>f(e),_=!0):B(e)?(b=!0,_=e.some(m=>vt(m)||Pe(m)),d=()=>e.map(m=>{if(de(m))return m.value;if(vt(m))return f(m);if(q(m))return c?c(m,2):m()})):q(e)?t?d=c?()=>c(e,2):e:d=()=>{if(g){ft();try{g()}finally{ut()}}const m=gt;gt=a;try{return c?c(e,3,[v]):e(v)}finally{gt=m}}:d=De,t&&r){const m=d,O=r===!0?1/0:r;d=()=>Je(m(),O)}const k=Oi(),P=()=>{a.stop(),k&&k.active&&Qs(k.effects,a)};if(i&&t){const m=t;t=(...O)=>{m(...O),P()}}let D=b?new Array(e.length).fill(Sn):Sn;const p=m=>{if(!(!(a.flags&1)||!a.dirty&&!m))if(t){const O=a.run();if(r||_||(b?O.some(($,R)=>ot($,D[R])):ot(O,D))){g&&g();const $=gt;gt=a;try{const R=[O,D===Sn?void 0:b&&D[0]===Sn?[]:D,v];c?c(t,3,R):t(...R),D=O}finally{gt=$}}}else a.run()};return l&&l(p),a=new Pi(d),a.scheduler=o?()=>o(p,!1):p,v=m=>cc(m,!1,a),g=a.onStop=()=>{const m=Vn.get(a);if(m){if(c)c(m,4);else for(const O of m)O();Vn.delete(a)}},t?s?p(!0):D=a.run():o?o(p.bind(null,!0),!0):a.run(),P.pause=a.pause.bind(a),P.resume=a.resume.bind(a),P.stop=P,P}function Je(e,t=1/0,n){if(t<=0||!ee(e)||e.__v_skip||(n=n||new Set,n.has(e)))return e;if(n.add(e),t--,de(e))Je(e.value,t,n);else if(B(e))for(let s=0;s<e.length;s++)Je(e[s],t,n);else if(kt(e)||Nt(e))e.forEach(s=>{Je(s,t,n)});else if(Ti(e)){for(const s in e)Je(e[s],t,n);for(const s of Object.getOwnPropertySymbols(e))Object.prototype.propertyIsEnumerable.call(e,s)&&Je(e[s],t,n)}return e}/**
* @vue/runtime-core v3.5.13
* (c) 2018-present Yuxi (Evan) You and Vue contributors
* @license MIT
**/function hn(e,t,n,s){try{return s?e(...s):e()}catch(r){pn(r,t,n)}}function Ve(e,t,n,s){if(q(e)){const r=hn(e,t,n,s);return r&&Si(r)&&r.catch(i=>{pn(i,t,n)}),r}if(B(e)){const r=[];for(let i=0;i<e.length;i++)r.push(Ve(e[i],t,n,s));return r}}function pn(e,t,n,s=!0){const r=t?t.vnode:null,{errorHandler:i,throwUnhandledErrorInProduction:o}=t&&t.appContext.config||te;if(t){let l=t.parent;const c=t.proxy,f=`https://vuejs.org/error-reference/#runtime-${n}`;for(;l;){const a=l.ec;if(a){for(let d=0;d<a.length;d++)if(a[d](e,c,f)===!1)return}l=l.parent}if(i){ft(),hn(i,null,10,[e,c,f]),ut();return}}fc(e,n,r,s,o)}function fc(e,t,n,s=!0,r=!1){if(r)throw e;console.error(e)}const xe=[];let Be=-1;const Ht=[];let st=null,Pt=0;const Xi=Promise.resolve();let kn=null;function gn(e){const t=kn||Xi;return e?t.then(this?e.bind(this):e):t}function uc(e){let t=Be+1,n=xe.length;for(;t<n;){const s=t+n>>>1,r=xe[s],i=on(r);i<e||i===e&&r.flags&2?t=s+1:n=s}return t}function cr(e){if(!(e.flags&1)){const t=on(e),n=xe[xe.length-1];!n||!(e.flags&2)&&t>=on(n)?xe.push(e):xe.splice(uc(t),0,e),e.flags|=1,Yi()}}function Yi(){kn||(kn=Xi.then(Ji))}function dc(e){B(e)?Ht.push(...e):st&&e.id===-1?st.splice(Pt+1,0,e):e.flags&1||(Ht.push(e),e.flags|=1),Yi()}function Rr(e,t,n=Be+1){for(;n<xe.length;n++){const s=xe[n];if(s&&s.flags&2){if(e&&s.id!==e.uid)continue;xe.splice(n,1),n--,s.flags&4&&(s.flags&=-2),s(),s.flags&4||(s.flags&=-2)}}}function Un(e){if(Ht.length){const t=[...new Set(Ht)].sort((n,s)=>on(n)-on(s));if(Ht.length=0,st){st.push(...t);return}for(st=t,Pt=0;Pt<st.length;Pt++){const n=st[Pt];n.flags&4&&(n.flags&=-2),n.flags&8||n(),n.flags&=-2}st=null,Pt=0}}const on=e=>e.id==null?e.flags&2?-1:1/0:e.id;function Ji(e){try{for(Be=0;Be<xe.length;Be++){const t=xe[Be];t&&!(t.flags&8)&&(t.flags&4&&(t.flags&=-2),hn(t,t.i,t.i?15:14),t.flags&4||(t.flags&=-2))}}finally{for(;Be<xe.length;Be++){const t=xe[Be];t&&(t.flags&=-2)}Be=-1,xe.length=0,Un(),kn=null,(xe.length||Ht.length)&&Ji()}}let ue=null,zi=null;function Wn(e){const t=ue;return ue=e,zi=e&&e.type.__scopeId||null,t}function hc(e,t=ue,n){if(!t||e._n)return e;const s=(...r)=>{s._d&&Wr(-1);const i=Wn(t);let o;try{o=e(...r)}finally{Wn(i),s._d&&Wr(1)}return o};return s._n=!0,s._c=!0,s._d=!0,s}function nu(e,t){if(ue===null)return e;const n=os(ue),s=e.dirs||(e.dirs=[]);for(let r=0;r<t.length;r++){let[i,o,l,c=te]=t[r];i&&(q(i)&&(i={mounted:i,updated:i}),i.deep&&Je(o),s.push({dir:i,instance:n,value:o,oldValue:void 0,arg:l,modifiers:c}))}return e}function Ke(e,t,n,s){const r=e.dirs,i=t&&t.dirs;for(let o=0;o<r.length;o++){const l=r[o];i&&(l.oldValue=i[o].value);let c=l.dir[s];c&&(ft(),Ve(c,n,8,[e.el,l,e,t]),ut())}}const Qi=Symbol("_vte"),Zi=e=>e.__isTeleport,Qt=e=>e&&(e.disabled||e.disabled===""),Mr=e=>e&&(e.defer||e.defer===""),Or=e=>typeof SVGElement<"u"&&e instanceof SVGElement,Pr=e=>typeof MathMLElement=="function"&&e instanceof MathMLElement,Ds=(e,t)=>{const n=e&&e.to;return oe(n)?t?t(n):null:n},eo={name:"Teleport",__isTeleport:!0,process(e,t,n,s,r,i,o,l,c,f){const{mc:a,pc:d,pbc:g,o:{insert:v,querySelector:_,createText:b,createComment:k}}=f,P=Qt(t.props);let{shapeFlag:D,children:p,dynamicChildren:m}=t;if(e==null){const O=t.el=b(""),$=t.anchor=b("");v(O,n,s),v($,n,s);const R=(x,M)=>{D&16&&(r&&r.isCE&&(r.ce._teleportTarget=x),a(p,x,M,r,i,o,l,c))},j=()=>{const x=t.target=Ds(t.props,_),M=to(x,t,b,v);x&&(o!=="svg"&&Or(x)?o="svg":o!=="mathml"&&Pr(x)&&(o="mathml"),P||(R(x,M),Nn(t,!1)))};P&&(R(n,$),Nn(t,!0)),Mr(t.props)?we(()=>{j(),t.el.__isMounted=!0},i):j()}else{if(Mr(t.props)&&!e.el.__isMounted){we(()=>{eo.process(e,t,n,s,r,i,o,l,c,f),delete e.el.__isMounted},i);return}t.el=e.el,t.targetStart=e.targetStart;const O=t.anchor=e.anchor,$=t.target=e.target,R=t.targetAnchor=e.targetAnchor,j=Qt(e.props),x=j?n:$,M=j?O:R;if(o==="svg"||Or($)?o="svg":(o==="mathml"||Pr($))&&(o="mathml"),m?(g(e.dynamicChildren,m,x,r,i,o,l),gr(e,t,!0)):c||d(e,t,x,M,r,i,o,l,!1),P)j?t.props&&e.props&&t.props.to!==e.props.to&&(t.props.to=e.props.to):xn(t,n,O,f,1);else if((t.props&&t.props.to)!==(e.props&&e.props.to)){const A=t.target=Ds(t.props,_);A&&xn(t,A,null,f,0)}else j&&xn(t,$,R,f,1);Nn(t,P)}},remove(e,t,n,{um:s,o:{remove:r}},i){const{shapeFlag:o,children:l,anchor:c,targetStart:f,targetAnchor:a,target:d,props:g}=e;if(d&&(r(f),r(a)),i&&r(c),o&16){const v=i||!Qt(g);for(let _=0;_<l.length;_++){const b=l[_];s(b,t,n,v,!!b.dynamicChildren)}}},move:xn,hydrate:pc};function xn(e,t,n,{o:{insert:s},m:r},i=2){i===0&&s(e.targetAnchor,t,n);const{el:o,anchor:l,shapeFlag:c,children:f,props:a}=e,d=i===2;if(d&&s(o,t,n),(!d||Qt(a))&&c&16)for(let g=0;g<f.length;g++)r(f[g],t,n,2);d&&s(l,t,n)}function pc(e,t,n,s,r,i,{o:{nextSibling:o,parentNode:l,querySelector:c,insert:f,createText:a}},d){const g=t.target=Ds(t.props,c);if(g){const v=Qt(t.props),_=g._lpa||g.firstChild;if(t.shapeFlag&16)if(v)t.anchor=d(o(e),t,l(e),n,s,r,i),t.targetStart=_,t.targetAnchor=_&&o(_);else{t.anchor=o(e);let b=_;for(;b;){if(b&&b.nodeType===8){if(b.data==="teleport start anchor")t.targetStart=b;else if(b.data==="teleport anchor"){t.targetAnchor=b,g._lpa=t.targetAnchor&&o(t.targetAnchor);break}}b=o(b)}t.targetAnchor||to(g,t,a,f),d(_&&o(_),t,g,n,s,r,i)}Nn(t,v)}return t.anchor&&o(t.anchor)}const su=eo;function Nn(e,t){const n=e.ctx;if(n&&n.ut){let s,r;for(t?(s=e.el,r=e.anchor):(s=e.targetStart,r=e.targetAnchor);s&&s!==r;)s.nodeType===1&&s.setAttribute("data-v-owner",n.uid),s=s.nextSibling;n.ut()}}function to(e,t,n,s){const r=t.targetStart=n(""),i=t.targetAnchor=n("");return r[Qi]=i,e&&(s(r,e),s(i,e)),i}const rt=Symbol("_leaveCb"),Tn=Symbol("_enterCb");function no(){const e={isMounted:!1,isLeaving:!1,isUnmounting:!1,leavingVNodes:new Map};return Ut(()=>{e.isMounted=!0}),ao(()=>{e.isUnmounting=!0}),e}const Re=[Function,Array],so={mode:String,appear:Boolean,persisted:Boolean,onBeforeEnter:Re,onEnter:Re,onAfterEnter:Re,onEnterCancelled:Re,onBeforeLeave:Re,onLeave:Re,onAfterLeave:Re,onLeaveCancelled:Re,onBeforeAppear:Re,onAppear:Re,onAfterAppear:Re,onAppearCancelled:Re},ro=e=>{const t=e.subTree;return t.component?ro(t.component):t},gc={name:"BaseTransition",props:so,setup(e,{slots:t}){const n=Wt(),s=no();return()=>{const r=t.default&&ar(t.default(),!0);if(!r||!r.length)return;const i=io(r),o=z(e),{mode:l}=o;if(s.isLeaving)return gs(i);const c=Ir(i);if(!c)return gs(i);let f=ln(c,o,s,n,d=>f=d);c.type!==be&&Ct(c,f);let a=n.subTree&&Ir(n.subTree);if(a&&a.type!==be&&!mt(c,a)&&ro(n).type!==be){let d=ln(a,o,s,n);if(Ct(a,d),l==="out-in"&&c.type!==be)return s.isLeaving=!0,d.afterLeave=()=>{s.isLeaving=!1,n.job.flags&8||n.update(),delete d.afterLeave,a=void 0},gs(i);l==="in-out"&&c.type!==be?d.delayLeave=(g,v,_)=>{const b=oo(s,a);b[String(a.key)]=a,g[rt]=()=>{v(),g[rt]=void 0,delete f.delayedLeave,a=void 0},f.delayedLeave=()=>{_(),delete f.delayedLeave,a=void 0}}:a=void 0}else a&&(a=void 0);return i}}};function io(e){let t=e[0];if(e.length>1){for(const n of e)if(n.type!==be){t=n;break}}return t}const mc=gc;function oo(e,t){const{leavingVNodes:n}=e;let s=n.get(t.type);return s||(s=Object.create(null),n.set(t.type,s)),s}function ln(e,t,n,s,r){const{appear:i,mode:o,persisted:l=!1,onBeforeEnter:c,onEnter:f,onAfterEnter:a,onEnterCancelled:d,onBeforeLeave:g,onLeave:v,onAfterLeave:_,onLeaveCancelled:b,onBeforeAppear:k,onAppear:P,onAfterAppear:D,onAppearCancelled:p}=t,m=String(e.key),O=oo(n,e),$=(x,M)=>{x&&Ve(x,s,9,M)},R=(x,M)=>{const A=M[1];$(x,M),B(x)?x.every(w=>w.length<=1)&&A():x.length<=1&&A()},j={mode:o,persisted:l,beforeEnter(x){let M=c;if(!n.isMounted)if(i)M=k||c;else return;x[rt]&&x[rt](!0);const A=O[m];A&&mt(e,A)&&A.el[rt]&&A.el[rt](),$(M,[x])},enter(x){let M=f,A=a,w=d;if(!n.isMounted)if(i)M=P||f,A=D||a,w=p||d;else return;let F=!1;const Y=x[Tn]=re=>{F||(F=!0,re?$(w,[x]):$(A,[x]),j.delayedLeave&&j.delayedLeave(),x[Tn]=void 0)};M?R(M,[x,Y]):Y()},leave(x,M){const A=String(e.key);if(x[Tn]&&x[Tn](!0),n.isUnmounting)return M();$(g,[x]);let w=!1;const F=x[rt]=Y=>{w||(w=!0,M(),Y?$(b,[x]):$(_,[x]),x[rt]=void 0,O[A]===e&&delete O[A])};O[A]=e,v?R(v,[x,F]):F()},clone(x){const M=ln(x,t,n,s,r);return r&&r(M),M}};return j}function gs(e){if(mn(e))return e=lt(e),e.children=null,e}function Ir(e){if(!mn(e))return Zi(e.type)&&e.children?io(e.children):e;const{shapeFlag:t,children:n}=e;if(n){if(t&16)return n[0];if(t&32&&q(n.default))return n.default()}}function Ct(e,t){e.shapeFlag&6&&e.component?(e.transition=t,Ct(e.component.subTree,t)):e.shapeFlag&128?(e.ssContent.transition=t.clone(e.ssContent),e.ssFallback.transition=t.clone(e.ssFallback)):e.transition=t}function ar(e,t=!1,n){let s=[],r=0;for(let i=0;i<e.length;i++){let o=e[i];const l=n==null?o.key:String(n)+String(o.key!=null?o.key:i);o.type===ve?(o.patchFlag&128&&r++,s=s.concat(ar(o.children,t,l))):(t||o.type!==be)&&s.push(l!=null?lt(o,{key:l}):o)}if(r>1)for(let i=0;i<s.length;i++)s[i].patchFlag=-2;return s}/*! #__NO_SIDE_EFFECTS__ */function fr(e,t){return q(e)?he({name:e.name},t,{setup:e}):e}function ur(e){e.ids=[e.ids[0]+e.ids[2]+++"-",0,0]}function cn(e,t,n,s,r=!1){if(B(e)){e.forEach((_,b)=>cn(_,t&&(B(t)?t[b]:t),n,s,r));return}if(bt(s)&&!r){s.shapeFlag&512&&s.type.__asyncResolved&&s.component.subTree.component&&cn(e,t,n,s.component.subTree);return}const i=s.shapeFlag&4?os(s.component):s.el,o=r?null:i,{i:l,r:c}=e,f=t&&t.r,a=l.refs===te?l.refs={}:l.refs,d=l.setupState,g=z(d),v=d===te?()=>!1:_=>Q(g,_);if(f!=null&&f!==c&&(oe(f)?(a[f]=null,v(f)&&(d[f]=null)):de(f)&&(f.value=null)),q(c))hn(c,l,12,[o,a]);else{const _=oe(c),b=de(c);if(_||b){const k=()=>{if(e.f){const P=_?v(c)?d[c]:a[c]:c.value;r?B(P)&&Qs(P,i):B(P)?P.includes(i)||P.push(i):_?(a[c]=[i],v(c)&&(d[c]=a[c])):(c.value=[i],e.k&&(a[e.k]=c.value))}else _?(a[c]=o,v(c)&&(d[c]=o)):b&&(c.value=o,e.k&&(a[e.k]=o))};o?(k.id=-1,we(k,n)):k()}}}let Lr=!1;const Ot=()=>{Lr||(console.error("Hydration completed but contains mismatches."),Lr=!0)},yc=e=>e.namespaceURI.includes("svg")&&e.tagName!=="foreignObject",vc=e=>e.namespaceURI.includes("MathML"),En=e=>{if(e.nodeType===1){if(yc(e))return"svg";if(vc(e))return"mathml"}},It=e=>e.nodeType===8;function bc(e){const{mt:t,p:n,o:{patchProp:s,createText:r,nextSibling:i,parentNode:o,remove:l,insert:c,createComment:f}}=e,a=(p,m)=>{if(!m.hasChildNodes()){n(null,p,m),Un(),m._vnode=p;return}d(m.firstChild,p,null,null,null),Un(),m._vnode=p},d=(p,m,O,$,R,j=!1)=>{j=j||!!m.dynamicChildren;const x=It(p)&&p.data==="[",M=()=>b(p,m,O,$,R,x),{type:A,ref:w,shapeFlag:F,patchFlag:Y}=m;let re=p.nodeType;m.el=p,Y===-2&&(j=!1,m.dynamicChildren=null);let U=null;switch(A){case St:re!==3?m.children===""?(c(m.el=r(""),o(p),p),U=p):U=M():(p.data!==m.children&&(Ot(),p.data=m.children),U=i(p));break;case be:D(p)?(U=i(p),P(m.el=p.content.firstChild,p,O)):re!==8||x?U=M():U=i(p);break;case en:if(x&&(p=i(p),re=p.nodeType),re===1||re===3){U=p;const X=!m.children.length;for(let V=0;V<m.staticCount;V++)X&&(m.children+=U.nodeType===1?U.outerHTML:U.data),V===m.staticCount-1&&(m.anchor=U),U=i(U);return x?i(U):U}else M();break;case ve:x?U=_(p,m,O,$,R,j):U=M();break;default:if(F&1)(re!==1||m.type.toLowerCase()!==p.tagName.toLowerCase())&&!D(p)?U=M():U=g(p,m,O,$,R,j);else if(F&6){m.slotScopeIds=R;const X=o(p);if(x?U=k(p):It(p)&&p.data==="teleport start"?U=k(p,p.data,"teleport end"):U=i(p),t(m,X,null,O,$,En(X),j),bt(m)&&!m.type.__asyncResolved){let V;x?(V=le(ve),V.anchor=U?U.previousSibling:X.lastChild):V=p.nodeType===3?jo(""):le("div"),V.el=p,m.component.subTree=V}}else F&64?re!==8?U=M():U=m.type.hydrate(p,m,O,$,R,j,e,v):F&128&&(U=m.type.hydrate(p,m,O,$,En(o(p)),R,j,e,d))}return w!=null&&cn(w,null,$,m),U},g=(p,m,O,$,R,j)=>{j=j||!!m.dynamicChildren;const{type:x,props:M,patchFlag:A,shapeFlag:w,dirs:F,transition:Y}=m,re=x==="input"||x==="option";if(re||A!==-1){F&&Ke(m,null,O,"created");let U=!1;if(D(p)){U=Ro(null,Y)&&O&&O.vnode.props&&O.vnode.props.appear;const V=p.content.firstChild;U&&Y.beforeEnter(V),P(V,p,O),m.el=p=V}if(w&16&&!(M&&(M.innerHTML||M.textContent))){let V=v(p.firstChild,m,p,O,$,R,j);for(;V;){Cn(p,1)||Ot();const ae=V;V=V.nextSibling,l(ae)}}else if(w&8){let V=m.children;V[0]===`
`&&(p.tagName==="PRE"||p.tagName==="TEXTAREA")&&(V=V.slice(1)),p.textContent!==V&&(Cn(p,0)||Ot(),p.textContent=m.children)}if(M){if(re||!j||A&48){const V=p.tagName.includes("-");for(const ae in M)(re&&(ae.endsWith("value")||ae==="indeterminate")||un(ae)&&!Ft(ae)||ae[0]==="."||V)&&s(p,ae,null,M[ae],void 0,O)}else if(M.onClick)s(p,"onClick",null,M.onClick,void 0,O);else if(A&4&&vt(M.style))for(const V in M.style)M.style[V]}let X;(X=M&&M.onVnodeBeforeMount)&&Me(X,O,m),F&&Ke(m,null,O,"beforeMount"),((X=M&&M.onVnodeMounted)||F||U)&&Fo(()=>{X&&Me(X,O,m),U&&Y.enter(p),F&&Ke(m,null,O,"mounted")},$)}return p.nextSibling},v=(p,m,O,$,R,j,x)=>{x=x||!!m.dynamicChildren;const M=m.children,A=M.length;for(let w=0;w<A;w++){const F=x?M[w]:M[w]=Oe(M[w]),Y=F.type===St;p?(Y&&!x&&w+1<A&&Oe(M[w+1]).type===St&&(c(r(p.data.slice(F.children.length)),O,i(p)),p.data=F.children),p=d(p,F,$,R,j,x)):Y&&!F.children?c(F.el=r(""),O):(Cn(O,1)||Ot(),n(null,F,O,null,$,R,En(O),j))}return p},_=(p,m,O,$,R,j)=>{const{slotScopeIds:x}=m;x&&(R=R?R.concat(x):x);const M=o(p),A=v(i(p),m,M,O,$,R,j);return A&&It(A)&&A.data==="]"?i(m.anchor=A):(Ot(),c(m.anchor=f("]"),M,A),A)},b=(p,m,O,$,R,j)=>{if(Cn(p.parentElement,1)||Ot(),m.el=null,j){const A=k(p);for(;;){const w=i(p);if(w&&w!==A)l(w);else break}}const x=i(p),M=o(p);return l(p),n(null,m,M,x,O,$,En(M),R),O&&(O.vnode.el=m.el,Lo(O,m.el)),x},k=(p,m="[",O="]")=>{let $=0;for(;p;)if(p=i(p),p&&It(p)&&(p.data===m&&$++,p.data===O)){if($===0)return i(p);$--}return p},P=(p,m,O)=>{const $=m.parentNode;$&&$.replaceChild(p,m);let R=O;for(;R;)R.vnode.el===m&&(R.vnode.el=R.subTree.el=p),R=R.parent},D=p=>p.nodeType===1&&p.tagName==="TEMPLATE";return[a,d]}const Nr="data-allow-mismatch",_c={0:"text",1:"children",2:"class",3:"style",4:"attribute"};function Cn(e,t){if(t===0||t===1)for(;e&&!e.hasAttribute(Nr);)e=e.parentElement;const n=e&&e.getAttribute(Nr);if(n==null)return!1;if(n==="")return!0;{const s=n.split(",");return t===0&&s.includes("children")?!0:n.split(",").includes(_c[t])}}Jn().requestIdleCallback;Jn().cancelIdleCallback;function wc(e,t){if(It(e)&&e.data==="["){let n=1,s=e.nextSibling;for(;s;){if(s.nodeType===1){if(t(s)===!1)break}else if(It(s))if(s.data==="]"){if(--n===0)break}else s.data==="["&&n++;s=s.nextSibling}}else t(e)}const bt=e=>!!e.type.__asyncLoader;/*! #__NO_SIDE_EFFECTS__ */function ru(e){q(e)&&(e={loader:e});const{loader:t,loadingComponent:n,errorComponent:s,delay:r=200,hydrate:i,timeout:o,suspensible:l=!0,onError:c}=e;let f=null,a,d=0;const g=()=>(d++,f=null,v()),v=()=>{let _;return f||(_=f=t().catch(b=>{if(b=b instanceof Error?b:new Error(String(b)),c)return new Promise((k,P)=>{c(b,()=>k(g()),()=>P(b),d+1)});throw b}).then(b=>_!==f&&f?f:(b&&(b.__esModule||b[Symbol.toStringTag]==="Module")&&(b=b.default),a=b,b)))};return fr({name:"AsyncComponentWrapper",__asyncLoader:v,__asyncHydrate(_,b,k){const P=i?()=>{const D=i(k,p=>wc(_,p));D&&(b.bum||(b.bum=[])).push(D)}:k;a?P():v().then(()=>!b.isUnmounted&&P())},get __asyncResolved(){return a},setup(){const _=fe;if(ur(_),a)return()=>ms(a,_);const b=p=>{f=null,pn(p,_,13,!s)};if(l&&_.suspense||$t)return v().then(p=>()=>ms(p,_)).catch(p=>(b(p),()=>s?le(s,{error:p}):null));const k=qe(!1),P=qe(),D=qe(!!r);return r&&setTimeout(()=>{D.value=!1},r),o!=null&&setTimeout(()=>{if(!k.value&&!P.value){const p=new Error(`Async component timed out after ${o}ms.`);b(p),P.value=p}},o),v().then(()=>{k.value=!0,_.parent&&mn(_.parent.vnode)&&_.parent.update()}).catch(p=>{b(p),P.value=p}),()=>{if(k.value&&a)return ms(a,_);if(P.value&&s)return le(s,{error:P.value});if(n&&!D.value)return le(n)}}})}function ms(e,t){const{ref:n,props:s,children:r,ce:i}=t.vnode,o=le(e,s,r);return o.ref=n,o.ce=i,delete t.vnode.ce,o}const mn=e=>e.type.__isKeepAlive;function Sc(e,t){lo(e,"a",t)}function xc(e,t){lo(e,"da",t)}function lo(e,t,n=fe){const s=e.__wdc||(e.__wdc=()=>{let r=n;for(;r;){if(r.isDeactivated)return;r=r.parent}return e()});if(ns(t,s,n),n){let r=n.parent;for(;r&&r.parent;)mn(r.parent.vnode)&&Tc(s,t,n,r),r=r.parent}}function Tc(e,t,n,s){const r=ns(t,e,s,!0);ss(()=>{Qs(s[t],r)},n)}function ns(e,t,n=fe,s=!1){if(n){const r=n[e]||(n[e]=[]),i=t.__weh||(t.__weh=(...o)=>{ft();const l=yn(n),c=Ve(t,n,e,o);return l(),ut(),c});return s?r.unshift(i):r.push(i),i}}const et=e=>(t,n=fe)=>{(!$t||e==="sp")&&ns(e,(...s)=>t(...s),n)},Ec=et("bm"),Ut=et("m"),Cc=et("bu"),co=et("u"),ao=et("bum"),ss=et("um"),Ac=et("sp"),Rc=et("rtg"),Mc=et("rtc");function Oc(e,t=fe){ns("ec",e,t)}const dr="components",Pc="directives";function iu(e,t){return hr(dr,e,!0,t)||e}const fo=Symbol.for("v-ndc");function ou(e){return oe(e)?hr(dr,e,!1)||e:e||fo}function lu(e){return hr(Pc,e)}function hr(e,t,n=!0,s=!1){const r=ue||fe;if(r){const i=r.type;if(e===dr){const l=pa(i,!1);if(l&&(l===t||l===Fe(t)||l===Yn(Fe(t))))return i}const o=Fr(r[e]||i[e],t)||Fr(r.appContext[e],t);return!o&&s?i:o}}function Fr(e,t){return e&&(e[t]||e[Fe(t)]||e[Yn(Fe(t))])}function cu(e,t,n,s){let r;const i=n,o=B(e);if(o||oe(e)){const l=o&&vt(e);let c=!1;l&&(c=!Pe(e),e=es(e)),r=new Array(e.length);for(let f=0,a=e.length;f<a;f++)r[f]=t(c?ye(e[f]):e[f],f,void 0,i)}else if(typeof e=="number"){r=new Array(e);for(let l=0;l<e;l++)r[l]=t(l+1,l,void 0,i)}else if(ee(e))if(e[Symbol.iterator])r=Array.from(e,(l,c)=>t(l,c,void 0,i));else{const l=Object.keys(e);r=new Array(l.length);for(let c=0,f=l.length;c<f;c++){const a=l[c];r[c]=t(e[a],a,c,i)}}else r=[];return r}function au(e,t){for(let n=0;n<t.length;n++){const s=t[n];if(B(s))for(let r=0;r<s.length;r++)e[s[r].name]=s[r].fn;else s&&(e[s.name]=s.key?(...r)=>{const i=s.fn(...r);return i&&(i.key=s.key),i}:s.fn)}return e}function fu(e,t,n={},s,r){if(ue.ce||ue.parent&&bt(ue.parent)&&ue.parent.ce)return t!=="default"&&(n.name=t),Us(),Ws(ve,null,[le("slot",n,s&&s())],64);let i=e[t];i&&i._c&&(i._d=!1),Us();const o=i&&uo(i(n)),l=n.key||o&&o.key,c=Ws(ve,{key:(l&&!je(l)?l:`_${t}`)+(!o&&s?"_fb":"")},o||(s?s():[]),o&&e._===1?64:-2);return!r&&c.scopeId&&(c.slotScopeIds=[c.scopeId+"-s"]),i&&i._c&&(i._d=!0),c}function uo(e){return e.some(t=>fn(t)?!(t.type===be||t.type===ve&&!uo(t.children)):!0)?e:null}function uu(e,t){const n={};for(const s in e)n[t&&/[A-Z]/.test(s)?`on:${s}`:Pn(s)]=e[s];return n}const $s=e=>e?Vo(e)?os(e):$s(e.parent):null,Zt=he(Object.create(null),{$:e=>e,$el:e=>e.vnode.el,$data:e=>e.data,$props:e=>e.props,$attrs:e=>e.attrs,$slots:e=>e.slots,$refs:e=>e.refs,$parent:e=>$s(e.parent),$root:e=>$s(e.root),$host:e=>e.ce,$emit:e=>e.emit,$options:e=>go(e),$forceUpdate:e=>e.f||(e.f=()=>{cr(e.update)}),$nextTick:e=>e.n||(e.n=gn.bind(e.proxy)),$watch:e=>Qc.bind(e)}),ys=(e,t)=>e!==te&&!e.__isScriptSetup&&Q(e,t),Ic={get({_:e},t){if(t==="__v_skip")return!0;const{ctx:n,setupState:s,data:r,props:i,accessCache:o,type:l,appContext:c}=e;let f;if(t[0]!=="$"){const v=o[t];if(v!==void 0)switch(v){case 1:return s[t];case 2:return r[t];case 4:return n[t];case 3:return i[t]}else{if(ys(s,t))return o[t]=1,s[t];if(r!==te&&Q(r,t))return o[t]=2,r[t];if((f=e.propsOptions[0])&&Q(f,t))return o[t]=3,i[t];if(n!==te&&Q(n,t))return o[t]=4,n[t];js&&(o[t]=0)}}const a=Zt[t];let d,g;if(a)return t==="$attrs"&&me(e.attrs,"get",""),a(e);if((d=l.__cssModules)&&(d=d[t]))return d;if(n!==te&&Q(n,t))return o[t]=4,n[t];if(g=c.config.globalProperties,Q(g,t))return g[t]},set({_:e},t,n){const{data:s,setupState:r,ctx:i}=e;return ys(r,t)?(r[t]=n,!0):s!==te&&Q(s,t)?(s[t]=n,!0):Q(e.props,t)||t[0]==="$"&&t.slice(1)in e?!1:(i[t]=n,!0)},has({_:{data:e,setupState:t,accessCache:n,ctx:s,appContext:r,propsOptions:i}},o){let l;return!!n[o]||e!==te&&Q(e,o)||ys(t,o)||(l=i[0])&&Q(l,o)||Q(s,o)||Q(Zt,o)||Q(r.config.globalProperties,o)},defineProperty(e,t,n){return n.get!=null?e._.accessCache[t]=0:Q(n,"value")&&this.set(e,t,n.value,null),Reflect.defineProperty(e,t,n)}};function du(){return ho().slots}function hu(){return ho().attrs}function ho(){const e=Wt();return e.setupContext||(e.setupContext=Uo(e))}function Hr(e){return B(e)?e.reduce((t,n)=>(t[n]=null,t),{}):e}let js=!0;function Lc(e){const t=go(e),n=e.proxy,s=e.ctx;js=!1,t.beforeCreate&&Dr(t.beforeCreate,e,"bc");const{data:r,computed:i,methods:o,watch:l,provide:c,inject:f,created:a,beforeMount:d,mounted:g,beforeUpdate:v,updated:_,activated:b,deactivated:k,beforeDestroy:P,beforeUnmount:D,destroyed:p,unmounted:m,render:O,renderTracked:$,renderTriggered:R,errorCaptured:j,serverPrefetch:x,expose:M,inheritAttrs:A,components:w,directives:F,filters:Y}=t;if(f&&Nc(f,s,null),o)for(const X in o){const V=o[X];q(V)&&(s[X]=V.bind(n))}if(r){const X=r.call(n,n);ee(X)&&(e.data=Dt(X))}if(js=!0,i)for(const X in i){const V=i[X],ae=q(V)?V.bind(n,n):q(V.get)?V.get.bind(n,n):De,vn=!q(V)&&q(V.set)?V.set.bind(n):De,dt=ie({get:ae,set:vn});Object.defineProperty(s,X,{enumerable:!0,configurable:!0,get:()=>dt.value,set:ke=>dt.value=ke})}if(l)for(const X in l)po(l[X],s,n,X);if(c){const X=q(c)?c.call(n):c;Reflect.ownKeys(X).forEach(V=>{Vc(V,X[V])})}a&&Dr(a,e,"c");function U(X,V){B(V)?V.forEach(ae=>X(ae.bind(n))):V&&X(V.bind(n))}if(U(Ec,d),U(Ut,g),U(Cc,v),U(co,_),U(Sc,b),U(xc,k),U(Oc,j),U(Mc,$),U(Rc,R),U(ao,D),U(ss,m),U(Ac,x),B(M))if(M.length){const X=e.exposed||(e.exposed={});M.forEach(V=>{Object.defineProperty(X,V,{get:()=>n[V],set:ae=>n[V]=ae})})}else e.exposed||(e.exposed={});O&&e.render===De&&(e.render=O),A!=null&&(e.inheritAttrs=A),w&&(e.components=w),F&&(e.directives=F),x&&ur(e)}function Nc(e,t,n=De){B(e)&&(e=Vs(e));for(const s in e){const r=e[s];let i;ee(r)?"default"in r?i=wt(r.from||s,r.default,!0):i=wt(r.from||s):i=wt(r),de(i)?Object.defineProperty(t,s,{enumerable:!0,configurable:!0,get:()=>i.value,set:o=>i.value=o}):t[s]=i}}function Dr(e,t,n){Ve(B(e)?e.map(s=>s.bind(t.proxy)):e.bind(t.proxy),t,n)}function po(e,t,n,s){let r=s.includes(".")?Po(n,s):()=>n[s];if(oe(e)){const i=t[e];q(i)&&Le(r,i)}else if(q(e))Le(r,e.bind(n));else if(ee(e))if(B(e))e.forEach(i=>po(i,t,n,s));else{const i=q(e.handler)?e.handler.bind(n):t[e.handler];q(i)&&Le(r,i,e)}}function go(e){const t=e.type,{mixins:n,extends:s}=t,{mixins:r,optionsCache:i,config:{optionMergeStrategies:o}}=e.appContext,l=i.get(t);let c;return l?c=l:!r.length&&!n&&!s?c=t:(c={},r.length&&r.forEach(f=>Bn(c,f,o,!0)),Bn(c,t,o)),ee(t)&&i.set(t,c),c}function Bn(e,t,n,s=!1){const{mixins:r,extends:i}=t;i&&Bn(e,i,n,!0),r&&r.forEach(o=>Bn(e,o,n,!0));for(const o in t)if(!(s&&o==="expose")){const l=Fc[o]||n&&n[o];e[o]=l?l(e[o],t[o]):t[o]}return e}const Fc={data:$r,props:jr,emits:jr,methods:Yt,computed:Yt,beforeCreate:_e,created:_e,beforeMount:_e,mounted:_e,beforeUpdate:_e,updated:_e,beforeDestroy:_e,beforeUnmount:_e,destroyed:_e,unmounted:_e,activated:_e,deactivated:_e,errorCaptured:_e,serverPrefetch:_e,components:Yt,directives:Yt,watch:Dc,provide:$r,inject:Hc};function $r(e,t){return t?e?function(){return he(q(e)?e.call(this,this):e,q(t)?t.call(this,this):t)}:t:e}function Hc(e,t){return Yt(Vs(e),Vs(t))}function Vs(e){if(B(e)){const t={};for(let n=0;n<e.length;n++)t[e[n]]=e[n];return t}return e}function _e(e,t){return e?[...new Set([].concat(e,t))]:t}function Yt(e,t){return e?he(Object.create(null),e,t):t}function jr(e,t){return e?B(e)&&B(t)?[...new Set([...e,...t])]:he(Object.create(null),Hr(e),Hr(t??{})):t}function Dc(e,t){if(!e)return t;if(!t)return e;const n=he(Object.create(null),e);for(const s in t)n[s]=_e(e[s],t[s]);return n}function mo(){return{app:null,config:{isNativeTag:vl,performance:!1,globalProperties:{},optionMergeStrategies:{},errorHandler:void 0,warnHandler:void 0,compilerOptions:{}},mixins:[],components:{},directives:{},provides:Object.create(null),optionsCache:new WeakMap,propsCache:new WeakMap,emitsCache:new WeakMap}}let $c=0;function jc(e,t){return function(s,r=null){q(s)||(s=he({},s)),r!=null&&!ee(r)&&(r=null);const i=mo(),o=new WeakSet,l=[];let c=!1;const f=i.app={_uid:$c++,_component:s,_props:r,_container:null,_context:i,_instance:null,version:ma,get config(){return i.config},set config(a){},use(a,...d){return o.has(a)||(a&&q(a.install)?(o.add(a),a.install(f,...d)):q(a)&&(o.add(a),a(f,...d))),f},mixin(a){return i.mixins.includes(a)||i.mixins.push(a),f},component(a,d){return d?(i.components[a]=d,f):i.components[a]},directive(a,d){return d?(i.directives[a]=d,f):i.directives[a]},mount(a,d,g){if(!c){const v=f._ceVNode||le(s,r);return v.appContext=i,g===!0?g="svg":g===!1&&(g=void 0),d&&t?t(v,a):e(v,a,g),c=!0,f._container=a,a.__vue_app__=f,os(v.component)}},onUnmount(a){l.push(a)},unmount(){c&&(Ve(l,f._instance,16),e(null,f._container),delete f._container.__vue_app__)},provide(a,d){return i.provides[a]=d,f},runWithContext(a){const d=_t;_t=f;try{return a()}finally{_t=d}}};return f}}let _t=null;function Vc(e,t){if(fe){let n=fe.provides;const s=fe.parent&&fe.parent.provides;s===n&&(n=fe.provides=Object.create(s)),n[e]=t}}function wt(e,t,n=!1){const s=fe||ue;if(s||_t){const r=_t?_t._context.provides:s?s.parent==null?s.vnode.appContext&&s.vnode.appContext.provides:s.parent.provides:void 0;if(r&&e in r)return r[e];if(arguments.length>1)return n&&q(t)?t.call(s&&s.proxy):t}}function yo(){return!!(fe||ue||_t)}const vo={},bo=()=>Object.create(vo),_o=e=>Object.getPrototypeOf(e)===vo;function kc(e,t,n,s=!1){const r={},i=bo();e.propsDefaults=Object.create(null),wo(e,t,r,i);for(const o in e.propsOptions[0])o in r||(r[o]=void 0);n?e.props=s?r:Ql(r):e.type.props?e.props=r:e.props=i,e.attrs=i}function Uc(e,t,n,s){const{props:r,attrs:i,vnode:{patchFlag:o}}=e,l=z(r),[c]=e.propsOptions;let f=!1;if((s||o>0)&&!(o&16)){if(o&8){const a=e.vnode.dynamicProps;for(let d=0;d<a.length;d++){let g=a[d];if(is(e.emitsOptions,g))continue;const v=t[g];if(c)if(Q(i,g))v!==i[g]&&(i[g]=v,f=!0);else{const _=Fe(g);r[_]=ks(c,l,_,v,e,!1)}else v!==i[g]&&(i[g]=v,f=!0)}}}else{wo(e,t,r,i)&&(f=!0);let a;for(const d in l)(!t||!Q(t,d)&&((a=at(d))===d||!Q(t,a)))&&(c?n&&(n[d]!==void 0||n[a]!==void 0)&&(r[d]=ks(c,l,d,void 0,e,!0)):delete r[d]);if(i!==l)for(const d in i)(!t||!Q(t,d))&&(delete i[d],f=!0)}f&&Ye(e.attrs,"set","")}function wo(e,t,n,s){const[r,i]=e.propsOptions;let o=!1,l;if(t)for(let c in t){if(Ft(c))continue;const f=t[c];let a;r&&Q(r,a=Fe(c))?!i||!i.includes(a)?n[a]=f:(l||(l={}))[a]=f:is(e.emitsOptions,c)||(!(c in s)||f!==s[c])&&(s[c]=f,o=!0)}if(i){const c=z(n),f=l||te;for(let a=0;a<i.length;a++){const d=i[a];n[d]=ks(r,c,d,f[d],e,!Q(f,d))}}return o}function ks(e,t,n,s,r,i){const o=e[n];if(o!=null){const l=Q(o,"default");if(l&&s===void 0){const c=o.default;if(o.type!==Function&&!o.skipFactory&&q(c)){const{propsDefaults:f}=r;if(n in f)s=f[n];else{const a=yn(r);s=f[n]=c.call(null,t),a()}}else s=c;r.ce&&r.ce._setProp(n,s)}o[0]&&(i&&!l?s=!1:o[1]&&(s===""||s===at(n))&&(s=!0))}return s}const Wc=new WeakMap;function So(e,t,n=!1){const s=n?Wc:t.propsCache,r=s.get(e);if(r)return r;const i=e.props,o={},l=[];let c=!1;if(!q(e)){const a=d=>{c=!0;const[g,v]=So(d,t,!0);he(o,g),v&&l.push(...v)};!n&&t.mixins.length&&t.mixins.forEach(a),e.extends&&a(e.extends),e.mixins&&e.mixins.forEach(a)}if(!i&&!c)return ee(e)&&s.set(e,Lt),Lt;if(B(i))for(let a=0;a<i.length;a++){const d=Fe(i[a]);Vr(d)&&(o[d]=te)}else if(i)for(const a in i){const d=Fe(a);if(Vr(d)){const g=i[a],v=o[d]=B(g)||q(g)?{type:g}:he({},g),_=v.type;let b=!1,k=!0;if(B(_))for(let P=0;P<_.length;++P){const D=_[P],p=q(D)&&D.name;if(p==="Boolean"){b=!0;break}else p==="String"&&(k=!1)}else b=q(_)&&_.name==="Boolean";v[0]=b,v[1]=k,(b||Q(v,"default"))&&l.push(d)}}const f=[o,l];return ee(e)&&s.set(e,f),f}function Vr(e){return e[0]!=="$"&&!Ft(e)}const xo=e=>e[0]==="_"||e==="$stable",pr=e=>B(e)?e.map(Oe):[Oe(e)],Bc=(e,t,n)=>{if(t._n)return t;const s=hc((...r)=>pr(t(...r)),n);return s._c=!1,s},To=(e,t,n)=>{const s=e._ctx;for(const r in e){if(xo(r))continue;const i=e[r];if(q(i))t[r]=Bc(r,i,s);else if(i!=null){const o=pr(i);t[r]=()=>o}}},Eo=(e,t)=>{const n=pr(t);e.slots.default=()=>n},Co=(e,t,n)=>{for(const s in t)(n||s!=="_")&&(e[s]=t[s])},Kc=(e,t,n)=>{const s=e.slots=bo();if(e.vnode.shapeFlag&32){const r=t._;r?(Co(s,t,n),n&&Ei(s,"_",r,!0)):To(t,s)}else t&&Eo(e,t)},qc=(e,t,n)=>{const{vnode:s,slots:r}=e;let i=!0,o=te;if(s.shapeFlag&32){const l=t._;l?n&&l===1?i=!1:Co(r,t,n):(i=!t.$stable,To(t,r)),o=t}else t&&(Eo(e,t),o={default:1});if(i)for(const l in r)!xo(l)&&o[l]==null&&delete r[l]},we=Fo;function Gc(e){return Ao(e)}function Xc(e){return Ao(e,bc)}function Ao(e,t){const n=Jn();n.__VUE__=!0;const{insert:s,remove:r,patchProp:i,createElement:o,createText:l,createComment:c,setText:f,setElementText:a,parentNode:d,nextSibling:g,setScopeId:v=De,insertStaticContent:_}=e,b=(u,h,y,E=null,S=null,T=null,N=void 0,L=null,I=!!h.dynamicChildren)=>{if(u===h)return;u&&!mt(u,h)&&(E=bn(u),ke(u,S,T,!0),u=null),h.patchFlag===-2&&(I=!1,h.dynamicChildren=null);const{type:C,ref:K,shapeFlag:H}=h;switch(C){case St:k(u,h,y,E);break;case be:P(u,h,y,E);break;case en:u==null&&D(h,y,E,N);break;case ve:w(u,h,y,E,S,T,N,L,I);break;default:H&1?O(u,h,y,E,S,T,N,L,I):H&6?F(u,h,y,E,S,T,N,L,I):(H&64||H&128)&&C.process(u,h,y,E,S,T,N,L,I,Rt)}K!=null&&S&&cn(K,u&&u.ref,T,h||u,!h)},k=(u,h,y,E)=>{if(u==null)s(h.el=l(h.children),y,E);else{const S=h.el=u.el;h.children!==u.children&&f(S,h.children)}},P=(u,h,y,E)=>{u==null?s(h.el=c(h.children||""),y,E):h.el=u.el},D=(u,h,y,E)=>{[u.el,u.anchor]=_(u.children,h,y,E,u.el,u.anchor)},p=({el:u,anchor:h},y,E)=>{let S;for(;u&&u!==h;)S=g(u),s(u,y,E),u=S;s(h,y,E)},m=({el:u,anchor:h})=>{let y;for(;u&&u!==h;)y=g(u),r(u),u=y;r(h)},O=(u,h,y,E,S,T,N,L,I)=>{h.type==="svg"?N="svg":h.type==="math"&&(N="mathml"),u==null?$(h,y,E,S,T,N,L,I):x(u,h,S,T,N,L,I)},$=(u,h,y,E,S,T,N,L)=>{let I,C;const{props:K,shapeFlag:H,transition:W,dirs:G}=u;if(I=u.el=o(u.type,T,K&&K.is,K),H&8?a(I,u.children):H&16&&j(u.children,I,null,E,S,vs(u,T),N,L),G&&Ke(u,null,E,"created"),R(I,u,u.scopeId,N,E),K){for(const ne in K)ne!=="value"&&!Ft(ne)&&i(I,ne,null,K[ne],T,E);"value"in K&&i(I,"value",null,K.value,T),(C=K.onVnodeBeforeMount)&&Me(C,E,u)}G&&Ke(u,null,E,"beforeMount");const J=Ro(S,W);J&&W.beforeEnter(I),s(I,h,y),((C=K&&K.onVnodeMounted)||J||G)&&we(()=>{C&&Me(C,E,u),J&&W.enter(I),G&&Ke(u,null,E,"mounted")},S)},R=(u,h,y,E,S)=>{if(y&&v(u,y),E)for(let T=0;T<E.length;T++)v(u,E[T]);if(S){let T=S.subTree;if(h===T||No(T.type)&&(T.ssContent===h||T.ssFallback===h)){const N=S.vnode;R(u,N,N.scopeId,N.slotScopeIds,S.parent)}}},j=(u,h,y,E,S,T,N,L,I=0)=>{for(let C=I;C<u.length;C++){const K=u[C]=L?it(u[C]):Oe(u[C]);b(null,K,h,y,E,S,T,N,L)}},x=(u,h,y,E,S,T,N)=>{const L=h.el=u.el;let{patchFlag:I,dynamicChildren:C,dirs:K}=h;I|=u.patchFlag&16;const H=u.props||te,W=h.props||te;let G;if(y&&ht(y,!1),(G=W.onVnodeBeforeUpdate)&&Me(G,y,h,u),K&&Ke(h,u,y,"beforeUpdate"),y&&ht(y,!0),(H.innerHTML&&W.innerHTML==null||H.textContent&&W.textContent==null)&&a(L,""),C?M(u.dynamicChildren,C,L,y,E,vs(h,S),T):N||V(u,h,L,null,y,E,vs(h,S),T,!1),I>0){if(I&16)A(L,H,W,y,S);else if(I&2&&H.class!==W.class&&i(L,"class",null,W.class,S),I&4&&i(L,"style",H.style,W.style,S),I&8){const J=h.dynamicProps;for(let ne=0;ne<J.length;ne++){const Z=J[ne],Te=H[Z],pe=W[Z];(pe!==Te||Z==="value")&&i(L,Z,Te,pe,S,y)}}I&1&&u.children!==h.children&&a(L,h.children)}else!N&&C==null&&A(L,H,W,y,S);((G=W.onVnodeUpdated)||K)&&we(()=>{G&&Me(G,y,h,u),K&&Ke(h,u,y,"updated")},E)},M=(u,h,y,E,S,T,N)=>{for(let L=0;L<h.length;L++){const I=u[L],C=h[L],K=I.el&&(I.type===ve||!mt(I,C)||I.shapeFlag&70)?d(I.el):y;b(I,C,K,null,E,S,T,N,!0)}},A=(u,h,y,E,S)=>{if(h!==y){if(h!==te)for(const T in h)!Ft(T)&&!(T in y)&&i(u,T,h[T],null,S,E);for(const T in y){if(Ft(T))continue;const N=y[T],L=h[T];N!==L&&T!=="value"&&i(u,T,L,N,S,E)}"value"in y&&i(u,"value",h.value,y.value,S)}},w=(u,h,y,E,S,T,N,L,I)=>{const C=h.el=u?u.el:l(""),K=h.anchor=u?u.anchor:l("");let{patchFlag:H,dynamicChildren:W,slotScopeIds:G}=h;G&&(L=L?L.concat(G):G),u==null?(s(C,y,E),s(K,y,E),j(h.children||[],y,K,S,T,N,L,I)):H>0&&H&64&&W&&u.dynamicChildren?(M(u.dynamicChildren,W,y,S,T,N,L),(h.key!=null||S&&h===S.subTree)&&gr(u,h,!0)):V(u,h,y,K,S,T,N,L,I)},F=(u,h,y,E,S,T,N,L,I)=>{h.slotScopeIds=L,u==null?h.shapeFlag&512?S.ctx.activate(h,y,E,N,I):Y(h,y,E,S,T,N,I):re(u,h,I)},Y=(u,h,y,E,S,T,N)=>{const L=u.component=fa(u,E,S);if(mn(u)&&(L.ctx.renderer=Rt),ua(L,!1,N),L.asyncDep){if(S&&S.registerDep(L,U,N),!u.el){const I=L.subTree=le(be);P(null,I,h,y)}}else U(L,u,h,y,S,T,N)},re=(u,h,y)=>{const E=h.component=u.component;if(sa(u,h,y))if(E.asyncDep&&!E.asyncResolved){X(E,h,y);return}else E.next=h,E.update();else h.el=u.el,E.vnode=h},U=(u,h,y,E,S,T,N)=>{const L=()=>{if(u.isMounted){let{next:H,bu:W,u:G,parent:J,vnode:ne}=u;{const Ee=Mo(u);if(Ee){H&&(H.el=ne.el,X(u,H,N)),Ee.asyncDep.then(()=>{u.isUnmounted||L()});return}}let Z=H,Te;ht(u,!1),H?(H.el=ne.el,X(u,H,N)):H=ne,W&&In(W),(Te=H.props&&H.props.onVnodeBeforeUpdate)&&Me(Te,J,H,ne),ht(u,!0);const pe=bs(u),He=u.subTree;u.subTree=pe,b(He,pe,d(He.el),bn(He),u,S,T),H.el=pe.el,Z===null&&Lo(u,pe.el),G&&we(G,S),(Te=H.props&&H.props.onVnodeUpdated)&&we(()=>Me(Te,J,H,ne),S)}else{let H;const{el:W,props:G}=h,{bm:J,m:ne,parent:Z,root:Te,type:pe}=u,He=bt(h);if(ht(u,!1),J&&In(J),!He&&(H=G&&G.onVnodeBeforeMount)&&Me(H,Z,h),ht(u,!0),W&&fs){const Ee=()=>{u.subTree=bs(u),fs(W,u.subTree,u,S,null)};He&&pe.__asyncHydrate?pe.__asyncHydrate(W,u,Ee):Ee()}else{Te.ce&&Te.ce._injectChildStyle(pe);const Ee=u.subTree=bs(u);b(null,Ee,y,E,u,S,T),h.el=Ee.el}if(ne&&we(ne,S),!He&&(H=G&&G.onVnodeMounted)){const Ee=h;we(()=>Me(H,Z,Ee),S)}(h.shapeFlag&256||Z&&bt(Z.vnode)&&Z.vnode.shapeFlag&256)&&u.a&&we(u.a,S),u.isMounted=!0,h=y=E=null}};u.scope.on();const I=u.effect=new Pi(L);u.scope.off();const C=u.update=I.run.bind(I),K=u.job=I.runIfDirty.bind(I);K.i=u,K.id=u.uid,I.scheduler=()=>cr(K),ht(u,!0),C()},X=(u,h,y)=>{h.component=u;const E=u.vnode.props;u.vnode=h,u.next=null,Uc(u,h.props,E,y),qc(u,h.children,y),ft(),Rr(u),ut()},V=(u,h,y,E,S,T,N,L,I=!1)=>{const C=u&&u.children,K=u?u.shapeFlag:0,H=h.children,{patchFlag:W,shapeFlag:G}=h;if(W>0){if(W&128){vn(C,H,y,E,S,T,N,L,I);return}else if(W&256){ae(C,H,y,E,S,T,N,L,I);return}}G&8?(K&16&&Bt(C,S,T),H!==C&&a(y,H)):K&16?G&16?vn(C,H,y,E,S,T,N,L,I):Bt(C,S,T,!0):(K&8&&a(y,""),G&16&&j(H,y,E,S,T,N,L,I))},ae=(u,h,y,E,S,T,N,L,I)=>{u=u||Lt,h=h||Lt;const C=u.length,K=h.length,H=Math.min(C,K);let W;for(W=0;W<H;W++){const G=h[W]=I?it(h[W]):Oe(h[W]);b(u[W],G,y,null,S,T,N,L,I)}C>K?Bt(u,S,T,!0,!1,H):j(h,y,E,S,T,N,L,I,H)},vn=(u,h,y,E,S,T,N,L,I)=>{let C=0;const K=h.length;let H=u.length-1,W=K-1;for(;C<=H&&C<=W;){const G=u[C],J=h[C]=I?it(h[C]):Oe(h[C]);if(mt(G,J))b(G,J,y,null,S,T,N,L,I);else break;C++}for(;C<=H&&C<=W;){const G=u[H],J=h[W]=I?it(h[W]):Oe(h[W]);if(mt(G,J))b(G,J,y,null,S,T,N,L,I);else break;H--,W--}if(C>H){if(C<=W){const G=W+1,J=G<K?h[G].el:E;for(;C<=W;)b(null,h[C]=I?it(h[C]):Oe(h[C]),y,J,S,T,N,L,I),C++}}else if(C>W)for(;C<=H;)ke(u[C],S,T,!0),C++;else{const G=C,J=C,ne=new Map;for(C=J;C<=W;C++){const Ce=h[C]=I?it(h[C]):Oe(h[C]);Ce.key!=null&&ne.set(Ce.key,C)}let Z,Te=0;const pe=W-J+1;let He=!1,Ee=0;const Kt=new Array(pe);for(C=0;C<pe;C++)Kt[C]=0;for(C=G;C<=H;C++){const Ce=u[C];if(Te>=pe){ke(Ce,S,T,!0);continue}let Ue;if(Ce.key!=null)Ue=ne.get(Ce.key);else for(Z=J;Z<=W;Z++)if(Kt[Z-J]===0&&mt(Ce,h[Z])){Ue=Z;break}Ue===void 0?ke(Ce,S,T,!0):(Kt[Ue-J]=C+1,Ue>=Ee?Ee=Ue:He=!0,b(Ce,h[Ue],y,null,S,T,N,L,I),Te++)}const Sr=He?Yc(Kt):Lt;for(Z=Sr.length-1,C=pe-1;C>=0;C--){const Ce=J+C,Ue=h[Ce],xr=Ce+1<K?h[Ce+1].el:E;Kt[C]===0?b(null,Ue,y,xr,S,T,N,L,I):He&&(Z<0||C!==Sr[Z]?dt(Ue,y,xr,2):Z--)}}},dt=(u,h,y,E,S=null)=>{const{el:T,type:N,transition:L,children:I,shapeFlag:C}=u;if(C&6){dt(u.component.subTree,h,y,E);return}if(C&128){u.suspense.move(h,y,E);return}if(C&64){N.move(u,h,y,Rt);return}if(N===ve){s(T,h,y);for(let H=0;H<I.length;H++)dt(I[H],h,y,E);s(u.anchor,h,y);return}if(N===en){p(u,h,y);return}if(E!==2&&C&1&&L)if(E===0)L.beforeEnter(T),s(T,h,y),we(()=>L.enter(T),S);else{const{leave:H,delayLeave:W,afterLeave:G}=L,J=()=>s(T,h,y),ne=()=>{H(T,()=>{J(),G&&G()})};W?W(T,J,ne):ne()}else s(T,h,y)},ke=(u,h,y,E=!1,S=!1)=>{const{type:T,props:N,ref:L,children:I,dynamicChildren:C,shapeFlag:K,patchFlag:H,dirs:W,cacheIndex:G}=u;if(H===-2&&(S=!1),L!=null&&cn(L,null,y,u,!0),G!=null&&(h.renderCache[G]=void 0),K&256){h.ctx.deactivate(u);return}const J=K&1&&W,ne=!bt(u);let Z;if(ne&&(Z=N&&N.onVnodeBeforeUnmount)&&Me(Z,h,u),K&6)yl(u.component,y,E);else{if(K&128){u.suspense.unmount(y,E);return}J&&Ke(u,null,h,"beforeUnmount"),K&64?u.type.remove(u,h,y,Rt,E):C&&!C.hasOnce&&(T!==ve||H>0&&H&64)?Bt(C,h,y,!1,!0):(T===ve&&H&384||!S&&K&16)&&Bt(I,h,y),E&&_r(u)}(ne&&(Z=N&&N.onVnodeUnmounted)||J)&&we(()=>{Z&&Me(Z,h,u),J&&Ke(u,null,h,"unmounted")},y)},_r=u=>{const{type:h,el:y,anchor:E,transition:S}=u;if(h===ve){ml(y,E);return}if(h===en){m(u);return}const T=()=>{r(y),S&&!S.persisted&&S.afterLeave&&S.afterLeave()};if(u.shapeFlag&1&&S&&!S.persisted){const{leave:N,delayLeave:L}=S,I=()=>N(y,T);L?L(u.el,T,I):I()}else T()},ml=(u,h)=>{let y;for(;u!==h;)y=g(u),r(u),u=y;r(h)},yl=(u,h,y)=>{const{bum:E,scope:S,job:T,subTree:N,um:L,m:I,a:C}=u;kr(I),kr(C),E&&In(E),S.stop(),T&&(T.flags|=8,ke(N,u,h,y)),L&&we(L,h),we(()=>{u.isUnmounted=!0},h),h&&h.pendingBranch&&!h.isUnmounted&&u.asyncDep&&!u.asyncResolved&&u.suspenseId===h.pendingId&&(h.deps--,h.deps===0&&h.resolve())},Bt=(u,h,y,E=!1,S=!1,T=0)=>{for(let N=T;N<u.length;N++)ke(u[N],h,y,E,S)},bn=u=>{if(u.shapeFlag&6)return bn(u.component.subTree);if(u.shapeFlag&128)return u.suspense.next();const h=g(u.anchor||u.el),y=h&&h[Qi];return y?g(y):h};let cs=!1;const wr=(u,h,y)=>{u==null?h._vnode&&ke(h._vnode,null,null,!0):b(h._vnode||null,u,h,null,null,null,y),h._vnode=u,cs||(cs=!0,Rr(),Un(),cs=!1)},Rt={p:b,um:ke,m:dt,r:_r,mt:Y,mc:j,pc:V,pbc:M,n:bn,o:e};let as,fs;return t&&([as,fs]=t(Rt)),{render:wr,hydrate:as,createApp:jc(wr,as)}}function vs({type:e,props:t},n){return n==="svg"&&e==="foreignObject"||n==="mathml"&&e==="annotation-xml"&&t&&t.encoding&&t.encoding.includes("html")?void 0:n}function ht({effect:e,job:t},n){n?(e.flags|=32,t.flags|=4):(e.flags&=-33,t.flags&=-5)}function Ro(e,t){return(!e||e&&!e.pendingBranch)&&t&&!t.persisted}function gr(e,t,n=!1){const s=e.children,r=t.children;if(B(s)&&B(r))for(let i=0;i<s.length;i++){const o=s[i];let l=r[i];l.shapeFlag&1&&!l.dynamicChildren&&((l.patchFlag<=0||l.patchFlag===32)&&(l=r[i]=it(r[i]),l.el=o.el),!n&&l.patchFlag!==-2&&gr(o,l)),l.type===St&&(l.el=o.el)}}function Yc(e){const t=e.slice(),n=[0];let s,r,i,o,l;const c=e.length;for(s=0;s<c;s++){const f=e[s];if(f!==0){if(r=n[n.length-1],e[r]<f){t[s]=r,n.push(s);continue}for(i=0,o=n.length-1;i<o;)l=i+o>>1,e[n[l]]<f?i=l+1:o=l;f<e[n[i]]&&(i>0&&(t[s]=n[i-1]),n[i]=s)}}for(i=n.length,o=n[i-1];i-- >0;)n[i]=o,o=t[o];return n}function Mo(e){const t=e.subTree.component;if(t)return t.asyncDep&&!t.asyncResolved?t:Mo(t)}function kr(e){if(e)for(let t=0;t<e.length;t++)e[t].flags|=8}const Jc=Symbol.for("v-scx"),zc=()=>wt(Jc);function Oo(e,t){return rs(e,null,t)}function pu(e,t){return rs(e,null,{flush:"post"})}function Le(e,t,n){return rs(e,t,n)}function rs(e,t,n=te){const{immediate:s,deep:r,flush:i,once:o}=n,l=he({},n),c=t&&s||!t&&i!=="post";let f;if($t){if(i==="sync"){const v=zc();f=v.__watcherHandles||(v.__watcherHandles=[])}else if(!c){const v=()=>{};return v.stop=De,v.resume=De,v.pause=De,v}}const a=fe;l.call=(v,_,b)=>Ve(v,a,_,b);let d=!1;i==="post"?l.scheduler=v=>{we(v,a&&a.suspense)}:i!=="sync"&&(d=!0,l.scheduler=(v,_)=>{_?v():cr(v)}),l.augmentJob=v=>{t&&(v.flags|=4),d&&(v.flags|=2,a&&(v.id=a.uid,v.i=a))};const g=ac(e,t,l);return $t&&(f?f.push(g):c&&g()),g}function Qc(e,t,n){const s=this.proxy,r=oe(e)?e.includes(".")?Po(s,e):()=>s[e]:e.bind(s,s);let i;q(t)?i=t:(i=t.handler,n=t);const o=yn(this),l=rs(r,i.bind(s),n);return o(),l}function Po(e,t){const n=t.split(".");return()=>{let s=e;for(let r=0;r<n.length&&s;r++)s=s[n[r]];return s}}const Zc=(e,t)=>t==="modelValue"||t==="model-value"?e.modelModifiers:e[`${t}Modifiers`]||e[`${Fe(t)}Modifiers`]||e[`${at(t)}Modifiers`];function ea(e,t,...n){if(e.isUnmounted)return;const s=e.vnode.props||te;let r=n;const i=t.startsWith("update:"),o=i&&Zc(s,t.slice(7));o&&(o.trim&&(r=n.map(a=>oe(a)?a.trim():a)),o.number&&(r=n.map($n)));let l,c=s[l=Pn(t)]||s[l=Pn(Fe(t))];!c&&i&&(c=s[l=Pn(at(t))]),c&&Ve(c,e,6,r);const f=s[l+"Once"];if(f){if(!e.emitted)e.emitted={};else if(e.emitted[l])return;e.emitted[l]=!0,Ve(f,e,6,r)}}function Io(e,t,n=!1){const s=t.emitsCache,r=s.get(e);if(r!==void 0)return r;const i=e.emits;let o={},l=!1;if(!q(e)){const c=f=>{const a=Io(f,t,!0);a&&(l=!0,he(o,a))};!n&&t.mixins.length&&t.mixins.forEach(c),e.extends&&c(e.extends),e.mixins&&e.mixins.forEach(c)}return!i&&!l?(ee(e)&&s.set(e,null),null):(B(i)?i.forEach(c=>o[c]=null):he(o,i),ee(e)&&s.set(e,o),o)}function is(e,t){return!e||!un(t)?!1:(t=t.slice(2).replace(/Once$/,""),Q(e,t[0].toLowerCase()+t.slice(1))||Q(e,at(t))||Q(e,t))}function bs(e){const{type:t,vnode:n,proxy:s,withProxy:r,propsOptions:[i],slots:o,attrs:l,emit:c,render:f,renderCache:a,props:d,data:g,setupState:v,ctx:_,inheritAttrs:b}=e,k=Wn(e);let P,D;try{if(n.shapeFlag&4){const m=r||s,O=m;P=Oe(f.call(O,m,a,d,v,g,_)),D=l}else{const m=t;P=Oe(m.length>1?m(d,{attrs:l,slots:o,emit:c}):m(d,null)),D=t.props?l:ta(l)}}catch(m){tn.length=0,pn(m,e,1),P=le(be)}let p=P;if(D&&b!==!1){const m=Object.keys(D),{shapeFlag:O}=p;m.length&&O&7&&(i&&m.some(zs)&&(D=na(D,i)),p=lt(p,D,!1,!0))}return n.dirs&&(p=lt(p,null,!1,!0),p.dirs=p.dirs?p.dirs.concat(n.dirs):n.dirs),n.transition&&Ct(p,n.transition),P=p,Wn(k),P}const ta=e=>{let t;for(const n in e)(n==="class"||n==="style"||un(n))&&((t||(t={}))[n]=e[n]);return t},na=(e,t)=>{const n={};for(const s in e)(!zs(s)||!(s.slice(9)in t))&&(n[s]=e[s]);return n};function sa(e,t,n){const{props:s,children:r,component:i}=e,{props:o,children:l,patchFlag:c}=t,f=i.emitsOptions;if(t.dirs||t.transition)return!0;if(n&&c>=0){if(c&1024)return!0;if(c&16)return s?Ur(s,o,f):!!o;if(c&8){const a=t.dynamicProps;for(let d=0;d<a.length;d++){const g=a[d];if(o[g]!==s[g]&&!is(f,g))return!0}}}else return(r||l)&&(!l||!l.$stable)?!0:s===o?!1:s?o?Ur(s,o,f):!0:!!o;return!1}function Ur(e,t,n){const s=Object.keys(t);if(s.length!==Object.keys(e).length)return!0;for(let r=0;r<s.length;r++){const i=s[r];if(t[i]!==e[i]&&!is(n,i))return!0}return!1}function Lo({vnode:e,parent:t},n){for(;t;){const s=t.subTree;if(s.suspense&&s.suspense.activeBranch===e&&(s.el=e.el),s===e)(e=t.vnode).el=n,t=t.parent;else break}}const No=e=>e.__isSuspense;function Fo(e,t){t&&t.pendingBranch?B(e)?t.effects.push(...e):t.effects.push(e):dc(e)}const ve=Symbol.for("v-fgt"),St=Symbol.for("v-txt"),be=Symbol.for("v-cmt"),en=Symbol.for("v-stc"),tn=[];let Ae=null;function Us(e=!1){tn.push(Ae=e?null:[])}function ra(){tn.pop(),Ae=tn[tn.length-1]||null}let an=1;function Wr(e,t=!1){an+=e,e<0&&Ae&&t&&(Ae.hasOnce=!0)}function Ho(e){return e.dynamicChildren=an>0?Ae||Lt:null,ra(),an>0&&Ae&&Ae.push(e),e}function gu(e,t,n,s,r,i){return Ho($o(e,t,n,s,r,i,!0))}function Ws(e,t,n,s,r){return Ho(le(e,t,n,s,r,!0))}function fn(e){return e?e.__v_isVNode===!0:!1}function mt(e,t){return e.type===t.type&&e.key===t.key}const Do=({key:e})=>e??null,Fn=({ref:e,ref_key:t,ref_for:n})=>(typeof e=="number"&&(e=""+e),e!=null?oe(e)||de(e)||q(e)?{i:ue,r:e,k:t,f:!!n}:e:null);function $o(e,t=null,n=null,s=0,r=null,i=e===ve?0:1,o=!1,l=!1){const c={__v_isVNode:!0,__v_skip:!0,type:e,props:t,key:t&&Do(t),ref:t&&Fn(t),scopeId:zi,slotScopeIds:null,children:n,component:null,suspense:null,ssContent:null,ssFallback:null,dirs:null,transition:null,el:null,anchor:null,target:null,targetStart:null,targetAnchor:null,staticCount:0,shapeFlag:i,patchFlag:s,dynamicProps:r,dynamicChildren:null,appContext:null,ctx:ue};return l?(mr(c,n),i&128&&e.normalize(c)):n&&(c.shapeFlag|=oe(n)?8:16),an>0&&!o&&Ae&&(c.patchFlag>0||i&6)&&c.patchFlag!==32&&Ae.push(c),c}const le=ia;function ia(e,t=null,n=null,s=0,r=null,i=!1){if((!e||e===fo)&&(e=be),fn(e)){const l=lt(e,t,!0);return n&&mr(l,n),an>0&&!i&&Ae&&(l.shapeFlag&6?Ae[Ae.indexOf(e)]=l:Ae.push(l)),l.patchFlag=-2,l}if(ga(e)&&(e=e.__vccOpts),t){t=oa(t);let{class:l,style:c}=t;l&&!oe(l)&&(t.class=Qn(l)),ee(c)&&(or(c)&&!B(c)&&(c=he({},c)),t.style=zn(c))}const o=oe(e)?1:No(e)?128:Zi(e)?64:ee(e)?4:q(e)?2:0;return $o(e,t,n,s,r,o,i,!0)}function oa(e){return e?or(e)||_o(e)?he({},e):e:null}function lt(e,t,n=!1,s=!1){const{props:r,ref:i,patchFlag:o,children:l,transition:c}=e,f=t?la(r||{},t):r,a={__v_isVNode:!0,__v_skip:!0,type:e.type,props:f,key:f&&Do(f),ref:t&&t.ref?n&&i?B(i)?i.concat(Fn(t)):[i,Fn(t)]:Fn(t):i,scopeId:e.scopeId,slotScopeIds:e.slotScopeIds,children:l,target:e.target,targetStart:e.targetStart,targetAnchor:e.targetAnchor,staticCount:e.staticCount,shapeFlag:e.shapeFlag,patchFlag:t&&e.type!==ve?o===-1?16:o|16:o,dynamicProps:e.dynamicProps,dynamicChildren:e.dynamicChildren,appContext:e.appContext,dirs:e.dirs,transition:c,component:e.component,suspense:e.suspense,ssContent:e.ssContent&&lt(e.ssContent),ssFallback:e.ssFallback&&lt(e.ssFallback),el:e.el,anchor:e.anchor,ctx:e.ctx,ce:e.ce};return c&&s&&Ct(a,c.clone(a)),a}function jo(e=" ",t=0){return le(St,null,e,t)}function mu(e,t){const n=le(en,null,e);return n.staticCount=t,n}function yu(e="",t=!1){return t?(Us(),Ws(be,null,e)):le(be,null,e)}function Oe(e){return e==null||typeof e=="boolean"?le(be):B(e)?le(ve,null,e.slice()):fn(e)?it(e):le(St,null,String(e))}function it(e){return e.el===null&&e.patchFlag!==-1||e.memo?e:lt(e)}function mr(e,t){let n=0;const{shapeFlag:s}=e;if(t==null)t=null;else if(B(t))n=16;else if(typeof t=="object")if(s&65){const r=t.default;r&&(r._c&&(r._d=!1),mr(e,r()),r._c&&(r._d=!0));return}else{n=32;const r=t._;!r&&!_o(t)?t._ctx=ue:r===3&&ue&&(ue.slots._===1?t._=1:(t._=2,e.patchFlag|=1024))}else q(t)?(t={default:t,_ctx:ue},n=32):(t=String(t),s&64?(n=16,t=[jo(t)]):n=8);e.children=t,e.shapeFlag|=n}function la(...e){const t={};for(let n=0;n<e.length;n++){const s=e[n];for(const r in s)if(r==="class")t.class!==s.class&&(t.class=Qn([t.class,s.class]));else if(r==="style")t.style=zn([t.style,s.style]);else if(un(r)){const i=t[r],o=s[r];o&&i!==o&&!(B(i)&&i.includes(o))&&(t[r]=i?[].concat(i,o):o)}else r!==""&&(t[r]=s[r])}return t}function Me(e,t,n,s=null){Ve(e,t,7,[n,s])}const ca=mo();let aa=0;function fa(e,t,n){const s=e.type,r=(t?t.appContext:e.appContext)||ca,i={uid:aa++,vnode:e,type:s,parent:t,appContext:r,root:null,next:null,subTree:null,effect:null,update:null,job:null,scope:new Mi(!0),render:null,proxy:null,exposed:null,exposeProxy:null,withProxy:null,provides:t?t.provides:Object.create(r.provides),ids:t?t.ids:["",0,0],accessCache:null,renderCache:[],components:null,directives:null,propsOptions:So(s,r),emitsOptions:Io(s,r),emit:null,emitted:null,propsDefaults:te,inheritAttrs:s.inheritAttrs,ctx:te,data:te,props:te,attrs:te,slots:te,refs:te,setupState:te,setupContext:null,suspense:n,suspenseId:n?n.pendingId:0,asyncDep:null,asyncResolved:!1,isMounted:!1,isUnmounted:!1,isDeactivated:!1,bc:null,c:null,bm:null,m:null,bu:null,u:null,um:null,bum:null,da:null,a:null,rtg:null,rtc:null,ec:null,sp:null};return i.ctx={_:i},i.root=t?t.root:i,i.emit=ea.bind(null,i),e.ce&&e.ce(i),i}let fe=null;const Wt=()=>fe||ue;let Kn,Bs;{const e=Jn(),t=(n,s)=>{let r;return(r=e[n])||(r=e[n]=[]),r.push(s),i=>{r.length>1?r.forEach(o=>o(i)):r[0](i)}};Kn=t("__VUE_INSTANCE_SETTERS__",n=>fe=n),Bs=t("__VUE_SSR_SETTERS__",n=>$t=n)}const yn=e=>{const t=fe;return Kn(e),e.scope.on(),()=>{e.scope.off(),Kn(t)}},Br=()=>{fe&&fe.scope.off(),Kn(null)};function Vo(e){return e.vnode.shapeFlag&4}let $t=!1;function ua(e,t=!1,n=!1){t&&Bs(t);const{props:s,children:r}=e.vnode,i=Vo(e);kc(e,s,i,t),Kc(e,r,n);const o=i?da(e,t):void 0;return t&&Bs(!1),o}function da(e,t){const n=e.type;e.accessCache=Object.create(null),e.proxy=new Proxy(e.ctx,Ic);const{setup:s}=n;if(s){ft();const r=e.setupContext=s.length>1?Uo(e):null,i=yn(e),o=hn(s,e,0,[e.props,r]),l=Si(o);if(ut(),i(),(l||e.sp)&&!bt(e)&&ur(e),l){if(o.then(Br,Br),t)return o.then(c=>{Kr(e,c)}).catch(c=>{pn(c,e,0)});e.asyncDep=o}else Kr(e,o)}else ko(e)}function Kr(e,t,n){q(t)?e.type.__ssrInlineRender?e.ssrRender=t:e.render=t:ee(t)&&(e.setupState=qi(t)),ko(e)}function ko(e,t,n){const s=e.type;e.render||(e.render=s.render||De);{const r=yn(e);ft();try{Lc(e)}finally{ut(),r()}}}const ha={get(e,t){return me(e,"get",""),e[t]}};function Uo(e){const t=n=>{e.exposed=n||{}};return{attrs:new Proxy(e.attrs,ha),slots:e.slots,emit:e.emit,expose:t}}function os(e){return e.exposed?e.exposeProxy||(e.exposeProxy=new Proxy(qi(Ln(e.exposed)),{get(t,n){if(n in t)return t[n];if(n in Zt)return Zt[n](e)},has(t,n){return n in t||n in Zt}})):e.proxy}function pa(e,t=!0){return q(e)?e.displayName||e.name:e.name||t&&e.__name}function ga(e){return q(e)&&"__vccOpts"in e}const ie=(e,t)=>lc(e,t,$t);function Ks(e,t,n){const s=arguments.length;return s===2?ee(t)&&!B(t)?fn(t)?le(e,null,[t]):le(e,t):le(e,null,t):(s>3?n=Array.prototype.slice.call(arguments,2):s===3&&fn(n)&&(n=[n]),le(e,t,n))}const ma="3.5.13",vu=De;/**
* @vue/runtime-dom v3.5.13
* (c) 2018-present Yuxi (Evan) You and Vue contributors
* @license MIT
**/let qs;const qr=typeof window<"u"&&window.trustedTypes;if(qr)try{qs=qr.createPolicy("vue",{createHTML:e=>e})}catch{}const Wo=qs?e=>qs.createHTML(e):e=>e,ya="http://www.w3.org/2000/svg",va="http://www.w3.org/1998/Math/MathML",Xe=typeof document<"u"?document:null,Gr=Xe&&Xe.createElement("template"),ba={insert:(e,t,n)=>{t.insertBefore(e,n||null)},remove:e=>{const t=e.parentNode;t&&t.removeChild(e)},createElement:(e,t,n,s)=>{const r=t==="svg"?Xe.createElementNS(ya,e):t==="mathml"?Xe.createElementNS(va,e):n?Xe.createElement(e,{is:n}):Xe.createElement(e);return e==="select"&&s&&s.multiple!=null&&r.setAttribute("multiple",s.multiple),r},createText:e=>Xe.createTextNode(e),createComment:e=>Xe.createComment(e),setText:(e,t)=>{e.nodeValue=t},setElementText:(e,t)=>{e.textContent=t},parentNode:e=>e.parentNode,nextSibling:e=>e.nextSibling,querySelector:e=>Xe.querySelector(e),setScopeId(e,t){e.setAttribute(t,"")},insertStaticContent(e,t,n,s,r,i){const o=n?n.previousSibling:t.lastChild;if(r&&(r===i||r.nextSibling))for(;t.insertBefore(r.cloneNode(!0),n),!(r===i||!(r=r.nextSibling)););else{Gr.innerHTML=Wo(s==="svg"?`<svg>${e}</svg>`:s==="mathml"?`<math>${e}</math>`:e);const l=Gr.content;if(s==="svg"||s==="mathml"){const c=l.firstChild;for(;c.firstChild;)l.appendChild(c.firstChild);l.removeChild(c)}t.insertBefore(l,n)}return[o?o.nextSibling:t.firstChild,n?n.previousSibling:t.lastChild]}},tt="transition",Gt="animation",jt=Symbol("_vtc"),Bo={name:String,type:String,css:{type:Boolean,default:!0},duration:[String,Number,Object],enterFromClass:String,enterActiveClass:String,enterToClass:String,appearFromClass:String,appearActiveClass:String,appearToClass:String,leaveFromClass:String,leaveActiveClass:String,leaveToClass:String},Ko=he({},so,Bo),_a=e=>(e.displayName="Transition",e.props=Ko,e),bu=_a((e,{slots:t})=>Ks(mc,qo(e),t)),pt=(e,t=[])=>{B(e)?e.forEach(n=>n(...t)):e&&e(...t)},Xr=e=>e?B(e)?e.some(t=>t.length>1):e.length>1:!1;function qo(e){const t={};for(const w in e)w in Bo||(t[w]=e[w]);if(e.css===!1)return t;const{name:n="v",type:s,duration:r,enterFromClass:i=`${n}-enter-from`,enterActiveClass:o=`${n}-enter-active`,enterToClass:l=`${n}-enter-to`,appearFromClass:c=i,appearActiveClass:f=o,appearToClass:a=l,leaveFromClass:d=`${n}-leave-from`,leaveActiveClass:g=`${n}-leave-active`,leaveToClass:v=`${n}-leave-to`}=e,_=wa(r),b=_&&_[0],k=_&&_[1],{onBeforeEnter:P,onEnter:D,onEnterCancelled:p,onLeave:m,onLeaveCancelled:O,onBeforeAppear:$=P,onAppear:R=D,onAppearCancelled:j=p}=t,x=(w,F,Y,re)=>{w._enterCancelled=re,nt(w,F?a:l),nt(w,F?f:o),Y&&Y()},M=(w,F)=>{w._isLeaving=!1,nt(w,d),nt(w,v),nt(w,g),F&&F()},A=w=>(F,Y)=>{const re=w?R:D,U=()=>x(F,w,Y);pt(re,[F,U]),Yr(()=>{nt(F,w?c:i),We(F,w?a:l),Xr(re)||Jr(F,s,b,U)})};return he(t,{onBeforeEnter(w){pt(P,[w]),We(w,i),We(w,o)},onBeforeAppear(w){pt($,[w]),We(w,c),We(w,f)},onEnter:A(!1),onAppear:A(!0),onLeave(w,F){w._isLeaving=!0;const Y=()=>M(w,F);We(w,d),w._enterCancelled?(We(w,g),Gs()):(Gs(),We(w,g)),Yr(()=>{w._isLeaving&&(nt(w,d),We(w,v),Xr(m)||Jr(w,s,k,Y))}),pt(m,[w,Y])},onEnterCancelled(w){x(w,!1,void 0,!0),pt(p,[w])},onAppearCancelled(w){x(w,!0,void 0,!0),pt(j,[w])},onLeaveCancelled(w){M(w),pt(O,[w])}})}function wa(e){if(e==null)return null;if(ee(e))return[_s(e.enter),_s(e.leave)];{const t=_s(e);return[t,t]}}function _s(e){return xl(e)}function We(e,t){t.split(/\s+/).forEach(n=>n&&e.classList.add(n)),(e[jt]||(e[jt]=new Set)).add(t)}function nt(e,t){t.split(/\s+/).forEach(s=>s&&e.classList.remove(s));const n=e[jt];n&&(n.delete(t),n.size||(e[jt]=void 0))}function Yr(e){requestAnimationFrame(()=>{requestAnimationFrame(e)})}let Sa=0;function Jr(e,t,n,s){const r=e._endId=++Sa,i=()=>{r===e._endId&&s()};if(n!=null)return setTimeout(i,n);const{type:o,timeout:l,propCount:c}=Go(e,t);if(!o)return s();const f=o+"end";let a=0;const d=()=>{e.removeEventListener(f,g),i()},g=v=>{v.target===e&&++a>=c&&d()};setTimeout(()=>{a<c&&d()},l+1),e.addEventListener(f,g)}function Go(e,t){const n=window.getComputedStyle(e),s=_=>(n[_]||"").split(", "),r=s(`${tt}Delay`),i=s(`${tt}Duration`),o=zr(r,i),l=s(`${Gt}Delay`),c=s(`${Gt}Duration`),f=zr(l,c);let a=null,d=0,g=0;t===tt?o>0&&(a=tt,d=o,g=i.length):t===Gt?f>0&&(a=Gt,d=f,g=c.length):(d=Math.max(o,f),a=d>0?o>f?tt:Gt:null,g=a?a===tt?i.length:c.length:0);const v=a===tt&&/\b(transform|all)(,|$)/.test(s(`${tt}Property`).toString());return{type:a,timeout:d,propCount:g,hasTransform:v}}function zr(e,t){for(;e.length<t.length;)e=e.concat(e);return Math.max(...t.map((n,s)=>Qr(n)+Qr(e[s])))}function Qr(e){return e==="auto"?0:Number(e.slice(0,-1).replace(",","."))*1e3}function Gs(){return document.body.offsetHeight}function xa(e,t,n){const s=e[jt];s&&(t=(t?[t,...s]:[...s]).join(" ")),t==null?e.removeAttribute("class"):n?e.setAttribute("class",t):e.className=t}const qn=Symbol("_vod"),Xo=Symbol("_vsh"),_u={beforeMount(e,{value:t},{transition:n}){e[qn]=e.style.display==="none"?"":e.style.display,n&&t?n.beforeEnter(e):Xt(e,t)},mounted(e,{value:t},{transition:n}){n&&t&&n.enter(e)},updated(e,{value:t,oldValue:n},{transition:s}){!t!=!n&&(s?t?(s.beforeEnter(e),Xt(e,!0),s.enter(e)):s.leave(e,()=>{Xt(e,!1)}):Xt(e,t))},beforeUnmount(e,{value:t}){Xt(e,t)}};function Xt(e,t){e.style.display=t?e[qn]:"none",e[Xo]=!t}const Ta=Symbol(""),Ea=/(^|;)\s*display\s*:/;function Ca(e,t,n){const s=e.style,r=oe(n);let i=!1;if(n&&!r){if(t)if(oe(t))for(const o of t.split(";")){const l=o.slice(0,o.indexOf(":")).trim();n[l]==null&&Hn(s,l,"")}else for(const o in t)n[o]==null&&Hn(s,o,"");for(const o in n)o==="display"&&(i=!0),Hn(s,o,n[o])}else if(r){if(t!==n){const o=s[Ta];o&&(n+=";"+o),s.cssText=n,i=Ea.test(n)}}else t&&e.removeAttribute("style");qn in e&&(e[qn]=i?s.display:"",e[Xo]&&(s.display="none"))}const Zr=/\s*!important$/;function Hn(e,t,n){if(B(n))n.forEach(s=>Hn(e,t,s));else if(n==null&&(n=""),t.startsWith("--"))e.setProperty(t,n);else{const s=Aa(e,t);Zr.test(n)?e.setProperty(at(s),n.replace(Zr,""),"important"):e[s]=n}}const ei=["Webkit","Moz","ms"],ws={};function Aa(e,t){const n=ws[t];if(n)return n;let s=Fe(t);if(s!=="filter"&&s in e)return ws[t]=s;s=Yn(s);for(let r=0;r<ei.length;r++){const i=ei[r]+s;if(i in e)return ws[t]=i}return t}const ti="http://www.w3.org/1999/xlink";function ni(e,t,n,s,r,i=Ml(t)){s&&t.startsWith("xlink:")?n==null?e.removeAttributeNS(ti,t.slice(6,t.length)):e.setAttributeNS(ti,t,n):n==null||i&&!Ci(n)?e.removeAttribute(t):e.setAttribute(t,i?"":je(n)?String(n):n)}function si(e,t,n,s,r){if(t==="innerHTML"||t==="textContent"){n!=null&&(e[t]=t==="innerHTML"?Wo(n):n);return}const i=e.tagName;if(t==="value"&&i!=="PROGRESS"&&!i.includes("-")){const l=i==="OPTION"?e.getAttribute("value")||"":e.value,c=n==null?e.type==="checkbox"?"on":"":String(n);(l!==c||!("_value"in e))&&(e.value=c),n==null&&e.removeAttribute(t),e._value=n;return}let o=!1;if(n===""||n==null){const l=typeof e[t];l==="boolean"?n=Ci(n):n==null&&l==="string"?(n="",o=!0):l==="number"&&(n=0,o=!0)}try{e[t]=n}catch{}o&&e.removeAttribute(r||t)}function ze(e,t,n,s){e.addEventListener(t,n,s)}function Ra(e,t,n,s){e.removeEventListener(t,n,s)}const ri=Symbol("_vei");function Ma(e,t,n,s,r=null){const i=e[ri]||(e[ri]={}),o=i[t];if(s&&o)o.value=s;else{const[l,c]=Oa(t);if(s){const f=i[t]=La(s,r);ze(e,l,f,c)}else o&&(Ra(e,l,o,c),i[t]=void 0)}}const ii=/(?:Once|Passive|Capture)$/;function Oa(e){let t;if(ii.test(e)){t={};let s;for(;s=e.match(ii);)e=e.slice(0,e.length-s[0].length),t[s[0].toLowerCase()]=!0}return[e[2]===":"?e.slice(3):at(e.slice(2)),t]}let Ss=0;const Pa=Promise.resolve(),Ia=()=>Ss||(Pa.then(()=>Ss=0),Ss=Date.now());function La(e,t){const n=s=>{if(!s._vts)s._vts=Date.now();else if(s._vts<=n.attached)return;Ve(Na(s,n.value),t,5,[s])};return n.value=e,n.attached=Ia(),n}function Na(e,t){if(B(t)){const n=e.stopImmediatePropagation;return e.stopImmediatePropagation=()=>{n.call(e),e._stopped=!0},t.map(s=>r=>!r._stopped&&s&&s(r))}else return t}const oi=e=>e.charCodeAt(0)===111&&e.charCodeAt(1)===110&&e.charCodeAt(2)>96&&e.charCodeAt(2)<123,Fa=(e,t,n,s,r,i)=>{const o=r==="svg";t==="class"?xa(e,s,o):t==="style"?Ca(e,n,s):un(t)?zs(t)||Ma(e,t,n,s,i):(t[0]==="."?(t=t.slice(1),!0):t[0]==="^"?(t=t.slice(1),!1):Ha(e,t,s,o))?(si(e,t,s),!e.tagName.includes("-")&&(t==="value"||t==="checked"||t==="selected")&&ni(e,t,s,o,i,t!=="value")):e._isVueCE&&(/[A-Z]/.test(t)||!oe(s))?si(e,Fe(t),s,i,t):(t==="true-value"?e._trueValue=s:t==="false-value"&&(e._falseValue=s),ni(e,t,s,o))};function Ha(e,t,n,s){if(s)return!!(t==="innerHTML"||t==="textContent"||t in e&&oi(t)&&q(n));if(t==="spellcheck"||t==="draggable"||t==="translate"||t==="form"||t==="list"&&e.tagName==="INPUT"||t==="type"&&e.tagName==="TEXTAREA")return!1;if(t==="width"||t==="height"){const r=e.tagName;if(r==="IMG"||r==="VIDEO"||r==="CANVAS"||r==="SOURCE")return!1}return oi(t)&&oe(n)?!1:t in e}const Yo=new WeakMap,Jo=new WeakMap,Gn=Symbol("_moveCb"),li=Symbol("_enterCb"),Da=e=>(delete e.props.mode,e),$a=Da({name:"TransitionGroup",props:he({},Ko,{tag:String,moveClass:String}),setup(e,{slots:t}){const n=Wt(),s=no();let r,i;return co(()=>{if(!r.length)return;const o=e.moveClass||`${e.name||"v"}-move`;if(!Ua(r[0].el,n.vnode.el,o))return;r.forEach(ja),r.forEach(Va);const l=r.filter(ka);Gs(),l.forEach(c=>{const f=c.el,a=f.style;We(f,o),a.transform=a.webkitTransform=a.transitionDuration="";const d=f[Gn]=g=>{g&&g.target!==f||(!g||/transform$/.test(g.propertyName))&&(f.removeEventListener("transitionend",d),f[Gn]=null,nt(f,o))};f.addEventListener("transitionend",d)})}),()=>{const o=z(e),l=qo(o);let c=o.tag||ve;if(r=[],i)for(let f=0;f<i.length;f++){const a=i[f];a.el&&a.el instanceof Element&&(r.push(a),Ct(a,ln(a,l,s,n)),Yo.set(a,a.el.getBoundingClientRect()))}i=t.default?ar(t.default()):[];for(let f=0;f<i.length;f++){const a=i[f];a.key!=null&&Ct(a,ln(a,l,s,n))}return le(c,null,i)}}}),wu=$a;function ja(e){const t=e.el;t[Gn]&&t[Gn](),t[li]&&t[li]()}function Va(e){Jo.set(e,e.el.getBoundingClientRect())}function ka(e){const t=Yo.get(e),n=Jo.get(e),s=t.left-n.left,r=t.top-n.top;if(s||r){const i=e.el.style;return i.transform=i.webkitTransform=`translate(${s}px,${r}px)`,i.transitionDuration="0s",e}}function Ua(e,t,n){const s=e.cloneNode(),r=e[jt];r&&r.forEach(l=>{l.split(/\s+/).forEach(c=>c&&s.classList.remove(c))}),n.split(/\s+/).forEach(l=>l&&s.classList.add(l)),s.style.display="none";const i=t.nodeType===1?t:t.parentNode;i.appendChild(s);const{hasTransform:o}=Go(s);return i.removeChild(s),o}const ct=e=>{const t=e.props["onUpdate:modelValue"]||!1;return B(t)?n=>In(t,n):t};function Wa(e){e.target.composing=!0}function ci(e){const t=e.target;t.composing&&(t.composing=!1,t.dispatchEvent(new Event("input")))}const Ne=Symbol("_assign"),Su={created(e,{modifiers:{lazy:t,trim:n,number:s}},r){e[Ne]=ct(r);const i=s||r.props&&r.props.type==="number";ze(e,t?"change":"input",o=>{if(o.target.composing)return;let l=e.value;n&&(l=l.trim()),i&&(l=$n(l)),e[Ne](l)}),n&&ze(e,"change",()=>{e.value=e.value.trim()}),t||(ze(e,"compositionstart",Wa),ze(e,"compositionend",ci),ze(e,"change",ci))},mounted(e,{value:t}){e.value=t??""},beforeUpdate(e,{value:t,oldValue:n,modifiers:{lazy:s,trim:r,number:i}},o){if(e[Ne]=ct(o),e.composing)return;const l=(i||e.type==="number")&&!/^0\d/.test(e.value)?$n(e.value):e.value,c=t??"";l!==c&&(document.activeElement===e&&e.type!=="range"&&(s&&t===n||r&&e.value.trim()===c)||(e.value=c))}},xu={deep:!0,created(e,t,n){e[Ne]=ct(n),ze(e,"change",()=>{const s=e._modelValue,r=Vt(e),i=e.checked,o=e[Ne];if(B(s)){const l=er(s,r),c=l!==-1;if(i&&!c)o(s.concat(r));else if(!i&&c){const f=[...s];f.splice(l,1),o(f)}}else if(kt(s)){const l=new Set(s);i?l.add(r):l.delete(r),o(l)}else o(zo(e,i))})},mounted:ai,beforeUpdate(e,t,n){e[Ne]=ct(n),ai(e,t,n)}};function ai(e,{value:t,oldValue:n},s){e._modelValue=t;let r;if(B(t))r=er(t,s.props.value)>-1;else if(kt(t))r=t.has(s.props.value);else{if(t===n)return;r=Tt(t,zo(e,!0))}e.checked!==r&&(e.checked=r)}const Tu={created(e,{value:t},n){e.checked=Tt(t,n.props.value),e[Ne]=ct(n),ze(e,"change",()=>{e[Ne](Vt(e))})},beforeUpdate(e,{value:t,oldValue:n},s){e[Ne]=ct(s),t!==n&&(e.checked=Tt(t,s.props.value))}},Eu={deep:!0,created(e,{value:t,modifiers:{number:n}},s){const r=kt(t);ze(e,"change",()=>{const i=Array.prototype.filter.call(e.options,o=>o.selected).map(o=>n?$n(Vt(o)):Vt(o));e[Ne](e.multiple?r?new Set(i):i:i[0]),e._assigning=!0,gn(()=>{e._assigning=!1})}),e[Ne]=ct(s)},mounted(e,{value:t}){fi(e,t)},beforeUpdate(e,t,n){e[Ne]=ct(n)},updated(e,{value:t}){e._assigning||fi(e,t)}};function fi(e,t){const n=e.multiple,s=B(t);if(!(n&&!s&&!kt(t))){for(let r=0,i=e.options.length;r<i;r++){const o=e.options[r],l=Vt(o);if(n)if(s){const c=typeof l;c==="string"||c==="number"?o.selected=t.some(f=>String(f)===String(l)):o.selected=er(t,l)>-1}else o.selected=t.has(l);else if(Tt(Vt(o),t)){e.selectedIndex!==r&&(e.selectedIndex=r);return}}!n&&e.selectedIndex!==-1&&(e.selectedIndex=-1)}}function Vt(e){return"_value"in e?e._value:e.value}function zo(e,t){const n=t?"_trueValue":"_falseValue";return n in e?e[n]:t}const Ba=["ctrl","shift","alt","meta"],Ka={stop:e=>e.stopPropagation(),prevent:e=>e.preventDefault(),self:e=>e.target!==e.currentTarget,ctrl:e=>!e.ctrlKey,shift:e=>!e.shiftKey,alt:e=>!e.altKey,meta:e=>!e.metaKey,left:e=>"button"in e&&e.button!==0,middle:e=>"button"in e&&e.button!==1,right:e=>"button"in e&&e.button!==2,exact:(e,t)=>Ba.some(n=>e[`${n}Key`]&&!t.includes(n))},Cu=(e,t)=>{const n=e._withMods||(e._withMods={}),s=t.join(".");return n[s]||(n[s]=(r,...i)=>{for(let o=0;o<t.length;o++){const l=Ka[t[o]];if(l&&l(r,t))return}return e(r,...i)})},qa={esc:"escape",space:" ",up:"arrow-up",left:"arrow-left",right:"arrow-right",down:"arrow-down",delete:"backspace"},Au=(e,t)=>{const n=e._withKeys||(e._withKeys={}),s=t.join(".");return n[s]||(n[s]=r=>{if(!("key"in r))return;const i=at(r.key);if(t.some(o=>o===i||qa[o]===i))return e(r)})},Qo=he({patchProp:Fa},ba);let nn,ui=!1;function Zo(){return nn||(nn=Gc(Qo))}function Ga(){return nn=ui?nn:Xc(Qo),ui=!0,nn}const Ru=(...e)=>{Zo().render(...e)},Mu=(...e)=>{const t=Zo().createApp(...e),{mount:n}=t;return t.mount=s=>{const r=tl(s);if(!r)return;const i=t._component;!q(i)&&!i.render&&!i.template&&(i.template=r.innerHTML),r.nodeType===1&&(r.textContent="");const o=n(r,!1,el(r));return r instanceof Element&&(r.removeAttribute("v-cloak"),r.setAttribute("data-v-app","")),o},t},Ou=(...e)=>{const t=Ga().createApp(...e),{mount:n}=t;return t.mount=s=>{const r=tl(s);if(r)return n(r,!0,el(r))},t};function el(e){if(e instanceof SVGElement)return"svg";if(typeof MathMLElement=="function"&&e instanceof MathMLElement)return"mathml"}function tl(e){return oe(e)?document.querySelector(e):e}const Xa=window.__VP_SITE_DATA__;function nl(e){return Oi()?(Il(e),!0):!1}const xs=new WeakMap,Ya=(...e)=>{var t;const n=e[0],s=(t=Wt())==null?void 0:t.proxy;if(s==null&&!yo())throw new Error("injectLocal must be called in setup");return s&&xs.has(s)&&n in xs.get(s)?xs.get(s)[n]:wt(...e)},sl=typeof window<"u"&&typeof document<"u";typeof WorkerGlobalScope<"u"&&globalThis instanceof WorkerGlobalScope;const Ja=Object.prototype.toString,za=e=>Ja.call(e)==="[object Object]",At=()=>{},di=Qa();function Qa(){var e,t;return sl&&((e=window==null?void 0:window.navigator)==null?void 0:e.userAgent)&&(/iP(?:ad|hone|od)/.test(window.navigator.userAgent)||((t=window==null?void 0:window.navigator)==null?void 0:t.maxTouchPoints)>2&&/iPad|Macintosh/.test(window==null?void 0:window.navigator.userAgent))}function yr(e,t){function n(...s){return new Promise((r,i)=>{Promise.resolve(e(()=>t.apply(this,s),{fn:t,thisArg:this,args:s})).then(r).catch(i)})}return n}const rl=e=>e();function Za(e,t={}){let n,s,r=At;const i=c=>{clearTimeout(c),r(),r=At};let o;return c=>{const f=ce(e),a=ce(t.maxWait);return n&&i(n),f<=0||a!==void 0&&a<=0?(s&&(i(s),s=null),Promise.resolve(c())):new Promise((d,g)=>{r=t.rejectOnCancel?g:d,o=c,a&&!s&&(s=setTimeout(()=>{n&&i(n),s=null,d(o())},a)),n=setTimeout(()=>{s&&i(s),s=null,d(c())},f)})}}function ef(...e){let t=0,n,s=!0,r=At,i,o,l,c,f;!de(e[0])&&typeof e[0]=="object"?{delay:o,trailing:l=!0,leading:c=!0,rejectOnCancel:f=!1}=e[0]:[o,l=!0,c=!0,f=!1]=e;const a=()=>{n&&(clearTimeout(n),n=void 0,r(),r=At)};return g=>{const v=ce(o),_=Date.now()-t,b=()=>i=g();return a(),v<=0?(t=Date.now(),b()):(_>v&&(c||!s)?(t=Date.now(),b()):l&&(i=new Promise((k,P)=>{r=f?P:k,n=setTimeout(()=>{t=Date.now(),s=!0,k(b()),a()},Math.max(0,v-_))})),!c&&!n&&(n=setTimeout(()=>s=!0,v)),s=!1,i)}}function tf(e=rl,t={}){const{initialState:n="active"}=t,s=vr(n==="active");function r(){s.value=!1}function i(){s.value=!0}const o=(...l)=>{s.value&&e(...l)};return{isActive:ts(s),pause:r,resume:i,eventFilter:o}}function hi(e){return e.endsWith("rem")?Number.parseFloat(e)*16:Number.parseFloat(e)}function nf(e){return Wt()}function Ts(e){return Array.isArray(e)?e:[e]}function vr(...e){if(e.length!==1)return ic(...e);const t=e[0];return typeof t=="function"?ts(nc(()=>({get:t,set:At}))):qe(t)}function sf(e,t=200,n={}){return yr(Za(t,n),e)}function rf(e,t=200,n=!1,s=!0,r=!1){return yr(ef(t,n,s,r),e)}function of(e,t,n={}){const{eventFilter:s=rl,...r}=n;return Le(e,yr(s,t),r)}function lf(e,t,n={}){const{eventFilter:s,initialState:r="active",...i}=n,{eventFilter:o,pause:l,resume:c,isActive:f}=tf(s,{initialState:r});return{stop:of(e,t,{...i,eventFilter:o}),pause:l,resume:c,isActive:f}}function ls(e,t=!0,n){nf()?Ut(e,n):t?e():gn(e)}function cf(e,t,n){return Le(e,t,{...n,immediate:!0})}const Qe=sl?window:void 0;function br(e){var t;const n=ce(e);return(t=n==null?void 0:n.$el)!=null?t:n}function Ze(...e){const t=[],n=()=>{t.forEach(l=>l()),t.length=0},s=(l,c,f,a)=>(l.addEventListener(c,f,a),()=>l.removeEventListener(c,f,a)),r=ie(()=>{const l=Ts(ce(e[0])).filter(c=>c!=null);return l.every(c=>typeof c!="string")?l:void 0}),i=cf(()=>{var l,c;return[(c=(l=r.value)==null?void 0:l.map(f=>br(f)))!=null?c:[Qe].filter(f=>f!=null),Ts(ce(r.value?e[1]:e[0])),Ts(lr(r.value?e[2]:e[1])),ce(r.value?e[3]:e[2])]},([l,c,f,a])=>{if(n(),!(l!=null&&l.length)||!(c!=null&&c.length)||!(f!=null&&f.length))return;const d=za(a)?{...a}:a;t.push(...l.flatMap(g=>c.flatMap(v=>f.map(_=>s(g,v,_,d)))))},{flush:"post"}),o=()=>{i(),n()};return nl(n),o}function af(){const e=Ie(!1),t=Wt();return t&&Ut(()=>{e.value=!0},t),e}function ff(e){const t=af();return ie(()=>(t.value,!!e()))}function uf(e){return typeof e=="function"?e:typeof e=="string"?t=>t.key===e:Array.isArray(e)?t=>e.includes(t.key):()=>!0}function Pu(...e){let t,n,s={};e.length===3?(t=e[0],n=e[1],s=e[2]):e.length===2?typeof e[1]=="object"?(t=!0,n=e[0],s=e[1]):(t=e[0],n=e[1]):(t=!0,n=e[0]);const{target:r=Qe,eventName:i="keydown",passive:o=!1,dedupe:l=!1}=s,c=uf(t);return Ze(r,i,a=>{a.repeat&&ce(l)||c(a)&&n(a)},o)}const df=Symbol("vueuse-ssr-width");function hf(){const e=yo()?Ya(df,null):null;return typeof e=="number"?e:void 0}function il(e,t={}){const{window:n=Qe,ssrWidth:s=hf()}=t,r=ff(()=>n&&"matchMedia"in n&&typeof n.matchMedia=="function"),i=Ie(typeof s=="number"),o=Ie(),l=Ie(!1),c=f=>{l.value=f.matches};return Oo(()=>{if(i.value){i.value=!r.value;const f=ce(e).split(",");l.value=f.some(a=>{const d=a.includes("not all"),g=a.match(/\(\s*min-width:\s*(-?\d+(?:\.\d*)?[a-z]+\s*)\)/),v=a.match(/\(\s*max-width:\s*(-?\d+(?:\.\d*)?[a-z]+\s*)\)/);let _=!!(g||v);return g&&_&&(_=s>=hi(g[1])),v&&_&&(_=s<=hi(v[1])),d?!_:_});return}r.value&&(o.value=n.matchMedia(ce(e)),l.value=o.value.matches)}),Ze(o,"change",c,{passive:!0}),ie(()=>l.value)}const An=typeof globalThis<"u"?globalThis:typeof window<"u"?window:typeof global<"u"?global:typeof self<"u"?self:{},Rn="__vueuse_ssr_handlers__",pf=gf();function gf(){return Rn in An||(An[Rn]=An[Rn]||{}),An[Rn]}function ol(e,t){return pf[e]||t}function ll(e){return il("(prefers-color-scheme: dark)",e)}function mf(e){return e==null?"any":e instanceof Set?"set":e instanceof Map?"map":e instanceof Date?"date":typeof e=="boolean"?"boolean":typeof e=="string"?"string":typeof e=="object"?"object":Number.isNaN(e)?"any":"number"}const yf={boolean:{read:e=>e==="true",write:e=>String(e)},object:{read:e=>JSON.parse(e),write:e=>JSON.stringify(e)},number:{read:e=>Number.parseFloat(e),write:e=>String(e)},any:{read:e=>e,write:e=>String(e)},string:{read:e=>e,write:e=>String(e)},map:{read:e=>new Map(JSON.parse(e)),write:e=>JSON.stringify(Array.from(e.entries()))},set:{read:e=>new Set(JSON.parse(e)),write:e=>JSON.stringify(Array.from(e))},date:{read:e=>new Date(e),write:e=>e.toISOString()}},pi="vueuse-storage";function vf(e,t,n,s={}){var r;const{flush:i="pre",deep:o=!0,listenToStorageChanges:l=!0,writeDefaults:c=!0,mergeDefaults:f=!1,shallow:a,window:d=Qe,eventFilter:g,onError:v=A=>{console.error(A)},initOnMounted:_}=s,b=(a?Ie:qe)(typeof t=="function"?t():t),k=ie(()=>ce(e));if(!n)try{n=ol("getDefaultStorage",()=>{var A;return(A=Qe)==null?void 0:A.localStorage})()}catch(A){v(A)}if(!n)return b;const P=ce(t),D=mf(P),p=(r=s.serializer)!=null?r:yf[D],{pause:m,resume:O}=lf(b,()=>R(b.value),{flush:i,deep:o,eventFilter:g});Le(k,()=>x(),{flush:i}),d&&l&&ls(()=>{n instanceof Storage?Ze(d,"storage",x,{passive:!0}):Ze(d,pi,M),_&&x()}),_||x();function $(A,w){if(d){const F={key:k.value,oldValue:A,newValue:w,storageArea:n};d.dispatchEvent(n instanceof Storage?new StorageEvent("storage",F):new CustomEvent(pi,{detail:F}))}}function R(A){try{const w=n.getItem(k.value);if(A==null)$(w,null),n.removeItem(k.value);else{const F=p.write(A);w!==F&&(n.setItem(k.value,F),$(w,F))}}catch(w){v(w)}}function j(A){const w=A?A.newValue:n.getItem(k.value);if(w==null)return c&&P!=null&&n.setItem(k.value,p.write(P)),P;if(!A&&f){const F=p.read(w);return typeof f=="function"?f(F,P):D==="object"&&!Array.isArray(F)?{...P,...F}:F}else return typeof w!="string"?w:p.read(w)}function x(A){if(!(A&&A.storageArea!==n)){if(A&&A.key==null){b.value=P;return}if(!(A&&A.key!==k.value)){m();try{(A==null?void 0:A.newValue)!==p.write(b.value)&&(b.value=j(A))}catch(w){v(w)}finally{A?gn(O):O()}}}}function M(A){x(A.detail)}return b}const bf="*,*::before,*::after{-webkit-transition:none!important;-moz-transition:none!important;-o-transition:none!important;-ms-transition:none!important;transition:none!important}";function _f(e={}){const{selector:t="html",attribute:n="class",initialValue:s="auto",window:r=Qe,storage:i,storageKey:o="vueuse-color-scheme",listenToStorageChanges:l=!0,storageRef:c,emitAuto:f,disableTransition:a=!0}=e,d={auto:"",light:"light",dark:"dark",...e.modes||{}},g=ll({window:r}),v=ie(()=>g.value?"dark":"light"),_=c||(o==null?vr(s):vf(o,s,i,{window:r,listenToStorageChanges:l})),b=ie(()=>_.value==="auto"?v.value:_.value),k=ol("updateHTMLAttrs",(m,O,$)=>{const R=typeof m=="string"?r==null?void 0:r.document.querySelector(m):br(m);if(!R)return;const j=new Set,x=new Set;let M=null;if(O==="class"){const w=$.split(/\s/g);Object.values(d).flatMap(F=>(F||"").split(/\s/g)).filter(Boolean).forEach(F=>{w.includes(F)?j.add(F):x.add(F)})}else M={key:O,value:$};if(j.size===0&&x.size===0&&M===null)return;let A;a&&(A=r.document.createElement("style"),A.appendChild(document.createTextNode(bf)),r.document.head.appendChild(A));for(const w of j)R.classList.add(w);for(const w of x)R.classList.remove(w);M&&R.setAttribute(M.key,M.value),a&&(r.getComputedStyle(A).opacity,document.head.removeChild(A))});function P(m){var O;k(t,n,(O=d[m])!=null?O:m)}function D(m){e.onChanged?e.onChanged(m,P):P(m)}Le(b,D,{flush:"post",immediate:!0}),ls(()=>D(b.value));const p=ie({get(){return f?_.value:b.value},set(m){_.value=m}});return Object.assign(p,{store:_,system:v,state:b})}function wf(e={}){const{valueDark:t="dark",valueLight:n=""}=e,s=_f({...e,onChanged:(o,l)=>{var c;e.onChanged?(c=e.onChanged)==null||c.call(e,o==="dark",l,o):l(o)},modes:{dark:t,light:n}}),r=ie(()=>s.system.value);return ie({get(){return s.value==="dark"},set(o){const l=o?"dark":"light";r.value===l?s.value="auto":s.value=l}})}function Es(e){return typeof Window<"u"&&e instanceof Window?e.document.documentElement:typeof Document<"u"&&e instanceof Document?e.documentElement:e}const gi=1;function Sf(e,t={}){const{throttle:n=0,idle:s=200,onStop:r=At,onScroll:i=At,offset:o={left:0,right:0,top:0,bottom:0},eventListenerOptions:l={capture:!1,passive:!0},behavior:c="auto",window:f=Qe,onError:a=R=>{console.error(R)}}=t,d=Ie(0),g=Ie(0),v=ie({get(){return d.value},set(R){b(R,void 0)}}),_=ie({get(){return g.value},set(R){b(void 0,R)}});function b(R,j){var x,M,A,w;if(!f)return;const F=ce(e);if(!F)return;(A=F instanceof Document?f.document.body:F)==null||A.scrollTo({top:(x=ce(j))!=null?x:_.value,left:(M=ce(R))!=null?M:v.value,behavior:ce(c)});const Y=((w=F==null?void 0:F.document)==null?void 0:w.documentElement)||(F==null?void 0:F.documentElement)||F;v!=null&&(d.value=Y.scrollLeft),_!=null&&(g.value=Y.scrollTop)}const k=Ie(!1),P=Dt({left:!0,right:!1,top:!0,bottom:!1}),D=Dt({left:!1,right:!1,top:!1,bottom:!1}),p=R=>{k.value&&(k.value=!1,D.left=!1,D.right=!1,D.top=!1,D.bottom=!1,r(R))},m=sf(p,n+s),O=R=>{var j;if(!f)return;const x=((j=R==null?void 0:R.document)==null?void 0:j.documentElement)||(R==null?void 0:R.documentElement)||br(R),{display:M,flexDirection:A,direction:w}=getComputedStyle(x),F=w==="rtl"?-1:1,Y=x.scrollLeft;D.left=Y<d.value,D.right=Y>d.value;const re=Math.abs(Y*F)<=(o.left||0),U=Math.abs(Y*F)+x.clientWidth>=x.scrollWidth-(o.right||0)-gi;M==="flex"&&A==="row-reverse"?(P.left=U,P.right=re):(P.left=re,P.right=U),d.value=Y;let X=x.scrollTop;R===f.document&&!X&&(X=f.document.body.scrollTop),D.top=X<g.value,D.bottom=X>g.value;const V=Math.abs(X)<=(o.top||0),ae=Math.abs(X)+x.clientHeight>=x.scrollHeight-(o.bottom||0)-gi;M==="flex"&&A==="column-reverse"?(P.top=ae,P.bottom=V):(P.top=V,P.bottom=ae),g.value=X},$=R=>{var j;if(!f)return;const x=(j=R.target.documentElement)!=null?j:R.target;O(x),k.value=!0,m(R),i(R)};return Ze(e,"scroll",n?rf($,n,!0,!1):$,l),ls(()=>{try{const R=ce(e);if(!R)return;O(R)}catch(R){a(R)}}),Ze(e,"scrollend",p,l),{x:v,y:_,isScrolling:k,arrivedState:P,directions:D,measure(){const R=ce(e);f&&R&&O(R)}}}function cl(e){const t=window.getComputedStyle(e);if(t.overflowX==="scroll"||t.overflowY==="scroll"||t.overflowX==="auto"&&e.clientWidth<e.scrollWidth||t.overflowY==="auto"&&e.clientHeight<e.scrollHeight)return!0;{const n=e.parentNode;return!n||n.tagName==="BODY"?!1:cl(n)}}function xf(e){const t=e||window.event,n=t.target;return cl(n)?!1:t.touches.length>1?!0:(t.preventDefault&&t.preventDefault(),!1)}const Cs=new WeakMap;function Iu(e,t=!1){const n=Ie(t);let s=null,r="";Le(vr(e),l=>{const c=Es(ce(l));if(c){const f=c;if(Cs.get(f)||Cs.set(f,f.style.overflow),f.style.overflow!=="hidden"&&(r=f.style.overflow),f.style.overflow==="hidden")return n.value=!0;if(n.value)return f.style.overflow="hidden"}},{immediate:!0});const i=()=>{const l=Es(ce(e));!l||n.value||(di&&(s=Ze(l,"touchmove",c=>{xf(c)},{passive:!1})),l.style.overflow="hidden",n.value=!0)},o=()=>{const l=Es(ce(e));!l||!n.value||(di&&(s==null||s()),l.style.overflow=r,Cs.delete(l),n.value=!1)};return nl(o),ie({get(){return n.value},set(l){l?i():o()}})}function Lu(e={}){const{window:t=Qe,...n}=e;return Sf(t,n)}function Nu(e={}){const{window:t=Qe,initialWidth:n=Number.POSITIVE_INFINITY,initialHeight:s=Number.POSITIVE_INFINITY,listenOrientation:r=!0,includeScrollbar:i=!0,type:o="inner"}=e,l=Ie(n),c=Ie(s),f=()=>{if(t)if(o==="outer")l.value=t.outerWidth,c.value=t.outerHeight;else if(o==="visual"&&t.visualViewport){const{width:d,height:g,scale:v}=t.visualViewport;l.value=Math.round(d*v),c.value=Math.round(g*v)}else i?(l.value=t.innerWidth,c.value=t.innerHeight):(l.value=t.document.documentElement.clientWidth,c.value=t.document.documentElement.clientHeight)};f(),ls(f);const a={passive:!0};if(Ze("resize",f,a),t&&o==="visual"&&t.visualViewport&&Ze(t.visualViewport,"resize",f,a),r){const d=il("(orientation: portrait)");Le(d,()=>f())}return{width:l,height:c}}const As={};var Rs={};const al=/^(?:[a-z]+:|\/\/)/i,Tf="vitepress-theme-appearance",Ef=/#.*$/,Cf=/[?#].*$/,Af=/(?:(^|\/)index)?\.(?:md|html)$/,ge=typeof document<"u",fl={relativePath:"404.md",filePath:"",title:"404",description:"Not Found",headers:[],frontmatter:{sidebar:!1,layout:"page"},lastUpdated:0,isNotFound:!0};function Rf(e,t,n=!1){if(t===void 0)return!1;if(e=mi(`/${e}`),n)return new RegExp(t).test(e);if(mi(t)!==e)return!1;const s=t.match(Ef);return s?(ge?location.hash:"")===s[0]:!0}function mi(e){return decodeURI(e).replace(Cf,"").replace(Af,"$1")}function Mf(e){return al.test(e)}function Of(e,t){return Object.keys((e==null?void 0:e.locales)||{}).find(n=>n!=="root"&&!Mf(n)&&Rf(t,`/${n}/`,!0))||"root"}function Pf(e,t){var s,r,i,o,l,c,f;const n=Of(e,t);return Object.assign({},e,{localeIndex:n,lang:((s=e.locales[n])==null?void 0:s.lang)??e.lang,dir:((r=e.locales[n])==null?void 0:r.dir)??e.dir,title:((i=e.locales[n])==null?void 0:i.title)??e.title,titleTemplate:((o=e.locales[n])==null?void 0:o.titleTemplate)??e.titleTemplate,description:((l=e.locales[n])==null?void 0:l.description)??e.description,head:dl(e.head,((c=e.locales[n])==null?void 0:c.head)??[]),themeConfig:{...e.themeConfig,...(f=e.locales[n])==null?void 0:f.themeConfig}})}function ul(e,t){const n=t.title||e.title,s=t.titleTemplate??e.titleTemplate;if(typeof s=="string"&&s.includes(":title"))return s.replace(/:title/g,n);const r=If(e.title,s);return n===r.slice(3)?n:`${n}${r}`}function If(e,t){return t===!1?"":t===!0||t===void 0?` | ${e}`:e===t?"":` | ${t}`}function Lf(e,t){const[n,s]=t;if(n!=="meta")return!1;const r=Object.entries(s)[0];return r==null?!1:e.some(([i,o])=>i===n&&o[r[0]]===r[1])}function dl(e,t){return[...e.filter(n=>!Lf(t,n)),...t]}const Nf=/[\u0000-\u001F"#$&*+,:;<=>?[\]^`{|}\u007F]/g,Ff=/^[a-z]:/i;function yi(e){const t=Ff.exec(e),n=t?t[0]:"";return n+e.slice(n.length).replace(Nf,"_").replace(/(^|\/)_+(?=[^/]*$)/,"$1")}const Ms=new Set;function Hf(e){if(Ms.size===0){const n=typeof process=="object"&&(Rs==null?void 0:Rs.VITE_EXTRA_EXTENSIONS)||(As==null?void 0:As.VITE_EXTRA_EXTENSIONS)||"";("3g2,3gp,aac,ai,apng,au,avif,bin,bmp,cer,class,conf,crl,css,csv,dll,doc,eps,epub,exe,gif,gz,ics,ief,jar,jpe,jpeg,jpg,js,json,jsonld,m4a,man,mid,midi,mjs,mov,mp2,mp3,mp4,mpe,mpeg,mpg,mpp,oga,ogg,ogv,ogx,opus,otf,p10,p7c,p7m,p7s,pdf,png,ps,qt,roff,rtf,rtx,ser,svg,t,tif,tiff,tr,ts,tsv,ttf,txt,vtt,wav,weba,webm,webp,woff,woff2,xhtml,xml,yaml,yml,zip"+(n&&typeof n=="string"?","+n:"")).split(",").forEach(s=>Ms.add(s))}const t=e.split(".").pop();return t==null||!Ms.has(t.toLowerCase())}const Df=Symbol(),xt=Ie(Xa);function Fu(e){const t=ie(()=>Pf(xt.value,e.data.relativePath)),n=t.value.appearance,s=n==="force-dark"?qe(!0):n==="force-auto"?ll():n?wf({storageKey:Tf,initialValue:()=>n==="dark"?"dark":"auto",...typeof n=="object"?n:{}}):qe(!1),r=qe(ge?location.hash:"");return ge&&window.addEventListener("hashchange",()=>{r.value=location.hash}),Le(()=>e.data,()=>{r.value=ge?location.hash:""}),{site:t,theme:ie(()=>t.value.themeConfig),page:ie(()=>e.data),frontmatter:ie(()=>e.data.frontmatter),params:ie(()=>e.data.params),lang:ie(()=>t.value.lang),dir:ie(()=>e.data.frontmatter.dir||t.value.dir),localeIndex:ie(()=>t.value.localeIndex||"root"),title:ie(()=>ul(t.value,e.data)),description:ie(()=>e.data.description||t.value.description),isDark:s,hash:ie(()=>r.value)}}function $f(){const e=wt(Df);if(!e)throw new Error("vitepress data not properly injected in app");return e}function jf(e,t){return`${e}${t}`.replace(/\/+/g,"/")}function vi(e){return al.test(e)||!e.startsWith("/")?e:jf(xt.value.base,e)}function Vf(e){let t=e.replace(/\.html$/,"");if(t=decodeURIComponent(t),t=t.replace(/\/$/,"/index"),ge){const n="/";t=yi(t.slice(n.length).replace(/\//g,"_")||"index")+".md";let s=__VP_HASH_MAP__[t.toLowerCase()];if(s||(t=t.endsWith("_index.md")?t.slice(0,-9)+".md":t.slice(0,-3)+"_index.md",s=__VP_HASH_MAP__[t.toLowerCase()]),!s)return null;t=`${n}assets/${t}.${s}.js`}else t=`./${yi(t.slice(1).replace(/\//g,"_"))}.md.js`;return t}let Dn=[];function Hu(e){Dn.push(e),ss(()=>{Dn=Dn.filter(t=>t!==e)})}function kf(){let e=xt.value.scrollOffset,t=0,n=24;if(typeof e=="object"&&"padding"in e&&(n=e.padding,e=e.selector),typeof e=="number")t=e;else if(typeof e=="string")t=bi(e,n);else if(Array.isArray(e))for(const s of e){const r=bi(s,n);if(r){t=r;break}}return t}function bi(e,t){const n=document.querySelector(e);if(!n)return 0;const s=n.getBoundingClientRect().bottom;return s<0?0:s+t}const Uf=Symbol(),hl="http://a.com",Wf=()=>({path:"/",component:null,data:fl});function Du(e,t){const n=Dt(Wf()),s={route:n,go:r};async function r(l=ge?location.href:"/"){var c,f;l=Os(l),await((c=s.onBeforeRouteChange)==null?void 0:c.call(s,l))!==!1&&(ge&&l!==Os(location.href)&&(history.replaceState({scrollPosition:window.scrollY},""),history.pushState({},"",l)),await o(l),await((f=s.onAfterRouteChange??s.onAfterRouteChanged)==null?void 0:f(l)))}let i=null;async function o(l,c=0,f=!1){var g,v;if(await((g=s.onBeforePageLoad)==null?void 0:g.call(s,l))===!1)return;const a=new URL(l,hl),d=i=a.pathname;try{let _=await e(d);if(!_)throw new Error(`Page not found: ${d}`);if(i===d){i=null;const{default:b,__pageData:k}=_;if(!b)throw new Error(`Invalid route component: ${b}`);await((v=s.onAfterPageLoad)==null?void 0:v.call(s,l)),n.path=ge?d:vi(d),n.component=Ln(b),n.data=Ln(k),ge&&gn(()=>{let P=xt.value.base+k.relativePath.replace(/(?:(^|\/)index)?\.md$/,"$1");if(!xt.value.cleanUrls&&!P.endsWith("/")&&(P+=".html"),P!==a.pathname&&(a.pathname=P,l=P+a.search+a.hash,history.replaceState({},"",l)),a.hash&&!c){let D=null;try{D=document.getElementById(decodeURIComponent(a.hash).slice(1))}catch(p){console.warn(p)}if(D){_i(D,a.hash);return}}window.scrollTo(0,c)})}}catch(_){if(!/fetch|Page not found/.test(_.message)&&!/^\/404(\.html|\/)?$/.test(l)&&console.error(_),!f)try{const b=await fetch(xt.value.base+"hashmap.json");window.__VP_HASH_MAP__=await b.json(),await o(l,c,!0);return}catch{}if(i===d){i=null,n.path=ge?d:vi(d),n.component=t?Ln(t):null;const b=ge?d.replace(/(^|\/)$/,"$1index").replace(/(\.html)?$/,".md").replace(/^\//,""):"404.md";n.data={...fl,relativePath:b}}}}return ge&&(history.state===null&&history.replaceState({},""),window.addEventListener("click",l=>{if(l.defaultPrevented||!(l.target instanceof Element)||l.target.closest("button")||l.button!==0||l.ctrlKey||l.shiftKey||l.altKey||l.metaKey)return;const c=l.target.closest("a");if(!c||c.closest(".vp-raw")||c.hasAttribute("download")||c.hasAttribute("target"))return;const f=c.getAttribute("href")??(c instanceof SVGAElement?c.getAttribute("xlink:href"):null);if(f==null)return;const{href:a,origin:d,pathname:g,hash:v,search:_}=new URL(f,c.baseURI),b=new URL(location.href);d===b.origin&&Hf(g)&&(l.preventDefault(),g===b.pathname&&_===b.search?(v!==b.hash&&(history.pushState({},"",a),window.dispatchEvent(new HashChangeEvent("hashchange",{oldURL:b.href,newURL:a}))),v?_i(c,v,c.classList.contains("header-anchor")):window.scrollTo(0,0)):r(a))},{capture:!0}),window.addEventListener("popstate",async l=>{var f;if(l.state===null)return;const c=Os(location.href);await o(c,l.state&&l.state.scrollPosition||0),await((f=s.onAfterRouteChange??s.onAfterRouteChanged)==null?void 0:f(c))}),window.addEventListener("hashchange",l=>{l.preventDefault()})),s}function Bf(){const e=wt(Uf);if(!e)throw new Error("useRouter() is called without provider.");return e}function pl(){return Bf().route}function _i(e,t,n=!1){let s=null;try{s=e.classList.contains("header-anchor")?e:document.getElementById(decodeURIComponent(t).slice(1))}catch(r){console.warn(r)}if(s){let r=function(){!n||Math.abs(o-window.scrollY)>window.innerHeight?window.scrollTo(0,o):window.scrollTo({left:0,top:o,behavior:"smooth"})};const i=parseInt(window.getComputedStyle(s).paddingTop,10),o=window.scrollY+s.getBoundingClientRect().top-kf()+i;requestAnimationFrame(r)}}function Os(e){const t=new URL(e,hl);return t.pathname=t.pathname.replace(/(^|\/)index(\.html)?$/,"$1"),xt.value.cleanUrls?t.pathname=t.pathname.replace(/\.html$/,""):!t.pathname.endsWith("/")&&!t.pathname.endsWith(".html")&&(t.pathname+=".html"),t.pathname+t.search+t.hash}const Mn=()=>Dn.forEach(e=>e()),$u=fr({name:"VitePressContent",props:{as:{type:[Object,String],default:"div"}},setup(e){const t=pl(),{frontmatter:n,site:s}=$f();return Le(n,Mn,{deep:!0,flush:"post"}),()=>Ks(e.as,s.value.contentProps??{style:{position:"relative"}},[t.component?Ks(t.component,{onVnodeMounted:Mn,onVnodeUpdated:Mn,onVnodeUnmounted:Mn}):"404 Page Not Found"])}}),ju=(e,t)=>{const n=e.__vccOpts||e;for(const[s,r]of t)n[s]=r;return n},Kf="modulepreload",qf=function(e){return"/"+e},wi={},Vu=function(t,n,s){let r=Promise.resolve();if(n&&n.length>0){document.getElementsByTagName("link");const o=document.querySelector("meta[property=csp-nonce]"),l=(o==null?void 0:o.nonce)||(o==null?void 0:o.getAttribute("nonce"));r=Promise.allSettled(n.map(c=>{const f=Xs;if(c=qf(c),c in wi)return;wi[c]=!0;const a=c.endsWith(f(0)),d=a?'[rel="stylesheet"]':"";if(document.querySelector('link[href="'+c+'"]'+d))return;const g=document[f(1)]("link");if(g.rel=a?"stylesheet":Kf,!a&&(g.as="script"),g.crossOrigin="",g.href=c,l&&g[f(2)]("nonce",l),document.head.appendChild(g),a)return new Promise((v,_)=>{g.addEventListener("load",v),g.addEventListener("error",()=>_(new Error("Unable to preload CSS for "+c)))})}))}function i(o){const l=new Event("vite:preloadError",{cancelable:!0});if(l.payload=o,window.dispatchEvent(l),!l.defaultPrevented)throw o}return r.then(o=>{for(const l of o||[])l.status==="rejected"&&i(l.reason);return t().catch(i)})};function Xs(e,t){const n=Ys();return Xs=function(s,r){return s=s-0,n[s]},Xs(e,t)}function Ys(){const e=[".css","createElement","setAttribute"];return Ys=function(){return e},Ys()}const ku=fr({setup(e,{slots:t}){const n=qe(!1);return Ut(()=>{n.value=!0}),()=>n.value&&t.default?t.default():null}});function Uu(){ge&&window.addEventListener("click",e=>{var n;const t=e.target;if(t.matches(".vp-code-group input")){const s=(n=t.parentElement)==null?void 0:n.parentElement;if(!s)return;const r=Array.from(s.querySelectorAll("input")).indexOf(t);if(r<0)return;const i=s.querySelector(".blocks");if(!i)return;const o=Array.from(i.children).find(f=>f.classList.contains("active"));if(!o)return;const l=i.children[r];if(!l||o===l)return;o.classList.remove("active"),l.classList.add("active");const c=s==null?void 0:s.querySelector(`label[for="${t.id}"]`);c==null||c.scrollIntoView({block:"nearest"})}})}function Wu(){if(ge){const e=new WeakMap;window.addEventListener("click",t=>{var s;const n=t.target;if(n.matches('div[class*="language-"] > button.copy')){const r=n.parentElement,i=(s=n.nextElementSibling)==null?void 0:s.nextElementSibling;if(!r||!i)return;const o=/language-(shellscript|shell|bash|sh|zsh)/.test(r.className),l=[".vp-copy-ignore",".diff.remove"],c=i.cloneNode(!0);c.querySelectorAll(l.join(",")).forEach(a=>a.remove());let f=c.textContent||"";o&&(f=f.replace(/^ *(\$|>) /gm,"").trim()),Gf(f).then(()=>{n.classList.add("copied"),clearTimeout(e.get(n));const a=setTimeout(()=>{n.classList.remove("copied"),n.blur(),e.delete(n)},2e3);e.set(n,a)})}})}}async function Gf(e){try{return navigator.clipboard.writeText(e)}catch{const t=document.createElement("textarea"),n=document.activeElement;t.value=e,t.setAttribute("readonly",""),t.style.contain="strict",t.style.position="absolute",t.style.left="-9999px",t.style.fontSize="12pt";const s=document.getSelection(),r=s?s.rangeCount>0&&s.getRangeAt(0):null;document.body.appendChild(t),t.select(),t.selectionStart=0,t.selectionEnd=e.length,document.execCommand("copy"),document.body.removeChild(t),r&&(s.removeAllRanges(),s.addRange(r)),n&&n.focus()}}function Bu(e,t){let n=!0,s=[];const r=i=>{if(n){n=!1,i.forEach(l=>{const c=Ps(l);for(const f of document.head.children)if(f.isEqualNode(c)){s.push(f);return}});return}const o=i.map(Ps);s.forEach((l,c)=>{const f=o.findIndex(a=>a==null?void 0:a.isEqualNode(l??null));f!==-1?delete o[f]:(l==null||l.remove(),delete s[c])}),o.forEach(l=>l&&document.head.appendChild(l)),s=[...s,...o].filter(Boolean)};Oo(()=>{const i=e.data,o=t.value,l=i&&i.description,c=i&&i.frontmatter.head||[],f=ul(o,i);f!==document.title&&(document.title=f);const a=l||o.description;let d=document.querySelector("meta[name=description]");d?d.getAttribute("content")!==a&&d.setAttribute("content",a):Ps(["meta",{name:"description",content:a}]),r(dl(o.head,Yf(c)))})}function Ps([e,t,n]){const s=document.createElement(e);for(const r in t)s.setAttribute(r,t[r]);return n&&(s.innerHTML=n),e==="script"&&t.async==null&&(s.async=!1),s}function Xf(e){return e[0]==="meta"&&e[1]&&e[1].name==="description"}function Yf(e){return e.filter(t=>!Xf(t))}const Is=new Set,gl=()=>document.createElement("link"),Jf=e=>{const t=gl();t.rel="prefetch",t.href=e,document.head.appendChild(t)},zf=e=>{const t=new XMLHttpRequest;t.open("GET",e,t.withCredentials=!0),t.send()};let On;const Qf=ge&&(On=gl())&&On.relList&&On.relList.supports&&On.relList.supports("prefetch")?Jf:zf;function Ku(){if(!ge||!window.IntersectionObserver)return;let e;if((e=navigator.connection)&&(e.saveData||/2g/.test(e.effectiveType)))return;const t=window.requestIdleCallback||setTimeout;let n=null;const s=()=>{n&&n.disconnect(),n=new IntersectionObserver(i=>{i.forEach(o=>{if(o.isIntersecting){const l=o.target;n.unobserve(l);const{pathname:c}=l;if(!Is.has(c)){Is.add(c);const f=Vf(c);f&&Qf(f)}}})}),t(()=>{document.querySelectorAll("#app a").forEach(i=>{const{hostname:o,pathname:l}=new URL(i.href instanceof SVGAnimatedString?i.href.animVal:i.href,i.baseURI),c=l.match(/\.\w+$/);c&&c[0]!==".html"||i.target!=="_blank"&&o===location.hostname&&(l!==location.pathname?n.observe(i):Is.add(l))})})};Ut(s);const r=pl();Le(()=>r.path,s),ss(()=>{n&&n.disconnect()})}export{du as $,kf as A,cu as B,iu as C,Ie as D,Hu as E,ve as F,le as G,ou as H,al as I,pl as J,la as K,wt as L,Nu as M,zn as N,Pu as O,gn as P,Lu as Q,ge as R,ts as S,bu as T,Iu as U,Vc as V,uu as W,Au as X,ao as Y,Cu as Z,ju as _,jo as a,Ou as a$,eu as a0,Ln as a1,yo as a2,Dt as a3,de as a4,vt as a5,z as a6,Oi as a7,Il as a8,tu as a9,Zf as aA,oa as aB,fn as aC,xu as aD,Tu as aE,Ks as aF,Yn as aG,Cc as aH,Si as aI,Su as aJ,wu as aK,Ti as aL,lu as aM,Pn as aN,Ru as aO,Mu as aP,at as aQ,Ql as aR,Bu as aS,Uf as aT,Fu as aU,Df as aV,$u as aW,ku as aX,xt as aY,Du as aZ,Vf as a_,ic as aa,mu as ab,ru as ac,Vu as ad,Wt as ae,oe as af,B as ag,ee as ah,nc as ai,Q as aj,vu as ak,q as al,Fe as am,De as an,nu as ao,_u as ap,hu as aq,Sc as ar,lt as as,St as at,be as au,su as av,Ec as aw,xc as ax,au as ay,Tr as az,Ws as b,Ku as b0,Wu as b1,Uu as b2,Bf as b3,Eu as b4,gu as c,fr as d,yu as e,Hf as f,vi as g,ie as h,Mf as i,$o as j,lr as k,Rf as l,il as m,Qn as n,Us as o,qe as p,Le as q,fu as r,Oo as s,Pl as t,$f as u,Ut as v,hc as w,ss as x,pu as y,co as z};
