{"version": 3, "file": "literal_types.js", "sourceRoot": "", "sources": ["../../src/constants/literal_types.ts"], "names": [], "mappings": "AAEA,IAAM,gBAAgB,GAAgC;IACpD,OAAO,EAAE,SAAS;IAClB,SAAS,EAAE,WAAW;IACtB,SAAS,EAAE,WAAW;CACvB,CAAC;AAEF,IAAM,eAAe,GAA+B;IAClD,aAAa,EAAE,eAAe;IAC9B,WAAW,EAAE,aAAa;IAC1B,YAAY,EAAE,cAAc;IAC5B,OAAO,EAAE,SAAS;IAClB,QAAQ,EAAE,UAAU;IACpB,QAAQ,EAAE,UAAU;IACpB,OAAO,EAAE,SAAS;IAClB,QAAQ,EAAE,UAAU;CACrB,CAAC;AAEF,IAAM,cAAc,GAA8B;IAChD,aAAa,EAAE,eAAe;IAC9B,mBAAmB,EAAE,qBAAqB;IAC1C,QAAQ,EAAE,UAAU;CACrB,CAAC;AAEF,OAAO,EAAE,gBAAgB,EAAE,eAAe,EAAE,cAAc,EAAE,CAAC"}