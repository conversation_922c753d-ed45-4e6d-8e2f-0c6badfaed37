import type { Readable } from 'node:stream';
import type { ReadableStream } from 'node:stream/web';
import type { AlipaySdkConfig } from './types.js';
export declare const ALIPAY_ALGORITHM_MAPPING: {
    RSA: string;
    RSA2: string;
};
export declare function aesEncryptText(plainText: string, aesKey: string): string;
export declare function aesDecryptText(encryptedText: string, aesKey: string): string;
export declare function aesEncrypt(data: object, aesKey: string): string;
export declare function aesDecrypt(encryptedText: string, aesKey: string): object;
interface SignOptions {
    /** 是否对 bizContent 做 SnakeCase 转换，默认 true */
    bizContentAutoSnakeCase?: boolean;
}
/**
 * OpenAPI 2.0 签名
 * @description https://opendocs.alipay.com/common/02kf5q
 * @param {string} method 调用接口方法名，比如 alipay.ebpp.bill.add
 * @param {object} params 请求参数
 * @param {object} config sdk 配置
 */
export declare function sign(method: string, params: Record<string, any>, config: Required<AlipaySdkConfig>, options?: SignOptions): Record<string, any>;
/** OpenAPI 3.0 签名，使用应用私钥签名 */
export declare function signatureV3(signString: string, appPrivateKey: string): string;
/** OpenAPI 3.0 验签，使用支付宝公钥验证签名 */
export declare function verifySignatureV3(signString: string, expectedSignature: string, alipayPublicKey: string): boolean;
export declare function createRequestId(): string;
export declare function readableToBytes(stream: Readable | ReadableStream): Promise<Buffer<ArrayBuffer>>;
export declare function decamelize(text: string): string;
export {};
