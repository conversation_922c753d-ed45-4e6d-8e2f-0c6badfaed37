import { LRU } from 'ylru';
const lru = new LRU(1000); // Cache up to 1000 entries
export function resetTimezone(date) {
    let TIMEZONE = '';
    const offsetInMinutes = date.getTimezoneOffset();
    const _hourOffset = Math.floor(-offsetInMinutes / 60);
    const _minuteOffset = Math.abs(offsetInMinutes % 60);
    TIMEZONE += _hourOffset >= 0 ? '+' : '-';
    TIMEZONE += `${String(Math.abs(_hourOffset)).padStart(2, '0')}${String(_minuteOffset).padStart(2, '0')}`;
    return TIMEZONE;
}
const MONTHS = {
    '01': 'Jan',
    '02': 'Feb',
    '03': 'Mar',
    '04': 'Apr',
    '05': 'May',
    '06': 'Jun',
    '07': 'Jul',
    '08': 'Aug',
    '09': 'Sep',
    // eslint-disable-next-line quote-props
    '10': 'Oct',
    // eslint-disable-next-line quote-props
    '11': 'Nov',
    // eslint-disable-next-line quote-props
    '12': 'Dec',
};
/**
 * return `[ YYYY, MM, DD, HH, mm, ss ]` date string array
 */
export function getDateStringParts(d, onlyDate) {
    d = d || new Date();
    const monthNum = d.getMonth() + 1;
    const month = monthNum < 10 ? `0${monthNum}` : `${monthNum}`;
    const dateNum = d.getDate();
    const date = dateNum < 10 ? `0${dateNum}` : `${dateNum}`;
    if (onlyDate) {
        return [`${d.getFullYear()}`, month, date];
    }
    const hoursNum = d.getHours();
    const hours = hoursNum < 10 ? `0${hoursNum}` : `${hoursNum}`;
    const minutesNum = d.getMinutes();
    const minutes = minutesNum < 10 ? `0${minutesNum}` : `${minutesNum}`;
    const secondsNum = d.getSeconds();
    const seconds = secondsNum < 10 ? `0${secondsNum}` : `${secondsNum}`;
    return [`${d.getFullYear()}`, month, date, hours, minutes, seconds];
}
/**
 * Access log format date. format: `moment().format('DD/MMM/YYYY:HH:mm:ss ZZ')`
 */
export function accessLogDate(d) {
    // 16/Apr/2013:16:40:09 +0800
    d = d || new Date();
    const [year, month, date, hours, minutes, seconds] = getDateStringParts(d);
    const TIMEZONE = getTimezone(d);
    return `${date}/${MONTHS[month]}/${year}:${hours}:${minutes}:${seconds} ${TIMEZONE}`;
}
export function getTimezone(d) {
    const key = d.getFullYear() + '-' + (d.getMonth() + 1) + '-' + d.getDate();
    const timeZone = lru.get(key);
    if (timeZone === undefined) {
        lru.set(key, resetTimezone(d), { maxAge: 86400000 }); // Cache for 24 hours
        return lru.get(key);
    }
    return timeZone;
}
export function logDate(d, msSep) {
    if (typeof d === 'string') {
        // logDate(msSep)
        msSep = d;
        d = new Date();
    }
    else {
        // logDate(d, msSep)
        d = d || new Date();
    }
    const [year, month, date, hours, minutes, seconds] = getDateStringParts(d);
    const millisecondsNum = d.getMilliseconds();
    let milliseconds = `${millisecondsNum}`;
    if (millisecondsNum < 10) {
        milliseconds = `00${millisecondsNum}`;
    }
    else if (millisecondsNum < 100) {
        milliseconds = `0${millisecondsNum}`;
    }
    msSep = msSep || '.';
    return `${year}-${month}-${date} ${hours}:${minutes}:${seconds}${msSep}${milliseconds}`;
}
export const YYYYMMDDHHmmssSSS = logDate;
/**
 * `moment().format('YYYY-MM-DD HH:mm:ss')` format date string.
 */
export function YYYYMMDDHHmmss(d, options) {
    d = d || new Date();
    if (!(d instanceof Date)) {
        d = new Date(d);
    }
    let dateSep = '-';
    let timeSep = ':';
    if (options?.dateSep) {
        dateSep = options.dateSep;
    }
    if (options?.timeSep) {
        timeSep = options.timeSep;
    }
    const [year, month, date, hours, minutes, seconds] = getDateStringParts(d);
    return `${year}${dateSep}${month}${dateSep}${date} ${hours}${timeSep}${minutes}${timeSep}${seconds}`;
}
/**
 * `moment().format('YYYY-MM-DD')` format date string.
 */
export function YYYYMMDD(d, sep) {
    if (typeof d === 'string') {
        // YYYYMMDD(sep)
        sep = d;
        d = new Date();
    }
    else {
        // YYYYMMDD(d, sep)
        d = d || new Date();
        if (typeof sep !== 'string') {
            sep = '-';
        }
    }
    const [year, month, date] = getDateStringParts(d, true);
    return `${year}${sep}${month}${sep}${date}`;
}
/**
 * return datetime struct.
 *
 * @return {Object} date
 *  - {Number} YYYYMMDD, 20130401
 *  - {Number} H, 0, 1, 9, 12, 23
 */
export function datestruct(now) {
    now = now || new Date();
    return {
        YYYYMMDD: now.getFullYear() * 10000 + (now.getMonth() + 1) * 100 + now.getDate(),
        H: now.getHours(),
    };
}
/**
 * Get Unix's timestamp in seconds.
 */
export function timestamp(t) {
    if (t) {
        // convert timestamp to Date
        // timestamp(timestampValue)
        let v;
        if (typeof t === 'string') {
            v = Number(t);
        }
        else {
            v = t;
        }
        if (String(v).length === 10) {
            v *= 1000;
        }
        return new Date(v);
    }
    // get current timestamp
    return Math.round(Date.now() / 1000);
}
/**
 * Parse timestamp to Date
 */
export function parseTimestamp(t) {
    return timestamp(t);
}
/**
 * Convert Date object to Unix timestamp in seconds.
 */
export function dateToUnixTimestamp(date) {
    return Math.round(date.getTime() / 1000);
}
export var DateFormat;
(function (DateFormat) {
    DateFormat["DateTimeWithTimeZone"] = "DateTimeWithTimeZone";
    DateFormat["DateTimeWithMilliSeconds"] = "DateTimeWithMilliSeconds";
    DateFormat["DateTimeWithSeconds"] = "DateTimeWithSeconds";
    DateFormat["UnixTimestamp"] = "UnixTimestamp";
})(DateFormat || (DateFormat = {}));
/**
 * Provide milliseconds, return a formatted string.
 */
export function getDateFromMilliseconds(milliseconds, format) {
    if (!Number.isFinite(milliseconds)) {
        throw new Error('Invalid milliseconds value');
    }
    switch (format) {
        case DateFormat.DateTimeWithTimeZone:
            return accessLogDate(new Date(milliseconds));
        case DateFormat.DateTimeWithMilliSeconds:
            return logDate(new Date(milliseconds));
        case DateFormat.DateTimeWithSeconds:
            return YYYYMMDDHHmmss(new Date(milliseconds));
        case DateFormat.UnixTimestamp:
            return dateToUnixTimestamp(new Date(milliseconds)).toString();
        default:
            return YYYYMMDD(new Date(milliseconds));
    }
}
//# sourceMappingURL=data:application/json;base64,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