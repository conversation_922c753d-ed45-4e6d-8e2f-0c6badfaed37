import{S as e}from"./chunks/ServicePurchase.BnozmWQP.js";import{c as a,o as t,G as r}from"./chunks/framework.B19ydMwb.js";import"./chunks/theme.CmWpOUCL.js";const m=JSON.parse('{"title":"","description":"","frontmatter":{"layout":"page"},"headers":[],"relativePath":"service-purchase/index.md","filePath":"service-purchase/index.md"}'),s={name:"service-purchase/index.md"},_=Object.assign(s,{setup(c){return(i,o)=>(t(),a("div",null,[r(e)]))}});export{m as __pageData,_ as default};
