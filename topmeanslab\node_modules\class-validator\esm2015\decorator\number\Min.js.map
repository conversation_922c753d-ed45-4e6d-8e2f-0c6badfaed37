{"version": 3, "file": "Min.js", "sourceRoot": "", "sources": ["../../../../src/decorator/number/Min.ts"], "names": [], "mappings": "AACA,OAAO,EAAE,YAAY,EAAE,UAAU,EAAE,MAAM,sBAAsB,CAAC;AAEhE,MAAM,CAAC,MAAM,GAAG,GAAG,KAAK,CAAC;AAEzB;;GAEG;AACH,MAAM,UAAU,GAAG,CAAC,GAAY,EAAE,GAAW;IAC3C,OAAO,OAAO,GAAG,KAAK,QAAQ,IAAI,OAAO,GAAG,KAAK,QAAQ,IAAI,GAAG,IAAI,GAAG,CAAC;AAC1E,CAAC;AAED;;GAEG;AACH,MAAM,UAAU,GAAG,CAAC,QAAgB,EAAE,iBAAqC;IACzE,OAAO,UAAU,CACf;QACE,IAAI,EAAE,GAAG;QACT,WAAW,EAAE,CAAC,QAAQ,CAAC;QACvB,SAAS,EAAE;YACT,QAAQ,EAAE,CAAC,KAAK,EAAE,IAAI,EAAW,EAAE,CAAC,GAAG,CAAC,KAAK,EAAE,IAAI,aAAJ,IAAI,uBAAJ,IAAI,CAAE,WAAW,CAAC,CAAC,CAAC,CAAC;YACpE,cAAc,EAAE,YAAY,CAC1B,UAAU,CAAC,EAAE,CAAC,UAAU,GAAG,8CAA8C,EACzE,iBAAiB,CAClB;SACF;KACF,EACD,iBAAiB,CAClB,CAAC;AACJ,CAAC", "sourcesContent": ["import { ValidationOptions } from '../ValidationOptions';\nimport { buildMessage, ValidateBy } from '../common/ValidateBy';\n\nexport const MIN = 'min';\n\n/**\n * Checks if the first number is greater than or equal to the second.\n */\nexport function min(num: unknown, min: number): boolean {\n  return typeof num === 'number' && typeof min === 'number' && num >= min;\n}\n\n/**\n * Checks if the value is greater than or equal to the allowed minimum value.\n */\nexport function Min(minValue: number, validationOptions?: ValidationOptions): PropertyDecorator {\n  return ValidateBy(\n    {\n      name: MIN,\n      constraints: [minValue],\n      validator: {\n        validate: (value, args): boolean => min(value, args?.constraints[0]),\n        defaultMessage: buildMessage(\n          eachPrefix => eachPrefix + '$property must not be less than $constraint1',\n          validationOptions\n        ),\n      },\n    },\n    validationOptions\n  );\n}\n"]}