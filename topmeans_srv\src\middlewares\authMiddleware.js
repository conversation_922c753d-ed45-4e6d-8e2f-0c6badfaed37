const jwt = require('jsonwebtoken');
const logger = require('../log/logger');

// JWT 认证中间件
const authenticateToken = (req, res, next) => {
  try {
    const authHeader = req.headers['authorization'];
    const token = authHeader && authHeader.split(' ')[1]; // Bearer TOKEN

    if (!token) {
      return res.status(401).json({
        success: false,
        message: '访问令牌缺失'
      });
    }

    jwt.verify(token, process.env.JWT_SECRET, (err, user) => {
      if (err) {
        logger.warn('JWT 验证失败:', err.message);
        return res.status(403).json({
          success: false,
          message: '访问令牌无效'
        });
      }

      req.user = user;
      next();
    });
  } catch (error) {
    logger.error('认证中间件错误:', error);
    res.status(500).json({
      success: false,
      message: '服务器内部错误'
    });
  }
};

// 可选认证中间件（不强制要求登录）
const optionalAuth = (req, res, next) => {
  try {
    const authHeader = req.headers['authorization'];
    const token = authHeader && authHeader.split(' ')[1];

    if (token) {
      jwt.verify(token, process.env.JWT_SECRET, (err, user) => {
        if (!err) {
          req.user = user;
        }
      });
    }

    next();
  } catch (error) {
    logger.error('可选认证中间件错误:', error);
    next(); // 继续执行，不阻断请求
  }
};

module.exports = {
  authenticateToken,
  optionalAuth
};
