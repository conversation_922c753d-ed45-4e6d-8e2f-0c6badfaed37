{"version": 3, "file": "decorator_utils.js", "sourceRoot": "", "sources": ["../../src/annotation/decorator_utils.ts"], "names": [], "mappings": "AAAA,OAAO,KAAK,UAAU,MAAM,yBAAyB,CAAC;AACtD,OAAO,KAAK,YAAY,MAAM,4BAA4B,CAAC;AAE3D,OAAO,EAAE,sBAAsB,EAAE,MAAM,aAAa,CAAC;AAErD,SAAS,2BAA2B,CAAa,MAA0B;IACzE,OAAQ,MAAiC,CAAC,SAAS,KAAK,SAAS,CAAC;AACpE,CAAC;AAgBD,SAAS,uBAAuB,CAAC,aAA0C;IACzE,IAAI,aAAa,KAAK,SAAS,EAAE;QAC/B,MAAM,IAAI,KAAK,CAAC,UAAU,CAAC,2BAA2B,CAAC,CAAC;KACzD;AACH,CAAC;AAGD,SAAS,YAAY,CACnB,gBAAiC,EACjC,aAA0C,EAC1C,cAAsB,EACtB,QAA4C;IAE5C,uBAAuB,CAAC,aAAa,CAAC,CAAC;IACvC,uBAAuB,CAAC,YAAY,CAAC,MAAM,EAAE,gBAAuC,EAAE,cAAc,CAAC,QAAQ,EAAE,EAAE,QAAQ,CAAC,CAAC;AAC7H,CAAC;AAED,SAAS,WAAW,CAClB,gBAAiC,EACjC,YAA6B,EAC7B,QAA4C;IAE5C,IAAI,2BAA2B,CAAC,gBAAgB,CAAC,EAAE;QACjD,MAAM,IAAI,KAAK,CAAC,UAAU,CAAC,2BAA2B,CAAC,CAAC;KACzD;IACD,uBAAuB,CAAC,YAAY,CAAC,WAAW,EAAE,gBAAgB,CAAC,WAAW,EAAE,YAAY,EAAE,QAAQ,CAAC,CAAC;AAC1G,CAAC;AAED,SAAS,8BAA8B,CAAC,QAA4C;IAClF,IAAI,SAAS,GAA0B,EAAE,CAAC;IAC1C,IAAI,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,EAAE;QAC3B,SAAS,GAAG,QAAQ,CAAC;QACrB,IAAM,SAAS,GAAG,sBAAsB,CAAC,SAAS,CAAC,GAAG,CAAC,UAAA,EAAE,IAAI,OAAA,EAAE,CAAC,GAAG,EAAN,CAAM,CAAC,CAAC,CAAC;QACtE,IAAI,SAAS,KAAK,SAAS,EAAE;YAC3B,MAAM,IAAI,KAAK,CAAI,UAAU,CAAC,mBAAmB,SAAI,SAAS,CAAC,QAAQ,EAAI,CAAC,CAAC;SAC9E;KACF;SAAM;QACL,SAAS,GAAG,CAAC,QAAQ,CAAC,CAAC;KACxB;IACD,OAAO,SAAS,CAAC;AACnB,CAAC;AAED,SAAS,uBAAuB,CAC9B,WAAmB,EACnB,gBAAiC,EACjC,GAAoB,EACpB,QAA4C;IAE5C,IAAM,SAAS,GAA0B,8BAA8B,CAAC,QAAQ,CAAC,CAAC;IAElF,IAAI,0BAA0B,GAA+D,EAAE,CAAC;IAEhG,IAAI,OAAO,CAAC,cAAc,CAAC,WAAW,EAAE,gBAAgB,CAAC,EAAE;QACzD,0BAA0B,GAAG,OAAO,CAAC,WAAW,CAAC,WAAW,EAAE,gBAAgB,CAAC,CAAC;KACjF;IAED,IAAI,uBAAuB,GAAsC,0BAA0B,CAAC,GAAa,CAAC,CAAC;IAE3G,IAAI,uBAAuB,KAAK,SAAS,EAAE;QACzC,uBAAuB,GAAG,EAAE,CAAC;KAC9B;SAAM;gCACM,CAAC;YACV,IAAI,SAAS,CAAC,IAAI,CAAC,UAAA,EAAE,IAAI,OAAA,EAAE,CAAC,GAAG,KAAK,CAAC,CAAC,GAAG,EAAhB,CAAgB,CAAC,EAAE;gBAC1C,MAAM,IAAI,KAAK,CAAI,UAAU,CAAC,mBAAmB,SAAI,CAAC,CAAC,GAAG,CAAC,QAAQ,EAAI,CAAC,CAAC;aAC1E;;QAHH,KAAgB,UAAuB,EAAvB,mDAAuB,EAAvB,qCAAuB,EAAvB,IAAuB;YAAlC,IAAM,CAAC,gCAAA;oBAAD,CAAC;SAIX;KACF;IAGD,uBAAuB,CAAC,IAAI,OAA5B,uBAAuB,EAAS,SAAS,EAAE;IAC3C,0BAA0B,CAAC,GAAG,CAAC,GAAG,uBAAuB,CAAC;IAC1D,OAAO,CAAC,cAAc,CAAC,WAAW,EAAE,0BAA0B,EAAE,gBAAgB,CAAC,CAAC;AAEpF,CAAC;AAID,SAAS,qBAAqB,CAC5B,QAA4C;IAE5C,OAAO,UACL,MAAuB,EACvB,SAA2B,EAC3B,yBAA+D;QAE/D,IAAI,OAAO,yBAAyB,KAAK,QAAQ,EAAE;YACjD,YAAY,CAAC,MAAM,EAAE,SAAS,EAAE,yBAAyB,EAAE,QAAQ,CAAC,CAAC;SACtE;aAAM;YACL,WAAW,CAAC,MAAM,EAAE,SAA4B,EAAE,QAAQ,CAAC,CAAC;SAC7D;IACH,CAAC,CAAC;AACJ,CAAC;AAED,SAAS,SAAS,CAChB,UAAsE,EACtE,MAAuB;IAEvB,OAAO,CAAC,QAAQ,CAAC,UAA8B,EAAE,MAAM,CAAC,CAAC;AAC3D,CAAC;AAED,SAAS,MAAM,CAAC,UAAkB,EAAE,SAA6B;IAC/D,OAAO,UAAU,MAAc,EAAE,GAAW,IAAI,SAAS,CAAC,MAAM,EAAE,GAAG,EAAE,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC;AACxF,CAAC;AAOD,SAAS,QAAQ,CACf,SAAmE,EACnE,MAAW,EACX,wBAA0C;IAE1C,IAAI,OAAO,wBAAwB,KAAK,QAAQ,EAAE;QAChD,SAAS,CAAC,CAAC,MAAM,CAAC,wBAAwB,EAAE,SAA+B,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC;KACxF;SAAM,IAAI,OAAO,wBAAwB,KAAK,QAAQ,EAAE;QACvD,OAAO,CAAC,QAAQ,CAAC,CAAC,SAA4B,CAAC,EAAE,MAAM,EAAE,wBAAwB,CAAC,CAAC;KACpF;SAAM;QACL,SAAS,CAAC,CAAC,SAAS,CAAC,EAAE,MAAM,CAAC,CAAC;KAChC;AACH,CAAC;AAED,OAAO,EAAE,QAAQ,EAAE,YAAY,EAAE,WAAW,EAAE,qBAAqB,EAAE,CAAC"}