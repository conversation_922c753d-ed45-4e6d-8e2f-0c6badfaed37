{"version": 3, "file": "IsArray.js", "sourceRoot": "", "sources": ["../../../../src/decorator/typechecker/IsArray.ts"], "names": [], "mappings": ";;;AACA,qDAAgE;AAEnD,QAAA,QAAQ,GAAG,SAAS,CAAC;AAElC;;GAEG;AACH,SAAgB,OAAO,CAAU,KAAc;IAC7C,OAAO,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;AAC9B,CAAC;AAFD,0BAEC;AAED;;GAEG;AACH,SAAgB,OAAO,CAAC,iBAAqC;IAC3D,OAAO,IAAA,uBAAU,EACf;QACE,IAAI,EAAE,gBAAQ;QACd,SAAS,EAAE;YACT,QAAQ,EAAE,CAAC,KAAK,EAAE,IAAI,EAAW,EAAE,CAAC,OAAO,CAAC,KAAK,CAAC;YAClD,cAAc,EAAE,IAAA,yBAAY,EAAC,UAAU,CAAC,EAAE,CAAC,UAAU,GAAG,4BAA4B,EAAE,iBAAiB,CAAC;SACzG;KACF,EACD,iBAAiB,CAClB,CAAC;AACJ,CAAC;AAXD,0BAWC", "sourcesContent": ["import { ValidationOptions } from '../ValidationOptions';\nimport { buildMessage, ValidateBy } from '../common/ValidateBy';\n\nexport const IS_ARRAY = 'isArray';\n\n/**\n * Checks if a given value is an array\n */\nexport function isArray<T = any>(value: unknown): value is Array<T> {\n  return Array.isArray(value);\n}\n\n/**\n * Checks if a given value is an array\n */\nexport function IsArray(validationOptions?: ValidationOptions): PropertyDecorator {\n  return ValidateBy(\n    {\n      name: IS_ARRAY,\n      validator: {\n        validate: (value, args): boolean => isArray(value),\n        defaultMessage: buildMessage(eachPrefix => eachPrefix + '$property must be an array', validationOptions),\n      },\n    },\n    validationOptions\n  );\n}\n"]}