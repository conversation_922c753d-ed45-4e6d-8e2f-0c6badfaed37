#!/bin/bash

# TopMeansLab 生产环境部署脚本

set -e  # 遇到错误立即退出

echo "🚀 开始部署 TopMeansLab 到生产环境..."

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# 配置变量
PROJECT_DIR="/path/to/topmeanslab"  # 请替换为实际路径
NGINX_SITES_DIR="/etc/nginx/sites-available"
NGINX_ENABLED_DIR="/etc/nginx/sites-enabled"
BACKUP_DIR="/backup/topmeanslab"

# 函数：打印带颜色的消息
print_message() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查是否为 root 用户
if [ "$EUID" -ne 0 ]; then
    print_error "请使用 root 权限运行此脚本"
    exit 1
fi

# 1. 备份当前版本
print_message "创建备份..."
mkdir -p $BACKUP_DIR
if [ -d "$PROJECT_DIR/.vitepress/dist" ]; then
    cp -r $PROJECT_DIR/.vitepress/dist $BACKUP_DIR/dist-$(date +%Y%m%d-%H%M%S)
    print_message "备份完成"
fi

# 2. 进入项目目录
cd $PROJECT_DIR/topmeanslab

# 3. 拉取最新代码 (如果使用 Git)
print_message "拉取最新代码..."
if [ -d ".git" ]; then
    git pull origin main || git pull origin master
    print_message "代码更新完成"
fi

# 4. 安装依赖
print_message "安装/更新依赖..."
npm ci --production=false

# 5. 清理缓存
print_message "清理构建缓存..."
npm run clear-cache

# 6. 构建生产版本
print_message "构建生产版本..."
npm run build:production

# 7. 配置 Nginx
print_message "配置 Nginx..."
if [ -f "../nginx-topmeanslab.conf" ]; then
    cp ../nginx-topmeanslab.conf $NGINX_SITES_DIR/topmeanslab
    
    # 创建软链接
    if [ ! -L "$NGINX_ENABLED_DIR/topmeanslab" ]; then
        ln -s $NGINX_SITES_DIR/topmeanslab $NGINX_ENABLED_DIR/topmeanslab
    fi
    
    # 测试 Nginx 配置
    nginx -t
    if [ $? -eq 0 ]; then
        print_message "Nginx 配置测试通过"
    else
        print_error "Nginx 配置测试失败"
        exit 1
    fi
else
    print_warning "未找到 Nginx 配置文件，请手动配置"
fi

# 8. 重启服务
print_message "重启服务..."

# 重启后端服务 (PM2)
cd ../topmeans_srv
if command -v pm2 &> /dev/null; then
    pm2 restart topmeans_srv || pm2 start ecosystem.config.js --env production
    print_message "后端服务重启完成"
else
    print_warning "PM2 未安装，请手动启动后端服务"
fi

# 重启 Nginx
systemctl reload nginx
print_message "Nginx 重新加载完成"

# 9. 验证部署
print_message "验证部署..."
sleep 5

# 检查后端服务状态
if command -v pm2 &> /dev/null; then
    pm2 status topmeans_srv
fi

# 检查 Nginx 状态
systemctl status nginx --no-pager -l

# 检查网站是否可访问
if command -v curl &> /dev/null; then
    HTTP_STATUS=$(curl -s -o /dev/null -w "%{http_code}" https://topmeanslab.com)
    if [ "$HTTP_STATUS" = "200" ]; then
        print_message "网站访问正常 (HTTP $HTTP_STATUS)"
    else
        print_warning "网站访问异常 (HTTP $HTTP_STATUS)"
    fi
fi

print_message "🎉 部署完成！"
print_message "请访问 https://topmeanslab.com 验证网站功能"

echo ""
echo "📋 部署后检查清单："
echo "  ✅ 检查网站是否正常访问"
echo "  ✅ 测试登录功能"
echo "  ✅ 检查 API 请求是否正常"
echo "  ✅ 验证 HTTPS 证书"
echo "  ✅ 检查日志文件"
