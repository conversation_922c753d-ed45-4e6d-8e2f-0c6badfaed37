{"version": 3, "file": "parsePhoneNumberWithError.js", "names": ["parsePhoneNumberWithError", "normalizeArguments", "arguments", "text", "options", "metadata", "parsePhoneNumberWithError_"], "sources": ["../source/parsePhoneNumberWithError.js"], "sourcesContent": ["import parsePhoneNumberWithError_ from './parsePhoneNumberWithError_.js'\r\nimport normalizeArguments from './normalizeArguments.js'\r\n\r\nexport default function parsePhoneNumberWithError() {\r\n\tconst { text, options, metadata } = normalizeArguments(arguments)\r\n\treturn parsePhoneNumberWithError_(text, options, metadata)\r\n}"], "mappings": ";;;;;;;AAAA;;AACA;;;;AAEe,SAASA,yBAAT,GAAqC;EACnD,0BAAoC,IAAAC,+BAAA,EAAmBC,SAAnB,CAApC;EAAA,IAAQC,IAAR,uBAAQA,IAAR;EAAA,IAAcC,OAAd,uBAAcA,OAAd;EAAA,IAAuBC,QAAvB,uBAAuBA,QAAvB;;EACA,OAAO,IAAAC,sCAAA,EAA2BH,IAA3B,EAAiCC,OAAjC,EAA0CC,QAA1C,CAAP;AACA"}