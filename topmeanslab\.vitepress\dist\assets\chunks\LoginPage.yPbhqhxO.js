import{_ as I,p as h,v as N,c as C,o as _,ab as $,j as s,e as S,n as A,ao as m,aJ as b,G as L,a as V,aD as B,t as M}from"./framework.neMYHtQj.js";import{V as U}from"./Valicode.Dl4gpGZx.js";import{_ as v,u as F,E as d}from"./theme.Ch1k4S35.js";const D={class:"login-page"},G={class:"login-container"},O={class:"auth-section"},J={class:"auth-container"},R={class:"auth-tabs"},z={key:0,class:"auth-form"},H={class:"form-group"},K={class:"form-group"},Q={class:"form-group"},W={class:"captcha-container"},X={class:"captcha-wrapper"},Y={class:"form-options"},Z={class:"remember-me"},ee=["disabled"],se={key:0,class:"loading-spinner"},ae={key:1,class:"auth-form"},te={class:"form-group"},oe={class:"form-group"},re={class:"form-group"},le={class:"form-group"},ne={class:"captcha-container"},ie={class:"captcha-wrapper"},de=["disabled"],ue={key:0,class:"loading-spinner"},ce={__name:"LoginPage",setup(ve){const x="https://topmeanslab.com/api";v.setLevel("info");const g=h("login"),w=F(),p=h(!1),T=u=>{P.value=u},a=h({account:"",password:"",valicode:"",remember:!1}),r=h({account:"",password:"",confirmPassword:"",valicode:"",nickname:"",avatar:"/images/default-avatar.jpg",signature:"这个人很懒，什么都没写~"}),c=h(null),P=h(""),j=async()=>{var y,k;if(!a.value.account||!a.value.password){d.error({message:"请输入账号和密码",duration:2e3}),(y=c.value)==null||y.refresh();return}if(!a.value.valicode){d.error({message:"请输入验证码",duration:2e3});return}if(a.value.valicode.toLowerCase()!==P.value.toLowerCase()){d.error({message:"验证码错误，请重新输入",duration:2e3}),(k=c.value)==null||k.refresh();return}p.value=!0;let u=0;const e=2,t=async()=>{var l,n;try{v.info("尝试登录:",{account:a.value.account,apiBase:x});const o=await fetch(`${x}/user/login`,{method:"POST",headers:{"Content-Type":"application/json",Accept:"application/json"},body:JSON.stringify({account:a.value.account,password:a.value.password}),signal:AbortSignal.timeout(1e4)});if(v.info("登录响应状态:",o.status),!o.ok){const f=await o.text();throw v.error("登录响应错误:",f),new Error(`服务器响应错误 (${o.status}): ${o.statusText}`)}const i=await o.json();if(v.info("登录响应结果:",i),i.success)a.value.remember?w.setStoredPassword(a.value.password):w.clearStoredPassword(),w.setToken(i.token),w.setUserInfo(i.user),a.value.remember?localStorage.setItem("rememberedAccount",a.value.account):localStorage.removeItem("rememberedAccount"),d.success({message:"登录成功，正在跳转...",duration:1e3,onClose:()=>{window.location.reload()}});else{let f="登录失败";i.message&&(i.message.includes("账号")||i.message.includes("用户")?f="账号不存在或格式错误":i.message.includes("密码")?f="密码错误，请检查后重试":i.message.includes("验证码")?f="验证码错误，请重新输入":f=i.message),d.error({message:f,duration:3e3}),(l=c.value)==null||l.refresh()}}catch(o){u++,v.error(`登录尝试 ${u} 失败:`,o);let i="登录失败，请稍后重试";if(o.name==="AbortError"||o.message.includes("timeout")?i="请求超时，请检查网络连接":o.message.includes("Failed to fetch")?i="网络连接失败，请检查网络设置":o.message.includes("Mixed Content")?i="网络安全限制，请联系管理员":o.message.includes("服务器响应错误")&&(i=o.message),u<=e)return d.warning({message:`${i}，正在重试... (${u}/${e})`,duration:2e3}),await new Promise(f=>setTimeout(f,1e3)),t();d.error({message:i,duration:3e3}),(n=c.value)==null||n.refresh()}};try{await t()}finally{p.value=!1}},E=async()=>{var u,e,t,y,k;if(!r.value.account||!r.value.password){d.error({message:"请输入账号和密码",duration:2e3}),(u=c.value)==null||u.refresh();return}if(r.value.account.length<3){d.error({message:"账号长度至少3个字符",duration:2e3});return}if(r.value.password.length<6){d.error({message:"密码长度至少6个字符",duration:2e3});return}if(r.value.password!==r.value.confirmPassword){d.error({message:"两次输入的密码不一致，请重新输入",duration:2e3}),(e=c.value)==null||e.refresh();return}if(!r.value.valicode){d.error({message:"请输入验证码",duration:2e3});return}if(r.value.valicode.toLowerCase()!==P.value.toLowerCase()){d.error({message:"验证码错误，请重新输入",duration:2e3}),(t=c.value)==null||t.refresh();return}p.value=!0;try{v.info("尝试注册:",{account:r.value.account,apiBase:x});const l=await fetch(`${x}/user/register`,{method:"POST",headers:{"Content-Type":"application/json",Accept:"application/json"},body:JSON.stringify(r.value),signal:AbortSignal.timeout(1e4)});if(v.info("注册响应状态:",l.status),!l.ok){const o=await l.text();throw v.error("注册响应错误:",o),new Error(`服务器响应错误 (${l.status}): ${l.statusText}`)}const n=await l.json();if(v.info("注册响应结果:",n),n.success)d.success({message:"注册成功，请登录",duration:2e3}),g.value="login",a.value.account=r.value.account,r.value={account:"",password:"",confirmPassword:"",valicode:"",nickname:"",avatar:"/images/default-avatar.jpg",signature:"这个人很懒，什么都没写~"};else{let o="注册失败";n.message&&(n.message.includes("账号")&&n.message.includes("存在")?o="该账号已存在，请更换账号或直接登录":n.message.includes("格式")?o="账号格式不正确，请使用中文、英文或数字":n.message.includes("密码")?o="密码格式不符合要求":o=n.message),d.error({message:o,duration:3e3}),(y=c.value)==null||y.refresh()}}catch(l){v.error("注册错误:",l);let n="注册失败，请稍后重试";l.name==="AbortError"||l.message.includes("timeout")?n="请求超时，请检查网络连接":l.message.includes("Failed to fetch")?n="网络连接失败，请检查网络设置":l.message.includes("Mixed Content")?n="网络安全限制，请联系管理员":l.message.includes("服务器响应错误")&&(n=l.message),d.error({message:n,duration:3e3}),(k=c.value)==null||k.refresh()}finally{p.value=!1}},q=u=>{u.preventDefault(),d.info("忘记密码功能暂未开放，请联系管理员")};return N(()=>{if(w.storedPassword&&localStorage.getItem("rememberedAccount")){const u=localStorage.getItem("rememberedAccount");a.value.account=u,a.value.password=w.storedPassword,a.value.remember=!0}}),(u,e)=>(_(),C("div",D,[e[21]||(e[21]=$('<div class="background-decoration" data-v-f5e85560><div class="floating-shapes" data-v-f5e85560><div class="shape shape-1" data-v-f5e85560></div><div class="shape shape-2" data-v-f5e85560></div><div class="shape shape-3" data-v-f5e85560></div><div class="shape shape-4" data-v-f5e85560></div><div class="shape shape-5" data-v-f5e85560></div></div></div>',1)),s("div",G,[e[20]||(e[20]=$('<div class="brand-section" data-v-f5e85560><div class="brand-content" data-v-f5e85560><h1 class="brand-title" data-v-f5e85560>旅美</h1><p class="brand-subtitle" data-v-f5e85560>一段旅途，一段人生，智慧出行，美好常伴</p><div class="brand-features" data-v-f5e85560><div class="feature-item" data-v-f5e85560><div class="feature-icon" data-v-f5e85560>🗺️</div><span data-v-f5e85560>智能路线规划</span></div><div class="feature-item" data-v-f5e85560><div class="feature-icon" data-v-f5e85560>🏨</div><span data-v-f5e85560>酒店推荐</span></div><div class="feature-item" data-v-f5e85560><div class="feature-icon" data-v-f5e85560>🍽️</div><span data-v-f5e85560>美食发现</span></div><div class="feature-item" data-v-f5e85560><div class="feature-icon" data-v-f5e85560>📸</div><span data-v-f5e85560>景点攻略</span></div></div></div></div>',1)),s("div",O,[s("div",J,[e[19]||(e[19]=s("div",{class:"auth-header"},[s("h2",null,"欢迎使用 TopMeansLab"),s("p",null,"请登录您的账户开始旅程规划")],-1)),s("div",R,[s("button",{class:A(["tab-btn",{active:g.value==="login"}]),onClick:e[0]||(e[0]=t=>g.value="login")}," 登录 ",2),s("button",{class:A(["tab-btn",{active:g.value==="register"}]),onClick:e[1]||(e[1]=t=>g.value="register")}," 注册 ",2)]),g.value==="login"?(_(),C("div",z,[s("div",H,[e[10]||(e[10]=s("label",null,"账号",-1)),m(s("input",{"onUpdate:modelValue":e[2]||(e[2]=t=>a.value.account=t),type:"text",placeholder:"请输入手机号或邮箱",class:"form-input",required:""},null,512),[[b,a.value.account]])]),s("div",K,[e[11]||(e[11]=s("label",null,"密码",-1)),m(s("input",{"onUpdate:modelValue":e[3]||(e[3]=t=>a.value.password=t),type:"password",placeholder:"请输入密码",class:"form-input",required:""},null,512),[[b,a.value.password]])]),s("div",Q,[e[12]||(e[12]=s("label",null,"验证码",-1)),s("div",W,[m(s("input",{"onUpdate:modelValue":e[4]||(e[4]=t=>a.value.valicode=t),type:"text",placeholder:"请输入验证码",class:"form-input captcha-input",required:""},null,512),[[b,a.value.valicode]]),s("div",X,[L(U,{ref_key:"valicode",ref:c,onGetCode:T},null,512)])])]),s("div",Y,[s("label",Z,[m(s("input",{type:"checkbox","onUpdate:modelValue":e[5]||(e[5]=t=>a.value.remember=t)},null,512),[[B,a.value.remember]]),e[13]||(e[13]=s("span",{class:"checkmark"},null,-1)),e[14]||(e[14]=V(" 记住密码 "))]),s("a",{href:"#",class:"forgot-password",onClick:q},"忘记密码？")]),s("button",{onClick:j,class:"submit-btn",disabled:p.value},[p.value?(_(),C("span",se)):S("",!0),V(" "+M(p.value?"登录中...":"登录"),1)],8,ee)])):S("",!0),g.value==="register"?(_(),C("div",ae,[s("div",te,[e[15]||(e[15]=s("label",null,"账号",-1)),m(s("input",{"onUpdate:modelValue":e[6]||(e[6]=t=>r.value.account=t),type:"text",placeholder:"请输入你的账号名称",class:"form-input",required:""},null,512),[[b,r.value.account]])]),s("div",oe,[e[16]||(e[16]=s("label",null,"密码",-1)),m(s("input",{"onUpdate:modelValue":e[7]||(e[7]=t=>r.value.password=t),type:"password",placeholder:"请输入密码，由6位以上的英文和数字组成",class:"form-input",required:""},null,512),[[b,r.value.password]])]),s("div",re,[e[17]||(e[17]=s("label",null,"确认密码",-1)),m(s("input",{"onUpdate:modelValue":e[8]||(e[8]=t=>r.value.confirmPassword=t),type:"password",placeholder:"请再次输入密码，确保两次密码一致",class:"form-input",required:""},null,512),[[b,r.value.confirmPassword]])]),s("div",le,[e[18]||(e[18]=s("label",null,"验证码",-1)),s("div",ne,[m(s("input",{"onUpdate:modelValue":e[9]||(e[9]=t=>r.value.valicode=t),type:"text",placeholder:"请输入验证码",class:"form-input captcha-input",required:""},null,512),[[b,r.value.valicode]]),s("div",ie,[L(U,{ref_key:"valicode",ref:c,onGetCode:T},null,512)])])]),s("button",{onClick:E,class:"submit-btn",disabled:p.value},[p.value?(_(),C("span",ue)):S("",!0),V(" "+M(p.value?"注册中...":"注册"),1)],8,de)])):S("",!0)])])])]))}},ge=I(ce,[["__scopeId","data-v-f5e85560"]]);export{ge as default};
