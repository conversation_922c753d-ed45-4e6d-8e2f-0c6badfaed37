import{_ as L,u as Pe}from"./theme.Ch1k4S35.js";import{m as oa}from"./TopmeansMarkdownService.BN2GN_Vw.js";import{c as J,o as X,ao as y,j as B,e as Vt,ap as I,t as R,ab as UA,n as sA,a as aA,aJ as Nt,b4 as Je,F as OA,B as qA,X as $r,Z as An,_ as la}from"./framework.neMYHtQj.js";/*!
 * html2canvas 1.4.1 <https://html2canvas.hertzen.com>
 * Copyright (c) 2022 <PERSON><PERSON> <https://hertzen.com>
 * Released under MIT License
 *//*! *****************************************************************************
Copyright (c) Microsoft Corporation.

Permission to use, copy, modify, and/or distribute this software for any
purpose with or without fee is hereby granted.

THE SOFTWARE IS PROVIDED "AS IS" AND THE AUTHOR DISCLAIMS ALL WARRANTIES WITH
REGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY
AND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY SPECIAL, DIRECT,
INDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM
LOSS OF USE, DATA OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE OR
OTHER TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR
PERFORMANCE OF THIS SOFTWARE.
***************************************************************************** */var ur=function(e,A){return ur=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,r){t.__proto__=r}||function(t,r){for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])},ur(e,A)};function CA(e,A){if(typeof A!="function"&&A!==null)throw new TypeError("Class extends value "+String(A)+" is not a constructor or null");ur(e,A);function t(){this.constructor=e}e.prototype=A===null?Object.create(A):(t.prototype=A.prototype,new t)}var gr=function(){return gr=Object.assign||function(A){for(var t,r=1,n=arguments.length;r<n;r++){t=arguments[r];for(var s in t)Object.prototype.hasOwnProperty.call(t,s)&&(A[s]=t[s])}return A},gr.apply(this,arguments)};function AA(e,A,t,r){function n(s){return s instanceof t?s:new t(function(a){a(s)})}return new(t||(t=Promise))(function(s,a){function o(c){try{l(r.next(c))}catch(x){a(x)}}function i(c){try{l(r.throw(c))}catch(x){a(x)}}function l(c){c.done?s(c.value):n(c.value).then(o,i)}l((r=r.apply(e,[])).next())})}function j(e,A){var t={label:0,sent:function(){if(s[0]&1)throw s[1];return s[1]},trys:[],ops:[]},r,n,s,a;return a={next:o(0),throw:o(1),return:o(2)},typeof Symbol=="function"&&(a[Symbol.iterator]=function(){return this}),a;function o(l){return function(c){return i([l,c])}}function i(l){if(r)throw new TypeError("Generator is already executing.");for(;t;)try{if(r=1,n&&(s=l[0]&2?n.return:l[0]?n.throw||((s=n.return)&&s.call(n),0):n.next)&&!(s=s.call(n,l[1])).done)return s;switch(n=0,s&&(l=[l[0]&2,s.value]),l[0]){case 0:case 1:s=l;break;case 4:return t.label++,{value:l[1],done:!1};case 5:t.label++,n=l[1],l=[0];continue;case 7:l=t.ops.pop(),t.trys.pop();continue;default:if(s=t.trys,!(s=s.length>0&&s[s.length-1])&&(l[0]===6||l[0]===2)){t=0;continue}if(l[0]===3&&(!s||l[1]>s[0]&&l[1]<s[3])){t.label=l[1];break}if(l[0]===6&&t.label<s[1]){t.label=s[1],s=l;break}if(s&&t.label<s[2]){t.label=s[2],t.ops.push(l);break}s[2]&&t.ops.pop(),t.trys.pop();continue}l=A.call(e,t)}catch(c){l=[6,c],n=0}finally{r=s=0}if(l[0]&5)throw l[1];return{value:l[0]?l[1]:void 0,done:!0}}}function Xe(e,A,t){if(arguments.length===2)for(var r=0,n=A.length,s;r<n;r++)(s||!(r in A))&&(s||(s=Array.prototype.slice.call(A,0,r)),s[r]=A[r]);return e.concat(s||A)}var KA=function(){function e(A,t,r,n){this.left=A,this.top=t,this.width=r,this.height=n}return e.prototype.add=function(A,t,r,n){return new e(this.left+A,this.top+t,this.width+r,this.height+n)},e.fromClientRect=function(A,t){return new e(t.left+A.windowBounds.left,t.top+A.windowBounds.top,t.width,t.height)},e.fromDOMRectList=function(A,t){var r=Array.from(t).find(function(n){return n.width!==0});return r?new e(r.left+A.windowBounds.left,r.top+A.windowBounds.top,r.width,r.height):e.EMPTY},e.EMPTY=new e(0,0,0,0),e}(),Dt=function(e,A){return KA.fromClientRect(e,A.getBoundingClientRect())},ca=function(e){var A=e.body,t=e.documentElement;if(!A||!t)throw new Error("Unable to get document size");var r=Math.max(Math.max(A.scrollWidth,t.scrollWidth),Math.max(A.offsetWidth,t.offsetWidth),Math.max(A.clientWidth,t.clientWidth)),n=Math.max(Math.max(A.scrollHeight,t.scrollHeight),Math.max(A.offsetHeight,t.offsetHeight),Math.max(A.clientHeight,t.clientHeight));return new KA(0,0,r,n)},Lt=function(e){for(var A=[],t=0,r=e.length;t<r;){var n=e.charCodeAt(t++);if(n>=55296&&n<=56319&&t<r){var s=e.charCodeAt(t++);(s&64512)===56320?A.push(((n&1023)<<10)+(s&1023)+65536):(A.push(n),t--)}else A.push(n)}return A},N=function(){for(var e=[],A=0;A<arguments.length;A++)e[A]=arguments[A];if(String.fromCodePoint)return String.fromCodePoint.apply(String,e);var t=e.length;if(!t)return"";for(var r=[],n=-1,s="";++n<t;){var a=e[n];a<=65535?r.push(a):(a-=65536,r.push((a>>10)+55296,a%1024+56320)),(n+1===t||r.length>16384)&&(s+=String.fromCharCode.apply(String,r),r.length=0)}return s},en="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",Ba=typeof Uint8Array>"u"?[]:new Uint8Array(256);for(var Ye=0;Ye<en.length;Ye++)Ba[en.charCodeAt(Ye)]=Ye;var tn="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",ye=typeof Uint8Array>"u"?[]:new Uint8Array(256);for(var We=0;We<tn.length;We++)ye[tn.charCodeAt(We)]=We;var xa=function(e){var A=e.length*.75,t=e.length,r,n=0,s,a,o,i;e[e.length-1]==="="&&(A--,e[e.length-2]==="="&&A--);var l=typeof ArrayBuffer<"u"&&typeof Uint8Array<"u"&&typeof Uint8Array.prototype.slice<"u"?new ArrayBuffer(A):new Array(A),c=Array.isArray(l)?l:new Uint8Array(l);for(r=0;r<t;r+=4)s=ye[e.charCodeAt(r)],a=ye[e.charCodeAt(r+1)],o=ye[e.charCodeAt(r+2)],i=ye[e.charCodeAt(r+3)],c[n++]=s<<2|a>>4,c[n++]=(a&15)<<4|o>>2,c[n++]=(o&3)<<6|i&63;return l},ua=function(e){for(var A=e.length,t=[],r=0;r<A;r+=2)t.push(e[r+1]<<8|e[r]);return t},ga=function(e){for(var A=e.length,t=[],r=0;r<A;r+=4)t.push(e[r+3]<<24|e[r+2]<<16|e[r+1]<<8|e[r]);return t},ae=5,Xr=11,Pt=2,ha=Xr-ae,ss=65536>>ae,da=1<<ae,Jt=da-1,wa=1024>>ae,fa=ss+wa,Qa=fa,Ca=32,pa=Qa+Ca,Ua=65536>>Xr,Fa=1<<ha,ma=Fa-1,rn=function(e,A,t){return e.slice?e.slice(A,t):new Uint16Array(Array.prototype.slice.call(e,A,t))},va=function(e,A,t){return e.slice?e.slice(A,t):new Uint32Array(Array.prototype.slice.call(e,A,t))},Ea=function(e,A){var t=xa(e),r=Array.isArray(t)?ga(t):new Uint32Array(t),n=Array.isArray(t)?ua(t):new Uint16Array(t),s=24,a=rn(n,s/2,r[4]/2),o=r[5]===2?rn(n,(s+r[4])/2):va(r,Math.ceil((s+r[4])/4));return new ya(r[0],r[1],r[2],r[3],a,o)},ya=function(){function e(A,t,r,n,s,a){this.initialValue=A,this.errorValue=t,this.highStart=r,this.highValueIndex=n,this.index=s,this.data=a}return e.prototype.get=function(A){var t;if(A>=0){if(A<55296||A>56319&&A<=65535)return t=this.index[A>>ae],t=(t<<Pt)+(A&Jt),this.data[t];if(A<=65535)return t=this.index[ss+(A-55296>>ae)],t=(t<<Pt)+(A&Jt),this.data[t];if(A<this.highStart)return t=pa-Ua+(A>>Xr),t=this.index[t],t+=A>>ae&ma,t=this.index[t],t=(t<<Pt)+(A&Jt),this.data[t];if(A<=1114111)return this.data[this.highValueIndex]}return this.errorValue},e}(),nn="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",Ha=typeof Uint8Array>"u"?[]:new Uint8Array(256);for(var Ze=0;Ze<nn.length;Ze++)Ha[nn.charCodeAt(Ze)]=Ze;var Ia="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",sn=50,ba=1,as=2,is=3,Sa=4,Da=5,an=7,os=8,on=9,kA=10,hr=11,ln=12,dr=13,La=14,He=15,wr=16,ze=17,Fe=18,Ka=19,cn=20,fr=21,me=22,Xt=23,le=24,xA=25,Ie=26,be=27,ce=28,Ta=29,te=30,Ma=31,qe=32,je=33,Qr=34,Cr=35,pr=36,Re=37,Ur=38,wt=39,ft=40,Yt=41,ls=42,Oa=43,_a=[9001,65288],cs="!",D="×",$e="÷",Fr=Ea(Ia),IA=[te,pr],mr=[ba,as,is,Da],Bs=[kA,os],Bn=[be,Ie],Ra=mr.concat(Bs),xn=[Ur,wt,ft,Qr,Cr],Ga=[He,dr],ka=function(e,A){A===void 0&&(A="strict");var t=[],r=[],n=[];return e.forEach(function(s,a){var o=Fr.get(s);if(o>sn?(n.push(!0),o-=sn):n.push(!1),["normal","auto","loose"].indexOf(A)!==-1&&[8208,8211,12316,12448].indexOf(s)!==-1)return r.push(a),t.push(wr);if(o===Sa||o===hr){if(a===0)return r.push(a),t.push(te);var i=t[a-1];return Ra.indexOf(i)===-1?(r.push(r[a-1]),t.push(i)):(r.push(a),t.push(te))}if(r.push(a),o===Ma)return t.push(A==="strict"?fr:Re);if(o===ls||o===Ta)return t.push(te);if(o===Oa)return s>=131072&&s<=196605||s>=196608&&s<=262141?t.push(Re):t.push(te);t.push(o)}),[r,t,n]},Wt=function(e,A,t,r){var n=r[t];if(Array.isArray(e)?e.indexOf(n)!==-1:e===n)for(var s=t;s<=r.length;){s++;var a=r[s];if(a===A)return!0;if(a!==kA)break}if(n===kA)for(var s=t;s>0;){s--;var o=r[s];if(Array.isArray(e)?e.indexOf(o)!==-1:e===o)for(var i=t;i<=r.length;){i++;var a=r[i];if(a===A)return!0;if(a!==kA)break}if(o!==kA)break}return!1},un=function(e,A){for(var t=e;t>=0;){var r=A[t];if(r===kA)t--;else return r}return 0},Va=function(e,A,t,r,n){if(t[r]===0)return D;var s=r-1;if(Array.isArray(n)&&n[s]===!0)return D;var a=s-1,o=s+1,i=A[s],l=a>=0?A[a]:0,c=A[o];if(i===as&&c===is)return D;if(mr.indexOf(i)!==-1)return cs;if(mr.indexOf(c)!==-1||Bs.indexOf(c)!==-1)return D;if(un(s,A)===os)return $e;if(Fr.get(e[s])===hr||(i===qe||i===je)&&Fr.get(e[o])===hr||i===an||c===an||i===on||[kA,dr,He].indexOf(i)===-1&&c===on||[ze,Fe,Ka,le,ce].indexOf(c)!==-1||un(s,A)===me||Wt(Xt,me,s,A)||Wt([ze,Fe],fr,s,A)||Wt(ln,ln,s,A))return D;if(i===kA)return $e;if(i===Xt||c===Xt)return D;if(c===wr||i===wr)return $e;if([dr,He,fr].indexOf(c)!==-1||i===La||l===pr&&Ga.indexOf(i)!==-1||i===ce&&c===pr||c===cn||IA.indexOf(c)!==-1&&i===xA||IA.indexOf(i)!==-1&&c===xA||i===be&&[Re,qe,je].indexOf(c)!==-1||[Re,qe,je].indexOf(i)!==-1&&c===Ie||IA.indexOf(i)!==-1&&Bn.indexOf(c)!==-1||Bn.indexOf(i)!==-1&&IA.indexOf(c)!==-1||[be,Ie].indexOf(i)!==-1&&(c===xA||[me,He].indexOf(c)!==-1&&A[o+1]===xA)||[me,He].indexOf(i)!==-1&&c===xA||i===xA&&[xA,ce,le].indexOf(c)!==-1)return D;if([xA,ce,le,ze,Fe].indexOf(c)!==-1)for(var x=s;x>=0;){var u=A[x];if(u===xA)return D;if([ce,le].indexOf(u)!==-1)x--;else break}if([be,Ie].indexOf(c)!==-1)for(var x=[ze,Fe].indexOf(i)!==-1?a:s;x>=0;){var u=A[x];if(u===xA)return D;if([ce,le].indexOf(u)!==-1)x--;else break}if(Ur===i&&[Ur,wt,Qr,Cr].indexOf(c)!==-1||[wt,Qr].indexOf(i)!==-1&&[wt,ft].indexOf(c)!==-1||[ft,Cr].indexOf(i)!==-1&&c===ft||xn.indexOf(i)!==-1&&[cn,Ie].indexOf(c)!==-1||xn.indexOf(c)!==-1&&i===be||IA.indexOf(i)!==-1&&IA.indexOf(c)!==-1||i===le&&IA.indexOf(c)!==-1||IA.concat(xA).indexOf(i)!==-1&&c===me&&_a.indexOf(e[o])===-1||IA.concat(xA).indexOf(c)!==-1&&i===Fe)return D;if(i===Yt&&c===Yt){for(var w=t[s],g=1;w>0&&(w--,A[w]===Yt);)g++;if(g%2!==0)return D}return i===qe&&c===je?D:$e},Na=function(e,A){A||(A={lineBreak:"normal",wordBreak:"normal"});var t=ka(e,A.lineBreak),r=t[0],n=t[1],s=t[2];(A.wordBreak==="break-all"||A.wordBreak==="break-word")&&(n=n.map(function(o){return[xA,te,ls].indexOf(o)!==-1?Re:o}));var a=A.wordBreak==="keep-all"?s.map(function(o,i){return o&&e[i]>=19968&&e[i]<=40959}):void 0;return[r,n,a]},Pa=function(){function e(A,t,r,n){this.codePoints=A,this.required=t===cs,this.start=r,this.end=n}return e.prototype.slice=function(){return N.apply(void 0,this.codePoints.slice(this.start,this.end))},e}(),Ja=function(e,A){var t=Lt(e),r=Na(t,A),n=r[0],s=r[1],a=r[2],o=t.length,i=0,l=0;return{next:function(){if(l>=o)return{done:!0,value:null};for(var c=D;l<o&&(c=Va(t,s,n,++l,a))===D;);if(c!==D||l===o){var x=new Pa(t,c,i,l);return i=l,{value:x,done:!1}}return{done:!0,value:null}}}},Xa=1,Ya=2,Ve=4,gn=8,pt=10,hn=47,Ke=92,Wa=9,Za=32,At=34,ve=61,za=35,qa=36,ja=37,et=39,tt=40,Ee=41,$a=95,lA=45,Ai=33,ei=60,ti=62,ri=64,ni=91,si=93,ai=61,ii=123,rt=63,oi=125,dn=124,li=126,ci=128,wn=65533,Zt=42,re=43,Bi=44,xi=58,ui=59,Ge=46,gi=0,hi=8,di=11,wi=14,fi=31,Qi=127,FA=-1,xs=48,us=97,gs=101,Ci=102,pi=117,Ui=122,hs=65,ds=69,ws=70,Fi=85,mi=90,$=function(e){return e>=xs&&e<=57},vi=function(e){return e>=55296&&e<=57343},Be=function(e){return $(e)||e>=hs&&e<=ws||e>=us&&e<=Ci},Ei=function(e){return e>=us&&e<=Ui},yi=function(e){return e>=hs&&e<=mi},Hi=function(e){return Ei(e)||yi(e)},Ii=function(e){return e>=ci},nt=function(e){return e===pt||e===Wa||e===Za},Ut=function(e){return Hi(e)||Ii(e)||e===$a},fn=function(e){return Ut(e)||$(e)||e===lA},bi=function(e){return e>=gi&&e<=hi||e===di||e>=wi&&e<=fi||e===Qi},GA=function(e,A){return e!==Ke?!1:A!==pt},st=function(e,A,t){return e===lA?Ut(A)||GA(A,t):Ut(e)?!0:!!(e===Ke&&GA(e,A))},zt=function(e,A,t){return e===re||e===lA?$(A)?!0:A===Ge&&$(t):$(e===Ge?A:e)},Si=function(e){var A=0,t=1;(e[A]===re||e[A]===lA)&&(e[A]===lA&&(t=-1),A++);for(var r=[];$(e[A]);)r.push(e[A++]);var n=r.length?parseInt(N.apply(void 0,r),10):0;e[A]===Ge&&A++;for(var s=[];$(e[A]);)s.push(e[A++]);var a=s.length,o=a?parseInt(N.apply(void 0,s),10):0;(e[A]===ds||e[A]===gs)&&A++;var i=1;(e[A]===re||e[A]===lA)&&(e[A]===lA&&(i=-1),A++);for(var l=[];$(e[A]);)l.push(e[A++]);var c=l.length?parseInt(N.apply(void 0,l),10):0;return t*(n+o*Math.pow(10,-a))*Math.pow(10,i*c)},Di={type:2},Li={type:3},Ki={type:4},Ti={type:13},Mi={type:8},Oi={type:21},_i={type:9},Ri={type:10},Gi={type:11},ki={type:12},Vi={type:14},at={type:23},Ni={type:1},Pi={type:25},Ji={type:24},Xi={type:26},Yi={type:27},Wi={type:28},Zi={type:29},zi={type:31},vr={type:32},fs=function(){function e(){this._value=[]}return e.prototype.write=function(A){this._value=this._value.concat(Lt(A))},e.prototype.read=function(){for(var A=[],t=this.consumeToken();t!==vr;)A.push(t),t=this.consumeToken();return A},e.prototype.consumeToken=function(){var A=this.consumeCodePoint();switch(A){case At:return this.consumeStringToken(At);case za:var t=this.peekCodePoint(0),r=this.peekCodePoint(1),n=this.peekCodePoint(2);if(fn(t)||GA(r,n)){var s=st(t,r,n)?Ya:Xa,a=this.consumeName();return{type:5,value:a,flags:s}}break;case qa:if(this.peekCodePoint(0)===ve)return this.consumeCodePoint(),Ti;break;case et:return this.consumeStringToken(et);case tt:return Di;case Ee:return Li;case Zt:if(this.peekCodePoint(0)===ve)return this.consumeCodePoint(),Vi;break;case re:if(zt(A,this.peekCodePoint(0),this.peekCodePoint(1)))return this.reconsumeCodePoint(A),this.consumeNumericToken();break;case Bi:return Ki;case lA:var o=A,i=this.peekCodePoint(0),l=this.peekCodePoint(1);if(zt(o,i,l))return this.reconsumeCodePoint(A),this.consumeNumericToken();if(st(o,i,l))return this.reconsumeCodePoint(A),this.consumeIdentLikeToken();if(i===lA&&l===ti)return this.consumeCodePoint(),this.consumeCodePoint(),Ji;break;case Ge:if(zt(A,this.peekCodePoint(0),this.peekCodePoint(1)))return this.reconsumeCodePoint(A),this.consumeNumericToken();break;case hn:if(this.peekCodePoint(0)===Zt)for(this.consumeCodePoint();;){var c=this.consumeCodePoint();if(c===Zt&&(c=this.consumeCodePoint(),c===hn))return this.consumeToken();if(c===FA)return this.consumeToken()}break;case xi:return Xi;case ui:return Yi;case ei:if(this.peekCodePoint(0)===Ai&&this.peekCodePoint(1)===lA&&this.peekCodePoint(2)===lA)return this.consumeCodePoint(),this.consumeCodePoint(),Pi;break;case ri:var x=this.peekCodePoint(0),u=this.peekCodePoint(1),w=this.peekCodePoint(2);if(st(x,u,w)){var a=this.consumeName();return{type:7,value:a}}break;case ni:return Wi;case Ke:if(GA(A,this.peekCodePoint(0)))return this.reconsumeCodePoint(A),this.consumeIdentLikeToken();break;case si:return Zi;case ai:if(this.peekCodePoint(0)===ve)return this.consumeCodePoint(),Mi;break;case ii:return Gi;case oi:return ki;case pi:case Fi:var g=this.peekCodePoint(0),h=this.peekCodePoint(1);return g===re&&(Be(h)||h===rt)&&(this.consumeCodePoint(),this.consumeUnicodeRangeToken()),this.reconsumeCodePoint(A),this.consumeIdentLikeToken();case dn:if(this.peekCodePoint(0)===ve)return this.consumeCodePoint(),_i;if(this.peekCodePoint(0)===dn)return this.consumeCodePoint(),Oi;break;case li:if(this.peekCodePoint(0)===ve)return this.consumeCodePoint(),Ri;break;case FA:return vr}return nt(A)?(this.consumeWhiteSpace(),zi):$(A)?(this.reconsumeCodePoint(A),this.consumeNumericToken()):Ut(A)?(this.reconsumeCodePoint(A),this.consumeIdentLikeToken()):{type:6,value:N(A)}},e.prototype.consumeCodePoint=function(){var A=this._value.shift();return typeof A>"u"?-1:A},e.prototype.reconsumeCodePoint=function(A){this._value.unshift(A)},e.prototype.peekCodePoint=function(A){return A>=this._value.length?-1:this._value[A]},e.prototype.consumeUnicodeRangeToken=function(){for(var A=[],t=this.consumeCodePoint();Be(t)&&A.length<6;)A.push(t),t=this.consumeCodePoint();for(var r=!1;t===rt&&A.length<6;)A.push(t),t=this.consumeCodePoint(),r=!0;if(r){var n=parseInt(N.apply(void 0,A.map(function(i){return i===rt?xs:i})),16),s=parseInt(N.apply(void 0,A.map(function(i){return i===rt?ws:i})),16);return{type:30,start:n,end:s}}var a=parseInt(N.apply(void 0,A),16);if(this.peekCodePoint(0)===lA&&Be(this.peekCodePoint(1))){this.consumeCodePoint(),t=this.consumeCodePoint();for(var o=[];Be(t)&&o.length<6;)o.push(t),t=this.consumeCodePoint();var s=parseInt(N.apply(void 0,o),16);return{type:30,start:a,end:s}}else return{type:30,start:a,end:a}},e.prototype.consumeIdentLikeToken=function(){var A=this.consumeName();return A.toLowerCase()==="url"&&this.peekCodePoint(0)===tt?(this.consumeCodePoint(),this.consumeUrlToken()):this.peekCodePoint(0)===tt?(this.consumeCodePoint(),{type:19,value:A}):{type:20,value:A}},e.prototype.consumeUrlToken=function(){var A=[];if(this.consumeWhiteSpace(),this.peekCodePoint(0)===FA)return{type:22,value:""};var t=this.peekCodePoint(0);if(t===et||t===At){var r=this.consumeStringToken(this.consumeCodePoint());return r.type===0&&(this.consumeWhiteSpace(),this.peekCodePoint(0)===FA||this.peekCodePoint(0)===Ee)?(this.consumeCodePoint(),{type:22,value:r.value}):(this.consumeBadUrlRemnants(),at)}for(;;){var n=this.consumeCodePoint();if(n===FA||n===Ee)return{type:22,value:N.apply(void 0,A)};if(nt(n))return this.consumeWhiteSpace(),this.peekCodePoint(0)===FA||this.peekCodePoint(0)===Ee?(this.consumeCodePoint(),{type:22,value:N.apply(void 0,A)}):(this.consumeBadUrlRemnants(),at);if(n===At||n===et||n===tt||bi(n))return this.consumeBadUrlRemnants(),at;if(n===Ke)if(GA(n,this.peekCodePoint(0)))A.push(this.consumeEscapedCodePoint());else return this.consumeBadUrlRemnants(),at;else A.push(n)}},e.prototype.consumeWhiteSpace=function(){for(;nt(this.peekCodePoint(0));)this.consumeCodePoint()},e.prototype.consumeBadUrlRemnants=function(){for(;;){var A=this.consumeCodePoint();if(A===Ee||A===FA)return;GA(A,this.peekCodePoint(0))&&this.consumeEscapedCodePoint()}},e.prototype.consumeStringSlice=function(A){for(var t=5e4,r="";A>0;){var n=Math.min(t,A);r+=N.apply(void 0,this._value.splice(0,n)),A-=n}return this._value.shift(),r},e.prototype.consumeStringToken=function(A){var t="",r=0;do{var n=this._value[r];if(n===FA||n===void 0||n===A)return t+=this.consumeStringSlice(r),{type:0,value:t};if(n===pt)return this._value.splice(0,r),Ni;if(n===Ke){var s=this._value[r+1];s!==FA&&s!==void 0&&(s===pt?(t+=this.consumeStringSlice(r),r=-1,this._value.shift()):GA(n,s)&&(t+=this.consumeStringSlice(r),t+=N(this.consumeEscapedCodePoint()),r=-1))}r++}while(!0)},e.prototype.consumeNumber=function(){var A=[],t=Ve,r=this.peekCodePoint(0);for((r===re||r===lA)&&A.push(this.consumeCodePoint());$(this.peekCodePoint(0));)A.push(this.consumeCodePoint());r=this.peekCodePoint(0);var n=this.peekCodePoint(1);if(r===Ge&&$(n))for(A.push(this.consumeCodePoint(),this.consumeCodePoint()),t=gn;$(this.peekCodePoint(0));)A.push(this.consumeCodePoint());r=this.peekCodePoint(0),n=this.peekCodePoint(1);var s=this.peekCodePoint(2);if((r===ds||r===gs)&&((n===re||n===lA)&&$(s)||$(n)))for(A.push(this.consumeCodePoint(),this.consumeCodePoint()),t=gn;$(this.peekCodePoint(0));)A.push(this.consumeCodePoint());return[Si(A),t]},e.prototype.consumeNumericToken=function(){var A=this.consumeNumber(),t=A[0],r=A[1],n=this.peekCodePoint(0),s=this.peekCodePoint(1),a=this.peekCodePoint(2);if(st(n,s,a)){var o=this.consumeName();return{type:15,number:t,flags:r,unit:o}}return n===ja?(this.consumeCodePoint(),{type:16,number:t,flags:r}):{type:17,number:t,flags:r}},e.prototype.consumeEscapedCodePoint=function(){var A=this.consumeCodePoint();if(Be(A)){for(var t=N(A);Be(this.peekCodePoint(0))&&t.length<6;)t+=N(this.consumeCodePoint());nt(this.peekCodePoint(0))&&this.consumeCodePoint();var r=parseInt(t,16);return r===0||vi(r)||r>1114111?wn:r}return A===FA?wn:A},e.prototype.consumeName=function(){for(var A="";;){var t=this.consumeCodePoint();if(fn(t))A+=N(t);else if(GA(t,this.peekCodePoint(0)))A+=N(this.consumeEscapedCodePoint());else return this.reconsumeCodePoint(t),A}},e}(),Qs=function(){function e(A){this._tokens=A}return e.create=function(A){var t=new fs;return t.write(A),new e(t.read())},e.parseValue=function(A){return e.create(A).parseComponentValue()},e.parseValues=function(A){return e.create(A).parseComponentValues()},e.prototype.parseComponentValue=function(){for(var A=this.consumeToken();A.type===31;)A=this.consumeToken();if(A.type===32)throw new SyntaxError("Error parsing CSS component value, unexpected EOF");this.reconsumeToken(A);var t=this.consumeComponentValue();do A=this.consumeToken();while(A.type===31);if(A.type===32)return t;throw new SyntaxError("Error parsing CSS component value, multiple values found when expecting only one")},e.prototype.parseComponentValues=function(){for(var A=[];;){var t=this.consumeComponentValue();if(t.type===32)return A;A.push(t),A.push()}},e.prototype.consumeComponentValue=function(){var A=this.consumeToken();switch(A.type){case 11:case 28:case 2:return this.consumeSimpleBlock(A.type);case 19:return this.consumeFunction(A)}return A},e.prototype.consumeSimpleBlock=function(A){for(var t={type:A,values:[]},r=this.consumeToken();;){if(r.type===32||ji(r,A))return t;this.reconsumeToken(r),t.values.push(this.consumeComponentValue()),r=this.consumeToken()}},e.prototype.consumeFunction=function(A){for(var t={name:A.value,values:[],type:18};;){var r=this.consumeToken();if(r.type===32||r.type===3)return t;this.reconsumeToken(r),t.values.push(this.consumeComponentValue())}},e.prototype.consumeToken=function(){var A=this._tokens.shift();return typeof A>"u"?vr:A},e.prototype.reconsumeToken=function(A){this._tokens.unshift(A)},e}(),Ne=function(e){return e.type===15},pe=function(e){return e.type===17},M=function(e){return e.type===20},qi=function(e){return e.type===0},Er=function(e,A){return M(e)&&e.value===A},Cs=function(e){return e.type!==31},Ce=function(e){return e.type!==31&&e.type!==4},vA=function(e){var A=[],t=[];return e.forEach(function(r){if(r.type===4){if(t.length===0)throw new Error("Error parsing function args, zero tokens for arg");A.push(t),t=[];return}r.type!==31&&t.push(r)}),t.length&&A.push(t),A},ji=function(e,A){return A===11&&e.type===12||A===28&&e.type===29?!0:A===2&&e.type===3},XA=function(e){return e.type===17||e.type===15},P=function(e){return e.type===16||XA(e)},ps=function(e){return e.length>1?[e[0],e[1]]:[e[0]]},q={type:17,number:0,flags:Ve},Yr={type:16,number:50,flags:Ve},VA={type:16,number:100,flags:Ve},Se=function(e,A,t){var r=e[0],n=e[1];return[_(r,A),_(typeof n<"u"?n:r,t)]},_=function(e,A){if(e.type===16)return e.number/100*A;if(Ne(e))switch(e.unit){case"rem":case"em":return 16*e.number;case"px":default:return e.number}return e.number},Us="deg",Fs="grad",ms="rad",vs="turn",Kt={name:"angle",parse:function(e,A){if(A.type===15)switch(A.unit){case Us:return Math.PI*A.number/180;case Fs:return Math.PI/200*A.number;case ms:return A.number;case vs:return Math.PI*2*A.number}throw new Error("Unsupported angle type")}},Es=function(e){return e.type===15&&(e.unit===Us||e.unit===Fs||e.unit===ms||e.unit===vs)},ys=function(e){var A=e.filter(M).map(function(t){return t.value}).join(" ");switch(A){case"to bottom right":case"to right bottom":case"left top":case"top left":return[q,q];case"to top":case"bottom":return dA(0);case"to bottom left":case"to left bottom":case"right top":case"top right":return[q,VA];case"to right":case"left":return dA(90);case"to top left":case"to left top":case"right bottom":case"bottom right":return[VA,VA];case"to bottom":case"top":return dA(180);case"to top right":case"to right top":case"left bottom":case"bottom left":return[VA,q];case"to left":case"right":return dA(270)}return 0},dA=function(e){return Math.PI*e/180},PA={name:"color",parse:function(e,A){if(A.type===18){var t=$i[A.name];if(typeof t>"u")throw new Error('Attempting to parse an unsupported color function "'+A.name+'"');return t(e,A.values)}if(A.type===5){if(A.value.length===3){var r=A.value.substring(0,1),n=A.value.substring(1,2),s=A.value.substring(2,3);return NA(parseInt(r+r,16),parseInt(n+n,16),parseInt(s+s,16),1)}if(A.value.length===4){var r=A.value.substring(0,1),n=A.value.substring(1,2),s=A.value.substring(2,3),a=A.value.substring(3,4);return NA(parseInt(r+r,16),parseInt(n+n,16),parseInt(s+s,16),parseInt(a+a,16)/255)}if(A.value.length===6){var r=A.value.substring(0,2),n=A.value.substring(2,4),s=A.value.substring(4,6);return NA(parseInt(r,16),parseInt(n,16),parseInt(s,16),1)}if(A.value.length===8){var r=A.value.substring(0,2),n=A.value.substring(2,4),s=A.value.substring(4,6),a=A.value.substring(6,8);return NA(parseInt(r,16),parseInt(n,16),parseInt(s,16),parseInt(a,16)/255)}}if(A.type===20){var o=LA[A.value.toUpperCase()];if(typeof o<"u")return o}return LA.TRANSPARENT}},JA=function(e){return(255&e)===0},W=function(e){var A=255&e,t=255&e>>8,r=255&e>>16,n=255&e>>24;return A<255?"rgba("+n+","+r+","+t+","+A/255+")":"rgb("+n+","+r+","+t+")"},NA=function(e,A,t,r){return(e<<24|A<<16|t<<8|Math.round(r*255)<<0)>>>0},Qn=function(e,A){if(e.type===17)return e.number;if(e.type===16){var t=A===3?1:255;return A===3?e.number/100*t:Math.round(e.number/100*t)}return 0},Cn=function(e,A){var t=A.filter(Ce);if(t.length===3){var r=t.map(Qn),n=r[0],s=r[1],a=r[2];return NA(n,s,a,1)}if(t.length===4){var o=t.map(Qn),n=o[0],s=o[1],a=o[2],i=o[3];return NA(n,s,a,i)}return 0};function qt(e,A,t){return t<0&&(t+=1),t>=1&&(t-=1),t<1/6?(A-e)*t*6+e:t<1/2?A:t<2/3?(A-e)*6*(2/3-t)+e:e}var pn=function(e,A){var t=A.filter(Ce),r=t[0],n=t[1],s=t[2],a=t[3],o=(r.type===17?dA(r.number):Kt.parse(e,r))/(Math.PI*2),i=P(n)?n.number/100:0,l=P(s)?s.number/100:0,c=typeof a<"u"&&P(a)?_(a,1):1;if(i===0)return NA(l*255,l*255,l*255,1);var x=l<=.5?l*(i+1):l+i-l*i,u=l*2-x,w=qt(u,x,o+1/3),g=qt(u,x,o),h=qt(u,x,o-1/3);return NA(w*255,g*255,h*255,c)},$i={hsl:pn,hsla:pn,rgb:Cn,rgba:Cn},Te=function(e,A){return PA.parse(e,Qs.create(A).parseComponentValue())},LA={ALICEBLUE:4042850303,ANTIQUEWHITE:4209760255,AQUA:16777215,AQUAMARINE:2147472639,AZURE:4043309055,BEIGE:4126530815,BISQUE:4293182719,BLACK:255,BLANCHEDALMOND:4293643775,BLUE:65535,BLUEVIOLET:2318131967,BROWN:2771004159,BURLYWOOD:3736635391,CADETBLUE:1604231423,CHARTREUSE:2147418367,CHOCOLATE:3530104575,CORAL:4286533887,CORNFLOWERBLUE:1687547391,CORNSILK:4294499583,CRIMSON:3692313855,CYAN:16777215,DARKBLUE:35839,DARKCYAN:9145343,DARKGOLDENROD:3095837695,DARKGRAY:2846468607,DARKGREEN:6553855,DARKGREY:2846468607,DARKKHAKI:3182914559,DARKMAGENTA:2332068863,DARKOLIVEGREEN:1433087999,DARKORANGE:4287365375,DARKORCHID:2570243327,DARKRED:2332033279,DARKSALMON:3918953215,DARKSEAGREEN:2411499519,DARKSLATEBLUE:1211993087,DARKSLATEGRAY:793726975,DARKSLATEGREY:793726975,DARKTURQUOISE:13554175,DARKVIOLET:2483082239,DEEPPINK:4279538687,DEEPSKYBLUE:12582911,DIMGRAY:1768516095,DIMGREY:1768516095,DODGERBLUE:512819199,FIREBRICK:2988581631,FLORALWHITE:4294635775,FORESTGREEN:579543807,FUCHSIA:4278255615,GAINSBORO:3705462015,GHOSTWHITE:4177068031,GOLD:4292280575,GOLDENROD:3668254975,GRAY:2155905279,GREEN:8388863,GREENYELLOW:2919182335,GREY:2155905279,HONEYDEW:4043305215,HOTPINK:4285117695,INDIANRED:3445382399,INDIGO:1258324735,IVORY:4294963455,KHAKI:4041641215,LAVENDER:3873897215,LAVENDERBLUSH:4293981695,LAWNGREEN:2096890111,LEMONCHIFFON:4294626815,LIGHTBLUE:2916673279,LIGHTCORAL:4034953471,LIGHTCYAN:3774873599,LIGHTGOLDENRODYELLOW:4210742015,LIGHTGRAY:3553874943,LIGHTGREEN:2431553791,LIGHTGREY:3553874943,LIGHTPINK:4290167295,LIGHTSALMON:4288707327,LIGHTSEAGREEN:548580095,LIGHTSKYBLUE:2278488831,LIGHTSLATEGRAY:2005441023,LIGHTSLATEGREY:2005441023,LIGHTSTEELBLUE:2965692159,LIGHTYELLOW:4294959359,LIME:16711935,LIMEGREEN:852308735,LINEN:4210091775,MAGENTA:4278255615,MAROON:2147483903,MEDIUMAQUAMARINE:1724754687,MEDIUMBLUE:52735,MEDIUMORCHID:3126187007,MEDIUMPURPLE:2473647103,MEDIUMSEAGREEN:1018393087,MEDIUMSLATEBLUE:2070474495,MEDIUMSPRINGGREEN:16423679,MEDIUMTURQUOISE:1221709055,MEDIUMVIOLETRED:3340076543,MIDNIGHTBLUE:421097727,MINTCREAM:4127193855,MISTYROSE:4293190143,MOCCASIN:4293178879,NAVAJOWHITE:4292783615,NAVY:33023,OLDLACE:4260751103,OLIVE:2155872511,OLIVEDRAB:1804477439,ORANGE:4289003775,ORANGERED:4282712319,ORCHID:3664828159,PALEGOLDENROD:4008225535,PALEGREEN:2566625535,PALETURQUOISE:2951671551,PALEVIOLETRED:3681588223,PAPAYAWHIP:4293907967,PEACHPUFF:4292524543,PERU:3448061951,PINK:4290825215,PLUM:3718307327,POWDERBLUE:2967529215,PURPLE:2147516671,REBECCAPURPLE:1714657791,RED:4278190335,ROSYBROWN:3163525119,ROYALBLUE:1097458175,SADDLEBROWN:2336560127,SALMON:4202722047,SANDYBROWN:4104413439,SEAGREEN:780883967,SEASHELL:4294307583,SIENNA:2689740287,SILVER:3233857791,SKYBLUE:2278484991,SLATEBLUE:1784335871,SLATEGRAY:1887473919,SLATEGREY:1887473919,SNOW:4294638335,SPRINGGREEN:16744447,STEELBLUE:1182971135,TAN:3535047935,TEAL:8421631,THISTLE:3636451583,TOMATO:4284696575,TRANSPARENT:0,TURQUOISE:1088475391,VIOLET:4001558271,WHEAT:4125012991,WHITE:4294967295,WHITESMOKE:4126537215,YELLOW:4294902015,YELLOWGREEN:2597139199},Ao={name:"background-clip",initialValue:"border-box",prefix:!1,type:1,parse:function(e,A){return A.map(function(t){if(M(t))switch(t.value){case"padding-box":return 1;case"content-box":return 2}return 0})}},eo={name:"background-color",initialValue:"transparent",prefix:!1,type:3,format:"color"},Tt=function(e,A){var t=PA.parse(e,A[0]),r=A[1];return r&&P(r)?{color:t,stop:r}:{color:t,stop:null}},Un=function(e,A){var t=e[0],r=e[e.length-1];t.stop===null&&(t.stop=q),r.stop===null&&(r.stop=VA);for(var n=[],s=0,a=0;a<e.length;a++){var o=e[a].stop;if(o!==null){var i=_(o,A);i>s?n.push(i):n.push(s),s=i}else n.push(null)}for(var l=null,a=0;a<n.length;a++){var c=n[a];if(c===null)l===null&&(l=a);else if(l!==null){for(var x=a-l,u=n[l-1],w=(c-u)/(x+1),g=1;g<=x;g++)n[l+g-1]=w*g;l=null}}return e.map(function(h,U){var d=h.color;return{color:d,stop:Math.max(Math.min(1,n[U]/A),0)}})},to=function(e,A,t){var r=A/2,n=t/2,s=_(e[0],A)-r,a=n-_(e[1],t);return(Math.atan2(a,s)+Math.PI*2)%(Math.PI*2)},ro=function(e,A,t){var r=typeof e=="number"?e:to(e,A,t),n=Math.abs(A*Math.sin(r))+Math.abs(t*Math.cos(r)),s=A/2,a=t/2,o=n/2,i=Math.sin(r-Math.PI/2)*o,l=Math.cos(r-Math.PI/2)*o;return[n,s-l,s+l,a-i,a+i]},fA=function(e,A){return Math.sqrt(e*e+A*A)},Fn=function(e,A,t,r,n){var s=[[0,0],[0,A],[e,0],[e,A]];return s.reduce(function(a,o){var i=o[0],l=o[1],c=fA(t-i,r-l);return(n?c<a.optimumDistance:c>a.optimumDistance)?{optimumCorner:o,optimumDistance:c}:a},{optimumDistance:n?1/0:-1/0,optimumCorner:null}).optimumCorner},no=function(e,A,t,r,n){var s=0,a=0;switch(e.size){case 0:e.shape===0?s=a=Math.min(Math.abs(A),Math.abs(A-r),Math.abs(t),Math.abs(t-n)):e.shape===1&&(s=Math.min(Math.abs(A),Math.abs(A-r)),a=Math.min(Math.abs(t),Math.abs(t-n)));break;case 2:if(e.shape===0)s=a=Math.min(fA(A,t),fA(A,t-n),fA(A-r,t),fA(A-r,t-n));else if(e.shape===1){var o=Math.min(Math.abs(t),Math.abs(t-n))/Math.min(Math.abs(A),Math.abs(A-r)),i=Fn(r,n,A,t,!0),l=i[0],c=i[1];s=fA(l-A,(c-t)/o),a=o*s}break;case 1:e.shape===0?s=a=Math.max(Math.abs(A),Math.abs(A-r),Math.abs(t),Math.abs(t-n)):e.shape===1&&(s=Math.max(Math.abs(A),Math.abs(A-r)),a=Math.max(Math.abs(t),Math.abs(t-n)));break;case 3:if(e.shape===0)s=a=Math.max(fA(A,t),fA(A,t-n),fA(A-r,t),fA(A-r,t-n));else if(e.shape===1){var o=Math.max(Math.abs(t),Math.abs(t-n))/Math.max(Math.abs(A),Math.abs(A-r)),x=Fn(r,n,A,t,!1),l=x[0],c=x[1];s=fA(l-A,(c-t)/o),a=o*s}break}return Array.isArray(e.size)&&(s=_(e.size[0],r),a=e.size.length===2?_(e.size[1],n):s),[s,a]},so=function(e,A){var t=dA(180),r=[];return vA(A).forEach(function(n,s){if(s===0){var a=n[0];if(a.type===20&&a.value==="to"){t=ys(n);return}else if(Es(a)){t=Kt.parse(e,a);return}}var o=Tt(e,n);r.push(o)}),{angle:t,stops:r,type:1}},it=function(e,A){var t=dA(180),r=[];return vA(A).forEach(function(n,s){if(s===0){var a=n[0];if(a.type===20&&["top","left","right","bottom"].indexOf(a.value)!==-1){t=ys(n);return}else if(Es(a)){t=(Kt.parse(e,a)+dA(270))%dA(360);return}}var o=Tt(e,n);r.push(o)}),{angle:t,stops:r,type:1}},ao=function(e,A){var t=dA(180),r=[],n=1,s=0,a=3,o=[];return vA(A).forEach(function(i,l){var c=i[0];if(l===0){if(M(c)&&c.value==="linear"){n=1;return}else if(M(c)&&c.value==="radial"){n=2;return}}if(c.type===18){if(c.name==="from"){var x=PA.parse(e,c.values[0]);r.push({stop:q,color:x})}else if(c.name==="to"){var x=PA.parse(e,c.values[0]);r.push({stop:VA,color:x})}else if(c.name==="color-stop"){var u=c.values.filter(Ce);if(u.length===2){var x=PA.parse(e,u[1]),w=u[0];pe(w)&&r.push({stop:{type:16,number:w.number*100,flags:w.flags},color:x})}}}}),n===1?{angle:(t+dA(180))%dA(360),stops:r,type:n}:{size:a,shape:s,stops:r,position:o,type:n}},Hs="closest-side",Is="farthest-side",bs="closest-corner",Ss="farthest-corner",Ds="circle",Ls="ellipse",Ks="cover",Ts="contain",io=function(e,A){var t=0,r=3,n=[],s=[];return vA(A).forEach(function(a,o){var i=!0;if(o===0){var l=!1;i=a.reduce(function(x,u){if(l)if(M(u))switch(u.value){case"center":return s.push(Yr),x;case"top":case"left":return s.push(q),x;case"right":case"bottom":return s.push(VA),x}else(P(u)||XA(u))&&s.push(u);else if(M(u))switch(u.value){case Ds:return t=0,!1;case Ls:return t=1,!1;case"at":return l=!0,!1;case Hs:return r=0,!1;case Ks:case Is:return r=1,!1;case Ts:case bs:return r=2,!1;case Ss:return r=3,!1}else if(XA(u)||P(u))return Array.isArray(r)||(r=[]),r.push(u),!1;return x},i)}if(i){var c=Tt(e,a);n.push(c)}}),{size:r,shape:t,stops:n,position:s,type:2}},ot=function(e,A){var t=0,r=3,n=[],s=[];return vA(A).forEach(function(a,o){var i=!0;if(o===0?i=a.reduce(function(c,x){if(M(x))switch(x.value){case"center":return s.push(Yr),!1;case"top":case"left":return s.push(q),!1;case"right":case"bottom":return s.push(VA),!1}else if(P(x)||XA(x))return s.push(x),!1;return c},i):o===1&&(i=a.reduce(function(c,x){if(M(x))switch(x.value){case Ds:return t=0,!1;case Ls:return t=1,!1;case Ts:case Hs:return r=0,!1;case Is:return r=1,!1;case bs:return r=2,!1;case Ks:case Ss:return r=3,!1}else if(XA(x)||P(x))return Array.isArray(r)||(r=[]),r.push(x),!1;return c},i)),i){var l=Tt(e,a);n.push(l)}}),{size:r,shape:t,stops:n,position:s,type:2}},oo=function(e){return e.type===1},lo=function(e){return e.type===2},Wr={name:"image",parse:function(e,A){if(A.type===22){var t={url:A.value,type:0};return e.cache.addImage(A.value),t}if(A.type===18){var r=Ms[A.name];if(typeof r>"u")throw new Error('Attempting to parse an unsupported image function "'+A.name+'"');return r(e,A.values)}throw new Error("Unsupported image type "+A.type)}};function co(e){return!(e.type===20&&e.value==="none")&&(e.type!==18||!!Ms[e.name])}var Ms={"linear-gradient":so,"-moz-linear-gradient":it,"-ms-linear-gradient":it,"-o-linear-gradient":it,"-webkit-linear-gradient":it,"radial-gradient":io,"-moz-radial-gradient":ot,"-ms-radial-gradient":ot,"-o-radial-gradient":ot,"-webkit-radial-gradient":ot,"-webkit-gradient":ao},Bo={name:"background-image",initialValue:"none",type:1,prefix:!1,parse:function(e,A){if(A.length===0)return[];var t=A[0];return t.type===20&&t.value==="none"?[]:A.filter(function(r){return Ce(r)&&co(r)}).map(function(r){return Wr.parse(e,r)})}},xo={name:"background-origin",initialValue:"border-box",prefix:!1,type:1,parse:function(e,A){return A.map(function(t){if(M(t))switch(t.value){case"padding-box":return 1;case"content-box":return 2}return 0})}},uo={name:"background-position",initialValue:"0% 0%",type:1,prefix:!1,parse:function(e,A){return vA(A).map(function(t){return t.filter(P)}).map(ps)}},go={name:"background-repeat",initialValue:"repeat",prefix:!1,type:1,parse:function(e,A){return vA(A).map(function(t){return t.filter(M).map(function(r){return r.value}).join(" ")}).map(ho)}},ho=function(e){switch(e){case"no-repeat":return 1;case"repeat-x":case"repeat no-repeat":return 2;case"repeat-y":case"no-repeat repeat":return 3;case"repeat":default:return 0}},Qe;(function(e){e.AUTO="auto",e.CONTAIN="contain",e.COVER="cover"})(Qe||(Qe={}));var wo={name:"background-size",initialValue:"0",prefix:!1,type:1,parse:function(e,A){return vA(A).map(function(t){return t.filter(fo)})}},fo=function(e){return M(e)||P(e)},Mt=function(e){return{name:"border-"+e+"-color",initialValue:"transparent",prefix:!1,type:3,format:"color"}},Qo=Mt("top"),Co=Mt("right"),po=Mt("bottom"),Uo=Mt("left"),Ot=function(e){return{name:"border-radius-"+e,initialValue:"0 0",prefix:!1,type:1,parse:function(A,t){return ps(t.filter(P))}}},Fo=Ot("top-left"),mo=Ot("top-right"),vo=Ot("bottom-right"),Eo=Ot("bottom-left"),_t=function(e){return{name:"border-"+e+"-style",initialValue:"solid",prefix:!1,type:2,parse:function(A,t){switch(t){case"none":return 0;case"dashed":return 2;case"dotted":return 3;case"double":return 4}return 1}}},yo=_t("top"),Ho=_t("right"),Io=_t("bottom"),bo=_t("left"),Rt=function(e){return{name:"border-"+e+"-width",initialValue:"0",type:0,prefix:!1,parse:function(A,t){return Ne(t)?t.number:0}}},So=Rt("top"),Do=Rt("right"),Lo=Rt("bottom"),Ko=Rt("left"),To={name:"color",initialValue:"transparent",prefix:!1,type:3,format:"color"},Mo={name:"direction",initialValue:"ltr",prefix:!1,type:2,parse:function(e,A){switch(A){case"rtl":return 1;case"ltr":default:return 0}}},Oo={name:"display",initialValue:"inline-block",prefix:!1,type:1,parse:function(e,A){return A.filter(M).reduce(function(t,r){return t|_o(r.value)},0)}},_o=function(e){switch(e){case"block":case"-webkit-box":return 2;case"inline":return 4;case"run-in":return 8;case"flow":return 16;case"flow-root":return 32;case"table":return 64;case"flex":case"-webkit-flex":return 128;case"grid":case"-ms-grid":return 256;case"ruby":return 512;case"subgrid":return 1024;case"list-item":return 2048;case"table-row-group":return 4096;case"table-header-group":return 8192;case"table-footer-group":return 16384;case"table-row":return 32768;case"table-cell":return 65536;case"table-column-group":return 131072;case"table-column":return 262144;case"table-caption":return 524288;case"ruby-base":return 1048576;case"ruby-text":return 2097152;case"ruby-base-container":return 4194304;case"ruby-text-container":return 8388608;case"contents":return 16777216;case"inline-block":return 33554432;case"inline-list-item":return 67108864;case"inline-table":return 134217728;case"inline-flex":return 268435456;case"inline-grid":return 536870912}return 0},Ro={name:"float",initialValue:"none",prefix:!1,type:2,parse:function(e,A){switch(A){case"left":return 1;case"right":return 2;case"inline-start":return 3;case"inline-end":return 4}return 0}},Go={name:"letter-spacing",initialValue:"0",prefix:!1,type:0,parse:function(e,A){return A.type===20&&A.value==="normal"?0:A.type===17||A.type===15?A.number:0}},Ft;(function(e){e.NORMAL="normal",e.STRICT="strict"})(Ft||(Ft={}));var ko={name:"line-break",initialValue:"normal",prefix:!1,type:2,parse:function(e,A){switch(A){case"strict":return Ft.STRICT;case"normal":default:return Ft.NORMAL}}},Vo={name:"line-height",initialValue:"normal",prefix:!1,type:4},mn=function(e,A){return M(e)&&e.value==="normal"?1.2*A:e.type===17?A*e.number:P(e)?_(e,A):A},No={name:"list-style-image",initialValue:"none",type:0,prefix:!1,parse:function(e,A){return A.type===20&&A.value==="none"?null:Wr.parse(e,A)}},Po={name:"list-style-position",initialValue:"outside",prefix:!1,type:2,parse:function(e,A){switch(A){case"inside":return 0;case"outside":default:return 1}}},yr={name:"list-style-type",initialValue:"none",prefix:!1,type:2,parse:function(e,A){switch(A){case"disc":return 0;case"circle":return 1;case"square":return 2;case"decimal":return 3;case"cjk-decimal":return 4;case"decimal-leading-zero":return 5;case"lower-roman":return 6;case"upper-roman":return 7;case"lower-greek":return 8;case"lower-alpha":return 9;case"upper-alpha":return 10;case"arabic-indic":return 11;case"armenian":return 12;case"bengali":return 13;case"cambodian":return 14;case"cjk-earthly-branch":return 15;case"cjk-heavenly-stem":return 16;case"cjk-ideographic":return 17;case"devanagari":return 18;case"ethiopic-numeric":return 19;case"georgian":return 20;case"gujarati":return 21;case"gurmukhi":return 22;case"hebrew":return 22;case"hiragana":return 23;case"hiragana-iroha":return 24;case"japanese-formal":return 25;case"japanese-informal":return 26;case"kannada":return 27;case"katakana":return 28;case"katakana-iroha":return 29;case"khmer":return 30;case"korean-hangul-formal":return 31;case"korean-hanja-formal":return 32;case"korean-hanja-informal":return 33;case"lao":return 34;case"lower-armenian":return 35;case"malayalam":return 36;case"mongolian":return 37;case"myanmar":return 38;case"oriya":return 39;case"persian":return 40;case"simp-chinese-formal":return 41;case"simp-chinese-informal":return 42;case"tamil":return 43;case"telugu":return 44;case"thai":return 45;case"tibetan":return 46;case"trad-chinese-formal":return 47;case"trad-chinese-informal":return 48;case"upper-armenian":return 49;case"disclosure-open":return 50;case"disclosure-closed":return 51;case"none":default:return-1}}},Gt=function(e){return{name:"margin-"+e,initialValue:"0",prefix:!1,type:4}},Jo=Gt("top"),Xo=Gt("right"),Yo=Gt("bottom"),Wo=Gt("left"),Zo={name:"overflow",initialValue:"visible",prefix:!1,type:1,parse:function(e,A){return A.filter(M).map(function(t){switch(t.value){case"hidden":return 1;case"scroll":return 2;case"clip":return 3;case"auto":return 4;case"visible":default:return 0}})}},zo={name:"overflow-wrap",initialValue:"normal",prefix:!1,type:2,parse:function(e,A){switch(A){case"break-word":return"break-word";case"normal":default:return"normal"}}},kt=function(e){return{name:"padding-"+e,initialValue:"0",prefix:!1,type:3,format:"length-percentage"}},qo=kt("top"),jo=kt("right"),$o=kt("bottom"),Al=kt("left"),el={name:"text-align",initialValue:"left",prefix:!1,type:2,parse:function(e,A){switch(A){case"right":return 2;case"center":case"justify":return 1;case"left":default:return 0}}},tl={name:"position",initialValue:"static",prefix:!1,type:2,parse:function(e,A){switch(A){case"relative":return 1;case"absolute":return 2;case"fixed":return 3;case"sticky":return 4}return 0}},rl={name:"text-shadow",initialValue:"none",type:1,prefix:!1,parse:function(e,A){return A.length===1&&Er(A[0],"none")?[]:vA(A).map(function(t){for(var r={color:LA.TRANSPARENT,offsetX:q,offsetY:q,blur:q},n=0,s=0;s<t.length;s++){var a=t[s];XA(a)?(n===0?r.offsetX=a:n===1?r.offsetY=a:r.blur=a,n++):r.color=PA.parse(e,a)}return r})}},nl={name:"text-transform",initialValue:"none",prefix:!1,type:2,parse:function(e,A){switch(A){case"uppercase":return 2;case"lowercase":return 1;case"capitalize":return 3}return 0}},sl={name:"transform",initialValue:"none",prefix:!0,type:0,parse:function(e,A){if(A.type===20&&A.value==="none")return null;if(A.type===18){var t=ol[A.name];if(typeof t>"u")throw new Error('Attempting to parse an unsupported transform function "'+A.name+'"');return t(A.values)}return null}},al=function(e){var A=e.filter(function(t){return t.type===17}).map(function(t){return t.number});return A.length===6?A:null},il=function(e){var A=e.filter(function(i){return i.type===17}).map(function(i){return i.number}),t=A[0],r=A[1];A[2],A[3];var n=A[4],s=A[5];A[6],A[7],A[8],A[9],A[10],A[11];var a=A[12],o=A[13];return A[14],A[15],A.length===16?[t,r,n,s,a,o]:null},ol={matrix:al,matrix3d:il},vn={type:16,number:50,flags:Ve},ll=[vn,vn],cl={name:"transform-origin",initialValue:"50% 50%",prefix:!0,type:1,parse:function(e,A){var t=A.filter(P);return t.length!==2?ll:[t[0],t[1]]}},Bl={name:"visible",initialValue:"none",prefix:!1,type:2,parse:function(e,A){switch(A){case"hidden":return 1;case"collapse":return 2;case"visible":default:return 0}}},Me;(function(e){e.NORMAL="normal",e.BREAK_ALL="break-all",e.KEEP_ALL="keep-all"})(Me||(Me={}));var xl={name:"word-break",initialValue:"normal",prefix:!1,type:2,parse:function(e,A){switch(A){case"break-all":return Me.BREAK_ALL;case"keep-all":return Me.KEEP_ALL;case"normal":default:return Me.NORMAL}}},ul={name:"z-index",initialValue:"auto",prefix:!1,type:0,parse:function(e,A){if(A.type===20)return{auto:!0,order:0};if(pe(A))return{auto:!1,order:A.number};throw new Error("Invalid z-index number parsed")}},Os={name:"time",parse:function(e,A){if(A.type===15)switch(A.unit.toLowerCase()){case"s":return 1e3*A.number;case"ms":return A.number}throw new Error("Unsupported time type")}},gl={name:"opacity",initialValue:"1",type:0,prefix:!1,parse:function(e,A){return pe(A)?A.number:1}},hl={name:"text-decoration-color",initialValue:"transparent",prefix:!1,type:3,format:"color"},dl={name:"text-decoration-line",initialValue:"none",prefix:!1,type:1,parse:function(e,A){return A.filter(M).map(function(t){switch(t.value){case"underline":return 1;case"overline":return 2;case"line-through":return 3;case"none":return 4}return 0}).filter(function(t){return t!==0})}},wl={name:"font-family",initialValue:"",prefix:!1,type:1,parse:function(e,A){var t=[],r=[];return A.forEach(function(n){switch(n.type){case 20:case 0:t.push(n.value);break;case 17:t.push(n.number.toString());break;case 4:r.push(t.join(" ")),t.length=0;break}}),t.length&&r.push(t.join(" ")),r.map(function(n){return n.indexOf(" ")===-1?n:"'"+n+"'"})}},fl={name:"font-size",initialValue:"0",prefix:!1,type:3,format:"length"},Ql={name:"font-weight",initialValue:"normal",type:0,prefix:!1,parse:function(e,A){if(pe(A))return A.number;if(M(A))switch(A.value){case"bold":return 700;case"normal":default:return 400}return 400}},Cl={name:"font-variant",initialValue:"none",type:1,prefix:!1,parse:function(e,A){return A.filter(M).map(function(t){return t.value})}},pl={name:"font-style",initialValue:"normal",prefix:!1,type:2,parse:function(e,A){switch(A){case"oblique":return"oblique";case"italic":return"italic";case"normal":default:return"normal"}}},Y=function(e,A){return(e&A)!==0},Ul={name:"content",initialValue:"none",type:1,prefix:!1,parse:function(e,A){if(A.length===0)return[];var t=A[0];return t.type===20&&t.value==="none"?[]:A}},Fl={name:"counter-increment",initialValue:"none",prefix:!0,type:1,parse:function(e,A){if(A.length===0)return null;var t=A[0];if(t.type===20&&t.value==="none")return null;for(var r=[],n=A.filter(Cs),s=0;s<n.length;s++){var a=n[s],o=n[s+1];if(a.type===20){var i=o&&pe(o)?o.number:1;r.push({counter:a.value,increment:i})}}return r}},ml={name:"counter-reset",initialValue:"none",prefix:!0,type:1,parse:function(e,A){if(A.length===0)return[];for(var t=[],r=A.filter(Cs),n=0;n<r.length;n++){var s=r[n],a=r[n+1];if(M(s)&&s.value!=="none"){var o=a&&pe(a)?a.number:0;t.push({counter:s.value,reset:o})}}return t}},vl={name:"duration",initialValue:"0s",prefix:!1,type:1,parse:function(e,A){return A.filter(Ne).map(function(t){return Os.parse(e,t)})}},El={name:"quotes",initialValue:"none",prefix:!0,type:1,parse:function(e,A){if(A.length===0)return null;var t=A[0];if(t.type===20&&t.value==="none")return null;var r=[],n=A.filter(qi);if(n.length%2!==0)return null;for(var s=0;s<n.length;s+=2){var a=n[s].value,o=n[s+1].value;r.push({open:a,close:o})}return r}},En=function(e,A,t){if(!e)return"";var r=e[Math.min(A,e.length-1)];return r?t?r.open:r.close:""},yl={name:"box-shadow",initialValue:"none",type:1,prefix:!1,parse:function(e,A){return A.length===1&&Er(A[0],"none")?[]:vA(A).map(function(t){for(var r={color:255,offsetX:q,offsetY:q,blur:q,spread:q,inset:!1},n=0,s=0;s<t.length;s++){var a=t[s];Er(a,"inset")?r.inset=!0:XA(a)?(n===0?r.offsetX=a:n===1?r.offsetY=a:n===2?r.blur=a:r.spread=a,n++):r.color=PA.parse(e,a)}return r})}},Hl={name:"paint-order",initialValue:"normal",prefix:!1,type:1,parse:function(e,A){var t=[0,1,2],r=[];return A.filter(M).forEach(function(n){switch(n.value){case"stroke":r.push(1);break;case"fill":r.push(0);break;case"markers":r.push(2);break}}),t.forEach(function(n){r.indexOf(n)===-1&&r.push(n)}),r}},Il={name:"-webkit-text-stroke-color",initialValue:"currentcolor",prefix:!1,type:3,format:"color"},bl={name:"-webkit-text-stroke-width",initialValue:"0",type:0,prefix:!1,parse:function(e,A){return Ne(A)?A.number:0}},Sl=function(){function e(A,t){var r,n;this.animationDuration=p(A,vl,t.animationDuration),this.backgroundClip=p(A,Ao,t.backgroundClip),this.backgroundColor=p(A,eo,t.backgroundColor),this.backgroundImage=p(A,Bo,t.backgroundImage),this.backgroundOrigin=p(A,xo,t.backgroundOrigin),this.backgroundPosition=p(A,uo,t.backgroundPosition),this.backgroundRepeat=p(A,go,t.backgroundRepeat),this.backgroundSize=p(A,wo,t.backgroundSize),this.borderTopColor=p(A,Qo,t.borderTopColor),this.borderRightColor=p(A,Co,t.borderRightColor),this.borderBottomColor=p(A,po,t.borderBottomColor),this.borderLeftColor=p(A,Uo,t.borderLeftColor),this.borderTopLeftRadius=p(A,Fo,t.borderTopLeftRadius),this.borderTopRightRadius=p(A,mo,t.borderTopRightRadius),this.borderBottomRightRadius=p(A,vo,t.borderBottomRightRadius),this.borderBottomLeftRadius=p(A,Eo,t.borderBottomLeftRadius),this.borderTopStyle=p(A,yo,t.borderTopStyle),this.borderRightStyle=p(A,Ho,t.borderRightStyle),this.borderBottomStyle=p(A,Io,t.borderBottomStyle),this.borderLeftStyle=p(A,bo,t.borderLeftStyle),this.borderTopWidth=p(A,So,t.borderTopWidth),this.borderRightWidth=p(A,Do,t.borderRightWidth),this.borderBottomWidth=p(A,Lo,t.borderBottomWidth),this.borderLeftWidth=p(A,Ko,t.borderLeftWidth),this.boxShadow=p(A,yl,t.boxShadow),this.color=p(A,To,t.color),this.direction=p(A,Mo,t.direction),this.display=p(A,Oo,t.display),this.float=p(A,Ro,t.cssFloat),this.fontFamily=p(A,wl,t.fontFamily),this.fontSize=p(A,fl,t.fontSize),this.fontStyle=p(A,pl,t.fontStyle),this.fontVariant=p(A,Cl,t.fontVariant),this.fontWeight=p(A,Ql,t.fontWeight),this.letterSpacing=p(A,Go,t.letterSpacing),this.lineBreak=p(A,ko,t.lineBreak),this.lineHeight=p(A,Vo,t.lineHeight),this.listStyleImage=p(A,No,t.listStyleImage),this.listStylePosition=p(A,Po,t.listStylePosition),this.listStyleType=p(A,yr,t.listStyleType),this.marginTop=p(A,Jo,t.marginTop),this.marginRight=p(A,Xo,t.marginRight),this.marginBottom=p(A,Yo,t.marginBottom),this.marginLeft=p(A,Wo,t.marginLeft),this.opacity=p(A,gl,t.opacity);var s=p(A,Zo,t.overflow);this.overflowX=s[0],this.overflowY=s[s.length>1?1:0],this.overflowWrap=p(A,zo,t.overflowWrap),this.paddingTop=p(A,qo,t.paddingTop),this.paddingRight=p(A,jo,t.paddingRight),this.paddingBottom=p(A,$o,t.paddingBottom),this.paddingLeft=p(A,Al,t.paddingLeft),this.paintOrder=p(A,Hl,t.paintOrder),this.position=p(A,tl,t.position),this.textAlign=p(A,el,t.textAlign),this.textDecorationColor=p(A,hl,(r=t.textDecorationColor)!==null&&r!==void 0?r:t.color),this.textDecorationLine=p(A,dl,(n=t.textDecorationLine)!==null&&n!==void 0?n:t.textDecoration),this.textShadow=p(A,rl,t.textShadow),this.textTransform=p(A,nl,t.textTransform),this.transform=p(A,sl,t.transform),this.transformOrigin=p(A,cl,t.transformOrigin),this.visibility=p(A,Bl,t.visibility),this.webkitTextStrokeColor=p(A,Il,t.webkitTextStrokeColor),this.webkitTextStrokeWidth=p(A,bl,t.webkitTextStrokeWidth),this.wordBreak=p(A,xl,t.wordBreak),this.zIndex=p(A,ul,t.zIndex)}return e.prototype.isVisible=function(){return this.display>0&&this.opacity>0&&this.visibility===0},e.prototype.isTransparent=function(){return JA(this.backgroundColor)},e.prototype.isTransformed=function(){return this.transform!==null},e.prototype.isPositioned=function(){return this.position!==0},e.prototype.isPositionedWithZIndex=function(){return this.isPositioned()&&!this.zIndex.auto},e.prototype.isFloating=function(){return this.float!==0},e.prototype.isInlineLevel=function(){return Y(this.display,4)||Y(this.display,33554432)||Y(this.display,268435456)||Y(this.display,536870912)||Y(this.display,67108864)||Y(this.display,134217728)},e}(),Dl=function(){function e(A,t){this.content=p(A,Ul,t.content),this.quotes=p(A,El,t.quotes)}return e}(),yn=function(){function e(A,t){this.counterIncrement=p(A,Fl,t.counterIncrement),this.counterReset=p(A,ml,t.counterReset)}return e}(),p=function(e,A,t){var r=new fs,n=t!==null&&typeof t<"u"?t.toString():A.initialValue;r.write(n);var s=new Qs(r.read());switch(A.type){case 2:var a=s.parseComponentValue();return A.parse(e,M(a)?a.value:A.initialValue);case 0:return A.parse(e,s.parseComponentValue());case 1:return A.parse(e,s.parseComponentValues());case 4:return s.parseComponentValue();case 3:switch(A.format){case"angle":return Kt.parse(e,s.parseComponentValue());case"color":return PA.parse(e,s.parseComponentValue());case"image":return Wr.parse(e,s.parseComponentValue());case"length":var o=s.parseComponentValue();return XA(o)?o:q;case"length-percentage":var i=s.parseComponentValue();return P(i)?i:q;case"time":return Os.parse(e,s.parseComponentValue())}break}},Ll="data-html2canvas-debug",Kl=function(e){var A=e.getAttribute(Ll);switch(A){case"all":return 1;case"clone":return 2;case"parse":return 3;case"render":return 4;default:return 0}},Hr=function(e,A){var t=Kl(e);return t===1||A===t},EA=function(){function e(A,t){if(this.context=A,this.textNodes=[],this.elements=[],this.flags=0,Hr(t,3))debugger;this.styles=new Sl(A,window.getComputedStyle(t,null)),Sr(t)&&(this.styles.animationDuration.some(function(r){return r>0})&&(t.style.animationDuration="0s"),this.styles.transform!==null&&(t.style.transform="none")),this.bounds=Dt(this.context,t),Hr(t,4)&&(this.flags|=16)}return e}(),Tl="AAAAAAAAAAAAEA4AGBkAAFAaAAACAAAAAAAIABAAGAAwADgACAAQAAgAEAAIABAACAAQAAgAEAAIABAACAAQAAgAEAAIABAAQABIAEQATAAIABAACAAQAAgAEAAIABAAVABcAAgAEAAIABAACAAQAGAAaABwAHgAgACIAI4AlgAIABAAmwCjAKgAsAC2AL4AvQDFAMoA0gBPAVYBWgEIAAgACACMANoAYgFkAWwBdAF8AX0BhQGNAZUBlgGeAaMBlQGWAasBswF8AbsBwwF0AcsBYwHTAQgA2wG/AOMBdAF8AekB8QF0AfkB+wHiAHQBfAEIAAMC5gQIAAsCEgIIAAgAFgIeAggAIgIpAggAMQI5AkACygEIAAgASAJQAlgCYAIIAAgACAAKBQoFCgUTBRMFGQUrBSsFCAAIAAgACAAIAAgACAAIAAgACABdAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACABoAmgCrwGvAQgAbgJ2AggAHgEIAAgACADnAXsCCAAIAAgAgwIIAAgACAAIAAgACACKAggAkQKZAggAPADJAAgAoQKkAqwCsgK6AsICCADJAggA0AIIAAgACAAIANYC3gIIAAgACAAIAAgACABAAOYCCAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAkASoB+QIEAAgACAA8AEMCCABCBQgACABJBVAFCAAIAAgACAAIAAgACAAIAAgACABTBVoFCAAIAFoFCABfBWUFCAAIAAgACAAIAAgAbQUIAAgACAAIAAgACABzBXsFfQWFBYoFigWKBZEFigWKBYoFmAWfBaYFrgWxBbkFCAAIAAgACAAIAAgACAAIAAgACAAIAMEFCAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAMgFCADQBQgACAAIAAgACAAIAAgACAAIAAgACAAIAO4CCAAIAAgAiQAIAAgACABAAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAD0AggACAD8AggACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIANYFCAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAMDvwAIAAgAJAIIAAgACAAIAAgACAAIAAgACwMTAwgACAB9BOsEGwMjAwgAKwMyAwsFYgE3A/MEPwMIAEUDTQNRAwgAWQOsAGEDCAAIAAgACAAIAAgACABpAzQFNQU2BTcFOAU5BToFNAU1BTYFNwU4BTkFOgU0BTUFNgU3BTgFOQU6BTQFNQU2BTcFOAU5BToFNAU1BTYFNwU4BTkFOgU0BTUFNgU3BTgFOQU6BTQFNQU2BTcFOAU5BToFNAU1BTYFNwU4BTkFOgU0BTUFNgU3BTgFOQU6BTQFNQU2BTcFOAU5BToFNAU1BTYFNwU4BTkFOgU0BTUFNgU3BTgFOQU6BTQFNQU2BTcFOAU5BToFNAU1BTYFNwU4BTkFOgU0BTUFNgU3BTgFOQU6BTQFNQU2BTcFOAU5BToFNAU1BTYFNwU4BTkFOgU0BTUFNgU3BTgFOQU6BTQFNQU2BTcFOAU5BToFNAU1BTYFNwU4BTkFOgU0BTUFNgU3BTgFOQU6BTQFNQU2BTcFOAU5BToFNAU1BTYFNwU4BTkFOgU0BTUFNgU3BTgFOQU6BTQFNQU2BTcFOAU5BToFNAU1BTYFNwU4BTkFOgU0BTUFNgU3BTgFOQU6BTQFNQU2BTcFOAU5BToFNAU1BTYFNwU4BTkFOgU0BTUFNgU3BTgFOQU6BTQFNQU2BTcFOAU5BToFNAU1BTYFNwU4BTkFOgU0BTUFNgU3BTgFOQU6BTQFNQU2BTcFOAU5BToFNAU1BTYFNwU4BTkFOgU0BTUFNgU3BTgFOQU6BTQFNQU2BTcFOAU5BToFNAU1BTYFNwU4BTkFOgU0BTUFNgU3BTgFOQU6BTQFNQU2BTcFOAU5BToFNAU1BTYFNwU4BTkFOgU0BTUFNgU3BTgFOQU6BTQFNQU2BTcFOAU5BToFNAU1BTYFNwU4BTkFOgU0BTUFNgU3BTgFOQU6BTQFNQU2BTcFOAU5BToFNAU1BTYFNwU4BTkFOgU0BTUFNgU3BTgFOQU6BTQFNQU2BTcFOAU5BToFNAU1BTYFNwU4BTkFIQUoBSwFCAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACABtAwgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACABMAEwACAAIAAgACAAIABgACAAIAAgACAC/AAgACAAyAQgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACACAAIAAwAAgACAAIAAgACAAIAAgACAAIAAAARABIAAgACAAIABQASAAIAAgAIABwAEAAjgCIABsAqAC2AL0AigDQAtwC+IJIQqVAZUBWQqVAZUBlQGVAZUBlQGrC5UBlQGVAZUBlQGVAZUBlQGVAXsKlQGVAbAK6wsrDGUMpQzlDJUBlQGVAZUBlQGVAZUBlQGVAZUBlQGVAZUBlQGVAZUBlQGVAZUBlQGVAZUBlQGVAZUBlQGVAZUBlQGVAZUBlQGVAZUBlQGVAZUBlQGVAZUBlQGVAZUBlQGVAZUBlQGVAZUBlQGVAZUBlQGVAZUBlQGVAZUBlQGVAZUBlQGVAZUBlQGVAZUBlQGVAZUBlQGVAZUBlQGVAZUBlQGVAZUBlQGVAZUBlQGVAZUBlQGVAZUBlQGVAZUBlQGVAZUBlQGVAZUBlQGVAZUBlQGVAZUBlQGVAZUBlQGVAZUBlQGVAZUBlQGVAZUBlQGVAZUBlQGVAZUBlQGVAZUBlQGVAZUBlQGVAZUBlQGVAZUBlQGVAZUBlQGVAZUBlQGVAZUBlQGVAZUBlQGVAZUBlQGVAZUBlQGVAZUBlQGVAZUBlQGVAZUBlQGVAZUBlQGVAZUBlQGVAZUBlQGVAZUBlQGVAZUBlQGVAZUBlQGVAZUBlQGVAZUBlQGVAZUBlQGVAZUBlQGVAZUBlQGVAZUBlQGVAZUBlQGVAZUBlQGVAZUBlQGVAZUBlQGVAZUBlQGVAZUBlQGVAZUBlQGVAZUBlQGVAZUBlQGVAZUBlQGVAZUBlQGVAZUBlQGVAZUBlQGVAZUBlQGVAZUBlQGVAZUBlQGVAZUBlQGVAZUBlQGVAZUBlQGVAZUBlQGVAZUBlQGVAZUBlQGVAZUBlQGVAZUBlQGVAZUBlQGVAZUBlQGVAZUBlQGVAZUBlQGVAZUBlQGVAZUBlQGVAZUBlQGVAZUBlQGVAZUBlQGVAZUBlQGVAZUBlQGVAZUBlQGVAZUBlQGVAZUBlQGVAZUBlQGVAZUBlQGVAZUBlQGVAZUBlQGVAZUBlQGVAZUBlQGVAZUBlQGVAZUBlQGVAZUBlQGVAZUBlQGVAZUBlQGVAZUBlQGVAZUBlQGVAZUBlQGVAZUBlQGVAZUBlQGVAZUBlQGVAZUBlQGVAZUBlQGVAZUBlQGVAZUBlQGVAZUBlQGVAZUBlQGVAZUBlQGVAfAKAAuZA64AtwCJALoC6ADwAAgAuACgA/oEpgO6AqsD+AAIAAgAswMIAAgACAAIAIkAuwP5AfsBwwPLAwgACAAIAAgACADRA9kDCAAIAOED6QMIAAgACAAIAAgACADuA/YDCAAIAP4DyQAIAAgABgQIAAgAXQAOBAgACAAIAAgACAAIABMECAAIAAgACAAIAAgACAD8AAQBCAAIAAgAGgQiBCoECAExBAgAEAEIAAgACAAIAAgACAAIAAgACAAIAAgACAA4BAgACABABEYECAAIAAgATAQYAQgAVAQIAAgACAAIAAgACAAIAAgACAAIAFoECAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgAOQEIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAB+BAcACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAEABhgSMBAgACAAIAAgAlAQIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAwAEAAQABAADAAMAAwADAAQABAAEAAQABAAEAAQABHATAAMAAwADAAMAAwADAAMAAwADAAMAAwADAAMAAwADAAMAAwADAAMAAwADAAMAAwADAAMAAwADAAMAAwADAAMAAwADAAMAAwADAAMAAwADAAMAAwADAAMAAwADAAMAAwADAAMAAwADAAMAAwADAAMAAwADAAMAAwADAAMAAwADAACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgAdQMIAAgACAAIAAgACAAIAMkACAAIAAgAfQMIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACACFA4kDCAAIAAgACAAIAOcBCAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAIcDCAAIAAgACAAIAAgACAAIAAgACAAIAJEDCAAIAAgACADFAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACABgBAgAZgQIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgAbAQCBXIECAAIAHkECAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACABAAJwEQACjBKoEsgQIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAC6BMIECAAIAAgACAAIAAgACABmBAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgAxwQIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAGYECAAIAAgAzgQIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgAigWKBYoFigWKBYoFigWKBd0FXwUIAOIF6gXxBYoF3gT5BQAGCAaKBYoFigWKBYoFigWKBYoFigWKBYoFigXWBIoFigWKBYoFigWKBYoFigWKBYsFEAaKBYoFigWKBYoFigWKBRQGCACKBYoFigWKBQgACAAIANEECAAIABgGigUgBggAJgYIAC4GMwaKBYoF0wQ3Bj4GigWKBYoFigWKBYoFigWKBYoFigWKBYoFigUIAAgACAAIAAgACAAIAAgAigWKBYoFigWKBYoFigWKBYoFigWKBYoFigWKBYoFigWKBYoFigWKBYoFigWKBYoFigWKBYoFigWKBYoFigWLBf///////wQABAAEAAQABAAEAAQABAAEAAQAAwAEAAQAAgAEAAQABAAEAAQABAAEAAQABAAEAAQABAAEAAQABAAEAAQABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAQABAAEAAQABAAEAAQABAAEAAQABAAEAAQABAAEAAQABAAEAAQABAAEAAQABAAEAAQABAAEAAQABAAEAAQABAAAAAAAAAAAAAAAAAAAAAAAAAAOAAAAAAAAAAQADgAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAUABQAFAAUABQAFAAUAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAAAAUAAAAFAAUAAAAFAAUAAAAFAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAABAAEAAQABAAEAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAUABQAFAAUABQAFAAUABQAFAAUABQAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAABQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAUABQAFAAUABQAFAAUAAQAAAAUABQAFAAUABQAFAAAAAAAFAAUAAAAFAAUABQAFAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAEAAAAFAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAFAAUABQAFAAUABQAFAAUABQAFAAUAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAFAAUABQAFAAUABQAFAAUABQAAAAAAAAAAAAAAAAAAAAAAAAAFAAAAAAAFAAUAAQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABwAFAAUABQAFAAAABwAHAAcAAAAHAAcABwAFAAEAAAAAAAAAAAAAAAAAAAAAAAUAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAHAAcABwAFAAUABQAFAAcABwAFAAUAAAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAHAAAAAQABAAAAAAAAAAAAAAAFAAUABQAFAAAABwAFAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAABQAHAAcABwAHAAcAAAAHAAcAAAAAAAUABQAHAAUAAQAHAAEABwAFAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAABQAFAAUABQAFAAUABwABAAUABQAFAAUAAAAAAAAAAAAAAAEAAQABAAEAAQABAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABwAFAAUAAAAAAAAAAAAAAAAABQAFAAUABQAFAAUAAQAFAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAUABQAFAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAQABQANAAQABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAQABAAEAAQABAAEAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAOAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAABAAEAAQABAAEAAQABAAEAAQABAAEAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAEAAQABAAEAAQABAAEAAQABAAAAAAAAAAAAAAAAAAAAAAABQAHAAUABQAFAAAAAAAAAAcABQAFAAUABQAFAAQABAAEAAQABAAEAAQABAAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABAAEAAQABAAEAAQABAAEAAQABAAEAAQABAAEAAQABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAUABQAFAAUAAAAFAAUABQAFAAUAAAAFAAUABQAAAAUABQAFAAUABQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAFAAUABQAAAAAAAAAAAAUABQAFAAcAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAABQAHAAUAAAAHAAcABwAFAAUABQAFAAUABQAFAAUABwAHAAcABwAFAAcABwAAAAUABQAFAAUABQAFAAUAAAAAAAAAAAAAAAAAAAAAAAAAAAAFAAUAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAUABwAHAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAABQAAAAUABwAHAAUABQAFAAUAAAAAAAcABwAAAAAABwAHAAUAAAAAAAAAAAAAAAAAAAAAAAAABQAAAAAAAAAAAAAAAAAAAAAAAAAAAAUABQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAABQAAAAAABQAFAAcAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAFAAAABwAHAAcABQAFAAAAAAAAAAAABQAFAAAAAAAFAAUABQAAAAAAAAAFAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAUABQAAAAAAAAAFAAAAAAAAAAAAAAAAAAAAAAAAAAAABwAFAAUABQAFAAUAAAAFAAUABwAAAAcABwAFAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAFAAUAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAFAAUABQAFAAUABQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAUAAAAFAAUABwAFAAUABQAFAAAAAAAHAAcAAAAAAAcABwAFAAAAAAAAAAAAAAAAAAAABQAFAAUAAAAAAAAAAAAAAAAAAAAAAAAAAAAFAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAFAAcABwAAAAAAAAAHAAcABwAAAAcABwAHAAUAAAAAAAAAAAAAAAAAAAAAAAAABQAAAAAAAAAAAAAAAAAAAAAABQAHAAcABwAFAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAUABwAHAAcABwAAAAUABQAFAAAABQAFAAUABQAAAAAAAAAAAAAAAAAAAAUABQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAABQAAAAcABQAHAAcABQAHAAcAAAAFAAcABwAAAAcABwAFAAUAAAAAAAAAAAAAAAAAAAAFAAUAAAAAAAAAAAAAAAAAAAAAAAAABQAFAAcABwAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAUABQAAAAUABwAAAAAAAAAAAAAAAAAAAAAAAAAAAAUAAAAAAAAAAAAFAAcABwAFAAUABQAAAAUAAAAHAAcABwAHAAcABwAHAAUAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAUAAAAHAAUABQAFAAUABQAFAAUAAAAAAAAAAAAAAAAAAAAAAAUABQAFAAUABQAFAAUABQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAFAAAABwAFAAUABQAFAAUABQAFAAUABQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAABQAFAAUABQAFAAUAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAUABQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAABQAAAAUAAAAFAAAAAAAAAAAABwAHAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABwAFAAUABQAFAAUAAAAFAAUAAAAAAAAAAAAAAAUABQAFAAUABQAFAAUABQAFAAUABQAAAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAUABQAFAAUABwAFAAUABQAFAAUABQAAAAUABQAHAAcABQAFAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAHAAcABQAFAAAAAAAAAAAABQAFAAUAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAFAAUABQAFAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAABQAAAAcABQAFAAAAAAAAAAAAAAAAAAUAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAABQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAABQAFAAUAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAUABQAHAAUABQAFAAUABQAFAAUABwAHAAcABwAHAAcABwAHAAUABwAHAAUABQAFAAUABQAFAAUABQAFAAUABQAAAAAAAAAAAAAAAAAAAAAAAAAFAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAABQAFAAUABwAHAAcABwAFAAUABwAHAAcAAAAAAAAAAAAHAAcABQAHAAcABwAHAAcABwAFAAUABQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAABQAFAAcABwAFAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAcABQAHAAUABQAFAAUABQAFAAUAAAAFAAAABQAAAAAABQAFAAUABQAFAAUABQAFAAcABwAHAAcABwAHAAUABQAFAAUABQAFAAUABQAFAAUAAAAAAAUABQAFAAUABQAHAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAUABQAFAAUABQAFAAUABwAFAAcABwAHAAcABwAFAAcABwAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAFAAUABQAFAAUABQAFAAUABQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAFAAUABwAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAHAAUABQAFAAUABwAHAAUABQAHAAUABQAFAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAFAAcABQAFAAcABwAHAAUABwAFAAUABQAHAAcAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAABwAHAAcABwAHAAcABwAHAAUABQAFAAUABQAFAAUABQAHAAcABQAFAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAABQAFAAUAAAAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAcABQAFAAUABQAFAAUABQAAAAAAAAAAAAUAAAAAAAAAAAAAAAAABQAAAAAABwAFAAUAAAAAAAAAAAAAAAAABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAAABQAFAAUABQAFAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAUABQAFAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAABQAFAAUABQAFAAUADgAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAOAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAUABQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAUABQAFAAUAAAAFAAUABQAFAAUABQAFAAUABQAFAAAAAAAAAAAABQAAAAAAAAAFAAAAAAAAAAAABQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAABwAHAAUABQAHAAAAAAAAAAAABQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAcABwAHAAcABQAFAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAUAAAAAAAAAAAAAAAAABQAFAAUABQAFAAUABQAFAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAUABQAFAAUABQAFAAUABQAFAAUABQAHAAcAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAFAAcABwAFAAUABQAFAAcABwAFAAUABwAHAAAAAAAAAAAAAAAFAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAUABQAFAAUABQAFAAcABwAFAAUABwAHAAUABQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAFAAAAAAAAAAAAAAAAAAAAAAAFAAcAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAABQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAUAAAAFAAUABQAAAAAABQAFAAAAAAAAAAAAAAAFAAUAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAcABQAFAAcABwAAAAAAAAAAAAAABwAFAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAcABwAFAAcABwAFAAcABwAAAAcABQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAABQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAFAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAUABQAFAAUABQAAAAAAAAAAAAAAAAAFAAUABQAAAAUABQAAAAAAAAAAAAAABQAFAAUABQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAFAAUABQAAAAAAAAAAAAUAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAUABQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAcABQAHAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAFAAUABQAFAAUABwAFAAUABQAFAAUABQAFAAUAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAFAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAABwAHAAcABQAFAAUABQAFAAUABQAFAAUABwAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAHAAcABwAFAAUABQAHAAcABQAHAAUABQAAAAAAAAAAAAAAAAAFAAAABwAHAAcABQAFAAUABQAFAAUABQAFAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAUABwAHAAcABwAAAAAABwAHAAAAAAAHAAcABwAAAAAAAAAAAAAAAAAAAAAAAAAFAAAAAAAAAAAAAAAAAAAAAAAAAAAABwAHAAAAAAAFAAUABQAFAAUABQAFAAAAAAAAAAUABQAFAAUABQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAHAAcABwAFAAUABQAFAAUABQAFAAUABwAHAAUABQAFAAcABQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAABQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAABQAHAAcABQAFAAUABQAFAAUABwAFAAcABwAFAAcABQAFAAcABQAFAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAABQAHAAcABQAFAAUABQAAAAAABwAHAAcABwAFAAUABwAFAAUAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAABQAFAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAcABwAHAAUABQAFAAUABQAFAAUABQAHAAcABQAHAAUAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAUABwAFAAcABwAFAAUABQAFAAUABQAHAAUAAAAAAAAAAAAAAAAAAAAAAAcABwAFAAUABQAFAAcABQAFAAUABQAFAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAHAAcABwAFAAUABQAFAAUABQAFAAUABQAHAAUABQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAHAAcABwAFAAUABQAFAAAAAAAFAAUABwAHAAcABwAFAAAAAAAAAAcAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAFAAUABQAFAAUABQAFAAUABQAFAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAUAAAAAAAAAAAAAAAAAAAAAAAAABQAFAAUABQAFAAUABwAHAAUABQAFAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAcABQAFAAUABQAFAAUABQAAAAUABQAFAAUABQAFAAcABQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUAAAAHAAUABQAFAAUABQAFAAUABwAFAAUABwAFAAUAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAABQAFAAUABQAFAAUAAAAAAAAABQAAAAUABQAAAAUAAAAAAAAAAAAAAAAAAAAAAAAAAAAHAAcABwAHAAcAAAAFAAUAAAAHAAcABQAHAAUAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAFAAUABwAHAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAFAAUABQAFAAUAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAFAAUABQAFAAUABQAFAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAABQAAAAcABwAHAAcABwAHAAcABwAHAAcABwAHAAcABwAHAAAAAAAAAAAAAAAAAAAABQAFAAUABQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAUAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAcABwAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAUABQAAAAUABQAFAAAAAAAFAAUABQAFAAUABQAFAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAABQAFAAUABQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAABQAFAAUAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAAAAAAAAAAABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAAAAAAAAAAAAAAAAAAAAAAFAAAAAAAAAAAAAAAAAAAAAAAAAAAABQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAUABQAFAAUABQAAAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAABQAFAAUABQAFAAUABQAAAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAFAAUABQAAAAAABQAFAAUABQAFAAUABQAAAAUABQAAAAUABQAFAAUABQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAFAAUABQAFAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAABQAFAAUABQAFAAUABQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAOAA4ADgAOAA4ADgAOAA4ADgAOAA4ADgAOAA4ADgAOAA4ADgAOAA4ADgAOAA4ADgAOAA4ADgAFAAUABQAFAAUADgAOAA4ADgAOAA4ADwAPAA8ADwAPAA8ADwAPAA8ADwAPAA8ADwAPAA8ADwAPAA8ADwAPAA8ADwAPAA8ADwAPAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAcABwAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAABwAHAAcABwAHAAcABwAHAAcABwAHAAcABwAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAABwAHAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAABwAHAAcABwAHAAcABwAHAAcABwAHAAcABwAHAAcABwAHAAcABwAHAAcABwAHAAcABwAHAAcABwAHAAcABwAHAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAgACAAIAAAAAAAAAAkACQAJAAkACQAJAAkACQAJAAkACQAJAAkACQAJAAkACQAJAAkACQAJAAkACQAJAAkACQAJAAkACQAJAAkACQAKAAoACgAKAAoACgAKAAoACgAKAAoACgAKAAoACgAKAAoACgAKAAoACgAKAAoACgAMAAwADAAMAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAkACQAJAAkACQAJAAkACQAJAAkACQAJAAkACQAJAAkACQAJAAkAAAAAAAAAAAAKAAoACgAKAAoACgAKAAoACgAKAAoACgAKAAoACgAKAAoACgAKAAoACgAKAAoACgAKAAoACgAKAAoACgAKAAoACgAAAAAAAAAAAAsADAAMAAwADAAMAAwADAAMAAwADAAMAAwADAAMAAwADAAMAAwADAAMAAwADAAMAAwADAAMAAwACwAMAAwADAAMAAwADAAMAAwADAAMAAwADAAMAAwADAAMAAwADAAMAAwADAAMAAwADAAMAAwADAAAAAAADgAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA4AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAOAA4ADgAOAA4ADgAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADgAOAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA4ADgAAAAAAAAAAAAAAAAAAAAAADgAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADgAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADgAOAA4ADgAOAA4ADgAOAA4ADgAOAAAAAAAAAAAADgAOAA4AAAAAAAAAAAAAAAAAAAAOAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADgAOAAAAAAAAAAAAAAAAAAAAAAAAAAAADgAAAAAAAAAAAAAAAAAAAAAAAAAOAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADgAOAA4ADgAAAA4ADgAOAA4ADgAOAAAADgAOAA4ADgAOAA4ADgAOAA4ADgAOAA4AAAAOAA4ADgAOAA4ADgAOAA4ADgAOAA4ADgAOAA4ADgAOAA4ADgAOAA4ADgAOAA4ADgAOAA4ADgAOAA4ADgAOAA4ADgAOAAAAAAAAAAAAAAAAAAAAAAAAAAAADgAOAA4ADgAOAA4ADgAOAA4ADgAOAA4ADgAOAA4ADgAOAA4AAAAAAA4ADgAOAA4ADgAOAA4ADgAOAA4ADgAAAA4AAAAOAAAAAAAAAAAAAAAAAA4AAAAAAAAAAAAAAAAADgAAAAAAAAAAAAAAAAAAAAAAAAAAAA4ADgAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADgAAAAAADgAAAAAAAAAAAA4AAAAOAAAAAAAAAAAADgAOAA4AAAAOAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAOAA4ADgAOAA4AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAOAA4ADgAAAAAAAAAAAAAAAAAAAAAAAAAOAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAOAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAOAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAOAA4AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA4ADgAOAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADgAOAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADgAAAAAAAAAAAA4AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAOAAAADgAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAOAA4ADgAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA4ADgAOAA4ADgAOAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA4ADgAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADgAAAAAADgAOAA4ADgAOAA4ADgAOAA4ADgAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADgAOAA4ADgAOAA4ADgAOAA4ADgAOAA4ADgAOAA4ADgAOAA4ADgAAAA4ADgAOAA4ADgAOAA4ADgAOAA4ADgAOAA4ADgAOAAAAAAAAAAAAAAAAAAAAAAAAAAAADgAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA4AAAAAAA4ADgAOAA4ADgAOAA4ADgAOAAAADgAOAA4ADgAAAAAAAAAAAAAAAAAAAAAAAAAOAA4ADgAOAA4ADgAOAA4ADgAOAA4ADgAOAA4ADgAOAA4ADgAOAA4ADgAOAA4AAAAAAAAAAAAAAAAADgAOAA4ADgAOAA4ADgAOAA4ADgAOAA4ADgAOAA4ADgAOAA4ADgAOAA4ADgAOAA4ADgAOAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA4ADgAOAA4ADgAOAA4ADgAOAA4ADgAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAOAA4ADgAOAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADgAOAA4ADgAOAA4ADgAOAAAAAAAAAAAAAAAAAAAAAAAAAAAADgAOAA4ADgAOAA4AAAAAAAAAAAAAAAAAAAAAAA4ADgAOAA4ADgAOAA4ADgAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAOAA4ADgAOAA4ADgAOAA4ADgAOAA4ADgAOAA4ADgAOAA4ADgAOAA4ADgAOAA4ADgAOAA4ADgAOAA4AAAAOAA4ADgAOAA4ADgAAAA4ADgAOAA4ADgAOAA4ADgAOAA4ADgAOAA4ADgAOAA4ADgAOAA4ADgAOAA4ADgAOAA4AAAAAAAAAAAA=",Hn="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",De=typeof Uint8Array>"u"?[]:new Uint8Array(256);for(var lt=0;lt<Hn.length;lt++)De[Hn.charCodeAt(lt)]=lt;var Ml=function(e){var A=e.length*.75,t=e.length,r,n=0,s,a,o,i;e[e.length-1]==="="&&(A--,e[e.length-2]==="="&&A--);var l=typeof ArrayBuffer<"u"&&typeof Uint8Array<"u"&&typeof Uint8Array.prototype.slice<"u"?new ArrayBuffer(A):new Array(A),c=Array.isArray(l)?l:new Uint8Array(l);for(r=0;r<t;r+=4)s=De[e.charCodeAt(r)],a=De[e.charCodeAt(r+1)],o=De[e.charCodeAt(r+2)],i=De[e.charCodeAt(r+3)],c[n++]=s<<2|a>>4,c[n++]=(a&15)<<4|o>>2,c[n++]=(o&3)<<6|i&63;return l},Ol=function(e){for(var A=e.length,t=[],r=0;r<A;r+=2)t.push(e[r+1]<<8|e[r]);return t},_l=function(e){for(var A=e.length,t=[],r=0;r<A;r+=4)t.push(e[r+3]<<24|e[r+2]<<16|e[r+1]<<8|e[r]);return t},ie=5,Zr=11,jt=2,Rl=Zr-ie,_s=65536>>ie,Gl=1<<ie,$t=Gl-1,kl=1024>>ie,Vl=_s+kl,Nl=Vl,Pl=32,Jl=Nl+Pl,Xl=65536>>Zr,Yl=1<<Rl,Wl=Yl-1,In=function(e,A,t){return e.slice?e.slice(A,t):new Uint16Array(Array.prototype.slice.call(e,A,t))},Zl=function(e,A,t){return e.slice?e.slice(A,t):new Uint32Array(Array.prototype.slice.call(e,A,t))},zl=function(e,A){var t=Ml(e),r=Array.isArray(t)?_l(t):new Uint32Array(t),n=Array.isArray(t)?Ol(t):new Uint16Array(t),s=24,a=In(n,s/2,r[4]/2),o=r[5]===2?In(n,(s+r[4])/2):Zl(r,Math.ceil((s+r[4])/4));return new ql(r[0],r[1],r[2],r[3],a,o)},ql=function(){function e(A,t,r,n,s,a){this.initialValue=A,this.errorValue=t,this.highStart=r,this.highValueIndex=n,this.index=s,this.data=a}return e.prototype.get=function(A){var t;if(A>=0){if(A<55296||A>56319&&A<=65535)return t=this.index[A>>ie],t=(t<<jt)+(A&$t),this.data[t];if(A<=65535)return t=this.index[_s+(A-55296>>ie)],t=(t<<jt)+(A&$t),this.data[t];if(A<this.highStart)return t=Jl-Xl+(A>>Zr),t=this.index[t],t+=A>>ie&Wl,t=this.index[t],t=(t<<jt)+(A&$t),this.data[t];if(A<=1114111)return this.data[this.highValueIndex]}return this.errorValue},e}(),bn="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",jl=typeof Uint8Array>"u"?[]:new Uint8Array(256);for(var ct=0;ct<bn.length;ct++)jl[bn.charCodeAt(ct)]=ct;var $l=1,Ar=2,er=3,Sn=4,Dn=5,Ac=7,Ln=8,tr=9,rr=10,Kn=11,Tn=12,Mn=13,On=14,nr=15,ec=function(e){for(var A=[],t=0,r=e.length;t<r;){var n=e.charCodeAt(t++);if(n>=55296&&n<=56319&&t<r){var s=e.charCodeAt(t++);(s&64512)===56320?A.push(((n&1023)<<10)+(s&1023)+65536):(A.push(n),t--)}else A.push(n)}return A},tc=function(){for(var e=[],A=0;A<arguments.length;A++)e[A]=arguments[A];if(String.fromCodePoint)return String.fromCodePoint.apply(String,e);var t=e.length;if(!t)return"";for(var r=[],n=-1,s="";++n<t;){var a=e[n];a<=65535?r.push(a):(a-=65536,r.push((a>>10)+55296,a%1024+56320)),(n+1===t||r.length>16384)&&(s+=String.fromCharCode.apply(String,r),r.length=0)}return s},rc=zl(Tl),uA="×",sr="÷",nc=function(e){return rc.get(e)},sc=function(e,A,t){var r=t-2,n=A[r],s=A[t-1],a=A[t];if(s===Ar&&a===er)return uA;if(s===Ar||s===er||s===Sn||a===Ar||a===er||a===Sn)return sr;if(s===Ln&&[Ln,tr,Kn,Tn].indexOf(a)!==-1||(s===Kn||s===tr)&&(a===tr||a===rr)||(s===Tn||s===rr)&&a===rr||a===Mn||a===Dn||a===Ac||s===$l)return uA;if(s===Mn&&a===On){for(;n===Dn;)n=A[--r];if(n===On)return uA}if(s===nr&&a===nr){for(var o=0;n===nr;)o++,n=A[--r];if(o%2===0)return uA}return sr},ac=function(e){var A=ec(e),t=A.length,r=0,n=0,s=A.map(nc);return{next:function(){if(r>=t)return{done:!0,value:null};for(var a=uA;r<t&&(a=sc(A,s,++r))===uA;);if(a!==uA||r===t){var o=tc.apply(null,A.slice(n,r));return n=r,{value:o,done:!1}}return{done:!0,value:null}}}},ic=function(e){for(var A=ac(e),t=[],r;!(r=A.next()).done;)r.value&&t.push(r.value.slice());return t},oc=function(e){var A=123;if(e.createRange){var t=e.createRange();if(t.getBoundingClientRect){var r=e.createElement("boundtest");r.style.height=A+"px",r.style.display="block",e.body.appendChild(r),t.selectNode(r);var n=t.getBoundingClientRect(),s=Math.round(n.height);if(e.body.removeChild(r),s===A)return!0}}return!1},lc=function(e){var A=e.createElement("boundtest");A.style.width="50px",A.style.display="block",A.style.fontSize="12px",A.style.letterSpacing="0px",A.style.wordSpacing="0px",e.body.appendChild(A);var t=e.createRange();A.innerHTML=typeof"".repeat=="function"?"&#128104;".repeat(10):"";var r=A.firstChild,n=Lt(r.data).map(function(i){return N(i)}),s=0,a={},o=n.every(function(i,l){t.setStart(r,s),t.setEnd(r,s+i.length);var c=t.getBoundingClientRect();s+=i.length;var x=c.x>a.x||c.y>a.y;return a=c,l===0?!0:x});return e.body.removeChild(A),o},cc=function(){return typeof new Image().crossOrigin<"u"},Bc=function(){return typeof new XMLHttpRequest().responseType=="string"},xc=function(e){var A=new Image,t=e.createElement("canvas"),r=t.getContext("2d");if(!r)return!1;A.src="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg'></svg>";try{r.drawImage(A,0,0),t.toDataURL()}catch{return!1}return!0},_n=function(e){return e[0]===0&&e[1]===255&&e[2]===0&&e[3]===255},uc=function(e){var A=e.createElement("canvas"),t=100;A.width=t,A.height=t;var r=A.getContext("2d");if(!r)return Promise.reject(!1);r.fillStyle="rgb(0, 255, 0)",r.fillRect(0,0,t,t);var n=new Image,s=A.toDataURL();n.src=s;var a=Ir(t,t,0,0,n);return r.fillStyle="red",r.fillRect(0,0,t,t),Rn(a).then(function(o){r.drawImage(o,0,0);var i=r.getImageData(0,0,t,t).data;r.fillStyle="red",r.fillRect(0,0,t,t);var l=e.createElement("div");return l.style.backgroundImage="url("+s+")",l.style.height=t+"px",_n(i)?Rn(Ir(t,t,0,0,l)):Promise.reject(!1)}).then(function(o){return r.drawImage(o,0,0),_n(r.getImageData(0,0,t,t).data)}).catch(function(){return!1})},Ir=function(e,A,t,r,n){var s="http://www.w3.org/2000/svg",a=document.createElementNS(s,"svg"),o=document.createElementNS(s,"foreignObject");return a.setAttributeNS(null,"width",e.toString()),a.setAttributeNS(null,"height",A.toString()),o.setAttributeNS(null,"width","100%"),o.setAttributeNS(null,"height","100%"),o.setAttributeNS(null,"x",t.toString()),o.setAttributeNS(null,"y",r.toString()),o.setAttributeNS(null,"externalResourcesRequired","true"),a.appendChild(o),o.appendChild(n),a},Rn=function(e){return new Promise(function(A,t){var r=new Image;r.onload=function(){return A(r)},r.onerror=t,r.src="data:image/svg+xml;charset=utf-8,"+encodeURIComponent(new XMLSerializer().serializeToString(e))})},z={get SUPPORT_RANGE_BOUNDS(){var e=oc(document);return Object.defineProperty(z,"SUPPORT_RANGE_BOUNDS",{value:e}),e},get SUPPORT_WORD_BREAKING(){var e=z.SUPPORT_RANGE_BOUNDS&&lc(document);return Object.defineProperty(z,"SUPPORT_WORD_BREAKING",{value:e}),e},get SUPPORT_SVG_DRAWING(){var e=xc(document);return Object.defineProperty(z,"SUPPORT_SVG_DRAWING",{value:e}),e},get SUPPORT_FOREIGNOBJECT_DRAWING(){var e=typeof Array.from=="function"&&typeof window.fetch=="function"?uc(document):Promise.resolve(!1);return Object.defineProperty(z,"SUPPORT_FOREIGNOBJECT_DRAWING",{value:e}),e},get SUPPORT_CORS_IMAGES(){var e=cc();return Object.defineProperty(z,"SUPPORT_CORS_IMAGES",{value:e}),e},get SUPPORT_RESPONSE_TYPE(){var e=Bc();return Object.defineProperty(z,"SUPPORT_RESPONSE_TYPE",{value:e}),e},get SUPPORT_CORS_XHR(){var e="withCredentials"in new XMLHttpRequest;return Object.defineProperty(z,"SUPPORT_CORS_XHR",{value:e}),e},get SUPPORT_NATIVE_TEXT_SEGMENTATION(){var e=!!(typeof Intl<"u"&&Intl.Segmenter);return Object.defineProperty(z,"SUPPORT_NATIVE_TEXT_SEGMENTATION",{value:e}),e}},Oe=function(){function e(A,t){this.text=A,this.bounds=t}return e}(),gc=function(e,A,t,r){var n=wc(A,t),s=[],a=0;return n.forEach(function(o){if(t.textDecorationLine.length||o.trim().length>0)if(z.SUPPORT_RANGE_BOUNDS){var i=Gn(r,a,o.length).getClientRects();if(i.length>1){var l=zr(o),c=0;l.forEach(function(u){s.push(new Oe(u,KA.fromDOMRectList(e,Gn(r,c+a,u.length).getClientRects()))),c+=u.length})}else s.push(new Oe(o,KA.fromDOMRectList(e,i)))}else{var x=r.splitText(o.length);s.push(new Oe(o,hc(e,r))),r=x}else z.SUPPORT_RANGE_BOUNDS||(r=r.splitText(o.length));a+=o.length}),s},hc=function(e,A){var t=A.ownerDocument;if(t){var r=t.createElement("html2canvaswrapper");r.appendChild(A.cloneNode(!0));var n=A.parentNode;if(n){n.replaceChild(r,A);var s=Dt(e,r);return r.firstChild&&n.replaceChild(r.firstChild,r),s}}return KA.EMPTY},Gn=function(e,A,t){var r=e.ownerDocument;if(!r)throw new Error("Node has no owner document");var n=r.createRange();return n.setStart(e,A),n.setEnd(e,A+t),n},zr=function(e){if(z.SUPPORT_NATIVE_TEXT_SEGMENTATION){var A=new Intl.Segmenter(void 0,{granularity:"grapheme"});return Array.from(A.segment(e)).map(function(t){return t.segment})}return ic(e)},dc=function(e,A){if(z.SUPPORT_NATIVE_TEXT_SEGMENTATION){var t=new Intl.Segmenter(void 0,{granularity:"word"});return Array.from(t.segment(e)).map(function(r){return r.segment})}return Qc(e,A)},wc=function(e,A){return A.letterSpacing!==0?zr(e):dc(e,A)},fc=[32,160,4961,65792,65793,4153,4241],Qc=function(e,A){for(var t=Ja(e,{lineBreak:A.lineBreak,wordBreak:A.overflowWrap==="break-word"?"break-word":A.wordBreak}),r=[],n,s=function(){if(n.value){var a=n.value.slice(),o=Lt(a),i="";o.forEach(function(l){fc.indexOf(l)===-1?i+=N(l):(i.length&&r.push(i),r.push(N(l)),i="")}),i.length&&r.push(i)}};!(n=t.next()).done;)s();return r},Cc=function(){function e(A,t,r){this.text=pc(t.data,r.textTransform),this.textBounds=gc(A,this.text,r,t)}return e}(),pc=function(e,A){switch(A){case 1:return e.toLowerCase();case 3:return e.replace(Uc,Fc);case 2:return e.toUpperCase();default:return e}},Uc=/(^|\s|:|-|\(|\))([a-z])/g,Fc=function(e,A,t){return e.length>0?A+t.toUpperCase():e},Rs=function(e){CA(A,e);function A(t,r){var n=e.call(this,t,r)||this;return n.src=r.currentSrc||r.src,n.intrinsicWidth=r.naturalWidth,n.intrinsicHeight=r.naturalHeight,n.context.cache.addImage(n.src),n}return A}(EA),Gs=function(e){CA(A,e);function A(t,r){var n=e.call(this,t,r)||this;return n.canvas=r,n.intrinsicWidth=r.width,n.intrinsicHeight=r.height,n}return A}(EA),ks=function(e){CA(A,e);function A(t,r){var n=e.call(this,t,r)||this,s=new XMLSerializer,a=Dt(t,r);return r.setAttribute("width",a.width+"px"),r.setAttribute("height",a.height+"px"),n.svg="data:image/svg+xml,"+encodeURIComponent(s.serializeToString(r)),n.intrinsicWidth=r.width.baseVal.value,n.intrinsicHeight=r.height.baseVal.value,n.context.cache.addImage(n.svg),n}return A}(EA),Vs=function(e){CA(A,e);function A(t,r){var n=e.call(this,t,r)||this;return n.value=r.value,n}return A}(EA),br=function(e){CA(A,e);function A(t,r){var n=e.call(this,t,r)||this;return n.start=r.start,n.reversed=typeof r.reversed=="boolean"&&r.reversed===!0,n}return A}(EA),mc=[{type:15,flags:0,unit:"px",number:3}],vc=[{type:16,flags:0,number:50}],Ec=function(e){return e.width>e.height?new KA(e.left+(e.width-e.height)/2,e.top,e.height,e.height):e.width<e.height?new KA(e.left,e.top+(e.height-e.width)/2,e.width,e.width):e},yc=function(e){var A=e.type===Hc?new Array(e.value.length+1).join("•"):e.value;return A.length===0?e.placeholder||"":A},mt="checkbox",vt="radio",Hc="password",kn=707406591,qr=function(e){CA(A,e);function A(t,r){var n=e.call(this,t,r)||this;switch(n.type=r.type.toLowerCase(),n.checked=r.checked,n.value=yc(r),(n.type===mt||n.type===vt)&&(n.styles.backgroundColor=3739148031,n.styles.borderTopColor=n.styles.borderRightColor=n.styles.borderBottomColor=n.styles.borderLeftColor=2779096575,n.styles.borderTopWidth=n.styles.borderRightWidth=n.styles.borderBottomWidth=n.styles.borderLeftWidth=1,n.styles.borderTopStyle=n.styles.borderRightStyle=n.styles.borderBottomStyle=n.styles.borderLeftStyle=1,n.styles.backgroundClip=[0],n.styles.backgroundOrigin=[0],n.bounds=Ec(n.bounds)),n.type){case mt:n.styles.borderTopRightRadius=n.styles.borderTopLeftRadius=n.styles.borderBottomRightRadius=n.styles.borderBottomLeftRadius=mc;break;case vt:n.styles.borderTopRightRadius=n.styles.borderTopLeftRadius=n.styles.borderBottomRightRadius=n.styles.borderBottomLeftRadius=vc;break}return n}return A}(EA),Ns=function(e){CA(A,e);function A(t,r){var n=e.call(this,t,r)||this,s=r.options[r.selectedIndex||0];return n.value=s&&s.text||"",n}return A}(EA),Ps=function(e){CA(A,e);function A(t,r){var n=e.call(this,t,r)||this;return n.value=r.value,n}return A}(EA),Js=function(e){CA(A,e);function A(t,r){var n=e.call(this,t,r)||this;n.src=r.src,n.width=parseInt(r.width,10)||0,n.height=parseInt(r.height,10)||0,n.backgroundColor=n.styles.backgroundColor;try{if(r.contentWindow&&r.contentWindow.document&&r.contentWindow.document.documentElement){n.tree=Ys(t,r.contentWindow.document.documentElement);var s=r.contentWindow.document.documentElement?Te(t,getComputedStyle(r.contentWindow.document.documentElement).backgroundColor):LA.TRANSPARENT,a=r.contentWindow.document.body?Te(t,getComputedStyle(r.contentWindow.document.body).backgroundColor):LA.TRANSPARENT;n.backgroundColor=JA(s)?JA(a)?n.styles.backgroundColor:a:s}}catch{}return n}return A}(EA),Ic=["OL","UL","MENU"],Qt=function(e,A,t,r){for(var n=A.firstChild,s=void 0;n;n=s)if(s=n.nextSibling,Ws(n)&&n.data.trim().length>0)t.textNodes.push(new Cc(e,n,t.styles));else if(fe(n))if(js(n)&&n.assignedNodes)n.assignedNodes().forEach(function(o){return Qt(e,o,t,r)});else{var a=Xs(e,n);a.styles.isVisible()&&(bc(n,a,r)?a.flags|=4:Sc(a.styles)&&(a.flags|=2),Ic.indexOf(n.tagName)!==-1&&(a.flags|=8),t.elements.push(a),n.slot,n.shadowRoot?Qt(e,n.shadowRoot,a,r):!Et(n)&&!Zs(n)&&!yt(n)&&Qt(e,n,a,r))}},Xs=function(e,A){return Dr(A)?new Rs(e,A):zs(A)?new Gs(e,A):Zs(A)?new ks(e,A):Dc(A)?new Vs(e,A):Lc(A)?new br(e,A):Kc(A)?new qr(e,A):yt(A)?new Ns(e,A):Et(A)?new Ps(e,A):qs(A)?new Js(e,A):new EA(e,A)},Ys=function(e,A){var t=Xs(e,A);return t.flags|=4,Qt(e,A,t,t),t},bc=function(e,A,t){return A.styles.isPositionedWithZIndex()||A.styles.opacity<1||A.styles.isTransformed()||jr(e)&&t.styles.isTransparent()},Sc=function(e){return e.isPositioned()||e.isFloating()},Ws=function(e){return e.nodeType===Node.TEXT_NODE},fe=function(e){return e.nodeType===Node.ELEMENT_NODE},Sr=function(e){return fe(e)&&typeof e.style<"u"&&!Ct(e)},Ct=function(e){return typeof e.className=="object"},Dc=function(e){return e.tagName==="LI"},Lc=function(e){return e.tagName==="OL"},Kc=function(e){return e.tagName==="INPUT"},Tc=function(e){return e.tagName==="HTML"},Zs=function(e){return e.tagName==="svg"},jr=function(e){return e.tagName==="BODY"},zs=function(e){return e.tagName==="CANVAS"},Vn=function(e){return e.tagName==="VIDEO"},Dr=function(e){return e.tagName==="IMG"},qs=function(e){return e.tagName==="IFRAME"},Nn=function(e){return e.tagName==="STYLE"},Mc=function(e){return e.tagName==="SCRIPT"},Et=function(e){return e.tagName==="TEXTAREA"},yt=function(e){return e.tagName==="SELECT"},js=function(e){return e.tagName==="SLOT"},Pn=function(e){return e.tagName.indexOf("-")>0},Oc=function(){function e(){this.counters={}}return e.prototype.getCounterValue=function(A){var t=this.counters[A];return t&&t.length?t[t.length-1]:1},e.prototype.getCounterValues=function(A){var t=this.counters[A];return t||[]},e.prototype.pop=function(A){var t=this;A.forEach(function(r){return t.counters[r].pop()})},e.prototype.parse=function(A){var t=this,r=A.counterIncrement,n=A.counterReset,s=!0;r!==null&&r.forEach(function(o){var i=t.counters[o.counter];i&&o.increment!==0&&(s=!1,i.length||i.push(1),i[Math.max(0,i.length-1)]+=o.increment)});var a=[];return s&&n.forEach(function(o){var i=t.counters[o.counter];a.push(o.counter),i||(i=t.counters[o.counter]=[]),i.push(o.reset)}),a},e}(),Jn={integers:[1e3,900,500,400,100,90,50,40,10,9,5,4,1],values:["M","CM","D","CD","C","XC","L","XL","X","IX","V","IV","I"]},Xn={integers:[9e3,8e3,7e3,6e3,5e3,4e3,3e3,2e3,1e3,900,800,700,600,500,400,300,200,100,90,80,70,60,50,40,30,20,10,9,8,7,6,5,4,3,2,1],values:["Ք","Փ","Ւ","Ց","Ր","Տ","Վ","Ս","Ռ","Ջ","Պ","Չ","Ո","Շ","Ն","Յ","Մ","Ճ","Ղ","Ձ","Հ","Կ","Ծ","Խ","Լ","Ի","Ժ","Թ","Ը","Է","Զ","Ե","Դ","Գ","Բ","Ա"]},_c={integers:[1e4,9e3,8e3,7e3,6e3,5e3,4e3,3e3,2e3,1e3,400,300,200,100,90,80,70,60,50,40,30,20,19,18,17,16,15,10,9,8,7,6,5,4,3,2,1],values:["י׳","ט׳","ח׳","ז׳","ו׳","ה׳","ד׳","ג׳","ב׳","א׳","ת","ש","ר","ק","צ","פ","ע","ס","נ","מ","ל","כ","יט","יח","יז","טז","טו","י","ט","ח","ז","ו","ה","ד","ג","ב","א"]},Rc={integers:[1e4,9e3,8e3,7e3,6e3,5e3,4e3,3e3,2e3,1e3,900,800,700,600,500,400,300,200,100,90,80,70,60,50,40,30,20,10,9,8,7,6,5,4,3,2,1],values:["ჵ","ჰ","ჯ","ჴ","ხ","ჭ","წ","ძ","ც","ჩ","შ","ყ","ღ","ქ","ფ","ჳ","ტ","ს","რ","ჟ","პ","ო","ჲ","ნ","მ","ლ","კ","ი","თ","ჱ","ზ","ვ","ე","დ","გ","ბ","ა"]},xe=function(e,A,t,r,n,s){return e<A||e>t?ke(e,n,s.length>0):r.integers.reduce(function(a,o,i){for(;e>=o;)e-=o,a+=r.values[i];return a},"")+s},$s=function(e,A,t,r){var n="";do t||e--,n=r(e)+n,e/=A;while(e*A>=A);return n},k=function(e,A,t,r,n){var s=t-A+1;return(e<0?"-":"")+($s(Math.abs(e),s,r,function(a){return N(Math.floor(a%s)+A)})+n)},jA=function(e,A,t){t===void 0&&(t=". ");var r=A.length;return $s(Math.abs(e),r,!1,function(n){return A[Math.floor(n%r)]})+t},de=1,_A=2,RA=4,Le=8,bA=function(e,A,t,r,n,s){if(e<-9999||e>9999)return ke(e,4,n.length>0);var a=Math.abs(e),o=n;if(a===0)return A[0]+o;for(var i=0;a>0&&i<=4;i++){var l=a%10;l===0&&Y(s,de)&&o!==""?o=A[l]+o:l>1||l===1&&i===0||l===1&&i===1&&Y(s,_A)||l===1&&i===1&&Y(s,RA)&&e>100||l===1&&i>1&&Y(s,Le)?o=A[l]+(i>0?t[i-1]:"")+o:l===1&&i>0&&(o=t[i-1]+o),a=Math.floor(a/10)}return(e<0?r:"")+o},Yn="十百千萬",Wn="拾佰仟萬",Zn="マイナス",ar="마이너스",ke=function(e,A,t){var r=t?". ":"",n=t?"、":"",s=t?", ":"",a=t?" ":"";switch(A){case 0:return"•"+a;case 1:return"◦"+a;case 2:return"◾"+a;case 5:var o=k(e,48,57,!0,r);return o.length<4?"0"+o:o;case 4:return jA(e,"〇一二三四五六七八九",n);case 6:return xe(e,1,3999,Jn,3,r).toLowerCase();case 7:return xe(e,1,3999,Jn,3,r);case 8:return k(e,945,969,!1,r);case 9:return k(e,97,122,!1,r);case 10:return k(e,65,90,!1,r);case 11:return k(e,1632,1641,!0,r);case 12:case 49:return xe(e,1,9999,Xn,3,r);case 35:return xe(e,1,9999,Xn,3,r).toLowerCase();case 13:return k(e,2534,2543,!0,r);case 14:case 30:return k(e,6112,6121,!0,r);case 15:return jA(e,"子丑寅卯辰巳午未申酉戌亥",n);case 16:return jA(e,"甲乙丙丁戊己庚辛壬癸",n);case 17:case 48:return bA(e,"零一二三四五六七八九",Yn,"負",n,_A|RA|Le);case 47:return bA(e,"零壹貳參肆伍陸柒捌玖",Wn,"負",n,de|_A|RA|Le);case 42:return bA(e,"零一二三四五六七八九",Yn,"负",n,_A|RA|Le);case 41:return bA(e,"零壹贰叁肆伍陆柒捌玖",Wn,"负",n,de|_A|RA|Le);case 26:return bA(e,"〇一二三四五六七八九","十百千万",Zn,n,0);case 25:return bA(e,"零壱弐参四伍六七八九","拾百千万",Zn,n,de|_A|RA);case 31:return bA(e,"영일이삼사오육칠팔구","십백천만",ar,s,de|_A|RA);case 33:return bA(e,"零一二三四五六七八九","十百千萬",ar,s,0);case 32:return bA(e,"零壹貳參四五六七八九","拾百千",ar,s,de|_A|RA);case 18:return k(e,2406,2415,!0,r);case 20:return xe(e,1,19999,Rc,3,r);case 21:return k(e,2790,2799,!0,r);case 22:return k(e,2662,2671,!0,r);case 22:return xe(e,1,10999,_c,3,r);case 23:return jA(e,"あいうえおかきくけこさしすせそたちつてとなにぬねのはひふへほまみむめもやゆよらりるれろわゐゑをん");case 24:return jA(e,"いろはにほへとちりぬるをわかよたれそつねならむうゐのおくやまけふこえてあさきゆめみしゑひもせす");case 27:return k(e,3302,3311,!0,r);case 28:return jA(e,"アイウエオカキクケコサシスセソタチツテトナニヌネノハヒフヘホマミムメモヤユヨラリルレロワヰヱヲン",n);case 29:return jA(e,"イロハニホヘトチリヌルヲワカヨタレソツネナラムウヰノオクヤマケフコエテアサキユメミシヱヒモセス",n);case 34:return k(e,3792,3801,!0,r);case 37:return k(e,6160,6169,!0,r);case 38:return k(e,4160,4169,!0,r);case 39:return k(e,2918,2927,!0,r);case 40:return k(e,1776,1785,!0,r);case 43:return k(e,3046,3055,!0,r);case 44:return k(e,3174,3183,!0,r);case 45:return k(e,3664,3673,!0,r);case 46:return k(e,3872,3881,!0,r);case 3:default:return k(e,48,57,!0,r)}},Aa="data-html2canvas-ignore",zn=function(){function e(A,t,r){if(this.context=A,this.options=r,this.scrolledElements=[],this.referenceElement=t,this.counters=new Oc,this.quoteDepth=0,!t.ownerDocument)throw new Error("Cloned element does not have an owner document");this.documentElement=this.cloneNode(t.ownerDocument.documentElement,!1)}return e.prototype.toIFrame=function(A,t){var r=this,n=Gc(A,t);if(!n.contentWindow)return Promise.reject("Unable to find iframe window");var s=A.defaultView.pageXOffset,a=A.defaultView.pageYOffset,o=n.contentWindow,i=o.document,l=Nc(n).then(function(){return AA(r,void 0,void 0,function(){var c,x;return j(this,function(u){switch(u.label){case 0:return this.scrolledElements.forEach(Yc),o&&(o.scrollTo(t.left,t.top),/(iPad|iPhone|iPod)/g.test(navigator.userAgent)&&(o.scrollY!==t.top||o.scrollX!==t.left)&&(this.context.logger.warn("Unable to restore scroll position for cloned document"),this.context.windowBounds=this.context.windowBounds.add(o.scrollX-t.left,o.scrollY-t.top,0,0))),c=this.options.onclone,x=this.clonedReferenceElement,typeof x>"u"?[2,Promise.reject("Error finding the "+this.referenceElement.nodeName+" in the cloned document")]:i.fonts&&i.fonts.ready?[4,i.fonts.ready]:[3,2];case 1:u.sent(),u.label=2;case 2:return/(AppleWebKit)/g.test(navigator.userAgent)?[4,Vc(i)]:[3,4];case 3:u.sent(),u.label=4;case 4:return typeof c=="function"?[2,Promise.resolve().then(function(){return c(i,x)}).then(function(){return n})]:[2,n]}})})});return i.open(),i.write(Jc(document.doctype)+"<html></html>"),Xc(this.referenceElement.ownerDocument,s,a),i.replaceChild(i.adoptNode(this.documentElement),i.documentElement),i.close(),l},e.prototype.createElementClone=function(A){if(Hr(A,2))debugger;if(zs(A))return this.createCanvasClone(A);if(Vn(A))return this.createVideoClone(A);if(Nn(A))return this.createStyleClone(A);var t=A.cloneNode(!1);return Dr(t)&&(Dr(A)&&A.currentSrc&&A.currentSrc!==A.src&&(t.src=A.currentSrc,t.srcset=""),t.loading==="lazy"&&(t.loading="eager")),Pn(t)?this.createCustomElementClone(t):t},e.prototype.createCustomElementClone=function(A){var t=document.createElement("html2canvascustomelement");return ir(A.style,t),t},e.prototype.createStyleClone=function(A){try{var t=A.sheet;if(t&&t.cssRules){var r=[].slice.call(t.cssRules,0).reduce(function(s,a){return a&&typeof a.cssText=="string"?s+a.cssText:s},""),n=A.cloneNode(!1);return n.textContent=r,n}}catch(s){if(this.context.logger.error("Unable to access cssRules property",s),s.name!=="SecurityError")throw s}return A.cloneNode(!1)},e.prototype.createCanvasClone=function(A){var t;if(this.options.inlineImages&&A.ownerDocument){var r=A.ownerDocument.createElement("img");try{return r.src=A.toDataURL(),r}catch{this.context.logger.info("Unable to inline canvas contents, canvas is tainted",A)}}var n=A.cloneNode(!1);try{n.width=A.width,n.height=A.height;var s=A.getContext("2d"),a=n.getContext("2d");if(a)if(!this.options.allowTaint&&s)a.putImageData(s.getImageData(0,0,A.width,A.height),0,0);else{var o=(t=A.getContext("webgl2"))!==null&&t!==void 0?t:A.getContext("webgl");if(o){var i=o.getContextAttributes();(i==null?void 0:i.preserveDrawingBuffer)===!1&&this.context.logger.warn("Unable to clone WebGL context as it has preserveDrawingBuffer=false",A)}a.drawImage(A,0,0)}return n}catch{this.context.logger.info("Unable to clone canvas as it is tainted",A)}return n},e.prototype.createVideoClone=function(A){var t=A.ownerDocument.createElement("canvas");t.width=A.offsetWidth,t.height=A.offsetHeight;var r=t.getContext("2d");try{return r&&(r.drawImage(A,0,0,t.width,t.height),this.options.allowTaint||r.getImageData(0,0,t.width,t.height)),t}catch{this.context.logger.info("Unable to clone video as it is tainted",A)}var n=A.ownerDocument.createElement("canvas");return n.width=A.offsetWidth,n.height=A.offsetHeight,n},e.prototype.appendChildNode=function(A,t,r){(!fe(t)||!Mc(t)&&!t.hasAttribute(Aa)&&(typeof this.options.ignoreElements!="function"||!this.options.ignoreElements(t)))&&(!this.options.copyStyles||!fe(t)||!Nn(t))&&A.appendChild(this.cloneNode(t,r))},e.prototype.cloneChildNodes=function(A,t,r){for(var n=this,s=A.shadowRoot?A.shadowRoot.firstChild:A.firstChild;s;s=s.nextSibling)if(fe(s)&&js(s)&&typeof s.assignedNodes=="function"){var a=s.assignedNodes();a.length&&a.forEach(function(o){return n.appendChildNode(t,o,r)})}else this.appendChildNode(t,s,r)},e.prototype.cloneNode=function(A,t){if(Ws(A))return document.createTextNode(A.data);if(!A.ownerDocument)return A.cloneNode(!1);var r=A.ownerDocument.defaultView;if(r&&fe(A)&&(Sr(A)||Ct(A))){var n=this.createElementClone(A);n.style.transitionProperty="none";var s=r.getComputedStyle(A),a=r.getComputedStyle(A,":before"),o=r.getComputedStyle(A,":after");this.referenceElement===A&&Sr(n)&&(this.clonedReferenceElement=n),jr(n)&&zc(n);var i=this.counters.parse(new yn(this.context,s)),l=this.resolvePseudoContent(A,n,a,_e.BEFORE);Pn(A)&&(t=!0),Vn(A)||this.cloneChildNodes(A,n,t),l&&n.insertBefore(l,n.firstChild);var c=this.resolvePseudoContent(A,n,o,_e.AFTER);return c&&n.appendChild(c),this.counters.pop(i),(s&&(this.options.copyStyles||Ct(A))&&!qs(A)||t)&&ir(s,n),(A.scrollTop!==0||A.scrollLeft!==0)&&this.scrolledElements.push([n,A.scrollLeft,A.scrollTop]),(Et(A)||yt(A))&&(Et(n)||yt(n))&&(n.value=A.value),n}return A.cloneNode(!1)},e.prototype.resolvePseudoContent=function(A,t,r,n){var s=this;if(r){var a=r.content,o=t.ownerDocument;if(!(!o||!a||a==="none"||a==="-moz-alt-content"||r.display==="none")){this.counters.parse(new yn(this.context,r));var i=new Dl(this.context,r),l=o.createElement("html2canvaspseudoelement");ir(r,l),i.content.forEach(function(x){if(x.type===0)l.appendChild(o.createTextNode(x.value));else if(x.type===22){var u=o.createElement("img");u.src=x.value,u.style.opacity="1",l.appendChild(u)}else if(x.type===18){if(x.name==="attr"){var w=x.values.filter(M);w.length&&l.appendChild(o.createTextNode(A.getAttribute(w[0].value)||""))}else if(x.name==="counter"){var g=x.values.filter(Ce),h=g[0],U=g[1];if(h&&M(h)){var d=s.counters.getCounterValue(h.value),f=U&&M(U)?yr.parse(s.context,U.value):3;l.appendChild(o.createTextNode(ke(d,f,!1)))}}else if(x.name==="counters"){var v=x.values.filter(Ce),h=v[0],m=v[1],U=v[2];if(h&&M(h)){var F=s.counters.getCounterValues(h.value),Q=U&&M(U)?yr.parse(s.context,U.value):3,b=m&&m.type===0?m.value:"",S=F.map(function(tA){return ke(tA,Q,!1)}).join(b);l.appendChild(o.createTextNode(S))}}}else if(x.type===20)switch(x.value){case"open-quote":l.appendChild(o.createTextNode(En(i.quotes,s.quoteDepth++,!0)));break;case"close-quote":l.appendChild(o.createTextNode(En(i.quotes,--s.quoteDepth,!1)));break;default:l.appendChild(o.createTextNode(x.value))}}),l.className=Lr+" "+Kr;var c=n===_e.BEFORE?" "+Lr:" "+Kr;return Ct(t)?t.className.baseValue+=c:t.className+=c,l}}},e.destroy=function(A){return A.parentNode?(A.parentNode.removeChild(A),!0):!1},e}(),_e;(function(e){e[e.BEFORE=0]="BEFORE",e[e.AFTER=1]="AFTER"})(_e||(_e={}));var Gc=function(e,A){var t=e.createElement("iframe");return t.className="html2canvas-container",t.style.visibility="hidden",t.style.position="fixed",t.style.left="-10000px",t.style.top="0px",t.style.border="0",t.width=A.width.toString(),t.height=A.height.toString(),t.scrolling="no",t.setAttribute(Aa,"true"),e.body.appendChild(t),t},kc=function(e){return new Promise(function(A){if(e.complete){A();return}if(!e.src){A();return}e.onload=A,e.onerror=A})},Vc=function(e){return Promise.all([].slice.call(e.images,0).map(kc))},Nc=function(e){return new Promise(function(A,t){var r=e.contentWindow;if(!r)return t("No window assigned for iframe");var n=r.document;r.onload=e.onload=function(){r.onload=e.onload=null;var s=setInterval(function(){n.body.childNodes.length>0&&n.readyState==="complete"&&(clearInterval(s),A(e))},50)}})},Pc=["all","d","content"],ir=function(e,A){for(var t=e.length-1;t>=0;t--){var r=e.item(t);Pc.indexOf(r)===-1&&A.style.setProperty(r,e.getPropertyValue(r))}return A},Jc=function(e){var A="";return e&&(A+="<!DOCTYPE ",e.name&&(A+=e.name),e.internalSubset&&(A+=e.internalSubset),e.publicId&&(A+='"'+e.publicId+'"'),e.systemId&&(A+='"'+e.systemId+'"'),A+=">"),A},Xc=function(e,A,t){e&&e.defaultView&&(A!==e.defaultView.pageXOffset||t!==e.defaultView.pageYOffset)&&e.defaultView.scrollTo(A,t)},Yc=function(e){var A=e[0],t=e[1],r=e[2];A.scrollLeft=t,A.scrollTop=r},Wc=":before",Zc=":after",Lr="___html2canvas___pseudoelement_before",Kr="___html2canvas___pseudoelement_after",qn=`{
    content: "" !important;
    display: none !important;
}`,zc=function(e){qc(e,"."+Lr+Wc+qn+`
         .`+Kr+Zc+qn)},qc=function(e,A){var t=e.ownerDocument;if(t){var r=t.createElement("style");r.textContent=A,e.appendChild(r)}},ea=function(){function e(){}return e.getOrigin=function(A){var t=e._link;return t?(t.href=A,t.href=t.href,t.protocol+t.hostname+t.port):"about:blank"},e.isSameOrigin=function(A){return e.getOrigin(A)===e._origin},e.setContext=function(A){e._link=A.document.createElement("a"),e._origin=e.getOrigin(A.location.href)},e._origin="about:blank",e}(),jc=function(){function e(A,t){this.context=A,this._options=t,this._cache={}}return e.prototype.addImage=function(A){var t=Promise.resolve();return this.has(A)||(lr(A)||t0(A))&&(this._cache[A]=this.loadImage(A)).catch(function(){}),t},e.prototype.match=function(A){return this._cache[A]},e.prototype.loadImage=function(A){return AA(this,void 0,void 0,function(){var t,r,n,s,a=this;return j(this,function(o){switch(o.label){case 0:return t=ea.isSameOrigin(A),r=!or(A)&&this._options.useCORS===!0&&z.SUPPORT_CORS_IMAGES&&!t,n=!or(A)&&!t&&!lr(A)&&typeof this._options.proxy=="string"&&z.SUPPORT_CORS_XHR&&!r,!t&&this._options.allowTaint===!1&&!or(A)&&!lr(A)&&!n&&!r?[2]:(s=A,n?[4,this.proxy(s)]:[3,2]);case 1:s=o.sent(),o.label=2;case 2:return this.context.logger.debug("Added image "+A.substring(0,256)),[4,new Promise(function(i,l){var c=new Image;c.onload=function(){return i(c)},c.onerror=l,(r0(s)||r)&&(c.crossOrigin="anonymous"),c.src=s,c.complete===!0&&setTimeout(function(){return i(c)},500),a._options.imageTimeout>0&&setTimeout(function(){return l("Timed out ("+a._options.imageTimeout+"ms) loading image")},a._options.imageTimeout)})];case 3:return[2,o.sent()]}})})},e.prototype.has=function(A){return typeof this._cache[A]<"u"},e.prototype.keys=function(){return Promise.resolve(Object.keys(this._cache))},e.prototype.proxy=function(A){var t=this,r=this._options.proxy;if(!r)throw new Error("No proxy defined");var n=A.substring(0,256);return new Promise(function(s,a){var o=z.SUPPORT_RESPONSE_TYPE?"blob":"text",i=new XMLHttpRequest;i.onload=function(){if(i.status===200)if(o==="text")s(i.response);else{var x=new FileReader;x.addEventListener("load",function(){return s(x.result)},!1),x.addEventListener("error",function(u){return a(u)},!1),x.readAsDataURL(i.response)}else a("Failed to proxy resource "+n+" with status code "+i.status)},i.onerror=a;var l=r.indexOf("?")>-1?"&":"?";if(i.open("GET",""+r+l+"url="+encodeURIComponent(A)+"&responseType="+o),o!=="text"&&i instanceof XMLHttpRequest&&(i.responseType=o),t._options.imageTimeout){var c=t._options.imageTimeout;i.timeout=c,i.ontimeout=function(){return a("Timed out ("+c+"ms) proxying "+n)}}i.send()})},e}(),$c=/^data:image\/svg\+xml/i,A0=/^data:image\/.*;base64,/i,e0=/^data:image\/.*/i,t0=function(e){return z.SUPPORT_SVG_DRAWING||!n0(e)},or=function(e){return e0.test(e)},r0=function(e){return A0.test(e)},lr=function(e){return e.substr(0,4)==="blob"},n0=function(e){return e.substr(-3).toLowerCase()==="svg"||$c.test(e)},C=function(){function e(A,t){this.type=0,this.x=A,this.y=t}return e.prototype.add=function(A,t){return new e(this.x+A,this.y+t)},e}(),ue=function(e,A,t){return new C(e.x+(A.x-e.x)*t,e.y+(A.y-e.y)*t)},Bt=function(){function e(A,t,r,n){this.type=1,this.start=A,this.startControl=t,this.endControl=r,this.end=n}return e.prototype.subdivide=function(A,t){var r=ue(this.start,this.startControl,A),n=ue(this.startControl,this.endControl,A),s=ue(this.endControl,this.end,A),a=ue(r,n,A),o=ue(n,s,A),i=ue(a,o,A);return t?new e(this.start,r,a,i):new e(i,o,s,this.end)},e.prototype.add=function(A,t){return new e(this.start.add(A,t),this.startControl.add(A,t),this.endControl.add(A,t),this.end.add(A,t))},e.prototype.reverse=function(){return new e(this.end,this.endControl,this.startControl,this.start)},e}(),hA=function(e){return e.type===1},s0=function(){function e(A){var t=A.styles,r=A.bounds,n=Se(t.borderTopLeftRadius,r.width,r.height),s=n[0],a=n[1],o=Se(t.borderTopRightRadius,r.width,r.height),i=o[0],l=o[1],c=Se(t.borderBottomRightRadius,r.width,r.height),x=c[0],u=c[1],w=Se(t.borderBottomLeftRadius,r.width,r.height),g=w[0],h=w[1],U=[];U.push((s+i)/r.width),U.push((g+x)/r.width),U.push((a+h)/r.height),U.push((l+u)/r.height);var d=Math.max.apply(Math,U);d>1&&(s/=d,a/=d,i/=d,l/=d,x/=d,u/=d,g/=d,h/=d);var f=r.width-i,v=r.height-u,m=r.width-x,F=r.height-h,Q=t.borderTopWidth,b=t.borderRightWidth,S=t.borderBottomWidth,E=t.borderLeftWidth,O=_(t.paddingTop,A.bounds.width),tA=_(t.paddingRight,A.bounds.width),cA=_(t.paddingBottom,A.bounds.width),T=_(t.paddingLeft,A.bounds.width);this.topLeftBorderDoubleOuterBox=s>0||a>0?G(r.left+E/3,r.top+Q/3,s-E/3,a-Q/3,K.TOP_LEFT):new C(r.left+E/3,r.top+Q/3),this.topRightBorderDoubleOuterBox=s>0||a>0?G(r.left+f,r.top+Q/3,i-b/3,l-Q/3,K.TOP_RIGHT):new C(r.left+r.width-b/3,r.top+Q/3),this.bottomRightBorderDoubleOuterBox=x>0||u>0?G(r.left+m,r.top+v,x-b/3,u-S/3,K.BOTTOM_RIGHT):new C(r.left+r.width-b/3,r.top+r.height-S/3),this.bottomLeftBorderDoubleOuterBox=g>0||h>0?G(r.left+E/3,r.top+F,g-E/3,h-S/3,K.BOTTOM_LEFT):new C(r.left+E/3,r.top+r.height-S/3),this.topLeftBorderDoubleInnerBox=s>0||a>0?G(r.left+E*2/3,r.top+Q*2/3,s-E*2/3,a-Q*2/3,K.TOP_LEFT):new C(r.left+E*2/3,r.top+Q*2/3),this.topRightBorderDoubleInnerBox=s>0||a>0?G(r.left+f,r.top+Q*2/3,i-b*2/3,l-Q*2/3,K.TOP_RIGHT):new C(r.left+r.width-b*2/3,r.top+Q*2/3),this.bottomRightBorderDoubleInnerBox=x>0||u>0?G(r.left+m,r.top+v,x-b*2/3,u-S*2/3,K.BOTTOM_RIGHT):new C(r.left+r.width-b*2/3,r.top+r.height-S*2/3),this.bottomLeftBorderDoubleInnerBox=g>0||h>0?G(r.left+E*2/3,r.top+F,g-E*2/3,h-S*2/3,K.BOTTOM_LEFT):new C(r.left+E*2/3,r.top+r.height-S*2/3),this.topLeftBorderStroke=s>0||a>0?G(r.left+E/2,r.top+Q/2,s-E/2,a-Q/2,K.TOP_LEFT):new C(r.left+E/2,r.top+Q/2),this.topRightBorderStroke=s>0||a>0?G(r.left+f,r.top+Q/2,i-b/2,l-Q/2,K.TOP_RIGHT):new C(r.left+r.width-b/2,r.top+Q/2),this.bottomRightBorderStroke=x>0||u>0?G(r.left+m,r.top+v,x-b/2,u-S/2,K.BOTTOM_RIGHT):new C(r.left+r.width-b/2,r.top+r.height-S/2),this.bottomLeftBorderStroke=g>0||h>0?G(r.left+E/2,r.top+F,g-E/2,h-S/2,K.BOTTOM_LEFT):new C(r.left+E/2,r.top+r.height-S/2),this.topLeftBorderBox=s>0||a>0?G(r.left,r.top,s,a,K.TOP_LEFT):new C(r.left,r.top),this.topRightBorderBox=i>0||l>0?G(r.left+f,r.top,i,l,K.TOP_RIGHT):new C(r.left+r.width,r.top),this.bottomRightBorderBox=x>0||u>0?G(r.left+m,r.top+v,x,u,K.BOTTOM_RIGHT):new C(r.left+r.width,r.top+r.height),this.bottomLeftBorderBox=g>0||h>0?G(r.left,r.top+F,g,h,K.BOTTOM_LEFT):new C(r.left,r.top+r.height),this.topLeftPaddingBox=s>0||a>0?G(r.left+E,r.top+Q,Math.max(0,s-E),Math.max(0,a-Q),K.TOP_LEFT):new C(r.left+E,r.top+Q),this.topRightPaddingBox=i>0||l>0?G(r.left+Math.min(f,r.width-b),r.top+Q,f>r.width+b?0:Math.max(0,i-b),Math.max(0,l-Q),K.TOP_RIGHT):new C(r.left+r.width-b,r.top+Q),this.bottomRightPaddingBox=x>0||u>0?G(r.left+Math.min(m,r.width-E),r.top+Math.min(v,r.height-S),Math.max(0,x-b),Math.max(0,u-S),K.BOTTOM_RIGHT):new C(r.left+r.width-b,r.top+r.height-S),this.bottomLeftPaddingBox=g>0||h>0?G(r.left+E,r.top+Math.min(F,r.height-S),Math.max(0,g-E),Math.max(0,h-S),K.BOTTOM_LEFT):new C(r.left+E,r.top+r.height-S),this.topLeftContentBox=s>0||a>0?G(r.left+E+T,r.top+Q+O,Math.max(0,s-(E+T)),Math.max(0,a-(Q+O)),K.TOP_LEFT):new C(r.left+E+T,r.top+Q+O),this.topRightContentBox=i>0||l>0?G(r.left+Math.min(f,r.width+E+T),r.top+Q+O,f>r.width+E+T?0:i-E+T,l-(Q+O),K.TOP_RIGHT):new C(r.left+r.width-(b+tA),r.top+Q+O),this.bottomRightContentBox=x>0||u>0?G(r.left+Math.min(m,r.width-(E+T)),r.top+Math.min(v,r.height+Q+O),Math.max(0,x-(b+tA)),u-(S+cA),K.BOTTOM_RIGHT):new C(r.left+r.width-(b+tA),r.top+r.height-(S+cA)),this.bottomLeftContentBox=g>0||h>0?G(r.left+E+T,r.top+F,Math.max(0,g-(E+T)),h-(S+cA),K.BOTTOM_LEFT):new C(r.left+E+T,r.top+r.height-(S+cA))}return e}(),K;(function(e){e[e.TOP_LEFT=0]="TOP_LEFT",e[e.TOP_RIGHT=1]="TOP_RIGHT",e[e.BOTTOM_RIGHT=2]="BOTTOM_RIGHT",e[e.BOTTOM_LEFT=3]="BOTTOM_LEFT"})(K||(K={}));var G=function(e,A,t,r,n){var s=4*((Math.sqrt(2)-1)/3),a=t*s,o=r*s,i=e+t,l=A+r;switch(n){case K.TOP_LEFT:return new Bt(new C(e,l),new C(e,l-o),new C(i-a,A),new C(i,A));case K.TOP_RIGHT:return new Bt(new C(e,A),new C(e+a,A),new C(i,l-o),new C(i,l));case K.BOTTOM_RIGHT:return new Bt(new C(i,A),new C(i,A+o),new C(e+a,l),new C(e,l));case K.BOTTOM_LEFT:default:return new Bt(new C(i,l),new C(i-a,l),new C(e,A+o),new C(e,A))}},Ht=function(e){return[e.topLeftBorderBox,e.topRightBorderBox,e.bottomRightBorderBox,e.bottomLeftBorderBox]},a0=function(e){return[e.topLeftContentBox,e.topRightContentBox,e.bottomRightContentBox,e.bottomLeftContentBox]},It=function(e){return[e.topLeftPaddingBox,e.topRightPaddingBox,e.bottomRightPaddingBox,e.bottomLeftPaddingBox]},i0=function(){function e(A,t,r){this.offsetX=A,this.offsetY=t,this.matrix=r,this.type=0,this.target=6}return e}(),xt=function(){function e(A,t){this.path=A,this.target=t,this.type=1}return e}(),o0=function(){function e(A){this.opacity=A,this.type=2,this.target=6}return e}(),l0=function(e){return e.type===0},ta=function(e){return e.type===1},c0=function(e){return e.type===2},jn=function(e,A){return e.length===A.length?e.some(function(t,r){return t===A[r]}):!1},B0=function(e,A,t,r,n){return e.map(function(s,a){switch(a){case 0:return s.add(A,t);case 1:return s.add(A+r,t);case 2:return s.add(A+r,t+n);case 3:return s.add(A,t+n)}return s})},ra=function(){function e(A){this.element=A,this.inlineLevel=[],this.nonInlineLevel=[],this.negativeZIndex=[],this.zeroOrAutoZIndexOrTransformedOrOpacity=[],this.positiveZIndex=[],this.nonPositionedFloats=[],this.nonPositionedInlineLevel=[]}return e}(),na=function(){function e(A,t){if(this.container=A,this.parent=t,this.effects=[],this.curves=new s0(this.container),this.container.styles.opacity<1&&this.effects.push(new o0(this.container.styles.opacity)),this.container.styles.transform!==null){var r=this.container.bounds.left+this.container.styles.transformOrigin[0].number,n=this.container.bounds.top+this.container.styles.transformOrigin[1].number,s=this.container.styles.transform;this.effects.push(new i0(r,n,s))}if(this.container.styles.overflowX!==0){var a=Ht(this.curves),o=It(this.curves);jn(a,o)?this.effects.push(new xt(a,6)):(this.effects.push(new xt(a,2)),this.effects.push(new xt(o,4)))}}return e.prototype.getEffects=function(A){for(var t=[2,3].indexOf(this.container.styles.position)===-1,r=this.parent,n=this.effects.slice(0);r;){var s=r.effects.filter(function(i){return!ta(i)});if(t||r.container.styles.position!==0||!r.parent){if(n.unshift.apply(n,s),t=[2,3].indexOf(r.container.styles.position)===-1,r.container.styles.overflowX!==0){var a=Ht(r.curves),o=It(r.curves);jn(a,o)||n.unshift(new xt(o,6))}}else n.unshift.apply(n,s);r=r.parent}return n.filter(function(i){return Y(i.target,A)})},e}(),Tr=function(e,A,t,r){e.container.elements.forEach(function(n){var s=Y(n.flags,4),a=Y(n.flags,2),o=new na(n,e);Y(n.styles.display,2048)&&r.push(o);var i=Y(n.flags,8)?[]:r;if(s||a){var l=s||n.styles.isPositioned()?t:A,c=new ra(o);if(n.styles.isPositioned()||n.styles.opacity<1||n.styles.isTransformed()){var x=n.styles.zIndex.order;if(x<0){var u=0;l.negativeZIndex.some(function(g,h){return x>g.element.container.styles.zIndex.order?(u=h,!1):u>0}),l.negativeZIndex.splice(u,0,c)}else if(x>0){var w=0;l.positiveZIndex.some(function(g,h){return x>=g.element.container.styles.zIndex.order?(w=h+1,!1):w>0}),l.positiveZIndex.splice(w,0,c)}else l.zeroOrAutoZIndexOrTransformedOrOpacity.push(c)}else n.styles.isFloating()?l.nonPositionedFloats.push(c):l.nonPositionedInlineLevel.push(c);Tr(o,c,s?c:t,i)}else n.styles.isInlineLevel()?A.inlineLevel.push(o):A.nonInlineLevel.push(o),Tr(o,A,t,i);Y(n.flags,8)&&sa(n,i)})},sa=function(e,A){for(var t=e instanceof br?e.start:1,r=e instanceof br?e.reversed:!1,n=0;n<A.length;n++){var s=A[n];s.container instanceof Vs&&typeof s.container.value=="number"&&s.container.value!==0&&(t=s.container.value),s.listValue=ke(t,s.container.styles.listStyleType,!0),t+=r?-1:1}},x0=function(e){var A=new na(e,null),t=new ra(A),r=[];return Tr(A,t,t,r),sa(A.container,r),t},$n=function(e,A){switch(A){case 0:return wA(e.topLeftBorderBox,e.topLeftPaddingBox,e.topRightBorderBox,e.topRightPaddingBox);case 1:return wA(e.topRightBorderBox,e.topRightPaddingBox,e.bottomRightBorderBox,e.bottomRightPaddingBox);case 2:return wA(e.bottomRightBorderBox,e.bottomRightPaddingBox,e.bottomLeftBorderBox,e.bottomLeftPaddingBox);case 3:default:return wA(e.bottomLeftBorderBox,e.bottomLeftPaddingBox,e.topLeftBorderBox,e.topLeftPaddingBox)}},u0=function(e,A){switch(A){case 0:return wA(e.topLeftBorderBox,e.topLeftBorderDoubleOuterBox,e.topRightBorderBox,e.topRightBorderDoubleOuterBox);case 1:return wA(e.topRightBorderBox,e.topRightBorderDoubleOuterBox,e.bottomRightBorderBox,e.bottomRightBorderDoubleOuterBox);case 2:return wA(e.bottomRightBorderBox,e.bottomRightBorderDoubleOuterBox,e.bottomLeftBorderBox,e.bottomLeftBorderDoubleOuterBox);case 3:default:return wA(e.bottomLeftBorderBox,e.bottomLeftBorderDoubleOuterBox,e.topLeftBorderBox,e.topLeftBorderDoubleOuterBox)}},g0=function(e,A){switch(A){case 0:return wA(e.topLeftBorderDoubleInnerBox,e.topLeftPaddingBox,e.topRightBorderDoubleInnerBox,e.topRightPaddingBox);case 1:return wA(e.topRightBorderDoubleInnerBox,e.topRightPaddingBox,e.bottomRightBorderDoubleInnerBox,e.bottomRightPaddingBox);case 2:return wA(e.bottomRightBorderDoubleInnerBox,e.bottomRightPaddingBox,e.bottomLeftBorderDoubleInnerBox,e.bottomLeftPaddingBox);case 3:default:return wA(e.bottomLeftBorderDoubleInnerBox,e.bottomLeftPaddingBox,e.topLeftBorderDoubleInnerBox,e.topLeftPaddingBox)}},h0=function(e,A){switch(A){case 0:return ut(e.topLeftBorderStroke,e.topRightBorderStroke);case 1:return ut(e.topRightBorderStroke,e.bottomRightBorderStroke);case 2:return ut(e.bottomRightBorderStroke,e.bottomLeftBorderStroke);case 3:default:return ut(e.bottomLeftBorderStroke,e.topLeftBorderStroke)}},ut=function(e,A){var t=[];return hA(e)?t.push(e.subdivide(.5,!1)):t.push(e),hA(A)?t.push(A.subdivide(.5,!0)):t.push(A),t},wA=function(e,A,t,r){var n=[];return hA(e)?n.push(e.subdivide(.5,!1)):n.push(e),hA(t)?n.push(t.subdivide(.5,!0)):n.push(t),hA(r)?n.push(r.subdivide(.5,!0).reverse()):n.push(r),hA(A)?n.push(A.subdivide(.5,!1).reverse()):n.push(A),n},aa=function(e){var A=e.bounds,t=e.styles;return A.add(t.borderLeftWidth,t.borderTopWidth,-(t.borderRightWidth+t.borderLeftWidth),-(t.borderTopWidth+t.borderBottomWidth))},bt=function(e){var A=e.styles,t=e.bounds,r=_(A.paddingLeft,t.width),n=_(A.paddingRight,t.width),s=_(A.paddingTop,t.width),a=_(A.paddingBottom,t.width);return t.add(r+A.borderLeftWidth,s+A.borderTopWidth,-(A.borderRightWidth+A.borderLeftWidth+r+n),-(A.borderTopWidth+A.borderBottomWidth+s+a))},d0=function(e,A){return e===0?A.bounds:e===2?bt(A):aa(A)},w0=function(e,A){return e===0?A.bounds:e===2?bt(A):aa(A)},cr=function(e,A,t){var r=d0(we(e.styles.backgroundOrigin,A),e),n=w0(we(e.styles.backgroundClip,A),e),s=f0(we(e.styles.backgroundSize,A),t,r),a=s[0],o=s[1],i=Se(we(e.styles.backgroundPosition,A),r.width-a,r.height-o),l=Q0(we(e.styles.backgroundRepeat,A),i,s,r,n),c=Math.round(r.left+i[0]),x=Math.round(r.top+i[1]);return[l,c,x,a,o]},ge=function(e){return M(e)&&e.value===Qe.AUTO},gt=function(e){return typeof e=="number"},f0=function(e,A,t){var r=A[0],n=A[1],s=A[2],a=e[0],o=e[1];if(!a)return[0,0];if(P(a)&&o&&P(o))return[_(a,t.width),_(o,t.height)];var i=gt(s);if(M(a)&&(a.value===Qe.CONTAIN||a.value===Qe.COVER)){if(gt(s)){var l=t.width/t.height;return l<s!=(a.value===Qe.COVER)?[t.width,t.width/s]:[t.height*s,t.height]}return[t.width,t.height]}var c=gt(r),x=gt(n),u=c||x;if(ge(a)&&(!o||ge(o))){if(c&&x)return[r,n];if(!i&&!u)return[t.width,t.height];if(u&&i){var w=c?r:n*s,g=x?n:r/s;return[w,g]}var h=c?r:t.width,U=x?n:t.height;return[h,U]}if(i){var d=0,f=0;return P(a)?d=_(a,t.width):P(o)&&(f=_(o,t.height)),ge(a)?d=f*s:(!o||ge(o))&&(f=d/s),[d,f]}var v=null,m=null;if(P(a)?v=_(a,t.width):o&&P(o)&&(m=_(o,t.height)),v!==null&&(!o||ge(o))&&(m=c&&x?v/r*n:t.height),m!==null&&ge(a)&&(v=c&&x?m/n*r:t.width),v!==null&&m!==null)return[v,m];throw new Error("Unable to calculate background-size for element")},we=function(e,A){var t=e[A];return typeof t>"u"?e[0]:t},Q0=function(e,A,t,r,n){var s=A[0],a=A[1],o=t[0],i=t[1];switch(e){case 2:return[new C(Math.round(r.left),Math.round(r.top+a)),new C(Math.round(r.left+r.width),Math.round(r.top+a)),new C(Math.round(r.left+r.width),Math.round(i+r.top+a)),new C(Math.round(r.left),Math.round(i+r.top+a))];case 3:return[new C(Math.round(r.left+s),Math.round(r.top)),new C(Math.round(r.left+s+o),Math.round(r.top)),new C(Math.round(r.left+s+o),Math.round(r.height+r.top)),new C(Math.round(r.left+s),Math.round(r.height+r.top))];case 1:return[new C(Math.round(r.left+s),Math.round(r.top+a)),new C(Math.round(r.left+s+o),Math.round(r.top+a)),new C(Math.round(r.left+s+o),Math.round(r.top+a+i)),new C(Math.round(r.left+s),Math.round(r.top+a+i))];default:return[new C(Math.round(n.left),Math.round(n.top)),new C(Math.round(n.left+n.width),Math.round(n.top)),new C(Math.round(n.left+n.width),Math.round(n.height+n.top)),new C(Math.round(n.left),Math.round(n.height+n.top))]}},C0="data:image/gif;base64,R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7",As="Hidden Text",p0=function(){function e(A){this._data={},this._document=A}return e.prototype.parseMetrics=function(A,t){var r=this._document.createElement("div"),n=this._document.createElement("img"),s=this._document.createElement("span"),a=this._document.body;r.style.visibility="hidden",r.style.fontFamily=A,r.style.fontSize=t,r.style.margin="0",r.style.padding="0",r.style.whiteSpace="nowrap",a.appendChild(r),n.src=C0,n.width=1,n.height=1,n.style.margin="0",n.style.padding="0",n.style.verticalAlign="baseline",s.style.fontFamily=A,s.style.fontSize=t,s.style.margin="0",s.style.padding="0",s.appendChild(this._document.createTextNode(As)),r.appendChild(s),r.appendChild(n);var o=n.offsetTop-s.offsetTop+2;r.removeChild(s),r.appendChild(this._document.createTextNode(As)),r.style.lineHeight="normal",n.style.verticalAlign="super";var i=n.offsetTop-r.offsetTop+2;return a.removeChild(r),{baseline:o,middle:i}},e.prototype.getMetrics=function(A,t){var r=A+" "+t;return typeof this._data[r]>"u"&&(this._data[r]=this.parseMetrics(A,t)),this._data[r]},e}(),ia=function(){function e(A,t){this.context=A,this.options=t}return e}(),U0=1e4,F0=function(e){CA(A,e);function A(t,r){var n=e.call(this,t,r)||this;return n._activeEffects=[],n.canvas=r.canvas?r.canvas:document.createElement("canvas"),n.ctx=n.canvas.getContext("2d"),r.canvas||(n.canvas.width=Math.floor(r.width*r.scale),n.canvas.height=Math.floor(r.height*r.scale),n.canvas.style.width=r.width+"px",n.canvas.style.height=r.height+"px"),n.fontMetrics=new p0(document),n.ctx.scale(n.options.scale,n.options.scale),n.ctx.translate(-r.x,-r.y),n.ctx.textBaseline="bottom",n._activeEffects=[],n.context.logger.debug("Canvas renderer initialized ("+r.width+"x"+r.height+") with scale "+r.scale),n}return A.prototype.applyEffects=function(t){for(var r=this;this._activeEffects.length;)this.popEffect();t.forEach(function(n){return r.applyEffect(n)})},A.prototype.applyEffect=function(t){this.ctx.save(),c0(t)&&(this.ctx.globalAlpha=t.opacity),l0(t)&&(this.ctx.translate(t.offsetX,t.offsetY),this.ctx.transform(t.matrix[0],t.matrix[1],t.matrix[2],t.matrix[3],t.matrix[4],t.matrix[5]),this.ctx.translate(-t.offsetX,-t.offsetY)),ta(t)&&(this.path(t.path),this.ctx.clip()),this._activeEffects.push(t)},A.prototype.popEffect=function(){this._activeEffects.pop(),this.ctx.restore()},A.prototype.renderStack=function(t){return AA(this,void 0,void 0,function(){var r;return j(this,function(n){switch(n.label){case 0:return r=t.element.container.styles,r.isVisible()?[4,this.renderStackContent(t)]:[3,2];case 1:n.sent(),n.label=2;case 2:return[2]}})})},A.prototype.renderNode=function(t){return AA(this,void 0,void 0,function(){return j(this,function(r){switch(r.label){case 0:if(Y(t.container.flags,16))debugger;return t.container.styles.isVisible()?[4,this.renderNodeBackgroundAndBorders(t)]:[3,3];case 1:return r.sent(),[4,this.renderNodeContent(t)];case 2:r.sent(),r.label=3;case 3:return[2]}})})},A.prototype.renderTextWithLetterSpacing=function(t,r,n){var s=this;if(r===0)this.ctx.fillText(t.text,t.bounds.left,t.bounds.top+n);else{var a=zr(t.text);a.reduce(function(o,i){return s.ctx.fillText(i,o,t.bounds.top+n),o+s.ctx.measureText(i).width},t.bounds.left)}},A.prototype.createFontStyle=function(t){var r=t.fontVariant.filter(function(a){return a==="normal"||a==="small-caps"}).join(""),n=H0(t.fontFamily).join(", "),s=Ne(t.fontSize)?""+t.fontSize.number+t.fontSize.unit:t.fontSize.number+"px";return[[t.fontStyle,r,t.fontWeight,s,n].join(" "),n,s]},A.prototype.renderTextNode=function(t,r){return AA(this,void 0,void 0,function(){var n,s,a,o,i,l,c,x,u=this;return j(this,function(w){return n=this.createFontStyle(r),s=n[0],a=n[1],o=n[2],this.ctx.font=s,this.ctx.direction=r.direction===1?"rtl":"ltr",this.ctx.textAlign="left",this.ctx.textBaseline="alphabetic",i=this.fontMetrics.getMetrics(a,o),l=i.baseline,c=i.middle,x=r.paintOrder,t.textBounds.forEach(function(g){x.forEach(function(h){switch(h){case 0:u.ctx.fillStyle=W(r.color),u.renderTextWithLetterSpacing(g,r.letterSpacing,l);var U=r.textShadow;U.length&&g.text.trim().length&&(U.slice(0).reverse().forEach(function(d){u.ctx.shadowColor=W(d.color),u.ctx.shadowOffsetX=d.offsetX.number*u.options.scale,u.ctx.shadowOffsetY=d.offsetY.number*u.options.scale,u.ctx.shadowBlur=d.blur.number,u.renderTextWithLetterSpacing(g,r.letterSpacing,l)}),u.ctx.shadowColor="",u.ctx.shadowOffsetX=0,u.ctx.shadowOffsetY=0,u.ctx.shadowBlur=0),r.textDecorationLine.length&&(u.ctx.fillStyle=W(r.textDecorationColor||r.color),r.textDecorationLine.forEach(function(d){switch(d){case 1:u.ctx.fillRect(g.bounds.left,Math.round(g.bounds.top+l),g.bounds.width,1);break;case 2:u.ctx.fillRect(g.bounds.left,Math.round(g.bounds.top),g.bounds.width,1);break;case 3:u.ctx.fillRect(g.bounds.left,Math.ceil(g.bounds.top+c),g.bounds.width,1);break}}));break;case 1:r.webkitTextStrokeWidth&&g.text.trim().length&&(u.ctx.strokeStyle=W(r.webkitTextStrokeColor),u.ctx.lineWidth=r.webkitTextStrokeWidth,u.ctx.lineJoin=window.chrome?"miter":"round",u.ctx.strokeText(g.text,g.bounds.left,g.bounds.top+l)),u.ctx.strokeStyle="",u.ctx.lineWidth=0,u.ctx.lineJoin="miter";break}})}),[2]})})},A.prototype.renderReplacedElement=function(t,r,n){if(n&&t.intrinsicWidth>0&&t.intrinsicHeight>0){var s=bt(t),a=It(r);this.path(a),this.ctx.save(),this.ctx.clip(),this.ctx.drawImage(n,0,0,t.intrinsicWidth,t.intrinsicHeight,s.left,s.top,s.width,s.height),this.ctx.restore()}},A.prototype.renderNodeContent=function(t){return AA(this,void 0,void 0,function(){var r,n,s,a,o,i,f,f,l,c,x,u,m,w,g,F,h,U,d,f,v,m,F;return j(this,function(Q){switch(Q.label){case 0:this.applyEffects(t.getEffects(4)),r=t.container,n=t.curves,s=r.styles,a=0,o=r.textNodes,Q.label=1;case 1:return a<o.length?(i=o[a],[4,this.renderTextNode(i,s)]):[3,4];case 2:Q.sent(),Q.label=3;case 3:return a++,[3,1];case 4:if(!(r instanceof Rs))return[3,8];Q.label=5;case 5:return Q.trys.push([5,7,,8]),[4,this.context.cache.match(r.src)];case 6:return f=Q.sent(),this.renderReplacedElement(r,n,f),[3,8];case 7:return Q.sent(),this.context.logger.error("Error loading image "+r.src),[3,8];case 8:if(r instanceof Gs&&this.renderReplacedElement(r,n,r.canvas),!(r instanceof ks))return[3,12];Q.label=9;case 9:return Q.trys.push([9,11,,12]),[4,this.context.cache.match(r.svg)];case 10:return f=Q.sent(),this.renderReplacedElement(r,n,f),[3,12];case 11:return Q.sent(),this.context.logger.error("Error loading svg "+r.svg.substring(0,255)),[3,12];case 12:return r instanceof Js&&r.tree?(l=new A(this.context,{scale:this.options.scale,backgroundColor:r.backgroundColor,x:0,y:0,width:r.width,height:r.height}),[4,l.render(r.tree)]):[3,14];case 13:c=Q.sent(),r.width&&r.height&&this.ctx.drawImage(c,0,0,r.width,r.height,r.bounds.left,r.bounds.top,r.bounds.width,r.bounds.height),Q.label=14;case 14:if(r instanceof qr&&(x=Math.min(r.bounds.width,r.bounds.height),r.type===mt?r.checked&&(this.ctx.save(),this.path([new C(r.bounds.left+x*.39363,r.bounds.top+x*.79),new C(r.bounds.left+x*.16,r.bounds.top+x*.5549),new C(r.bounds.left+x*.27347,r.bounds.top+x*.44071),new C(r.bounds.left+x*.39694,r.bounds.top+x*.5649),new C(r.bounds.left+x*.72983,r.bounds.top+x*.23),new C(r.bounds.left+x*.84,r.bounds.top+x*.34085),new C(r.bounds.left+x*.39363,r.bounds.top+x*.79)]),this.ctx.fillStyle=W(kn),this.ctx.fill(),this.ctx.restore()):r.type===vt&&r.checked&&(this.ctx.save(),this.ctx.beginPath(),this.ctx.arc(r.bounds.left+x/2,r.bounds.top+x/2,x/4,0,Math.PI*2,!0),this.ctx.fillStyle=W(kn),this.ctx.fill(),this.ctx.restore())),m0(r)&&r.value.length){switch(u=this.createFontStyle(s),m=u[0],w=u[1],g=this.fontMetrics.getMetrics(m,w).baseline,this.ctx.font=m,this.ctx.fillStyle=W(s.color),this.ctx.textBaseline="alphabetic",this.ctx.textAlign=E0(r.styles.textAlign),F=bt(r),h=0,r.styles.textAlign){case 1:h+=F.width/2;break;case 2:h+=F.width;break}U=F.add(h,0,0,-F.height/2+1),this.ctx.save(),this.path([new C(F.left,F.top),new C(F.left+F.width,F.top),new C(F.left+F.width,F.top+F.height),new C(F.left,F.top+F.height)]),this.ctx.clip(),this.renderTextWithLetterSpacing(new Oe(r.value,U),s.letterSpacing,g),this.ctx.restore(),this.ctx.textBaseline="alphabetic",this.ctx.textAlign="left"}if(!Y(r.styles.display,2048))return[3,20];if(r.styles.listStyleImage===null)return[3,19];if(d=r.styles.listStyleImage,d.type!==0)return[3,18];f=void 0,v=d.url,Q.label=15;case 15:return Q.trys.push([15,17,,18]),[4,this.context.cache.match(v)];case 16:return f=Q.sent(),this.ctx.drawImage(f,r.bounds.left-(f.width+10),r.bounds.top),[3,18];case 17:return Q.sent(),this.context.logger.error("Error loading list-style-image "+v),[3,18];case 18:return[3,20];case 19:t.listValue&&r.styles.listStyleType!==-1&&(m=this.createFontStyle(s)[0],this.ctx.font=m,this.ctx.fillStyle=W(s.color),this.ctx.textBaseline="middle",this.ctx.textAlign="right",F=new KA(r.bounds.left,r.bounds.top+_(r.styles.paddingTop,r.bounds.width),r.bounds.width,mn(s.lineHeight,s.fontSize.number)/2+1),this.renderTextWithLetterSpacing(new Oe(t.listValue,F),s.letterSpacing,mn(s.lineHeight,s.fontSize.number)/2+2),this.ctx.textBaseline="bottom",this.ctx.textAlign="left"),Q.label=20;case 20:return[2]}})})},A.prototype.renderStackContent=function(t){return AA(this,void 0,void 0,function(){var r,n,d,s,a,d,o,i,d,l,c,d,x,u,d,w,g,d,h,U,d;return j(this,function(f){switch(f.label){case 0:if(Y(t.element.container.flags,16))debugger;return[4,this.renderNodeBackgroundAndBorders(t.element)];case 1:f.sent(),r=0,n=t.negativeZIndex,f.label=2;case 2:return r<n.length?(d=n[r],[4,this.renderStack(d)]):[3,5];case 3:f.sent(),f.label=4;case 4:return r++,[3,2];case 5:return[4,this.renderNodeContent(t.element)];case 6:f.sent(),s=0,a=t.nonInlineLevel,f.label=7;case 7:return s<a.length?(d=a[s],[4,this.renderNode(d)]):[3,10];case 8:f.sent(),f.label=9;case 9:return s++,[3,7];case 10:o=0,i=t.nonPositionedFloats,f.label=11;case 11:return o<i.length?(d=i[o],[4,this.renderStack(d)]):[3,14];case 12:f.sent(),f.label=13;case 13:return o++,[3,11];case 14:l=0,c=t.nonPositionedInlineLevel,f.label=15;case 15:return l<c.length?(d=c[l],[4,this.renderStack(d)]):[3,18];case 16:f.sent(),f.label=17;case 17:return l++,[3,15];case 18:x=0,u=t.inlineLevel,f.label=19;case 19:return x<u.length?(d=u[x],[4,this.renderNode(d)]):[3,22];case 20:f.sent(),f.label=21;case 21:return x++,[3,19];case 22:w=0,g=t.zeroOrAutoZIndexOrTransformedOrOpacity,f.label=23;case 23:return w<g.length?(d=g[w],[4,this.renderStack(d)]):[3,26];case 24:f.sent(),f.label=25;case 25:return w++,[3,23];case 26:h=0,U=t.positiveZIndex,f.label=27;case 27:return h<U.length?(d=U[h],[4,this.renderStack(d)]):[3,30];case 28:f.sent(),f.label=29;case 29:return h++,[3,27];case 30:return[2]}})})},A.prototype.mask=function(t){this.ctx.beginPath(),this.ctx.moveTo(0,0),this.ctx.lineTo(this.canvas.width,0),this.ctx.lineTo(this.canvas.width,this.canvas.height),this.ctx.lineTo(0,this.canvas.height),this.ctx.lineTo(0,0),this.formatPath(t.slice(0).reverse()),this.ctx.closePath()},A.prototype.path=function(t){this.ctx.beginPath(),this.formatPath(t),this.ctx.closePath()},A.prototype.formatPath=function(t){var r=this;t.forEach(function(n,s){var a=hA(n)?n.start:n;s===0?r.ctx.moveTo(a.x,a.y):r.ctx.lineTo(a.x,a.y),hA(n)&&r.ctx.bezierCurveTo(n.startControl.x,n.startControl.y,n.endControl.x,n.endControl.y,n.end.x,n.end.y)})},A.prototype.renderRepeat=function(t,r,n,s){this.path(t),this.ctx.fillStyle=r,this.ctx.translate(n,s),this.ctx.fill(),this.ctx.translate(-n,-s)},A.prototype.resizeImage=function(t,r,n){var s;if(t.width===r&&t.height===n)return t;var a=(s=this.canvas.ownerDocument)!==null&&s!==void 0?s:document,o=a.createElement("canvas");o.width=Math.max(1,r),o.height=Math.max(1,n);var i=o.getContext("2d");return i.drawImage(t,0,0,t.width,t.height,0,0,r,n),o},A.prototype.renderBackgroundImage=function(t){return AA(this,void 0,void 0,function(){var r,n,s,a,o,i;return j(this,function(l){switch(l.label){case 0:r=t.styles.backgroundImage.length-1,n=function(c){var x,u,w,O,rA,nA,T,Z,S,g,O,rA,nA,T,Z,h,U,d,f,v,m,F,Q,b,S,E,O,tA,cA,T,Z,TA,rA,nA,YA,pA,MA,WA,ZA,yA,zA,HA;return j(this,function(oe){switch(oe.label){case 0:if(c.type!==0)return[3,5];x=void 0,u=c.url,oe.label=1;case 1:return oe.trys.push([1,3,,4]),[4,s.context.cache.match(u)];case 2:return x=oe.sent(),[3,4];case 3:return oe.sent(),s.context.logger.error("Error loading background-image "+u),[3,4];case 4:return x&&(w=cr(t,r,[x.width,x.height,x.width/x.height]),O=w[0],rA=w[1],nA=w[2],T=w[3],Z=w[4],S=s.ctx.createPattern(s.resizeImage(x,T,Z),"repeat"),s.renderRepeat(O,S,rA,nA)),[3,6];case 5:oo(c)?(g=cr(t,r,[null,null,null]),O=g[0],rA=g[1],nA=g[2],T=g[3],Z=g[4],h=ro(c.angle,T,Z),U=h[0],d=h[1],f=h[2],v=h[3],m=h[4],F=document.createElement("canvas"),F.width=T,F.height=Z,Q=F.getContext("2d"),b=Q.createLinearGradient(d,v,f,m),Un(c.stops,U).forEach(function(Ue){return b.addColorStop(Ue.stop,W(Ue.color))}),Q.fillStyle=b,Q.fillRect(0,0,T,Z),T>0&&Z>0&&(S=s.ctx.createPattern(F,"repeat"),s.renderRepeat(O,S,rA,nA))):lo(c)&&(E=cr(t,r,[null,null,null]),O=E[0],tA=E[1],cA=E[2],T=E[3],Z=E[4],TA=c.position.length===0?[Yr]:c.position,rA=_(TA[0],T),nA=_(TA[TA.length-1],Z),YA=no(c,rA,nA,T,Z),pA=YA[0],MA=YA[1],pA>0&&MA>0&&(WA=s.ctx.createRadialGradient(tA+rA,cA+nA,0,tA+rA,cA+nA,pA),Un(c.stops,pA*2).forEach(function(Ue){return WA.addColorStop(Ue.stop,W(Ue.color))}),s.path(O),s.ctx.fillStyle=WA,pA!==MA?(ZA=t.bounds.left+.5*t.bounds.width,yA=t.bounds.top+.5*t.bounds.height,zA=MA/pA,HA=1/zA,s.ctx.save(),s.ctx.translate(ZA,yA),s.ctx.transform(1,0,0,zA,0,0),s.ctx.translate(-ZA,-yA),s.ctx.fillRect(tA,HA*(cA-yA)+yA,T,Z*HA),s.ctx.restore()):s.ctx.fill())),oe.label=6;case 6:return r--,[2]}})},s=this,a=0,o=t.styles.backgroundImage.slice(0).reverse(),l.label=1;case 1:return a<o.length?(i=o[a],[5,n(i)]):[3,4];case 2:l.sent(),l.label=3;case 3:return a++,[3,1];case 4:return[2]}})})},A.prototype.renderSolidBorder=function(t,r,n){return AA(this,void 0,void 0,function(){return j(this,function(s){return this.path($n(n,r)),this.ctx.fillStyle=W(t),this.ctx.fill(),[2]})})},A.prototype.renderDoubleBorder=function(t,r,n,s){return AA(this,void 0,void 0,function(){var a,o;return j(this,function(i){switch(i.label){case 0:return r<3?[4,this.renderSolidBorder(t,n,s)]:[3,2];case 1:return i.sent(),[2];case 2:return a=u0(s,n),this.path(a),this.ctx.fillStyle=W(t),this.ctx.fill(),o=g0(s,n),this.path(o),this.ctx.fill(),[2]}})})},A.prototype.renderNodeBackgroundAndBorders=function(t){return AA(this,void 0,void 0,function(){var r,n,s,a,o,i,l,c,x=this;return j(this,function(u){switch(u.label){case 0:return this.applyEffects(t.getEffects(2)),r=t.container.styles,n=!JA(r.backgroundColor)||r.backgroundImage.length,s=[{style:r.borderTopStyle,color:r.borderTopColor,width:r.borderTopWidth},{style:r.borderRightStyle,color:r.borderRightColor,width:r.borderRightWidth},{style:r.borderBottomStyle,color:r.borderBottomColor,width:r.borderBottomWidth},{style:r.borderLeftStyle,color:r.borderLeftColor,width:r.borderLeftWidth}],a=v0(we(r.backgroundClip,0),t.curves),n||r.boxShadow.length?(this.ctx.save(),this.path(a),this.ctx.clip(),JA(r.backgroundColor)||(this.ctx.fillStyle=W(r.backgroundColor),this.ctx.fill()),[4,this.renderBackgroundImage(t.container)]):[3,2];case 1:u.sent(),this.ctx.restore(),r.boxShadow.slice(0).reverse().forEach(function(w){x.ctx.save();var g=Ht(t.curves),h=w.inset?0:U0,U=B0(g,-h+(w.inset?1:-1)*w.spread.number,(w.inset?1:-1)*w.spread.number,w.spread.number*(w.inset?-2:2),w.spread.number*(w.inset?-2:2));w.inset?(x.path(g),x.ctx.clip(),x.mask(U)):(x.mask(g),x.ctx.clip(),x.path(U)),x.ctx.shadowOffsetX=w.offsetX.number+h,x.ctx.shadowOffsetY=w.offsetY.number,x.ctx.shadowColor=W(w.color),x.ctx.shadowBlur=w.blur.number,x.ctx.fillStyle=w.inset?W(w.color):"rgba(0,0,0,1)",x.ctx.fill(),x.ctx.restore()}),u.label=2;case 2:o=0,i=0,l=s,u.label=3;case 3:return i<l.length?(c=l[i],c.style!==0&&!JA(c.color)&&c.width>0?c.style!==2?[3,5]:[4,this.renderDashedDottedBorder(c.color,c.width,o,t.curves,2)]:[3,11]):[3,13];case 4:return u.sent(),[3,11];case 5:return c.style!==3?[3,7]:[4,this.renderDashedDottedBorder(c.color,c.width,o,t.curves,3)];case 6:return u.sent(),[3,11];case 7:return c.style!==4?[3,9]:[4,this.renderDoubleBorder(c.color,c.width,o,t.curves)];case 8:return u.sent(),[3,11];case 9:return[4,this.renderSolidBorder(c.color,o,t.curves)];case 10:u.sent(),u.label=11;case 11:o++,u.label=12;case 12:return i++,[3,3];case 13:return[2]}})})},A.prototype.renderDashedDottedBorder=function(t,r,n,s,a){return AA(this,void 0,void 0,function(){var o,i,l,c,x,u,w,g,h,U,d,f,v,m,F,Q,F,Q;return j(this,function(b){return this.ctx.save(),o=h0(s,n),i=$n(s,n),a===2&&(this.path(i),this.ctx.clip()),hA(i[0])?(l=i[0].start.x,c=i[0].start.y):(l=i[0].x,c=i[0].y),hA(i[1])?(x=i[1].end.x,u=i[1].end.y):(x=i[1].x,u=i[1].y),n===0||n===2?w=Math.abs(l-x):w=Math.abs(c-u),this.ctx.beginPath(),a===3?this.formatPath(o):this.formatPath(i.slice(0,2)),g=r<3?r*3:r*2,h=r<3?r*2:r,a===3&&(g=r,h=r),U=!0,w<=g*2?U=!1:w<=g*2+h?(d=w/(2*g+h),g*=d,h*=d):(f=Math.floor((w+h)/(g+h)),v=(w-f*g)/(f-1),m=(w-(f+1)*g)/f,h=m<=0||Math.abs(h-v)<Math.abs(h-m)?v:m),U&&(a===3?this.ctx.setLineDash([0,g+h]):this.ctx.setLineDash([g,h])),a===3?(this.ctx.lineCap="round",this.ctx.lineWidth=r):this.ctx.lineWidth=r*2+1.1,this.ctx.strokeStyle=W(t),this.ctx.stroke(),this.ctx.setLineDash([]),a===2&&(hA(i[0])&&(F=i[3],Q=i[0],this.ctx.beginPath(),this.formatPath([new C(F.end.x,F.end.y),new C(Q.start.x,Q.start.y)]),this.ctx.stroke()),hA(i[1])&&(F=i[1],Q=i[2],this.ctx.beginPath(),this.formatPath([new C(F.end.x,F.end.y),new C(Q.start.x,Q.start.y)]),this.ctx.stroke())),this.ctx.restore(),[2]})})},A.prototype.render=function(t){return AA(this,void 0,void 0,function(){var r;return j(this,function(n){switch(n.label){case 0:return this.options.backgroundColor&&(this.ctx.fillStyle=W(this.options.backgroundColor),this.ctx.fillRect(this.options.x,this.options.y,this.options.width,this.options.height)),r=x0(t),[4,this.renderStack(r)];case 1:return n.sent(),this.applyEffects([]),[2,this.canvas]}})})},A}(ia),m0=function(e){return e instanceof Ps||e instanceof Ns?!0:e instanceof qr&&e.type!==vt&&e.type!==mt},v0=function(e,A){switch(e){case 0:return Ht(A);case 2:return a0(A);case 1:default:return It(A)}},E0=function(e){switch(e){case 1:return"center";case 2:return"right";case 0:default:return"left"}},y0=["-apple-system","system-ui"],H0=function(e){return/iPhone OS 15_(0|1)/.test(window.navigator.userAgent)?e.filter(function(A){return y0.indexOf(A)===-1}):e},I0=function(e){CA(A,e);function A(t,r){var n=e.call(this,t,r)||this;return n.canvas=r.canvas?r.canvas:document.createElement("canvas"),n.ctx=n.canvas.getContext("2d"),n.options=r,n.canvas.width=Math.floor(r.width*r.scale),n.canvas.height=Math.floor(r.height*r.scale),n.canvas.style.width=r.width+"px",n.canvas.style.height=r.height+"px",n.ctx.scale(n.options.scale,n.options.scale),n.ctx.translate(-r.x,-r.y),n.context.logger.debug("EXPERIMENTAL ForeignObject renderer initialized ("+r.width+"x"+r.height+" at "+r.x+","+r.y+") with scale "+r.scale),n}return A.prototype.render=function(t){return AA(this,void 0,void 0,function(){var r,n;return j(this,function(s){switch(s.label){case 0:return r=Ir(this.options.width*this.options.scale,this.options.height*this.options.scale,this.options.scale,this.options.scale,t),[4,b0(r)];case 1:return n=s.sent(),this.options.backgroundColor&&(this.ctx.fillStyle=W(this.options.backgroundColor),this.ctx.fillRect(0,0,this.options.width*this.options.scale,this.options.height*this.options.scale)),this.ctx.drawImage(n,-this.options.x*this.options.scale,-this.options.y*this.options.scale),[2,this.canvas]}})})},A}(ia),b0=function(e){return new Promise(function(A,t){var r=new Image;r.onload=function(){A(r)},r.onerror=t,r.src="data:image/svg+xml;charset=utf-8,"+encodeURIComponent(new XMLSerializer().serializeToString(e))})},S0=function(){function e(A){var t=A.id,r=A.enabled;this.id=t,this.enabled=r,this.start=Date.now()}return e.prototype.debug=function(){for(var A=[],t=0;t<arguments.length;t++)A[t]=arguments[t];this.enabled&&(typeof window<"u"&&window.console&&typeof console.debug=="function"?console.debug.apply(console,Xe([this.id,this.getTime()+"ms"],A)):this.info.apply(this,A))},e.prototype.getTime=function(){return Date.now()-this.start},e.prototype.info=function(){for(var A=[],t=0;t<arguments.length;t++)A[t]=arguments[t];this.enabled&&typeof window<"u"&&window.console&&typeof console.info=="function"&&console.info.apply(console,Xe([this.id,this.getTime()+"ms"],A))},e.prototype.warn=function(){for(var A=[],t=0;t<arguments.length;t++)A[t]=arguments[t];this.enabled&&(typeof window<"u"&&window.console&&typeof console.warn=="function"?console.warn.apply(console,Xe([this.id,this.getTime()+"ms"],A)):this.info.apply(this,A))},e.prototype.error=function(){for(var A=[],t=0;t<arguments.length;t++)A[t]=arguments[t];this.enabled&&(typeof window<"u"&&window.console&&typeof console.error=="function"?console.error.apply(console,Xe([this.id,this.getTime()+"ms"],A)):this.info.apply(this,A))},e.instances={},e}(),D0=function(){function e(A,t){var r;this.windowBounds=t,this.instanceName="#"+e.instanceCount++,this.logger=new S0({id:this.instanceName,enabled:A.logging}),this.cache=(r=A.cache)!==null&&r!==void 0?r:new jc(this,A)}return e.instanceCount=1,e}(),L0=function(e,A){return A===void 0&&(A={}),K0(e,A)};typeof window<"u"&&ea.setContext(window);var K0=function(e,A){return AA(void 0,void 0,void 0,function(){var t,r,n,s,a,o,i,l,c,x,u,w,g,h,U,d,f,v,m,F,b,Q,b,S,E,O,tA,cA,T,Z,TA,rA,nA,YA,pA,MA,WA,ZA,yA,zA;return j(this,function(HA){switch(HA.label){case 0:if(!e||typeof e!="object")return[2,Promise.reject("Invalid element provided as first argument")];if(t=e.ownerDocument,!t)throw new Error("Element is not attached to a Document");if(r=t.defaultView,!r)throw new Error("Document is not attached to a Window");return n={allowTaint:(S=A.allowTaint)!==null&&S!==void 0?S:!1,imageTimeout:(E=A.imageTimeout)!==null&&E!==void 0?E:15e3,proxy:A.proxy,useCORS:(O=A.useCORS)!==null&&O!==void 0?O:!1},s=gr({logging:(tA=A.logging)!==null&&tA!==void 0?tA:!0,cache:A.cache},n),a={windowWidth:(cA=A.windowWidth)!==null&&cA!==void 0?cA:r.innerWidth,windowHeight:(T=A.windowHeight)!==null&&T!==void 0?T:r.innerHeight,scrollX:(Z=A.scrollX)!==null&&Z!==void 0?Z:r.pageXOffset,scrollY:(TA=A.scrollY)!==null&&TA!==void 0?TA:r.pageYOffset},o=new KA(a.scrollX,a.scrollY,a.windowWidth,a.windowHeight),i=new D0(s,o),l=(rA=A.foreignObjectRendering)!==null&&rA!==void 0?rA:!1,c={allowTaint:(nA=A.allowTaint)!==null&&nA!==void 0?nA:!1,onclone:A.onclone,ignoreElements:A.ignoreElements,inlineImages:l,copyStyles:l},i.logger.debug("Starting document clone with size "+o.width+"x"+o.height+" scrolled to "+-o.left+","+-o.top),x=new zn(i,e,c),u=x.clonedReferenceElement,u?[4,x.toIFrame(t,o)]:[2,Promise.reject("Unable to find element in cloned iframe")];case 1:return w=HA.sent(),g=jr(u)||Tc(u)?ca(u.ownerDocument):Dt(i,u),h=g.width,U=g.height,d=g.left,f=g.top,v=T0(i,u,A.backgroundColor),m={canvas:A.canvas,backgroundColor:v,scale:(pA=(YA=A.scale)!==null&&YA!==void 0?YA:r.devicePixelRatio)!==null&&pA!==void 0?pA:1,x:((MA=A.x)!==null&&MA!==void 0?MA:0)+d,y:((WA=A.y)!==null&&WA!==void 0?WA:0)+f,width:(ZA=A.width)!==null&&ZA!==void 0?ZA:Math.ceil(h),height:(yA=A.height)!==null&&yA!==void 0?yA:Math.ceil(U)},l?(i.logger.debug("Document cloned, using foreign object rendering"),b=new I0(i,m),[4,b.render(u)]):[3,3];case 2:return F=HA.sent(),[3,5];case 3:return i.logger.debug("Document cloned, element located at "+d+","+f+" with size "+h+"x"+U+" using computed rendering"),i.logger.debug("Starting DOM parsing"),Q=Ys(i,u),v===Q.styles.backgroundColor&&(Q.styles.backgroundColor=LA.TRANSPARENT),i.logger.debug("Starting renderer for element at "+m.x+","+m.y+" with size "+m.width+"x"+m.height),b=new F0(i,m),[4,b.render(Q)];case 4:F=HA.sent(),HA.label=5;case 5:return(!((zA=A.removeContainer)!==null&&zA!==void 0)||zA)&&(zn.destroy(w)||i.logger.error("Cannot detach cloned iframe as it is not in the DOM anymore")),i.logger.debug("Finished rendering"),[2,F]}})})},T0=function(e,A,t){var r=A.ownerDocument,n=r.documentElement?Te(e,getComputedStyle(r.documentElement).backgroundColor):LA.TRANSPARENT,s=r.body?Te(e,getComputedStyle(r.body).backgroundColor):LA.TRANSPARENT,a=typeof t=="string"?Te(e,t):t===null?LA.TRANSPARENT:4294967295;return A===r.documentElement?JA(n)?JA(s)?a:s:n:a};function oA(e,A){const t=Mr();return oA=function(r,n){return r=r-0,t[r]},oA(e,A)}const V=oA,Br="https://topmeanslab.com";function Mr(){const e=["GET","获取API密钥异常，请检查网络连接","AMap","createCustomAutoComplete","key","hideSuggestions","value","addEventListener","className","custom-amap-suggest","style","max","createElement","textContent","div",`
                color: var(--vp-c-text-2);
                font-size: 0.8rem;
                line-height: 1.3;
            `,"cityname","length","none","classList","clearSelection","removeProperty","div:last-child","name","important","0 12px 40px rgba(0, 0, 0, 0.5)","setProperty","color","extractLocationInfo","district","address","/api/amap","initMap","Map","map-container-","saveMapAsImage",".planning-box","visibility","image/png","selectedDayIndex","parentElement","map-controls","width","stringify"," 时出错:","mapInstances","cleanupCustomAutoComplete","Geocoder","geocodes","location","warn","calculateDistance","getBatchAccurateCoordinates"];return Mr=function(){return e},Mr()}class M0{constructor(){this.AMap=null,this.mapInstances=[]}async initialize(){const A=oA;if(typeof window>"u")return;let t="",r="";try{const n=await fetch(Br+"/api/amap_keys",{method:A(0),credentials:"include"});if(!n.ok)throw L.error("获取API密钥失败，请检查网络连接",n),new Error("获取API密钥失败，请检查网络连接");const{AMAP_CODE:s,AMAP_KEY:a}=await n.json();t=s,r=a}catch(n){throw L.error(A(1),n),n}window._AMapSecurityConfig={securityJsCode:t};try{return this[A(2)]=await this.loadAMapScript(r),this.AMap}catch(n){throw new Error("地图初始化失败: "+n.message)}}setupAutoComplete(A,t,r,n){const s=oA;if(!this.AMap)throw new Error("地图服务未初始化");this.createCustomAutoComplete(A,r),this[s(3)](t,n)}[V(3)](A,t){const r=document.getElementById(A);if(!r)return;const n=this.createSuggestContainer(A);this.AMap.plugin(["AMap.PlaceSearch"],()=>{const s=oA,a=new this.AMap.PlaceSearch({pageSize:10,pageIndex:1,citylimit:!1,extensions:"all"});let o=null,i=-1;const l=g=>{const h=g.target.value.trim();if(h.length<2){this.hideSuggestions(n);return}o&&clearTimeout(o),o=setTimeout(()=>{this.searchPlaces(a,h,n,t,r)},300)},c=g=>{const h=oA,U=n.querySelectorAll(".custom-suggest-item");switch(g[h(4)]){case"ArrowDown":g.preventDefault(),i=Math.min(i+1,U.length-1),this.updateSelection(U,i);break;case"ArrowUp":g.preventDefault(),i=Math.max(i-1,-1),this.updateSelection(U,i);break;case"Enter":g.preventDefault(),i>=0&&U[i]&&this.selectSuggestion(U[i],t,r,n);break;case"Escape":this[h(5)](n),r.blur();break}},x=()=>{setTimeout(()=>{!n.matches(":hover")&&this.hideSuggestions(n)},150)},u=()=>{n.style.display!=="none"&&this.positionSuggestContainer(n,r)},w=()=>{n.style.display!=="none"&&this.positionSuggestContainer(n,r)};r.addEventListener("input",l),r.addEventListener("keydown",c),r.addEventListener("blur",x),r.addEventListener("focus",()=>{if(r[oA(6)].trim().length>=2){const h=r.value.trim();this.searchPlaces(a,h,n,t,r)}}),window.addEventListener("resize",u),window[s(7)]("scroll",w,!0),r._cleanup=()=>{r.removeEventListener("input",l),r.removeEventListener("keydown",c),r.removeEventListener("blur",x),window.removeEventListener("resize",u),window.removeEventListener("scroll",w,!0),o&&clearTimeout(o),n.parentNode&&n.parentNode.removeChild(n)}})}createSuggestContainer(A){const t=V;document.getElementById(A);const r=document.createElement("div");return r[t(8)]=t(9),r.id=A+"-suggest",r[t(10)].cssText=`
            position: fixed;
            background: var(--vp-c-bg);
            border: 2px solid var(--vp-c-divider);
            border-radius: 12px;
            box-shadow: 0 12px 40px rgba(0, 0, 0, 0.2);
            z-index: 99999;
            max-height: 300px;
            overflow-y: auto;
            display: none;
            backdrop-filter: blur(15px);
            -webkit-backdrop-filter: blur(15px);
            min-width: 300px;
        `,document.body.appendChild(r),r}positionSuggestContainer(A,t){const r=V,n=t.getBoundingClientRect(),s=window.innerHeight,a=300,o=s-n.bottom,i=n.top;o>=a||o>=i?(A.style.top=n.bottom+4+"px",A.style.maxHeight=Math.min(a,o-20)+"px"):(A.style.top=n.top-Math.min(a,i-20)+"px",A.style.maxHeight=Math.min(a,i-20)+"px"),A.style.left=n.left+"px",A.style.width=Math[r(11)](n.width,300)+"px"}searchPlaces(A,t,r,n,s){A.search(t,(a,o)=>{a==="complete"&&o.poiList&&o.poiList.pois?this.showSuggestions(r,o.poiList.pois,n,s):this.hideSuggestions(r)})}showSuggestions(A,t,r,n){A.innerHTML="",this.positionSuggestContainer(A,n),t.slice(0,8).forEach((s,a)=>{const o=oA,i=document[o(12)]("div");i.className="custom-suggest-item",i.style.cssText=`
                padding: 12px 16px;
                cursor: pointer;
                border-bottom: 1px solid var(--vp-c-divider-light);
                transition: all 0.2s ease;
                display: flex;
                flex-direction: column;
                gap: 4px;
            `;const l=document.createElement("div");l.style.cssText=`
                font-weight: 500;
                color: var(--vp-c-text-1);
                font-size: 0.875rem;
            `,l[o(13)]=s.name;const c=document.createElement(o(14));c.style.cssText=o(15),c.textContent=s.address||s.pname+s[o(16)]+s.adname,i.appendChild(l),i.appendChild(c),i.addEventListener("mouseenter",()=>{this.clearSelection(A)}),i.addEventListener("click",x=>{x.preventDefault(),x.stopPropagation(),this.selectSuggestion(i,r,n,A,s)}),(a===t[o(17)]-1||a===7)&&(i.style.borderBottom=o(18)),A.appendChild(i)}),A.style.display="block",this.updateThemeStyles(A)}hideSuggestions(A){const t=V;A.style.display=t(18),A.innerHTML=""}updateSelection(A,t){const r=V;this.clearSelection(A[0].parentNode),t>=0&&A[t]&&(A[t][r(19)].add("selected"),A[t].scrollIntoView({block:"nearest",behavior:"smooth"}))}[V(20)](A){A.querySelectorAll(".custom-suggest-item").forEach(r=>{const n=oA;r.style.removeProperty("background"),r.style[n(21)]("color"),r.classList.remove("selected")})}selectSuggestion(A,t,r,n,s=null){const a=V;if(!s){const i=A.querySelector("div:first-child").textContent,l=A.querySelector(a(22))[a(13)];s={name:i,address:l}}r.value=s[a(23)];const o=this.extractLocationInfo(s);t(s.name,o),this.hideSuggestions(n),r.blur()}updateThemeStyles(A){const t=V;document.documentElement.classList.contains("dark")?(A.style.setProperty("background","var(--vp-c-bg-soft)","important"),A[t(10)].setProperty("border-color","var(--vp-c-divider)",t(24)),A.style.setProperty("box-shadow",t(25),"important"),A.querySelectorAll(".custom-suggest-item").forEach(s=>{const a=t,o=s.querySelector("div:first-child"),i=s.querySelector(a(22));o&&o.style.setProperty("color","var(--vp-c-text-1)","important"),i&&i.style[a(26)](a(27),"var(--vp-c-text-2)","important")})):(A.style.setProperty("background","var(--vp-c-bg)","important"),A.style.setProperty("border-color","var(--vp-c-divider)","important"),A.style.setProperty("box-shadow","0 12px 40px rgba(0, 0, 0, 0.2)","important"),A.querySelectorAll(".custom-suggest-item").forEach(s=>{const a=t,o=s.querySelector("div:first-child"),i=s.querySelector("div:last-child");o&&o.style.setProperty("color","var(--vp-c-text-1)","important"),i&&i.style.setProperty("color","var(--vp-c-text-2)",a(24))}))}[V(28)](A){var s,a;const t=V;if(!A)return null;const r=A[t(29)]||A[t(30)]||"",n=this.parseAddress(r);return{lng:((s=A.location)==null?void 0:s.lng)||null,lat:((a=A.location)==null?void 0:a.lat)||null,province:n.province||null,city:n.city||null,district:n.district||null,address:r,adcode:A.adcode||null}}parseAddress(A){if(!A)return{province:null,city:null,district:null};const t=/(.*?)(省|自治区|市|特别行政区)/,r=/(.*?)(市|地区|州|盟)/,n=/(.*?)(区|县|市|旗)/;let s=null,a=null,o=null;const i=A.match(t);i&&(s=i[0],A=A.replace(s,""));const l=A.match(r);l&&(a=l[0],A=A.replace(a,""));const c=A.match(n);return c&&(o=c[0]),{province:s,city:a,district:o}}loadAMapScript(A){return new Promise(async(t,r)=>{const n=oA;if(window.AMap)return t(window[n(2)]);try{const s=await fetch(Br+n(31),{method:"GET",credentials:"include"});if(!s.ok)throw new Error("地图脚本加载失败，请检查网络连接");const{scriptUrl:a}=await s.json(),o=document.createElement("script");o.src=a,o.onload=()=>t(window.AMap),o.onerror=i=>r(new Error("地图脚本加载失败，请检查网络连接",{cause:i})),document.head.appendChild(o)}catch(s){r(new Error("地图脚本加载失败，请检查网络连接",{cause:s}))}})}async getGeocodePosition(A,t){return new Promise((r,n)=>{A.getLocation(t,(s,a)=>{s==="complete"&&a.geocodes.length?r(a.geocodes[0].location):n(new Error("无法解析地址: "+t))})})}async[V(32)](A,t,r,n,s){const a=V;if(!this.AMap)throw new Error("地图服务未初始化");const{s_lng:o,s_lat:i}=t,{e_lng:l,e_lat:c}=r;try{const x=new this.AMap[a(33)](a(34)+A,{renderer:"canvas",resizeEnable:!0,viewMode:"2D",crossOrigin:"anonymous",WebGLParams:{preserveDrawingBuffer:!0}});for(;this.mapInstances.length<=A;)this.mapInstances.push(null);this.mapInstances[A]=x;const u=new this.AMap.Driving({map:x,panel:"",renderer:"canvas",policy:n});return await new Promise((w,g)=>{const h=setTimeout(()=>{g(new Error("路线规划超时: "+A))},15e3);u.search(new AMap.LngLat(o,i),new AMap.LngLat(l,c),{waypoints:s},function(U,d){clearTimeout(h),U!=="complete"?(L.error("获取驾车数据失败："+d),g(new Error("路线规划失败: "+d))):setTimeout(()=>{w()},1e3)})}),x}catch(x){throw L.error("地图初始化失败:",x),new Error("地图初始化失败: "+x.message)}}async drivingPlanning(A,t,r,n=0,s=[]){try{if(!document.getElementById("map-container-"+A))throw L.error("地图容器未找到"),new Error("地图容器未找到");this.destroyMapInstance(A),await this.initMap(A,t,r,n,s)}catch(a){throw L.error("路线生成错误",a),new Error(a.message||"路线生成失败")}}async[V(35)](A,t,r,n=null){const s=V;let a=null,o=null,i=!1;try{const l=document.getElementById("map-container-"+A);if(!l)throw new Error("地图容器未找到");const c=this.mapInstances[A];if(!c)throw new Error("地图实例 "+A+" 未找到");const x=l.closest(s(36));if(x&&n){const d=window.getComputedStyle(x);(d.display==="none"||d[s(37)]==="hidden")&&(a=n.expandedSectionType,o=n.selectedDayIndex,i=!0,n.expandedSectionType="driving",n.selectedDayIndex=A,await n.$nextTick(),await new Promise(v=>setTimeout(v,1e3)))}const u=l.getBoundingClientRect();if(u.width===0||u.height===0){const d=[];let f=l;for(;f&&f!==document.body;){const v=window.getComputedStyle(f);v.display==="none"&&(d.push({element:f,originalDisplay:f.style.display}),f.style.display="block"),v.visibility==="hidden"&&(d.push({element:f,originalVisibility:f.style.visibility}),f.style.visibility="visible"),f=f.parentElement}await new Promise(v=>setTimeout(v,500)),typeof c.resize=="function"&&(c.resize(),await new Promise(v=>setTimeout(v,1e3)));try{const v=await this.captureMapCanvas(l,A),m=v.toDataURL("image/png");if(!m||m.length<1e3)throw new Error("地图图片数据无效或过小");return await this.uploadMapImage(m,t,r,A)}finally{d.forEach(({element:v,originalDisplay:m,originalVisibility:F})=>{m!==void 0&&(v.style.display=m),F!==void 0&&(v.style.visibility=F)})}}await new Promise(d=>setTimeout(d,2e3)),!l.querySelector(".amap-container")&&await new Promise(d=>setTimeout(d,3e3)),typeof c.resize=="function"&&(c.resize(),await new Promise(d=>setTimeout(d,500)));const g=await this.captureMapCanvas(l,A),h=g.toDataURL(s(38));if(!h||h.length<1e3)throw new Error("地图图片数据无效或过小");return await this.uploadMapImage(h,t,r,A)}catch(l){throw L.error("保存地图为图片失败:",l),l}finally{i&&n&&setTimeout(()=>{const l=s;n.expandedSectionType=a,n[l(39)]=o},500)}}async captureMapCanvas(A,t){const r=V,n=await L0(A,{useCORS:!0,allowTaint:!0,logging:!1,scale:2,backgroundColor:"#f5f5f5",onclone:s=>{const a=oA,o=s.getElementById("map-container-"+t);o&&(o.style.display="block",o.style.visibility="visible",o.style.opacity="1",o.style.position="relative",o.style.zIndex="1");let i=o==null?void 0:o.parentElement;for(;i&&i!==s.body;)i.style.display="block",i[a(10)].visibility="visible",i[a(10)].opacity="1",i=i[a(40)]},ignoreElements:s=>{const a=oA;return s[a(19)].contains(a(41))||s.classList.contains("amap-copyright")||s.classList.contains("header-toggle")||s.id==="some-obstructive-element"}});if(!n||n.width===0||n.height===0)throw new Error("地图截图失败: 画布尺寸为 "+((n==null?void 0:n[r(42)])||0)+"x"+((n==null?void 0:n.height)||0));return n}async uploadMapImage(A,t,r,n){const s=V,a=await fetch(Br+"/api/save_amap_img",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON[s(43)]({image:A,user:t,filename:"map-"+r+"-"+n+".png"})});if(!a.ok)throw L.error("图片保存失败，请检查网络连接",a),new Error("图片保存失败");return await a.json()}destroyMapInstance(A){const t=V;if(this.mapInstances[A]){try{this.mapInstances[A].destroy()}catch(r){L.warn("销毁地图实例 "+A+t(44),r)}this.mapInstances[A]=null}}cleanup(){const A=V;this[A(45)].forEach((t,r)=>{if(t&&t.destroy)try{t.destroy()}catch(n){L.warn("清理地图实例 "+r+" 时出错:",n)}}),this.mapInstances=[],this[A(46)]()}cleanupCustomAutoComplete(){["start-tipinput","end-tipinput"].forEach(t=>{const r=document.getElementById(t);r&&r._cleanup&&(r._cleanup(),delete r._cleanup);const n=document.getElementById(t+"-suggest");n&&n.parentNode&&n.parentNode.removeChild(n)})}getMapInstance(A){return this.mapInstances[A]}getAllMapInstances(){return this.mapInstances}async getAccurateCoordinates(A,t,r){try{return this.AMap?new Promise((n,s)=>{const a=setTimeout(()=>{console.warn("地理编码超时: "+A+"，返回空对象"),n({})},1e4);try{this.AMap.plugin("AMap.Geocoder",()=>{const o=oA;try{new this.AMap[o(47)]({city:r,radius:500,extensions:"all"}).getLocation(A,(l,c)=>{const x=o;try{if(l==="complete"&&c.geocodes&&c.geocodes.length>0)clearTimeout(a),n(c[x(48)][0][x(49)]);else{const u="高德地理编码失败 - 地址: "+A+", 状态: "+l;console[x(50)](u,c),clearTimeout(a),n({})}}catch(u){console.warn("地理编码回调处理错误:",u),clearTimeout(a),n({})}})}catch(i){console[o(50)]("地理编码插件加载错误:",i),clearTimeout(a),n({})}})}catch(o){console.warn("地理编码Promise创建错误:",o),clearTimeout(a),n({})}}):(console.warn("地图服务未初始化，返回空对象"),{})}catch(n){return console.warn("地理编码外层异常:",n),{}}}[V(51)](A,t,r,n){const a=(n-t)*Math.PI/180,o=(r-A)*Math.PI/180,i=Math.sin(a/2)*Math.sin(a/2)+Math.cos(t*Math.PI/180)*Math.cos(n*Math.PI/180)*Math.sin(o/2)*Math.sin(o/2);return 6371*(2*Math.atan2(Math.sqrt(i),Math.sqrt(1-i)))}async[V(52)](A,t=null){var o;const r=V;if(!this[r(2)])throw new Error("地图服务未初始化");if(!Array.isArray(A)||A.length===0)return[];const n=[],s=A.map(i=>this.getAccurateCoordinates(i,t).catch(l=>({error:l.message,address:i}))),a=await Promise.allSettled(s);for(let i=0;i<a.length;i++){const l=a[i];l.status==="fulfilled"?l[r(6)].error?n.push({success:!1,address:A[i],error:l.value.error,lng:null,lat:null}):n.push({success:!0,address:A[i],...l.value}):n.push({success:!1,address:A[i],error:((o=l.reason)==null?void 0:o.message)||"未知错误",lng:null,lat:null})}return n.filter(i=>i.success).length,n}}const iA=new M0,$A=St;class O0{constructor(){this.autoScrollEnabled=!0,this.userScrollTimeout=null,this.lastScrollTop=0,this.isUserScrolling=!1,this.scrollObserver=null,this.handleUserScroll=null,this.lastUserScrollTime=0,this.scrollVelocity=0,this.consecutiveScrollCount=0}[$A(0)](){typeof window>"u"||(this.handleUserScroll=this.throttle(()=>{const A=Date.now(),t=window.pageYOffset||document.documentElement.scrollTop,r=Math.abs(t-this.lastScrollTop),n=A-this.lastUserScrollTime;if(this.scrollVelocity=n>0?r/n:0,r>5){this.consecutiveScrollCount++;const s=r>30,a=t<this.lastScrollTop,o=this.scrollVelocity>.5,i=this.consecutiveScrollCount>=2;a||s||o||i&&r>15?(!this.isUserScrolling&&(this.isUserScrolling=!0,this.autoScrollEnabled=!1),this.userScrollTimeout&&clearTimeout(this.userScrollTimeout),this.userScrollTimeout=setTimeout(()=>{this.isUserScrolling=!1,this.consecutiveScrollCount=0,!this.checkAllContentCompleted()&&(this.autoScrollEnabled=!0,setTimeout(()=>{this.smartScrollToContent()},200))},5e3)):this.isUserScrolling&&r>8&&(this.userScrollTimeout&&clearTimeout(this.userScrollTimeout),this.userScrollTimeout=setTimeout(()=>{const c=St;this.isUserScrolling=!1,this.consecutiveScrollCount=0,!this[c(1)]()&&(this.autoScrollEnabled=!0,setTimeout(()=>{this.smartScrollToContent()},200))},5e3)),this.lastScrollTop=t,this.lastUserScrollTime=A}else this.consecutiveScrollCount=Math.max(0,this.consecutiveScrollCount-1)},80),window.addEventListener("scroll",this.handleUserScroll,{passive:!0}))}cleanupScrollListener(){typeof window>"u"||(this.handleUserScroll&&window.removeEventListener("scroll",this.handleUserScroll),this.userScrollTimeout&&clearTimeout(this.userScrollTimeout))}throttle(A,t){let r;return function(){const n=arguments,s=this;!r&&(A.apply(s,n),r=!0,setTimeout(()=>r=!1,t))}}setContentCompletedChecker(A){this.checkAllContentCompleted=A}setDisplayStateChecker(A){this.getDisplayState=A}checkAllContentCompleted(){return!1}getDisplayState(){return{selectedDayIndex:-1,isAnyLoading:!1}}smartScrollToContent(){const A=$A;if(!(!this.autoScrollEnabled||typeof window>"u")){if(this.checkAllContentCompleted()){this.autoScrollEnabled=!1;return}try{const t=document.querySelectorAll(A(2));if(t.length===0)return;let r=null,n=!1;n=this.getDisplayState().isAnyLoading;const a=document.querySelector(".global-loading-banner");if(!n&&a&&window.getComputedStyle(a).display!=="none"&&(n=!0),n)for(let o=t.length-1;o>=0;o--){const i=t[o];if(i.offsetParent!==null&&window.getComputedStyle(i).display!=="none"){r=i;break}}if(!n){if(this.checkAllContentCompleted()){this.autoScrollEnabled=!1;return}for(let o=t.length-1;o>=0;o--){const i=t[o];if(i.offsetParent!==null&&window.getComputedStyle(i).display!==A(3)){r=i;break}}}if(r){const o=r[A(4)](),i=window.pageYOffset||document.documentElement[A(5)],l=window.innerHeight,c=a&&n?a.offsetHeight:0,x=i+o.bottom-l*.7+c;window.scrollTo({top:Math.max(0,x),behavior:"smooth"})}else window.scrollTo({top:document.body.scrollHeight,behavior:"smooth"})}catch{window.scrollTo({top:document.body.scrollHeight,behavior:"smooth"})}}}scrollPageToBottom(){this[$A(6)]()}resetScrollState(){this.autoScrollEnabled=!0,this.isUserScrolling=!1,typeof window<"u"?this.lastScrollTop=window.pageYOffset||document.documentElement.scrollTop||0:this.lastScrollTop=0,this.userScrollTimeout&&(clearTimeout(this.userScrollTimeout),this.userScrollTimeout=null),this.lastUserScrollTime=Date.now(),this.scrollVelocity=0,this.consecutiveScrollCount=0}[$A(7)](){return{autoScrollEnabled:this.autoScrollEnabled,isUserScrolling:this.isUserScrolling,lastScrollTop:this.lastScrollTop}}[$A(8)](A){this.autoScrollEnabled=A}isUserScrollingActive(){return this.isUserScrolling}forceScrollToBottom(){const A=$A;typeof window>"u"||window.scrollTo({top:document.body[A(9)],behavior:"smooth"})}scrollToElement(A,t="smooth"){!A||typeof window>"u"||A.scrollIntoView({behavior:t,block:"center"})}cleanup(){const A=$A;this.cleanupScrollListener(),this.autoScrollEnabled=!0,this.isUserScrolling=!1,this[A(10)]=0,this.userScrollTimeout=null,this.scrollObserver=null,this.handleUserScroll=null,this[A(11)]=0,this.scrollVelocity=0,this.consecutiveScrollCount=0}}function St(e,A){const t=Or();return St=function(r,n){return r=r-0,t[r]},St(e,A)}const QA=new O0;function Or(){const e=["initScrollListener","checkAllContentCompleted",".answer-area-container","none","getBoundingClientRect","scrollTop","smartScrollToContent","getScrollState","setAutoScrollEnabled","scrollHeight","lastScrollTop","lastUserScrollTime"];return Or=function(){return e},Or()}const _0=gA;function _r(){const e=["formDataKey","undefined","表单验证通过","startDate","travel_mode","plan_mode","province","stringify","__localStorage_test__","cleanupExpiredData"];return _r=function(){return e},_r()}function gA(e,A){const t=_r();return gA=function(r,n){return r=r-0,t[r]},gA(e,A)}class R0{constructor(){const A=gA;this[A(0)]="topmeans_form_data"}saveFormData(A){const t=gA;if(!(typeof window>"u"))try{const r={...A,timestamp:Date.now()};localStorage.setItem(this[t(0)],JSON.stringify(r))}catch{}}loadFormData(){if(typeof window===gA(1))return null;try{const t=localStorage.getItem(this.formDataKey);if(!t)return null;const r=JSON.parse(t),n=7*24*60*60*1e3;return r.timestamp&&Date.now()-r.timestamp>n?(this.clearFormData(),null):r}catch{return this.clearFormData(),null}}clearFormData(){if(!(typeof window>"u"))try{localStorage.removeItem(this.formDataKey)}catch{}}getDefaultFormData(){return{s_address:null,e_address:null,startDate:null,dates:3,plan_mode:"往返",travel_mode:"自驾",s_location:{lng:null,lat:null,province:null,city:null,district:null,address:null,adcode:null},e_location:{lng:null,lat:null,province:null,city:null,district:null,address:null,adcode:null}}}resetFormData(){return this.clearFormData(),this.getDefaultFormData()}validateFormData(A){const t=gA,{s_address:r,e_address:n,startDate:s,dates:a}=A,o=[r,n].every(c=>typeof c=="string"&&c.trim().length>=2),i=s!==null,l=a>0&&a<=5;return a>5?{isValid:!1,message:"处于规划耗时，请勿一次性规划超过5天的行程，可以分多次规划"}:o?i?l?{isValid:!0,message:t(2)}:{isValid:!1,message:"游玩天数必须在1-5天之间"}:{isValid:!1,message:"请选择开始日期"}:{isValid:!1,message:"请输入有效的起点和终点地址（至少2个字符）"}}mergeFormData(A,t){const r=gA;return t?{s_address:t.s_address!==void 0?t.s_address:A.s_address,e_address:t.e_address!==void 0?t.e_address:A.e_address,startDate:t.startDate!==void 0?t[r(3)]:A.startDate,dates:t.dates!==void 0?t.dates:A.dates,plan_mode:t.plan_mode!==void 0?t.plan_mode:A.plan_mode,travel_mode:t.travel_mode!==void 0?t.travel_mode:A[r(4)],s_location:t.s_location!==void 0?t.s_location:A.s_location,e_location:t.e_location!==void 0?t.e_location:A.e_location}:A}hasFormDataChanged(A,t){return!!(["s_address","e_address","startDate","dates",gA(5),"travel_mode"].some(s=>A[s]!==t[s])||this.hasLocationChanged(A.s_location,t.s_location)||this.hasLocationChanged(A.e_location,t.e_location))}hasLocationChanged(A,t){return!A&&!t?!1:!A||!t?!0:["lng","lat","province","city","district","address","adcode"].some(n=>A[n]!==t[n])}getFormDataSummary(A){const{s_address:t,e_address:r,startDate:n,dates:s,plan_mode:a,travel_mode:o,s_location:i,e_location:l}=A;return{route:(t||"未设置")+" → "+(r||"未设置"),duration:s+"天",mode:a+" - "+o,startDate:n||"未设置",startLocationInfo:this.getLocationSummary(i),endLocationInfo:this.getLocationSummary(l),hasCoordinates:this.hasCompleteCoordinates(i,l),isComplete:!!(t&&r&&n&&s>0)}}getLocationSummary(A){const t=gA;if(!A)return"位置信息未设置";const r=[];A.province&&r.push(A[t(6)]),A.city&&r.push(A.city),A.district&&r.push(A.district);const n=r.length>0?r.join(" "):A.address||"地址信息不完整",s=A.lng&&A.lat?"("+A.lng+", "+A.lat+")":"(坐标未获取)";return n+" "+s}hasCompleteCoordinates(A,t){return!!(A!=null&&A.lng&&(A!=null&&A.lat)&&(t!=null&&t.lng)&&(t!=null&&t.lat))}exportFormData(A){const t=gA;try{return JSON[t(7)](A,null,2)}catch{return null}}importFormData(A){try{const t=JSON.parse(A),r=this.validateFormData(t);if(r.isValid)return t;throw new Error(r.message)}catch(t){throw t}}setStorageKey(A){this.formDataKey=A}getStorageKey(){return this.formDataKey}isLocalStorageAvailable(){const A=gA;if(typeof window>"u")return!1;try{const t=A(8);return localStorage.setItem(t,t),localStorage.removeItem(t),!0}catch{return!1}}getStorageSize(){if(!this.isLocalStorageAvailable())return 0;try{const A=localStorage.getItem(this.formDataKey);return A?new Blob([A]).size:0}catch{return 0}}[_0(9)](A=7*24*60*60*1e3){const t=this.loadFormData();return t&&t.timestamp&&Date.now()-t.timestamp>A?(this.clearFormData(),!0):!1}}const SA=new R0,es=mA;function mA(e,A){const t=Rr();return mA=function(r,n){return r=r-0,t[r]},mA(e,A)}const BA="https://topmeanslab.com";function Rr(){const e=["abortController","/api/hotel","景点信息获取失败","json","保存计划失败，请检查网络连接","body","/api/amap","地图脚本加载失败，请检查网络连接","post","POST"];return Rr=function(){return e},Rr()}class G0{constructor(){const A=mA;this[A(0)]=null}async getHotelUrl(A,t,r,n,s){const a=mA;try{const o=await fetch(BA+a(1),{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({name:""+s+A.replace("*",""),user:t,create_time:r,day:""+n})});if(!o.ok)throw new Error("酒店信息获取失败");const{success:i,url:l}=await o.json();return l}catch(o){throw o}}async getFoodImgUrl(A,t){try{const r=await fetch(BA+"/api/ai_img",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({prompt:"一种食物或一个著名饭店，请根据后续描述来进行写实风格的图片生成，名字："+A+",相关信息："+t})});if(!r.ok)throw new Error("美食图片获取失败");const{success:n,url:s}=await r.json();return s}catch(r){throw r}}async getAIImg(A,t){try{const r=await fetch(BA+"/api/ai_img2",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({prompt:A+"："+t})});if(!r.ok)throw new Error("美食图片获取失败");const{success:n,url:s}=await r.json();return s}catch(r){throw r}}async getViewUrl(A){const t=mA;try{const r=await fetch(BA+"/api/view",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({name:A})});if(!r.ok)throw new Error(t(2));const{success:n,url:s}=await r[t(3)]();return s}catch(r){throw r}}async savePlanToDB(A){const t=mA,{content:r,account:n,filename:s}=A;try{let a=await fetch(BA+"/api/save_plan",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({content:r,user:n,filename:s})});if(!a.ok)throw new Error(t(4));return await a[t(3)]()}catch(a){throw a}}async addPlanToUser(A){const{account:t,create_time:r,days:n}=A;try{const s=await fetch(BA+"/api/user/add_plan",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({account:t,create_time:r,days:n})});if(!s.ok)throw new Error("计划存库失败，请检查网络连接");return await s.json()}catch(s){throw s}}async sendMessage(A,t,r,n){const s=mA;this.abortController&&this.abortController.abort(),this.abortController=new AbortController;try{const a=await fetch(BA+"/api/ds",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({msg:A}),signal:this[s(0)].signal});if(!a.ok)throw new Error("DS API 请求失败!");const o=a[s(5)].getReader(),i=new TextDecoder("utf-8");let l="";for(;;){const{done:c,value:x}=await o.read();if(c)break;const u=i.decode(x),w=u.split(`
`).filter(g=>g.trim());for(const g of w)try{if(!g.startsWith("data: "))continue;const h=g.slice(6);if(h==="[DONE]")break;const U=JSON.parse(h),d=U.choices[0].delta.content;d&&(l+=d,t&&t(l,d))}catch{}}return r&&r(l),l}catch(a){if(a.name==="AbortError")return null;throw n&&n(a),a}}cancelCurrentRequest(){this.abortController&&(this.abortController.abort(),this.abortController=null)}async getApiKeys(){try{const A=await fetch(BA+"/api/amap_keys",{method:"GET",credentials:"include"});if(!A.ok)throw new Error("获取API密钥失败，请检查网络连接");return await A.json()}catch(A){throw A}}async getMapScriptUrl(){const A=mA;try{const t=await fetch(BA+A(6),{method:"GET",credentials:"include"});if(!t.ok)throw new Error(A(7));return await t.json()}catch{throw new Error("地图脚本加载失败，请检查网络连接")}}async saveMapImage(A){const t=mA,{image:r,user:n,filename:s}=A;try{const a=await fetch(BA+"/api/save_amap_img",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({image:r,user:n,filename:s})});if(!a.ok)throw new Error("图片保存失败");return await a[t(3)]()}catch(a){throw a}}async get(A,t={}){try{const r=await fetch(""+BA+A,{method:"GET",credentials:"include",...t});if(!r.ok)throw new Error("GET请求失败: "+r.status);return await r.json()}catch(r){throw r}}async[es(8)](A,t,r={}){const n=es;try{const s=await fetch(""+BA+A,{method:n(9),headers:{"Content-Type":"application/json",...r.headers},body:JSON.stringify(t),credentials:"include",...r});if(!s.ok)throw new Error("POST请求失败: "+s.status);return await s.json()}catch(s){throw s}}async checkConnection(){try{return(await fetch(BA+"/api/health",{method:"GET",timeout:5e3})).ok}catch{return!1}}cleanup(){this.cancelCurrentRequest()}}const ee=new G0,he=ne;class k0{constructor(){this.amapObserver=null,this.amapStyleInterval=null}ensureAmapSuggestStyles(){typeof window>"u"||(this.injectAmapStyles(),this.startAmapStyleMonitor())}injectAmapStyles(){const A=ne,t=document.getElementById("amap-dark-theme-fix");t&&t.remove();const r=document.createElement("style");r.id=A(0),r.innerHTML=A(1),document.head.appendChild(r)}[he(2)](){const A=he;this.amapStyleInterval&&clearInterval(this.amapStyleInterval),this[A(3)]=setInterval(()=>{this.forceApplyAmapStyles()},500);const t=new MutationObserver(r=>{let n=!1;r.forEach(s=>{s.addedNodes.forEach(a=>{const o=ne;if(a.nodeType===Node.ELEMENT_NODE){const i=a.className||"";(i[o(4)]("amap")||i.includes("sug")||i.includes("auto"))&&(n=!0)}})}),n&&setTimeout(()=>this.forceApplyAmapStyles(),100)});t.observe(document.body,{childList:!0,subtree:!0}),this.amapObserver=t}forceApplyAmapStyles(){const A=he;if(!(typeof window>"u"))try{const t=document.documentElement.classList.contains("dark"),r=t?"#ffffff":"var(--vp-c-text-1)",n=t?A(5):"var(--vp-c-bg)";[".amap-sug-result",".amap-ui-autocomplete",'div[class*="amap"][class*="sug"]','div[class*="amap"][class*="auto"]','[class*="amap-sug"]','[class*="amap-auto"]'].forEach(a=>{document.querySelectorAll(a).forEach(i=>{i&&i.style&&(i.style.setProperty("background",n,"important"),i.style.setProperty("border","1px solid var(--vp-c-divider)","important"),i.style.setProperty("border-radius","8px","important"),i.style.setProperty("z-index","99999","important")),i.querySelectorAll('.auto-item, li, .amap-ui-autocomplete-item, [class*="item"]').forEach(c=>{const x=ne;c&&c.style&&(c.style.setProperty("background",n,"important"),c.style.setProperty("color",r,"important"),c.style.setProperty("padding","12px 16px",x(6)),c.querySelectorAll("*").forEach(w=>{w&&w.style&&w.style.setProperty("color",r,"important")}))})})})}catch{}}isDarkMode(){return typeof window>"u"?!1:document.documentElement.classList.contains("dark")}getThemeColors(){const A=he;if(typeof window>"u")return{};const t=getComputedStyle(document.documentElement);return{bg:t.getPropertyValue("--vp-c-bg").trim(),bgSoft:t.getPropertyValue("--vp-c-bg-soft").trim(),text1:t.getPropertyValue("--vp-c-text-1").trim(),divider:t.getPropertyValue("--vp-c-divider")[A(7)](),dividerLight:t.getPropertyValue("--vp-c-divider-light").trim(),brand:t.getPropertyValue("--vp-c-brand").trim(),brandSoft:t[A(8)]("--vp-c-brand-soft").trim(),brandDimm:t.getPropertyValue("--vp-c-brand-dimm").trim()}}watchThemeChange(A){if(typeof window===he(9))return;const r=new MutationObserver(n=>{n.forEach(s=>{const a=ne;if(s.type==="attributes"&&s[a(10)]==="class"){const o=document.documentElement.classList[a(11)]("dark");A(o)}})});return r.observe(document.documentElement,{attributes:!0,attributeFilter:["class"]}),r}applyCustomStyles(A,t){const r=he,n=document.getElementById(A);n&&n.remove();const s=document.createElement("style");s.id=A,s.innerHTML=t,document[r(12)].appendChild(s)}removeCustomStyles(A){const t=document.getElementById(A);t&&t.remove()}cleanup(){this.amapObserver&&(this.amapObserver.disconnect(),this.amapObserver=null),this.amapStyleInterval&&(clearInterval(this.amapStyleInterval),this.amapStyleInterval=null),this.removeCustomStyles("amap-dark-theme-fix")}reinitialize(){this.cleanup(),this.ensureAmapSuggestStyles()}getStyleState(){return{hasAmapObserver:!!this.amapObserver,hasStyleInterval:!!this.amapStyleInterval,isDarkMode:this.isDarkMode(),themeColors:this.getThemeColors()}}}function Gr(){const e=["amap-dark-theme-fix",`
            /* 高德地图提示框强制样式 - 最高优先级 */
            .amap-sug-result,
            .amap-ui-autocomplete,
            div[class*="amap"][class*="sug"],
            div[class*="amap"][class*="auto"] {
                background: var(--vp-c-bg) !important;
                border: 1px solid var(--vp-c-divider) !important;
                border-radius: 8px !important;
                box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1) !important;
                z-index: 99999 !important;
            }

            .amap-sug-result .auto-item,
            .amap-sug-result li,
            .amap-ui-autocomplete .amap-ui-autocomplete-item,
            .amap-ui-autocomplete li,
            div[class*="amap"] .auto-item,
            div[class*="amap"] li {
                background: var(--vp-c-bg) !important;
                color: var(--vp-c-text-1) !important;
                border-bottom: 1px solid var(--vp-c-divider-light) !important;
                padding: 12px 16px !important;
                font-size: 0.875rem !important;
                line-height: 1.4 !important;
            }

            .amap-sug-result .auto-item *,
            .amap-sug-result li *,
            .amap-ui-autocomplete .amap-ui-autocomplete-item *,
            .amap-ui-autocomplete li *,
            div[class*="amap"] .auto-item *,
            div[class*="amap"] li * {
                color: var(--vp-c-text-1) !important;
            }

            /* 深色主题特殊处理 */
            .dark .amap-sug-result,
            .dark .amap-ui-autocomplete,
            .dark div[class*="amap"][class*="sug"],
            .dark div[class*="amap"][class*="auto"] {
                background: var(--vp-c-bg-soft) !important;
                border-color: var(--vp-c-divider) !important;
                box-shadow: 0 4px 16px rgba(0, 0, 0, 0.3) !important;
            }

            .dark .amap-sug-result .auto-item,
            .dark .amap-sug-result li,
            .dark .amap-ui-autocomplete .amap-ui-autocomplete-item,
            .dark .amap-ui-autocomplete li,
            .dark div[class*="amap"] .auto-item,
            .dark div[class*="amap"] li {
                background: var(--vp-c-bg-soft) !important;
                color: #ffffff !important;
                border-bottom-color: var(--vp-c-divider) !important;
            }

            .dark .amap-sug-result .auto-item *,
            .dark .amap-sug-result li *,
            .dark .amap-ui-autocomplete .amap-ui-autocomplete-item *,
            .dark .amap-ui-autocomplete li *,
            .dark div[class*="amap"] .auto-item *,
            .dark div[class*="amap"] li * {
                color: #ffffff !important;
            }

            /* 悬停效果 */
            .amap-sug-result .auto-item:hover,
            .amap-sug-result li:hover,
            .amap-ui-autocomplete .amap-ui-autocomplete-item:hover,
            .amap-ui-autocomplete li:hover {
                background: var(--vp-c-brand-soft) !important;
                color: var(--vp-c-brand-1) !important;
            }

            .dark .amap-sug-result .auto-item:hover,
            .dark .amap-sug-result li:hover,
            .dark .amap-ui-autocomplete .amap-ui-autocomplete-item:hover,
            .dark .amap-ui-autocomplete li:hover {
                background: var(--vp-c-brand-dimm) !important;
                color: #ffffff !important;
            }

            .amap-sug-result .auto-item:hover *,
            .amap-sug-result li:hover *,
            .amap-ui-autocomplete .amap-ui-autocomplete-item:hover *,
            .amap-ui-autocomplete li:hover *,
            .dark .amap-sug-result .auto-item:hover *,
            .dark .amap-sug-result li:hover *,
            .dark .amap-ui-autocomplete .amap-ui-autocomplete-item:hover *,
            .dark .amap-ui-autocomplete li:hover * {
                color: inherit !important;
            }
        `,"startAmapStyleMonitor","amapStyleInterval","includes","var(--vp-c-bg-soft)","important","trim","getPropertyValue","undefined","attributeName","contains","head"];return Gr=function(){return e},Gr()}function ne(e,A){const t=Gr();return ne=function(r,n){return r=r-0,t[r]},ne(e,A)}const kr=new k0,ht=se;function Vr(){const e=["services","initialize","style","cleanup","scroll","getApiService"];return Vr=function(){return e},Vr()}class V0{constructor(){const A=se;this[A(0)]={map:null,scroll:null,form:null,api:null,style:null},this.initialized=!1}async initialize(){const A=se;if(!this.initialized)try{this.services.map=iA,this.services.scroll=QA,this.services.form=SA,this[A(0)].api=ee,this.services.style=kr,this.services.map&&typeof this.services.map.initialize=="function"&&await this.services.map.initialize(),this.services.style&&typeof this.services.style.initialize=="function"&&await this.services.style[A(1)](),this.initialized=!0}catch(t){throw t}}getMapService(){return this.services.map}getScrollManager(){return this.services.scroll}getFormManager(){return this[se(0)].form}getApiService(){return this.services.api}getStyleManager(){const A=se;return this[A(0)][A(2)]}[ht(3)](){const A=ht;Object.values(this.services).forEach(t=>{t&&typeof t.cleanup=="function"&&t.cleanup()}),this[A(0)]={map:null,scroll:null,form:null,api:null,style:null},this.initialized=!1}async reinitialize(){this[ht(3)](),await this.initialize()}isInitialized(){return this.initialized}getServicesStatus(){const A=ht;return{initialized:this.initialized,services:{map:!!this.services.map,scroll:!!this.services[A(4)],form:!!this.services.form,api:!!this.services.api,style:!!this.services.style}}}}new V0;function se(e,A){const t=Vr();return se=function(r,n){return r=r-0,t[r]},se(e,A)}const H=DA;function Nr(){const e=["https://topmeanslab.com","isAnyLoading","viewCompleted","undefined","initialize","地图初始化失败:","activeDayDetailIndex","autoScrollEnabled","saveFormData","weatherCompleted","refreshVisibleMaps","expandedSectionType","$nextTick","driving","querySelector","smooth","selectedDayIndex","none","contents","hotel","remove","amap","length","clearPlanningContents","rent_requirements","last_end","plan_requirements","hotel_requirements","s_address","startDate","dates","plan_mode","removeKeyboardListener","addKeyboardListener","insertBefore","function","setFitView","地图 ","forceMapResize","keydown","路线生成失败","weather","view","food","url",`)

`,"## [携程直达：","## ",".md","美食图片获取失败:","includes","replace","error","e_address","travel_mode","rent_customized","e_location","lng","rent",", 终点是","circle","askDS","split","trim","getAccurateCoordinates","%CIR","lat","warn","这几个地方了，不要重复，","@@@@","name","foodCompleted","cost","setLoadingState","validateFormData","为避免地点位置错误，请根据弹出的地点信息选择终点","planningFinishProc","planningDriving","resetScrollState","showPlanningFailure","failureReason","documentElement","themeObserver","attributes","attributeName","querySelectorAll","planningWeather","drivingCompleted","#map-container-","强制刷新第","天地图失败:"];return Nr=function(){return e},Nr()}const dt=H(0),xr="你好，你是一个旅游规划专家，我最近想外出旅游，请你帮我做一份旅游规划，我将在 startDate 出发，从 s_address 到 e_address ，请给出 plan_mode 的旅游规划，采取 travel_mode 的方式旅游，一共游玩 dates 天，请按照游玩天数进行规划，把更多的时间放到目的地，但是自驾的规划也不要太累，并且尽量安排风景好，不堵车的路线。我会分多个维度进行提问，所以，请回答我问你的这个维度问题即可。比如我问你天气信息，就只回答我天气信息即可。",Ae="使用 MarkDown 语法回答，效果要足够酷炫和精美，注意，勿给出 html 代码，只要纯 MarkDown 内容，并且也不要```markdown，只给出具体的 markdown 内容即可，便于我进行渲染",N0="请结合旅游起点和终点信息给出旅游的这几天天气以及必备物品、穿着等信息",ts="请给出租车建议，customized_rent_prompt 针对油车和电车分别给出三个租车车辆类型推荐，具体到品牌和车型，然后给出价位和靠谱的租车公司，并附加租车公司链接",rs="请给出第 index 天的自驾路线信息， driving_mid_s_e_prompt 规划的路线起点使用^^进行包裹便于我解析，例如^^北京^^，规划的路线终点使用%% 进行包裹便于我解析，例如%% 上海 %%，同时，请给出起点和终点所属的省份，城市信息，起点省份使用 %SP 进行包裹，例如 %SP 四川 %SP，起点城市使用 %SC 进行包裹，例如：%SC 成都 %SC，终点省份使用 %EP 进行包裹，终点城市使用 %EC 进行包裹。如果当天是环线，也就是起点和终点相同，则额外给出当前规划的环线信息，环线使用%CIR包裹，每个途径点使用 → 进行连接。例如：%CIR上海市  → 东方明珠 → 上海市%CIR。last_circle_prompt customized_driving_prompt 给出距离信息，路况信息，避堵建议，如果走高速，请给出关键的几个服务区信息，包括服务区支持的燃油公司，油品，价格等信息，以及服务区是否支持充电。如果不走高速，请给出建议的休息地点，以及距离该地点最近的加油站信息，包括油品，价格等信息，以及最近的充电站位置信息",P0="请给出 e_address 最值得游玩的 3 个景点，last_view_prompt 每个景点回答信息通过一行单独的@@@@开始便于我解析，注意回答每个景点信息尾部不要再添加@@@@了，保证每个景点信息之间只有一个@@@@行。景点名字均使用^^进行包裹方便我解析，例如^^故宫^^。景点要提供对于该景点外貌非常详细的描述以便我作为 prompt 进行绘图，外貌描述信息使用 %PT进行包裹，比如%PT 飞流的瀑布奔腾不息，一条钢筋大桥横跨其上 %PT。另外景点要提供是否开放、门票等信息",J0="请给出 e_address 最值得品尝的3 个特色美食以及对应的餐馆信息，last_food_prompt 每个美食回答信息通过一行单独的@@@@开始便于我解析，注意回答每个美食信息尾部不要再添加@@@@了，保证每个美食信息之间只有一个@@@@行。美食名均使用^^进行包裹方便我解析，例如^^北京烤鸭^^，对应的餐馆信息均使用$$进行包裹方便我解析，例如$$北京全聚德$$。美食要提供对于该美食外观非常详细的描述以便我作为 prompt 进行绘图，外观描述信息使用 %PT进行包裹，比如%PT 深琥珀色的腊肉表面油润发亮，横切面呈现大理石纹路般的肥瘦相间 %PT。餐馆要提供营业时间和人均消费价格等信息",ns="请给出 e_address 推荐的3 个酒店，customized_hotel_prompt 每个酒店回答信息通过一行单独的@@@@开始便于我解析，注意回答每个酒店信息尾部不要再添加@@@@了，保证每个酒店信息之间只有一个@@@@行。酒店名通过^^进行包裹方便我解析，例如^^北京皇冠假日酒店^^，酒店要提供位置、3 个房型以及对应的价格等信息",X0="";L.setLevel("info");function DA(e,A){const t=Nr();return DA=function(r,n){return r=r-0,t[r]},DA(e,A)}const Y0={data(){return{contents:[],showBtn:!0,loading:!1,errorMessage:"",last_start:"",last_end:"",rent_requirements:"",rent_customized:!1,plan_requirements:"0",plan_customized:!1,hotel_requirements:"",hotel_customized:!1,maximizedMapIndex:-1,originalParent:null,originalNextSibling:null,planOptions:[{value:"0",text:"速度优先(默认值)"},{value:"1",text:"费用优先"},{value:"2",text:"距离优先"},{value:"3",text:"不走高速且避免收费"},{value:"4",text:"躲避拥堵"},{value:"5",text:"不走高速"},{value:"6",text:"躲避拥堵且不走高速"},{value:"7",text:"躲避拥堵且距离优先"},{value:"8",text:"躲避拥堵且不走高速且距离优先"},{value:"9",text:"躲避拥堵且不走高速且费用优先"}],...SA.getDefaultFormData(),selectedDayIndex:-1,activeDayDetailIndex:-1,expandedSectionType:null,loadingStates:{weather:!1,rent:!1,driving:!1,view:!1,food:!1,hotel:!1,cost:!1},showCompletionModal:!1,showFailureModal:!1,failureReason:"",showFloatingButtons:!1}},computed:{selectedPlanText(){const e=this.planOptions.find(A=>A.value===this.plan_requirements);return e?e.text:""},isAnyLoading(){return Object.values(this.loadingStates).some(e=>e)},currentLoadingMessage(){const e=this.getCurrentProcessingDay(),A={weather:"正在查询天气信息，请稍等...",rent:"正在思考租车方案，请稍等...",driving:"正在规划第"+e+"天的路线，请稍等...",view:"正在规划第"+e+"天的景点信息，请稍等...",food:"正在规划第"+e+"天的美食信息，请稍等...",hotel:"正在规划第"+e+"天的住宿信息，请稍等...",cost:"正在计算费用预算，请稍等..."};for(const[t,r]of Object.entries(this.loadingStates))if(r)return A[t];return"正在处理中，请稍等..."},currentProgressStep(){const e=H;if(this.contents.length===0)return 1;if(this[e(1)]||!this.showBtn)return 2;const A=this.contents.some(r=>r.weatherCompleted===2||r.rentCompleted===2||r.drivingCompleted===2||r.viewCompleted===2||r.foodCompleted===2||r.hotelCompleted===2||r.costCompleted===2);return this.contents.every(r=>{const n=e;return r.weatherCompleted===2||r.rentCompleted===2||r.drivingCompleted===2||r[n(2)]===2||r.foodCompleted===2||r.hotelCompleted===2||r.costCompleted===2})&&this.contents.length>0?3:A?2:1}},async mounted(){const e=H;if(typeof window===e(3))return;try{await iA[e(4)](),iA.setupAutoComplete("start-tipinput","end-tipinput",(t,r)=>{this.s_address=t,this.s_location=r||this.getDefaultFormData().s_location},(t,r)=>{this.e_address=t,this.e_location=r||this.getDefaultFormData().e_location}),kr.ensureAmapSuggestStyles()}catch(t){this.errorMessage=t.message,L.error(e(5),t)}QA.initScrollListener(),QA.setContentCompletedChecker(()=>this.showBtn),QA.setDisplayStateChecker(()=>({selectedDayIndex:this.selectedDayIndex,isAnyLoading:this.isAnyLoading})),this.loadFormData(),this.initFloatingButtons(),this.initThemeObserver(),this.loadPlanningContents()&&(this.shouldContinuePlanning()?(this.showBtn=!1,this.$nextTick(()=>{setTimeout(()=>{this.handleContentRestoration()},500)})):(this.clearPlanningContents(),this.contents=[],this.selectedDayIndex=-1,this[e(6)]=-1,this.expandedSectionType=null,this.showBtn=!0))},beforeUnmount(){this.maximizedMapIndex!==-1&&this.restoreMapToOriginalPosition(this.maximizedMapIndex),QA.cleanup(),kr.cleanup(),iA.cleanup(),ee.cleanup(),this.removeKeyboardListener(),this.handleScroll&&typeof window<"u"&&window.removeEventListener("scroll",this.handleScroll),this.themeObserver&&this.themeObserver.disconnect()},watch:{contents:{handler(e){const A=H;e.length>0&&QA.getScrollState()[A(7)]&&this.$nextTick(()=>{this[A(1)]&&QA.smartScrollToContent()}),e.length>0&&this.savePlanningContents()},deep:!0},s_address:{handler(){this.saveFormData()}},e_address:{handler(){this.saveFormData()}},s_location:{handler(){this.saveFormData()},deep:!0},e_location:{handler(){this[H(8)]()},deep:!0},startDate:{handler(){this.saveFormData()}},dates:{handler(){this.saveFormData()}},plan_mode:{handler(){this.saveFormData()}},travel_mode:{handler(){this.saveFormData()}},selectedDayIndex:{handler(){this.contents.length>0&&this.savePlanningContents()}},activeDayDetailIndex:{handler(){this.contents.length>0&&this.savePlanningContents()}},expandedSectionType:{handler(){this.contents.length>0&&this.savePlanningContents()}},showBtn:{handler(){this.contents.length>0&&this.savePlanningContents()}}},methods:{parseMarkdown(e){return oa.parse(e||"")},getCurrentProcessingDay(){const e=H;if(!this.contents||this.contents.length===0)return 1;for(let A=this.contents.length-1;A>=0;A--){const t=this.contents[A];if(t[e(9)]>0||t.rentCompleted>0||t.drivingCompleted>0||t.viewCompleted>0||t.foodCompleted>0||t.hotelCompleted>0||t.costCompleted>0)return A+1}return 1},selectDay(e){const A=H;e<0||e>=this.contents.length||!this.contents[e]||this.isDayCompleted(this.contents[e])&&(this.activeDayDetailIndex=this[A(6)]===e?-1:e,this.activeDayDetailIndex!==-1&&(this.expandedSectionType=null,this.selectedDayIndex=-1),this.$nextTick(()=>{this[A(10)]()}))},handleDetailPanelClick(e){const A=H,t=this.activeDayDetailIndex;t===-1||t>=this.contents.length||t<0||this.contents[t]&&(this[A(11)]=e,this.selectedDayIndex=t,this[A(12)](()=>{this.scrollToSection(e,t),this.refreshVisibleMaps()}))},scrollToSection(e,A){const t=H;let r="";switch(e){case"weather":r=".weather-header";break;case"rent":r=".rent-header";break;case t(13):r=".driving-header";break;case"view":r=".view-header";break;case"food":r=".food-header";break;case"hotel":r=".hotel-header";break}if(r){const n=document[t(14)](r);n&&setTimeout(()=>{const s=t;QA.scrollToElement(n,s(15))},100)}},shouldShowSection(e,A){const t=H;if(this.expandedSectionType===null||A>=this.contents.length||A<0)return!1;const r=this.expandedSectionType===e&&(this.selectedDayIndex===-1||this[t(16)]===A);return this.$nextTick(()=>{document.querySelectorAll(".answer-area-container").forEach(s=>{const a=DA,o=s.querySelector(".section-content");o&&(window.getComputedStyle(o).display!==a(17)?s.classList.add("expanded"):s.classList.remove("expanded"))})}),r},isSectionAvailable(e,A){if(A>=this.contents.length||A<0)return!1;const t=this.contents[A];if(!t)return!1;switch(e){case"weather":return A===0&&t.weatherCompleted===2;case"rent":return A===0&&this.travel_mode==="租车"&&t.rentCompleted===2;case"driving":return t.drivingCompleted===2;case"view":return t.viewCompleted===2;case"food":return t.foodCompleted===2;case"hotel":return t.hotelCompleted===2;default:return!1}},shouldShowSectionHeader(e,A){const t=H;if(this.activeDayDetailIndex===-1||A!==this.activeDayDetailIndex||A>=this.contents.length||A<0)return!1;const r=this[t(18)][A];if(!r)return!1;switch(e){case"weather":return A===0&&r.weatherCompleted!==0;case"rent":return A===0&&this.travel_mode==="租车"&&r.rentCompleted!==0;case"driving":return r.drivingCompleted!==0;case"view":return r.viewCompleted!==0;case"food":return r.foodCompleted!==0;case t(19):return r.hotelCompleted!==0;case"cost":return r.costCompleted!==0&&(A===this[t(18)].length-1||this[t(18)].every(n=>n&&(n.drivingCompleted===2||n[t(2)]===2)));default:return!1}},handleSectionHeaderClick(e,A){const t=H;this.expandedSectionType===e&&this.selectedDayIndex===A?(this.expandedSectionType=null,this[t(16)]=-1):(this.expandedSectionType=e,this.selectedDayIndex=A,this.activeDayDetailIndex=A,this.$nextTick(()=>{this.refreshVisibleMaps()}))},collapseAll(){this.expandedSectionType=null,this.selectedDayIndex=-1,this.activeDayDetailIndex=-1,this.$nextTick(()=>{document.querySelectorAll(".answer-area-container").forEach(A=>{const t=DA;A.classList[t(20)]("expanded")}),this.refreshVisibleMaps()})},async temporarilyExpandSection(e,A,t=2e3){const r=this.expandedSectionType,n=this.selectedDayIndex;return this.expandedSectionType=e,this.selectedDayIndex=A,await this.$nextTick(),new Promise(s=>{setTimeout(()=>{this.expandedSectionType=r,this.selectedDayIndex=n,s()},t)})},refreshVisibleMaps(){const e=H;typeof window!==e(3)&&this.contents.forEach((A,t)=>{const r=e;if(r(21)in A&&(this[r(16)]===-1||this.selectedDayIndex===t)){const s=document.querySelector("#map-container-"+t);s&&s.offsetParent!==null&&setTimeout(()=>{this.forceMapResize(t)},100)}})},isDayCompleted(e){return e?e.weatherCompleted===2||e.rentCompleted===2||e.drivingCompleted===2||e.viewCompleted===2||e.foodCompleted===2||e.hotelCompleted===2||e.costCompleted===2:!1},getDayTitle(e,A){const t=H;return A===0?this.s_address+" → "+this.e_address:A===this.contents[t(22)]-1&&this.plan_mode==="往返"?this.e_address+" → "+this.s_address:this.e_address+"游览"},setLoadingState(e,A){this.loadingStates[e]=A},resetAllLoadingStates(){this.loadingStates={weather:!1,rent:!1,driving:!1,view:!1,food:!1,hotel:!1,cost:!1}},scrollPageToBottom(){QA.smartScrollToContent()},resetScrollState(){QA.resetScrollState()},saveFormData(){const e={s_address:this.s_address,e_address:this.e_address,startDate:this.startDate,dates:this.dates,plan_mode:this.plan_mode,travel_mode:this.travel_mode,s_location:this.s_location,e_location:this.e_location};SA.saveFormData(e)},loadFormData(){const e=SA.loadFormData();if(e){const A=SA.mergeFormData({s_address:this.s_address,e_address:this.e_address,startDate:this.startDate,dates:this.dates,plan_mode:this.plan_mode,travel_mode:this.travel_mode,s_location:this.s_location,e_location:this.e_location},e);Object.assign(this,A)}},clearFormData(){SA.clearFormData()},resetFormData(){const e=H,A=SA.resetFormData();Object.assign(this,A),this[e(23)](),this.contents=[],this.selectedDayIndex=-1,this.activeDayDetailIndex=-1,this.expandedSectionType=null,this.showBtn=!0},savePlanningContents(){const e=H;if(!(typeof window>"u"))try{const A={contents:this.contents,last_start:this.last_start,last_end:this.last_end,selectedDayIndex:this.selectedDayIndex,activeDayDetailIndex:this.activeDayDetailIndex,expandedSectionType:this.expandedSectionType,rent_requirements:this[e(24)],rent_customized:this.rent_customized,plan_requirements:this.plan_requirements,plan_customized:this.plan_customized,hotel_requirements:this.hotel_requirements,hotel_customized:this.hotel_customized,showBtn:this.showBtn,formData:{s_address:this.s_address,e_address:this.e_address,startDate:this.startDate,dates:this.dates,plan_mode:this.plan_mode,travel_mode:this.travel_mode,s_location:this.s_location,e_location:this.e_location},timestamp:Date.now()};localStorage.setItem("topmeans_planning_contents",JSON.stringify(A))}catch(A){console.warn("保存规划内容失败:",A)}},loadPlanningContents(){const e=H;if(typeof window>"u")return!1;try{const A=localStorage.getItem("topmeans_planning_contents");if(!A)return!1;const t=JSON.parse(A),r=24*60*60*1e3;return t.timestamp&&Date.now()-t.timestamp>r?(this.clearPlanningContents(),!1):(this.contents=t.contents||[],this.last_start=t.last_start||"",this[e(25)]=t.last_end||"",this.selectedDayIndex=t.selectedDayIndex!==void 0?t.selectedDayIndex:-1,this.activeDayDetailIndex=t.activeDayDetailIndex!==void 0?t.activeDayDetailIndex:-1,this[e(11)]=t.expandedSectionType||null,this.rent_requirements=t.rent_requirements||"",this.rent_customized=t.rent_customized||!1,this.plan_requirements=t[e(26)]||"0",this.plan_customized=t.plan_customized||!1,this[e(27)]=t.hotel_requirements||"",this.hotel_customized=t.hotel_customized||!1,this.showBtn=t.showBtn!==void 0?t.showBtn:!0,t.formData&&(this.s_address=t.formData.s_address||this[e(28)],this.e_address=t.formData.e_address||this.e_address,this.startDate=t.formData[e(29)]||this.startDate,this[e(30)]=t.formData.dates||this.dates,this.plan_mode=t.formData.plan_mode||this[e(31)],this.travel_mode=t.formData.travel_mode||this.travel_mode,this.s_location=t.formData.s_location||this.s_location,this.e_location=t.formData.e_location||this.e_location),!0)}catch(A){return console.warn("加载规划内容失败:",A),this.clearPlanningContents(),!1}},clearPlanningContents(){if(!(typeof window>"u"))try{localStorage.removeItem("topmeans_planning_contents")}catch(e){console.warn("清除规划内容失败:",e)}},hasSavedPlanningContents(){if(typeof window>"u")return!1;try{const e=localStorage.getItem("topmeans_planning_contents");if(!e)return!1;const A=JSON.parse(e),t=24*60*60*1e3;return A.timestamp&&Date.now()-A.timestamp>t?!1:A.contents&&A.contents.length>0}catch{return!1}},updateRentRequirements(e){this.rent_requirements=e.target.value},updateHotelRequirements(e){this.hotel_requirements=e.target.value},toggleMapSize(e){const A=H;this.maximizedMapIndex===e?(this.restoreMapToOriginalPosition(e),this.maximizedMapIndex=-1,this[A(32)]()):(this.moveMapToBodyForMaximize(e),this.maximizedMapIndex=e,this[A(33)]()),this.$nextTick(()=>{setTimeout(()=>{this.forceMapResize(e)},350)})},moveMapToBodyForMaximize(e){const A=H;if(typeof window>"u")return;const t=document[A(14)]("#map-container-"+e).closest(".map-wrapper");t&&(this.originalParent=t.parentNode,this.originalNextSibling=t.nextSibling,document.body.appendChild(t))},restoreMapToOriginalPosition(e){const A=H;if(typeof window>"u")return;const t=document.querySelector("#map-container-"+e).closest(".map-wrapper");t&&this.originalParent&&(this.originalNextSibling?this.originalParent[A(34)](t,this.originalNextSibling):this.originalParent.appendChild(t),this.originalParent=null,this.originalNextSibling=null)},forceMapResize(e){const A=H;try{const t=iA.getMapInstance(e);if(!t)return;typeof t.resize===A(35)&&t.resize();const r=t.getCenter(),n=t.getZoom();typeof t.getSize=="function"&&t.getSize(),typeof t[A(36)]===A(35)&&t.setFitView(),setTimeout(()=>{try{r&&n&&typeof t.setZoomAndCenter=="function"&&t.setZoomAndCenter(n,r),setTimeout(()=>{try{if(typeof t.getZoom=="function"&&typeof t.setZoom=="function"){const s=t.getZoom();t.setZoom(s+.01),setTimeout(()=>{t.setZoom(s)},50)}}catch(s){console.warn("地图 "+e+" 缩放调整时出现错误:",s)}},100)}catch(s){console.warn("地图 "+e+" 中心点设置时出现错误:",s)}},100)}catch(t){console.warn(A(37)+e+" 重新渲染时出现错误:",t)}},addKeyboardListener(){const e=H;typeof window<"u"&&(this.escKeyHandler=A=>{A.key==="Escape"&&this.maximizedMapIndex!==-1&&this.toggleMapSize(this.maximizedMapIndex)},this.resizeHandler=()=>{this.maximizedMapIndex!==-1&&(clearTimeout(this.resizeTimeout),this.resizeTimeout=setTimeout(()=>{this[DA(38)](this.maximizedMapIndex)},300))},window.addEventListener(e(39),this.escKeyHandler),window.addEventListener("resize",this.resizeHandler))},removeKeyboardListener(){typeof window<"u"&&(this.escKeyHandler&&(window.removeEventListener("keydown",this.escKeyHandler),this.escKeyHandler=null),this.resizeHandler&&(window.removeEventListener("resize",this.resizeHandler),this.resizeHandler=null),this.resizeTimeout&&(clearTimeout(this.resizeTimeout),this.resizeTimeout=null))},handleRentCustomize(e){this.rent_requirements&&this.rent_requirements.trim()&&this.handleActionClick("rent",e)},async drivingPlanning(e,A,t,r,n){const s=H;try{await this[s(12)](),await this.$nextTick(),await iA.drivingPlanning(e,A,t,r,n),await new Promise(a=>setTimeout(a,2e3))}catch(a){L.error("路线生成错误",a),this.errorMessage=a.message||s(40)}finally{this.loading=!1}},async savePlanToDB(e,A,t){const r=H;await this.saveMapAsImage(e,A,t);let n=`# Smart Travel Plan

## `+this.s_address+" 到 "+this.e_address+`

`;if(this.contents[e][r(41)]&&(n+=this.contents[e].weather+`

`),this.contents[e].rent&&(n+=this.contents[e].rent+`

`),this.contents[e].driving&&(n+=this.contents[e].driving+`

`,n+="![路线规划](./map-"+t+"-"+e+`.png)

`),this[r(18)][e].view)for(let a=0;a<this.contents[e][r(42)].length;a++)n+="## "+this.contents[e].view[a].name+`

`,this.contents[e].view[a].url&&(n+="!["+this.contents[e][r(42)][a].name+"]("+this.contents[e].view[a].url+`)

`),n+=this[r(18)][e].view[a].info+`

`;if(this.contents[e].food)for(let a=0;a<this.contents[e].food[r(22)];a++)n+="## "+this.contents[e].food[a].name+`

`,this[r(18)][e].food[a].url&&(n+="!["+this.contents[e].food[a].name+"]("+this.contents[e][r(43)][a][r(44)]+r(45)),n+=this[r(18)][e].food[a].info+`

`;if(this.contents[e].hotel)for(let a=0;a<this.contents[e].hotel.length;a++)this.contents[e].hotel[a].url?n+=r(46)+this.contents[e].hotel[a].name+"]("+this.contents[e].hotel[a].url+' "'+this.contents[e].hotel[a].name+`")

`:n+=r(47)+this.contents[e].hotel[a].name+`

`,n+=this.contents[e].hotel[a].info+`

`;let s=await fetch(dt+"/api/save_plan",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({content:n,user:A,filename:"plan-"+t+"-"+e+r(48)})});if(!s.ok)throw L.error("保存计划失败，请检查网络连接",s),new Error("保存计划失败，请检查网络连接");if(s=await fetch(dt+"/api/user/add_plan",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({account:A,create_time:t,days:this[r(30)]})}),!s.ok)throw L.error("计划存库失败，请检查网络连接",s),new Error("计划存库失败，请检查网络连接")},async saveMapAsImage(e,A,t){try{await iA.saveMapAsImage(e,A,t,this)}catch(r){throw L.error("保存地图为图片失败:",r),r}},async getHotelUrl(e,A,t,r){try{return await ee.getHotelUrl(e,A,t,r,"")}catch(n){throw L.error("酒店信息获取失败:",n),n}},async getFoodImgUrl(e,A){const t=H;try{return await ee.getFoodImgUrl(e,A)}catch(r){throw L.error(t(49),r),r}},async getViewUrl(e){try{return await ee.getViewUrl(e)}catch(A){throw L.error("景点信息获取失败:",A),A}},async askDeepSeek(e,A,t){const r=H,n=Pe(),{user:s}=await n.getUserInfo(),a=this.getFormattedDate();try{const o=await fetch(dt+"/api/ds",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({msg:A})});if(!o.ok)throw L.error("DS API 请求失败:"+o.statusText),new Error("DS API 请求失败!");const i=o.body.getReader(),l=new TextDecoder("utf-8");let c="";for(;;){const{done:x,value:u}=await i.read();if(x)break;const w=l.decode(u),g=w.split(`
`).filter(h=>h.trim());for(const h of g)try{if(!h.startsWith("data: "))continue;const U=h.slice(6);if(U==="[DONE]")break;const d=JSON.parse(U),f=d.choices[0].delta.content;f&&(c+=f),t==="rent"?this.contents[e].rent=`**租车建议**
`+c:t==="plan"&&(this.contents[e].plan="**第"+(e+1)+`天的规划**

`+c)}catch(U){L.error("解析数据失败:",U)}}if(c&&t==="hotel"){const x=c.split("住宿推荐");for(let u=0;u<x.length;u++)if(u!==0&&(u>this[r(18)][e].hotel.length&&this.contents[e].hotel.push({}),this.contents[e].hotel[u-1].info=`**酒店信息:** 
`,x[u][r(50)]("**酒店信息:**"))){this.contents[e].hotel[u-1].info+=x[u].split("**酒店信息:**")[1].trim(),this.contents[e][r(19)][u-1].name=x[u].split("@@")[1].split("$$")[0].trim();try{this.contents[e].hotel[u-1].url=await this.getHotelUrl(this.contents[e].hotel[u-1].name,s.account,a,e+1)}catch{}}}}catch(o){throw L.error("DS API 请求失败:",o),o}},async askDS(e,A){const t=H;let r="";try{const n=await fetch(dt+"/api/ds",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({msg:A})});if(!n.ok)throw L.error("DS API 请求失败!"),new Error("DS API 请求失败!");const s=n.body.getReader(),a=new TextDecoder("utf-8");for(;;){const{done:o,value:i}=await s.read();if(o)break;const l=a.decode(i),c=l.split(`
`).filter(x=>x.trim());for(const x of c)try{if(!x.startsWith("data: "))continue;const u=x.slice(6);if(u==="[DONE]")break;const w=JSON.parse(u),g=w.choices[0].delta.content;g&&(r+=g),this.contents[e].think=r.replace(/%SP/g,"").replace(/%SC/g,"").replace(/%EP/g,"").replace(/%EC/g,"").replace(/%CIR/g,"").replace(/@/g,"").replace(/#/g,"").replace(/\$/g,"").replace(/\^/g,"").replace(/\*/g,"").replace(/%/g,"")[t(51)](/&/g,"").trim()}catch{}}}catch(n){throw L[t(52)]("DS API 请求失败:"+n),n}return r},async handleActionClick(e,A){const t=H;let r=!1;const n=Pe(),{user:s}=await n.getUserInfo(),a=this.getFormattedDate();let o=xr;if(o=o.replace(/startDate/g,this.startDate).replace(/s_address/g,this.s_address).replace(/e_address/g,this[t(53)]).replace(/plan_mode/g,this[t(31)]).replace(/travel_mode/g,this[t(54)]).replace(/dates/g,this.dates),e==="rent"){if(this.rent_requirements){this.contents[A].rentCompleted=1;const i=o+ts.replace(/customized_rent_prompt/g,this.rent_requirements||"");this.contents[A].rent="",await this.planningRent(A,s,a,i),this.contents[A].rentCompleted=2,this[t(55)]=!0,r=!0}}else if(e==="driving"){if(this.plan_requirements){this.contents[A].drivingCompleted=1;let i="";A>0&&(i="注意，第"+(A-1)+"天路线已完成规划，起点是"+this.last_start+", 终点是"+this.last_end+"，因此今天的起点就是"+this.last_end+"，最终的目的地是"+this.e_address);const l=o+rs.replace(/index/g,A+1).replace(/driving_mid_s_e_prompt/g,i).replace(/customized_driving_prompt/g,this.selectedPlanText||"");this.contents[A].driving="";const c=parseInt(this.plan_requirements,10);await this.planningDriving(A,s,a,l,c),this[t(18)][A].drivingCompleted=2,this.plan_customized=!0,r=!0}}else if(e==="hotel"&&this.hotel_requirements){this.contents[A].hotelCompleted=1;const i=o+ns.replace(/e_address/g,this[t(53)]).replace(/customized_hotel_prompt/g,this.hotel_requirements||"");for(let l=0;l<this.contents[A].hotel.length;l++)this.contents[A].hotel[l].info="",this.contents[A].hotel[l].name="",this.contents[A].hotel[l][t(44)]="";await this.planningHotel(A,s,a,i),this.contents[A].hotelCompleted=2,this.hotel_customized=!0,r=!0}r&&this.showBtn&&(await this.savePlanToDB(A,s.account,a),this.savePlanningContents())},validateFormData(){const e=H,A={s_address:this.s_address,e_address:this.e_address,startDate:this.startDate,dates:this[e(30)],plan_mode:this.plan_mode,travel_mode:this.travel_mode,s_location:this.s_location,e_location:this[e(56)]},t=SA.validateFormData(A);return!t.isValid&&alert(t.message),t.isValid},getDefaultFormData(){return SA.getDefaultFormData()},getLocationSummary(e){if(!e)return"未设置";const A=[];return e.province&&A.push(e.province),e.city&&A.push(e.city),e.district&&A.push(e.district),A.length>0?A.join(" "):e.address||"位置信息不完整"},hasCompleteLocation(e){return e&&e[H(57)]&&e.lat||e&&e.city&&e.province},getFormattedDate(){const e=new Date,A=e.getFullYear(),t=String(e.getMonth()+1).padStart(2,"0"),r=String(e.getDate()).padStart(2,"0"),n=""+A+t+r,s=""+String(e.getHours()).padStart(2,"0")+String(e.getMinutes()).padStart(2,"0")+String(e.getSeconds()).padStart(2,"0");return""+n+s},async planningWeather(e,A,t,r){this.contents[e].weatherCompleted=1,this.setLoadingState("weather",!0),this.expandedSectionType="weather",this.selectedDayIndex=e,this.activeDayDetailIndex=e,await this.$nextTick();const n=r+N0+Ae;this.contents[e].weather=await this.askDS(e,n),this.contents[e].weatherCompleted=2,this.setLoadingState("weather",!1),this.expandedSectionType=null,this.selectedDayIndex=-1},async planningRent(e,A,t,r){const n=H;this.contents[e].rentCompleted=1,this.setLoadingState("rent",!0),this[n(11)]="rent",this.selectedDayIndex=e,this.activeDayDetailIndex=e,await this.$nextTick();const s=ts.replace(/customized_rent_prompt/g,this.rent_requirements||""),a=r+s+Ae;this.contents[e][n(58)]=await this.askDS(e,a),this.contents[e].rentCompleted=2,this.setLoadingState("rent",!1),this.expandedSectionType=null,this[n(16)]=-1},async planningDriving(e,A,t,r,n){const s=H;this.contents[e].drivingCompleted=1,this.setLoadingState("driving",!0),this.expandedSectionType="driving",this.selectedDayIndex=e,this.activeDayDetailIndex=e,await this.$nextTick();let a="";e>0&&e<this.dates-1||e>0&&e===this.dates-1&&this.plan_mode==="单程"?a="注意，第"+e+"天路线已完成规划，起点是"+this.last_start+s(59)+this.last_end+"，因此今天的起点就是"+this.last_end+"，最终的目的地是"+this.e_address:e===this.dates-1&&this.dates>1&&this.plan_mode==="往返"&&(a="注意，第"+e+"天路线已完成规划，起点是"+this.last_start+", 终点是"+this.last_end+"，因此今天的起点就是"+this.last_end+"，最终的目的地是"+this.s_address);let o="";for(let Q=0;Q<this.dates;++Q)if(this[s(18)][Q][s(60)])for(let b=0;b<this.contents[Q].circle.length;b++)o+=this[s(18)][Q].circle[b]+",";o&&(o="注意，我已经去过"+o+"这几个地方了，规划环线时不要重复，");const i=rs.replace(/index/g,e+1).replace(/driving_mid_s_e_prompt/g,a).replace(/last_circle_prompt/g,o).replace(/customized_driving_prompt/g,this.selectedPlanText||""),l=r+i+Ae,c=await this[s(61)](e,l);this.contents[e].driving=c.replace(/%SP/g,"").replace(/%SC/g,"")[s(51)](/%EP/g,"").replace(/%EC/g,"").replace(/%CIR/g,"").replace(/\^/g,"")[s(51)](/%/g,"");const x=c[s(62)]("^^")[1][s(62)]("^^")[0].trim(),u=c.split("%SP")[1].split("%SP")[0].trim(),w=c.split("%SC")[1].split("%SC")[0].trim(),g=c.split("%%")[1].split("%%")[0].trim(),h=c.split("%EP")[1].split("%EP")[0].trim(),U=c.split("%EC")[1].split("%EC")[0][s(63)]();this.last_start=x,this.last_end=g;let d={lng:null,lat:null},f={lng:null,lat:null};const v=await iA.getAccurateCoordinates(x,u,w),m=await iA[s(64)](g,h,U);!v.lng||!v.lat?d={lng:null,lat:null}:d={lng:v.lng,lat:v.lat},!m.lng||!m.lat?f={lng:null,lat:null}:f={lng:m.lng,lat:m.lat};let F=[];if(x===g){const Q=c.split(s(65))[1],b=Q.split(" → ");this.contents[e].circle=[];for(let S=1;S<b[s(22)]-1;++S){const E=b[S].trim(),O=await iA.getAccurateCoordinates(E,h,U);this[s(18)][e].circle.push(E),O&&O.lng&&O.lat?F.push([O.lng,O.lat]):console.warn("环线地点地理编码失败: "+E+"，跳过该地点")}}if(this.contents[e].pos={s_address:x,s_province:u,s_city:w,e_address:g,e_province:h,e_city:U,s_location:{s_lng:d.lng,s_lat:d.lat},e_location:{e_lng:f.lng,e_lat:f.lat},circle_locations:F},this.contents[e].amap="",d.lng&&d[s(66)]&&f.lng&&f[s(66)]){await this.$nextTick(),await this.$nextTick();try{await this.drivingPlanning(e,{s_lng:d.lng,s_lat:d.lat},{e_lng:f[s(57)],e_lat:f.lat},n,F),await new Promise(Q=>setTimeout(Q,1e3))}catch(Q){L.error("第"+(e+1)+"天导航规划失败:",Q)}}else console[s(67)]("第"+(e+1)+"天地理编码失败，跳过地图规划，但继续其他规划流程");this.contents[e].drivingCompleted=2,this.setLoadingState("driving",!1),this.expandedSectionType=null,this.selectedDayIndex=-1},async planningView(e,A,t,r){const n=H;this[n(18)][e].viewCompleted=1,this.setLoadingState("view",!0),this.expandedSectionType="view",this[n(16)]=e,this.activeDayDetailIndex=e,await this.$nextTick(),this.contents[e].view=[];let s=P0[n(51)](/e_address/g,this.last_end);if(e>0){let l="";for(let c=0;c<e;++c)for(let x=0;x<this.contents[c].view[n(22)];++x)l+=this.contents[c].view[x].name+",";s=s.replace(/last_view_prompt/g,"注意，我已经去过"+l+n(68))}const a=r+s+Ae,o=await this.askDS(e,a),i=o.split(n(69));for(let l=0;l<i.length;l++)if(l!==0){l>this.contents[e].view.length&&this.contents[e].view.push({}),i[l].includes("^^")?this.contents[e][n(42)][l-1].name=i[l].split("^^")[1].split("^^")[0].trim():i[l].includes("$$")&&(this.contents[e].view[l-1].name=i[l].split("$$")[1].split("$$")[0].trim()),this[n(18)][e].view[l-1].info=i[l].replace(/\^\^.*\^\^/g,"").replace(/\$\$.*\$\$/g,"")[n(51)](/%PT/g,"").trim(),this[n(18)][e].view[l-1].prompt=i[l].split("%PT")[1].split("%PT")[0].trim();try{this[n(18)][e].view[l-1].url=await ee.getAIImg(this[n(18)][e][n(42)][l-1].name,this[n(18)][e].view[l-1].prompt)}catch{this[n(18)][e].view[l-1].url=""}}this[n(18)][e].viewCompleted=2,this.setLoadingState("view",!1),this.expandedSectionType=null,this.selectedDayIndex=-1},async planningFood(e,A,t,r){const n=H;this.contents[e].foodCompleted=1,this.setLoadingState("food",!0),this.expandedSectionType="food",this.selectedDayIndex=e,this.activeDayDetailIndex=e,await this.$nextTick(),this.contents[e][n(43)]=[];let s=J0.replace(/e_address/g,this.last_end);if(e>0){let l="";for(let c=0;c<e;++c)for(let x=0;x<this[n(18)][c].food.length;++x)l+=this.contents[c].food[x].name+",";s=s.replace(/last_food_prompt/g,"注意，我已经吃过"+l+"这几个美食了，不要重复，")}const a=r+s+Ae,o=await this.askDS(e,a),i=o.split("@@@@");for(let l=0;l<i.length;l++)if(l!==0){l>this.contents[e][n(43)].length&&this[n(18)][e].food.push({}),i[l].includes("^^")?this.contents[e].food[l-1].name=i[l].split("^^")[1].split("^^")[0].trim():i[l].includes("$$")&&(this.contents[e].food[l-1][n(70)]=i[l].split("$$")[1].split("$$")[0].trim()),this.contents[e].food[l-1].info=i[l].replace(/\^/g,"").replace(/\$/g,"").replace(/%PT/g,"").trim(),this.contents[e].food[l-1].prompt=i[l].split("%PT")[1].split("%PT")[0][n(63)]();try{this.contents[e].food[l-1][n(44)]=await ee.getAIImg(this.contents[e].food[l-1].name,this.contents[e].food[l-1].prompt)}catch{this.contents[e].food[l-1].url=""}}this.contents[e][n(71)]=2,this.setLoadingState("food",!1),this.expandedSectionType=null,this.selectedDayIndex=-1},async planningHotel(e,A,t,r){const n=H;this[n(18)][e].hotelCompleted=1,this.setLoadingState("hotel",!0),this.expandedSectionType="hotel",this.selectedDayIndex=e,this.activeDayDetailIndex=e,await this.$nextTick(),this.contents[e].hotel=[];const s=ns.replace(/e_address/g,this.last_end).replace(/customized_hotel_prompt/g,this.hotel_requirements||""),a=r+s+Ae,o=await this.askDS(e,a),i=o.split("@@@@");for(let l=0;l<i.length;l++)if(l!==0){l>this.contents[e].hotel.length&&this[n(18)][e].hotel.push({}),this.contents[e].hotel[l-1][n(70)]=i[l].split("^^")[1].split("^^")[0].trim(),this.contents[e].hotel[l-1].info=i[l].replace(/\^\^.*\^\^/g,"")[n(51)](/\$\$.*\$\$/g,"").trim();try{this.contents[e].hotel[l-1].url=await this.getHotelUrl(this.contents[e].hotel[l-1].name,A.account,t,l)}catch{this.contents[e].hotel[l-1][n(44)]="https://www.ctrip.com"}}this.contents[e].hotelCompleted=2,this.setLoadingState("hotel",!1),this.expandedSectionType=null,this.selectedDayIndex=-1},async planningCost(e,A,t,r){const n=H;this.contents[e].costCompleted=1,this.setLoadingState("cost",!0),this[n(11)]=n(72),this.selectedDayIndex=e,this.activeDayDetailIndex=e,await this.$nextTick();const s=r+X0+Ae;this.contents[e].cost=await this.askDS(e,s),this.contents[e].costCompleted=2,this[n(73)]("cost",!1),this.expandedSectionType=null,this[n(16)]=-1},async planningNew(){const e=H,A=Pe();if(!A.checkLoginStatus()){alert("请先登录");return}const{user:t}=await A.getUserInfo();if(this.hasSavedPlanningContents()){if(this.loadPlanningContents()&&this.shouldContinuePlanning()&&confirm(`检测到有未完成的规划内容，是否继续之前的规划？

点击"确定"继续之前的规划
点击"取消"开始新的规划`)){await this.$nextTick(),setTimeout(()=>{this.handleContentRestoration(!1)},500);return}this.clearPlanningContents(),this.contents=[],this.selectedDayIndex=-1,this.activeDayDetailIndex=-1,this.expandedSectionType=null}if(!this[e(74)]())return;if(!this.hasCompleteLocation(this.s_location)){alert("为避免地点位置错误，请根据弹出的地点信息选择起点");return}if(!this.hasCompleteLocation(this.e_location)){alert(e(75));return}this.showBtn=!1,this.last_start=this.s_address,this.last_end=this.e_address,this.resetScrollState(),this.clearPlanningContents(),this.contents=[];for(let s=0;s<this.dates;s++)this.contents.push({weatherCompleted:0,rentCompleted:0,drivingCompleted:0,viewCompleted:0,hotelCompleted:0,foodCompleted:0,costCompleted:0});const r=this.getFormattedDate();let n=xr;n=n.replace(/startDate/g,this.startDate).replace(/s_address/g,this.s_address).replace(/e_address/g,this.e_address).replace(/plan_mode/g,this.plan_mode).replace(/travel_mode/g,this.travel_mode).replace(/dates/g,this.dates);try{await this.planningWeather(0,t,r,n)}catch(s){L.error("天气规划失败："+s),this.planningFinishProc(),this.resetScrollState(),this.contents=[],this.showPlanningFailure("天气信息获取失败，请检查网络连接后重试");return}if(this.travel_mode==="租车")try{await this.planningRent(0,t,r,n)}catch(s){L.error("租车方案规划失败："+s),this[e(76)](),this.resetScrollState(),this[e(18)]=[],this.showPlanningFailure("租车方案规划失败，请检查网络连接后重试");return}for(let s=0;s<this.dates;s++){try{await this[e(77)](s,t,r,n)}catch(a){L.error("路线规划失败："+a),this.planningFinishProc(),this.resetScrollState(),this.contents=[],this.showPlanningFailure("路线规划失败，请检查网络连接后重试");return}if(s!==this.dates-1||s===0||this.plan_mode==="单程"){try{await this.planningView(s,t,r,n)}catch(a){L.error("景点规划失败："+a),this.planningFinishProc(),this.resetScrollState(),this.contents=[],this.showPlanningFailure("景点推荐规划失败，请检查网络连接后重试");return}try{await this.planningFood(s,t,r,n)}catch(a){L[e(52)]("美食规划失败："+a),this.planningFinishProc(),this[e(78)](),this.contents=[],this[e(79)]("美食推荐规划失败，请检查网络连接后重试");return}try{await this.planningHotel(s,t,r,n)}catch(a){L[e(52)]("住宿规划失败："+a),this.planningFinishProc(),this.resetScrollState(),this.contents=[],this.showPlanningFailure("住宿推荐规划失败，请检查网络连接后重试");return}}try{await this.savePlanToDB(s,t.account,r)}catch(a){L.error("旅游规划存库失败："+a)}}await this.planningFinishProc(),this.showCompletionModal=!0},async planningFinishProc(){const e=H;this.showBtn=!0,this.selectedDayIndex=0,this[e(6)]=0,this.expandedSectionType=null,await this.$nextTick(),this.refreshVisibleMaps(),this.isPlanningFullyCompleted()&&this.clearPlanningContents()},handleCompletionModalConfirm(){this.showCompletionModal=!1,this.clearPlanningContents(),this.$nextTick(()=>{const e=document.querySelector(".day-navigation");if(e){const A=e.offsetTop-100;window.scrollTo({top:Math.max(0,A),behavior:"smooth"})}})},handleFailureModalConfirm(){this.showFailureModal=!1},showPlanningFailure(e){const A=H;this[A(80)]=e,this.showFailureModal=!0},initFloatingButtons(){typeof window>"u"||(this.handleScroll=this.throttle(()=>{const e=DA,A=window.pageYOffset||document[e(81)].scrollTop,t=document.documentElement.scrollHeight,r=document.documentElement.clientHeight;this.showFloatingButtons=A>200&&t>r+400},100),window.addEventListener("scroll",this.handleScroll,{passive:!0}))},scrollToTop(){const e=H;window.scrollTo({top:0,behavior:e(15)})},scrollToBottom(){window.scrollTo({top:document.documentElement.scrollHeight,behavior:"smooth"})},throttle(e,A){let t;return function(...n){const s=()=>{clearTimeout(t),e.apply(this,n)};clearTimeout(t),t=setTimeout(s,A)}},initThemeObserver(){const e=H;typeof window>"u"||(this[e(82)]=new MutationObserver(A=>{A.forEach(t=>{const r=DA;t.type===r(83)&&t[r(84)]==="class"&&this.updateCustomSuggestTheme()})}),this.themeObserver.observe(document.documentElement,{attributes:!0,attributeFilter:["class"]}))},updateCustomSuggestTheme(){document[H(85)](".custom-amap-suggest").forEach(t=>{iA.updateThemeStyles&&iA.updateThemeStyles(t)})},async handleContentRestoration(e=!0){const A=H;try{await this.restoreMapInstances(),this.restoreScrollManagerState();let t=!1;e&&(t=this.shouldContinuePlanning(),t&&await this.continuePlanning()),(!e||!t)&&(this.restoreCompletedState(),await this.planningFinishProc(),this.showCompletionModal=!0)}catch{this.clearPlanningContents(),this[A(18)]=[],this.selectedDayIndex=-1,this.showBtn=!0,this.resetAllLoadingStates(),alert("恢复规划内容失败，请重新开始规划")}},async restoreMapInstances(){const e=H;if(!(!this.contents||this.contents.length===0)){await this.$nextTick(),await this.$nextTick();try{await iA.initialize()}catch(A){console.warn("地图服务重新初始化失败:",A)}for(let A=0;A<this.contents.length;A++)if(this.contents[A].drivingCompleted===2)try{await new Promise(n=>setTimeout(n,200*A));const r=document.querySelector("#map-container-"+A);r?(r.innerHTML="",await this[e(12)](),await this.regenerateMapRoute(A)):console.warn("第"+(A+1)+"天地图容器不存在")}catch(r){console[e(67)]("地图 "+(A+1)+" 恢复失败:",r)}setTimeout(()=>{this.refreshVisibleMaps(),setTimeout(()=>{this.forceRefreshAllMaps()},3e3)},2e3)}},async regenerateMapRoute(e){const A=H;try{const t=this.contents[e];if(!t||!t.driving){console.warn("第"+(e+1)+"天没有路线规划数据");return}if(this.contents[e].pos.s_location&&this.contents[e].pos.e_location&&this.contents[e].pos.s_location.s_lng&&this[A(18)][e].pos.s_location.s_lat&&this.contents[e].pos.e_location.e_lng&&this.contents[e].pos.e_location.e_lat){const r=parseInt(this.plan_requirements||"0",10);await this.$nextTick(),await this.$nextTick(),await new Promise(n=>setTimeout(n,500)),await this.drivingPlanning(e,this.contents[e].pos.s_location,this[A(18)][e].pos[A(56)],r,this.contents[e].pos.circle_locations),setTimeout(()=>{this.forceMapResize(e)},1e3)}else console.warn("第"+(e+1)+"天坐标信息无效，无法生成地图")}catch(t){console.error("第"+(e+1)+"天地图路线重新生成失败:",t),setTimeout(()=>{const r=A;try{document[r(14)]("#map-container-"+e)&&this.forceMapResize(e)}catch(n){console.error("第"+(e+1)+"天地图resize失败:",n)}},1e3)}},restoreScrollManagerState(){QA.resetScrollState()},isPlanningFullyCompleted(){if(!this[H(18)]||this.contents.length===0)return!1;for(let A=0;A<this.contents.length;A++){const t=this.contents[A],r=["weatherCompleted","rentCompleted","drivingCompleted","viewCompleted","foodCompleted","hotelCompleted","costCompleted"];for(const n of r)if(t[n]===1)return!1;if(this.hasIncompleteStages(t,A))return!1}return!0},shouldContinuePlanning(){return!this.isPlanningFullyCompleted()},hasIncompleteStages(e,A){return A===0&&e.weatherCompleted===0||A===0&&this.travel_mode==="租车"&&e.rentCompleted===0||e.drivingCompleted===0||(A!==this.dates-1||A===0||this.plan_mode==="单程")&&(e.viewCompleted===0||e.foodCompleted===0||e.hotelCompleted===0)},async continuePlanning(){const e=H;try{const A=Pe(),{user:t}=await A.getUserInfo(),r=this.getFormattedDate();let n=xr;n=n.replace(/startDate/g,this.startDate).replace(/s_address/g,this.s_address).replace(/e_address/g,this.e_address).replace(/plan_mode/g,this.plan_mode).replace(/travel_mode/g,this.travel_mode).replace(/dates/g,this.dates);for(let s=0;s<this.contents.length;s++){const a=this[e(18)][s];if(s===0&&a.weatherCompleted!==2)try{await this[e(86)](s,t,r,n)}catch(i){throw console.warn("第"+(s+1)+"天天气规划失败:",i),i}if(s===0&&this.travel_mode==="租车"&&a.rentCompleted!==2)try{await this.planningRent(s,t,r,n)}catch(i){throw console.warn("第"+(s+1)+"天租车规划失败:",i),i}if(a.drivingCompleted!==2){const i=parseInt(this.plan_requirements,10);try{await this.planningDriving(s,t,r,n,i)}catch(l){throw console.warn("第"+(s+1)+"天路线规划失败:",l),l}}if(s!==this.dates-1||s===0||this.plan_mode==="单程"){if(a.viewCompleted!==2)try{await this.planningView(s,t,r,n)}catch(i){throw console.warn("第"+(s+1)+"天景点规划失败:",i),i}if(a.foodCompleted!==2)try{await this.planningFood(s,t,r,n)}catch(i){throw console[e(67)]("第"+(s+1)+"天美食规划失败:",i),i}if(a.hotelCompleted!==2)try{await this.planningHotel(s,t,r,n)}catch(i){throw console.warn("第"+(s+1)+"天住宿规划失败:",i),i}}try{await this.savePlanToDB(s,t.account,r)}catch(i){console[e(67)]("第"+(s+1)+"天存库失败:",i)}}await this.planningFinishProc(),this.showCompletionModal=!0}catch(A){console.error("继续规划失败:",A),this.resetAllLoadingStates(),this.showBtn=!0,this[e(79)]("继续规划失败，请检查网络连接后重试")}},restoreCompletedState(){this.showBtn=!0,this.resetAllLoadingStates(),this.isPlanningFullyCompleted()&&this.clearPlanningContents(),this.contents.length>0&&(this.selectedDayIndex=0,this.$nextTick(()=>{setTimeout(()=>{this.refreshVisibleMaps(),this.contents.forEach((e,A)=>{e.drivingCompleted===2&&setTimeout(()=>{this.forceMapResize(A)},500*(A+1))})},1e3)}))},forceRefreshAllMaps(){this.contents.forEach((e,A)=>{const t=DA;e[t(87)]===2&&setTimeout(()=>{const r=t;try{document.querySelector(r(88)+A)&&(iA.getMapInstance(A)?this.forceMapResize(A):this.regenerateMapRoute(A))}catch(n){console.error(r(89)+(A+1)+r(90),n)}},1e3*(A+1))})}}};function Pr(){const e=["form-progress","location-section","disabled","details-grid","input-wrapper","panel-header","onKeypress","answer-action-input-container","onClick","vitepress-divider","span","step-number","div"," 选择出发地与目的地 ","form-label","currentColor","path"," 目的地 ","datesinput","label-icon","plan_mode","showBtn","isDayCompleted","activeDayDetailIndex","feature-button","weather","handleDetailPanelClick","hotel","expandedSectionType","contents","shouldShowSection","answer-action-input","updateRentRequirements","header-title","重新规划(一次免费机会)","header-icon","🗺️","缩小地图 (ESC)","maximizedMapIndex","map-container","view","section-header food-header","handleSectionHeaderClick","food","hotel_requirements","enter","think","answer-area","strong","btn-icon","handleFailureModalConfirm","failureReason","button"];return Pr=function(){return e},Pr()}const eA=Jr;function Jr(e,A){const t=Pr();return Jr=function(r,n){return r=r-0,t[r]},Jr(e,A)}const W0={class:"global-loading-banner"},Z0={class:"global-loading-content"},z0={class:"global-loading-text"},q0={class:"travel-planning-container"},j0={class:"travel-form"},$0={class:eA(0)},AB={class:eA(1)},eB={class:"location-inputs"},tB={class:"form-item location-item"},rB={class:"input-wrapper"},nB=[eA(2)],sB={class:"form-item location-item"},aB={class:"input-wrapper"},iB=["disabled"],oB={class:"travel-details-section"},lB={class:eA(3)},cB={class:"form-item detail-item"},BB={class:eA(4)},xB=["disabled"],uB={class:"form-item detail-item"},gB={class:"input-wrapper"},hB=[eA(2)],dB=["value","selected"],wB={class:"form-item detail-item"},fB={class:"input-wrapper"},QB=["disabled"],CB={class:"form-item detail-item"},pB={class:"input-wrapper"},UB=[eA(2)],FB={class:"action-section"},mB={class:"button-group"},vB=["disabled"],EB={class:"loading-state"},yB={class:"day-navigation"},HB=["onClick","disabled"],IB={class:"day-number"},bB={class:"day-title"},SB={class:"day-detail-panel"},DB={class:eA(5)},LB={class:"panel-title"},KB={class:"panel-subtitle"},TB={class:"panel-body"},MB={class:"panel-features"},OB=["disabled"],_B=["disabled"],RB=["disabled"],GB=["disabled"],kB=["disabled"],VB=[eA(2)],NB={class:"panel-actions"},PB={id:"scroll-area",class:"scroll-container",ref:"scrollContainer"},JB={"data-dynamic-item":"",class:"answer-area-container"},XB=["onClick"],YB={class:"header-toggle"},WB={class:"section-content"},ZB=["innerHTML"],zB={"data-dynamic-item":"",class:"answer-area-container"},qB=["onClick"],jB={class:"header-toggle"},$B={class:"section-content"},Ax=["innerHTML"],ex={class:"answer-action-input-container"},tx=["value",eA(6)],rx=["onClick"],nx={"data-dynamic-item":"",class:"answer-area-container"},sx=["onClick"],ax={class:"header-toggle"},ix={class:"section-content"},ox=["innerHTML"],lx={class:eA(7)},cx=["value"],Bx=[eA(8)],xx={class:"planning-box"},ux=["onClick"],gx={class:"map-controls"},hx=["onClick","title"],dx={key:0},wx={key:1},fx=["id"],Qx={"data-dynamic-item":"",class:"answer-area-container"},Cx=[eA(8)],px={class:"header-toggle"},Ux={class:"section-content"},Fx={class:"content-wrapper"},mx=["innerHTML"],vx=["src"],Ex=["innerHTML"],yx={"data-dynamic-item":"",class:"answer-area-container"},Hx=["onClick"],Ix={class:"header-toggle"},bx={class:"section-content"},Sx={class:"content-wrapper"},Dx=["innerHTML"],Lx=["src"],Kx=["innerHTML"],Tx={"data-dynamic-item":"",class:"answer-area-container"},Mx=["onClick"],Ox={class:"header-toggle"},_x={class:"section-content"},Rx={class:"content-wrapper"},Gx=["innerHTML"],kx=["href"],Vx=["innerHTML"],Nx={class:"answer-action-input-container"},Px=["value","onKeypress"],Jx=["onClick"],Xx={"data-dynamic-item":"",class:"answer-area-container"},Yx=["innerHTML"],Wx={class:eA(9)},Zx={class:"modal-body"},zx={class:"modal-message"},qx={class:"modal-subtitle"},jx={class:"location"},$x={class:"location"},Au={class:"modal-footer"},eu={class:"modal-body"},tu={class:"modal-message"},ru={class:"modal-subtitle"},nu={class:"modal-footer"};function su(e,A,t,r,n,s){const a=eA;return X(),J(OA,null,[y(B("div",W0,[B("div",Z0,[A[26]||(A[26]=B("div",{class:"global-spinner"},null,-1)),B(a(10),z0,R(e.currentLoadingMessage),1)])],512),[[I,e.isAnyLoading]]),B("div",q0,[A[84]||(A[84]=UA('<div class="page-header" data-v-19315b56><div class="header-content" data-v-19315b56><h1 class="page-title" data-v-19315b56><svg class="title-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" data-v-19315b56><path d="M21 10c0 7-9 13-9 13s-9-6-9-13a9 9 0 0 1 18 0z" data-v-19315b56></path><circle cx="12" cy="10" r="3" data-v-19315b56></circle></svg> 行旅诗笺 </h1><p class="page-subtitle" data-v-19315b56>为您智能定制完美的旅行体验</p></div><div class="header-decoration" data-v-19315b56><div class="decoration-circle" data-v-19315b56></div><div class="decoration-circle" data-v-19315b56></div><div class="decoration-circle" data-v-19315b56></div></div></div>',1)),B("div",j0,[B("div",$0,[B("div",{class:sA(["progress-step",{active:e.currentProgressStep>=1}])},A[27]||(A[27]=[B("div",{class:"step-number"},"1",-1),B("span",{class:"step-label"},"基本信息",-1)]),2),B("div",{class:sA(["progress-line",{active:e.currentProgressStep>=2}])},null,2),B("div",{class:sA(["progress-step",{active:e.currentProgressStep>=2}])},A[28]||(A[28]=[B("div",{class:a(11)},"2",-1),B(a(10),{class:"step-label"},"生成规划",-1)]),2),B("div",{class:sA(["progress-line",{active:e.currentProgressStep>=3}])},null,2),B("div",{class:sA(["progress-step",{active:e.currentProgressStep>=3}])},A[29]||(A[29]=[B("div",{class:"step-number"},"3",-1),B("span",{class:"step-label"},"完成",-1)]),2)]),B(a(12),AB,[A[35]||(A[35]=B("h3",{class:"section-title"},[B("svg",{class:"section-icon",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor","stroke-width":"2"},[B("path",{d:"M21 10c0 7-9 13-9 13s-9-6-9-13a9 9 0 0 1 18 0z"}),B("circle",{cx:"12",cy:"10",r:"3"})]),aA(a(13))],-1)),B("div",eB,[B(a(12),tB,[A[31]||(A[31]=B("label",{for:"start-tipinput",class:a(14)},[B("svg",{class:"label-icon",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor","stroke-width":"2"},[B("circle",{cx:"12",cy:"12",r:"10"}),B("circle",{cx:"12",cy:"12",r:"3"})]),aA(" 出发地 ")],-1)),B("div",rB,[y(B("input",{id:"start-tipinput",class:"form-input","onUpdate:modelValue":A[0]||(A[0]=o=>e.s_address=o),type:"text",placeholder:"请输入出发城市/地点",disabled:!e.showBtn,autocomplete:"off"},null,8,nB),[[Nt,e.s_address]]),A[30]||(A[30]=B("div",{class:"input-decoration"},null,-1))])]),A[34]||(A[34]=B("div",{class:"route-connector"},[B("svg",{viewBox:"0 0 24 24",fill:"none",stroke:"currentColor","stroke-width":"2"},[B("path",{d:"M5 12h14"}),B("path",{d:"m12 5 7 7-7 7"})])],-1)),B("div",sB,[A[33]||(A[33]=B("label",{for:"end-tipinput",class:a(14)},[B("svg",{class:"label-icon",viewBox:"0 0 24 24",fill:"none",stroke:a(15),"stroke-width":"2"},[B(a(16),{d:"M4 15s1-1 4-1 5 2 8 2 4-1 4-1V3s-1 1-4 1-5-2-8-2-4 1-4 1z"}),B("path",{d:"M4 22v-7"})]),aA(a(17))],-1)),B("div",aB,[y(B("input",{id:"end-tipinput",class:"form-input","onUpdate:modelValue":A[1]||(A[1]=o=>e.e_address=o),type:"text",placeholder:"请输入目的地城市/景点",disabled:!e.showBtn,autocomplete:"off"},null,8,iB),[[Nt,e.e_address]]),A[32]||(A[32]=B("div",{class:"input-decoration"},null,-1))])])])]),B("div",oB,[A[46]||(A[46]=UA('<h3 class="section-title" data-v-19315b56><svg class="section-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" data-v-19315b56><rect x="3" y="4" width="18" height="18" rx="2" ry="2" data-v-19315b56></rect><line x1="16" y1="2" x2="16" y2="6" data-v-19315b56></line><line x1="8" y1="2" x2="8" y2="6" data-v-19315b56></line><line x1="3" y1="10" x2="21" y2="10" data-v-19315b56></line></svg> 设置旅行参数 </h3>',1)),B("div",lB,[B("div",cB,[A[37]||(A[37]=UA('<label for="start-dateinput" class="form-label" data-v-19315b56><svg class="label-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" data-v-19315b56><rect x="3" y="4" width="18" height="18" rx="2" ry="2" data-v-19315b56></rect><line x1="16" y1="2" x2="16" y2="6" data-v-19315b56></line><line x1="8" y1="2" x2="8" y2="6" data-v-19315b56></line><line x1="3" y1="10" x2="21" y2="10" data-v-19315b56></line></svg> 出发日期 </label>',1)),B("div",BB,[y(B("input",{type:"date",class:"form-input date-input","onUpdate:modelValue":A[2]||(A[2]=o=>e.startDate=o),disabled:!e.showBtn},null,8,xB),[[Nt,e.startDate]]),A[36]||(A[36]=B("div",{class:"input-decoration"},null,-1))])]),B("div",uB,[A[39]||(A[39]=B("label",{for:a(18),class:"form-label"},[B("svg",{class:a(19),viewBox:"0 0 24 24",fill:"none",stroke:"currentColor","stroke-width":"2"},[B("circle",{cx:"12",cy:"12",r:"10"}),B("polyline",{points:"12,6 12,12 16,14"})]),aA(" 游玩天数 ")],-1)),B("div",gB,[y(B("select",{id:"datesinput",class:"form-input select-input","onUpdate:modelValue":A[3]||(A[3]=o=>e.dates=o),disabled:!e.showBtn},[(X(),J(OA,null,qA(5,o=>B("option",{value:o,key:o,selected:o===3},R(o)+"天",9,dB)),64))],8,hB),[[Je,e.dates,void 0,{number:!0}]]),A[38]||(A[38]=B("div",{class:"input-decoration"},null,-1))])]),B("div",wB,[A[42]||(A[42]=UA('<label for="plan-mode" class="form-label" data-v-19315b56><svg class="label-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" data-v-19315b56><path d="M21 16V8a2 2 0 0 0-1-1.73l-7-4a2 2 0 0 0-2 0l-7 4A2 2 0 0 0 3 8v8a2 2 0 0 0 1 1.73l7 4a2 2 0 0 0 2 0l7-4A2 2 0 0 0 21 16z" data-v-19315b56></path><polyline points="3.27,6.96 12,12.01 20.73,6.96" data-v-19315b56></polyline><line x1="12" y1="22.08" x2="12" y2="12" data-v-19315b56></line></svg> 旅行模式 </label>',1)),B("div",fB,[y(B("select",{id:"plan-mode",class:"form-input select-input","onUpdate:modelValue":A[4]||(A[4]=o=>e.plan_mode=o),disabled:!e.showBtn},A[40]||(A[40]=[B("option",{value:"往返"},"往返旅行",-1),B("option",{value:"单程"},"单程旅行",-1)]),8,QB),[[Je,e[a(20)]]]),A[41]||(A[41]=B("div",{class:"input-decoration"},null,-1))])]),B("div",CB,[A[45]||(A[45]=UA('<label for="travel-mode" class="form-label" data-v-19315b56><svg class="label-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" data-v-19315b56><path d="M7 17m-2 0a2 2 0 1 0 4 0a2 2 0 1 0 -4 0" data-v-19315b56></path><path d="M17 17m-2 0a2 2 0 1 0 4 0a2 2 0 1 0 -4 0" data-v-19315b56></path><path d="M5 17h-2v-6l2-5h9l4 5h1a2 2 0 0 1 2 2v4h-2" data-v-19315b56></path><path d="M9 17v-6h4v6" data-v-19315b56></path><path d="M2 6h15" data-v-19315b56></path></svg> 交通方式 </label>',1)),B("div",pB,[y(B("select",{id:"travel-mode",class:"form-input select-input","onUpdate:modelValue":A[5]||(A[5]=o=>e.travel_mode=o),disabled:!e[a(21)]},A[43]||(A[43]=[B("option",{value:"自驾"},"自驾出行",-1),B("option",{value:"租车"},"租车出行",-1)]),8,UB),[[Je,e.travel_mode]]),A[44]||(A[44]=B("div",{class:"input-decoration"},null,-1))])])])]),B("div",FB,[B("div",mB,[y(B("button",{class:"btn-primary btn-planning",onClick:A[6]||(A[6]=(...o)=>e.planningNew&&e.planningNew(...o))},A[47]||(A[47]=[UA('<div class="btn-content" data-v-19315b56><svg class="btn-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" data-v-19315b56><path d="M4.5 16.5c-1.5 1.5-1.5 4.5 0 6s4.5 1.5 6 0l1-1" data-v-19315b56></path><path d="M15 7l-6.5 6.5a1.5 1.5 0 0 0 0 2.12l.88.88a1.5 1.5 0 0 0 2.12 0L18 10" data-v-19315b56></path><path d="M9 5l8 8" data-v-19315b56></path><path d="M21 3l-6 6" data-v-19315b56></path></svg><span class="btn-text" data-v-19315b56>开始规划</span></div><div class="btn-shine" data-v-19315b56></div>',2)]),512),[[I,e.showBtn]]),y(B("button",{class:"btn-secondary btn-reset",onClick:A[7]||(A[7]=(...o)=>e.resetFormData&&e.resetFormData(...o)),title:"清除所有输入内容",disabled:!e.showBtn},A[48]||(A[48]=[UA('<div class="btn-content" data-v-19315b56><svg class="btn-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" data-v-19315b56><path d="M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8" data-v-19315b56></path><path d="M21 3v5h-5" data-v-19315b56></path><path d="M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16" data-v-19315b56></path><path d="M3 21v-5h5" data-v-19315b56></path></svg><span class="btn-text" data-v-19315b56>重置表单</span></div>',1)]),8,vB),[[I,e.showBtn]])]),y(B("div",EB,A[49]||(A[49]=[UA('<div class="loading-animation" data-v-19315b56><div class="loading-spinner" data-v-19315b56><svg viewBox="0 0 50 50" data-v-19315b56><circle cx="25" cy="25" r="20" fill="none" stroke="currentColor" stroke-width="4" stroke-linecap="round" stroke-dasharray="31.416" stroke-dashoffset="31.416" data-v-19315b56><animate attributeName="stroke-array" dur="2s" values="0 31.416;15.708 15.708;0 31.416" repeatCount="indefinite" data-v-19315b56></animate><animate attributeName="stroke-dashoffset" dur="2s" values="0;-15.708;-31.416" repeatCount="indefinite" data-v-19315b56></animate></circle></svg></div><div class="loading-text" data-v-19315b56><span class="loading-message" data-v-19315b56>正在为您规划完美旅程...</span><div class="loading-dots" data-v-19315b56><span data-v-19315b56></span><span data-v-19315b56></span><span data-v-19315b56></span></div></div></div>',1)]),512),[[I,!e.showBtn]])])]),y(B("div",yB,[(X(!0),J(OA,null,qA(e.contents,(o,i)=>{const l=a;return X(),J("button",{key:"nav-"+i,class:sA(["day-nav-btn",{active:e.activeDayDetailIndex===i,completed:e[l(22)](o),disabled:!e.isDayCompleted(o)}]),onClick:c=>e.selectDay(i),disabled:!e.isDayCompleted(o)},[B("span",IB,"第"+R(i+1)+"天",1),B("span",bB,R(e.getDayTitle(o,i)),1)],10,HB)}),128))],512),[[I,e.contents.length>0]]),y(B("div",SB,[B("div",DB,[A[50]||(A[50]=B("div",{class:"panel-icon"},"✨",-1)),B("h3",LB,"第"+R(e.activeDayDetailIndex+1)+"天行程详情",1),B("p",KB,R(e[a(23)]!==-1&&e.contents[e.activeDayDetailIndex]?e.getDayTitle(e.contents[e.activeDayDetailIndex],e.activeDayDetailIndex):""),1)]),B("div",TB,[B("div",MB,[y(B("button",{class:sA([a(24),{available:e.isSectionAvailable(a(25),e.activeDayDetailIndex),active:e.expandedSectionType==="weather"}]),disabled:!e.isSectionAvailable("weather",e.activeDayDetailIndex),onClick:A[8]||(A[8]=o=>e.handleDetailPanelClick("weather"))},A[51]||(A[51]=[B("span",{class:"feature-icon"},"🌤️",-1),B("span",{class:"feature-text"},"天气",-1)]),10,OB),[[I,e.activeDayDetailIndex===0]]),y(B("button",{class:sA(["feature-button",{available:e.isSectionAvailable("rent",e.activeDayDetailIndex),active:e.expandedSectionType==="rent"}]),disabled:!e.isSectionAvailable("rent",e.activeDayDetailIndex),onClick:A[9]||(A[9]=o=>e.handleDetailPanelClick("rent"))},A[52]||(A[52]=[B("span",{class:"feature-icon"},"🚗",-1),B("span",{class:"feature-text"},"租车",-1)]),10,_B),[[I,e.activeDayDetailIndex===0&&e.travel_mode==="租车"]]),B("button",{class:sA(["feature-button",{available:e.isSectionAvailable("driving",e.activeDayDetailIndex),active:e.expandedSectionType==="driving"}]),disabled:!e.isSectionAvailable("driving",e.activeDayDetailIndex),onClick:A[10]||(A[10]=o=>e[a(26)]("driving"))},A[53]||(A[53]=[B("span",{class:"feature-icon"},"🗺️",-1),B("span",{class:"feature-text"},"路线",-1)]),10,RB),B("button",{class:sA(["feature-button",{available:e.isSectionAvailable("view",e.activeDayDetailIndex),active:e.expandedSectionType==="view"}]),disabled:!e.isSectionAvailable("view",e.activeDayDetailIndex),onClick:A[11]||(A[11]=o=>e.handleDetailPanelClick("view"))},A[54]||(A[54]=[B("span",{class:"feature-icon"},"🏞️",-1),B("span",{class:"feature-text"},"景点",-1)]),10,GB),B("button",{class:sA(["feature-button",{available:e.isSectionAvailable("food",e.activeDayDetailIndex),active:e.expandedSectionType==="food"}]),disabled:!e.isSectionAvailable("food",e.activeDayDetailIndex),onClick:A[12]||(A[12]=o=>e.handleDetailPanelClick("food"))},A[55]||(A[55]=[B("span",{class:"feature-icon"},"🍜",-1),B("span",{class:"feature-text"},"美食",-1)]),10,kB),B("button",{class:sA(["feature-button",{available:e.isSectionAvailable(a(27),e.activeDayDetailIndex),active:e[a(28)]===a(27)}]),disabled:!e.isSectionAvailable("hotel",e.activeDayDetailIndex),onClick:A[13]||(A[13]=o=>e.handleDetailPanelClick("hotel"))},A[56]||(A[56]=[B(a(10),{class:"feature-icon"},"🏨",-1),B("span",{class:"feature-text"},"住宿",-1)]),10,VB)]),B("div",NB,[y(B("button",{class:"collapse-all-btn",onClick:A[14]||(A[14]=(...o)=>e.collapseAll&&e.collapseAll(...o))},A[57]||(A[57]=[B("span",{class:"btn-icon"},"📝",-1),B("span",{class:"btn-text"},"收起全部",-1)]),512),[[I,e.expandedSectionType!==null]])])])],512),[[I,e.activeDayDetailIndex!==-1&&e[a(29)].length>0&&e.contents[e.activeDayDetailIndex]]]),B(a(12),PB,[(X(!0),J(OA,null,qA(e.contents,(o,i)=>{const l=a;return X(),J("div",{key:"day-"+i},[y(B("div",JB,[B("div",{class:"section-header weather-header",onClick:c=>e.handleSectionHeaderClick("weather",i)},[A[58]||(A[58]=B("div",{class:"header-icon"},"🌤️",-1)),A[59]||(A[59]=B("div",{class:"header-title"},"天气信息",-1)),A[60]||(A[60]=B("div",{class:"header-subtitle"},"Weather Information",-1)),y(B("div",YB,"▼",512),[[I,e.shouldShowSection("weather",i)]])],8,XB),y(B("div",WB,[y(B("div",{class:"think-area"},R(o.think),513),[[I,o.weatherCompleted===1]]),B("div",{class:"answer-area",innerHTML:e.parseMarkdown(o.weather)},null,8,ZB)],512),[[I,e[l(30)]("weather",i)]])],512),[[I,e.shouldShowSectionHeader("weather",i)]]),y(B("div",zB,[B("div",{class:"section-header rent-header",onClick:c=>e.handleSectionHeaderClick("rent",i)},[A[61]||(A[61]=B("div",{class:"header-icon"},"🚗",-1)),A[62]||(A[62]=B("div",{class:"header-title"},"租车方案",-1)),A[63]||(A[63]=B("div",{class:"header-subtitle"},"Car Rental",-1)),y(B("div",jB,"▼",512),[[I,e.shouldShowSection("rent",i)]])],8,qB),y(B("div",$B,[y(B("div",{class:"think-area"},R(o.think),513),[[I,o.rentCompleted===1]]),B(l(12),{class:"answer-area",innerHTML:e.parseMarkdown(o.rent)},null,8,Ax),y(B("div",ex,[B("input",{value:e.rent_requirements,type:"text",class:l(31),placeholder:"请输入您的租车需求，我们可以根据您的需求重新定制一次...",onInput:A[15]||(A[15]=(...c)=>e.updateRentRequirements&&e[l(32)](...c)),onKeypress:$r(c=>e.handleRentCustomize(i),["enter"])},null,40,tx)],512),[[I,o.rentCompleted===2&&!e.rent_customized]]),y(B("button",{class:"answer-action-btn",onClick:c=>e.handleActionClick("rent",i)},A[64]||(A[64]=[B("span",{class:"btn-icon"},"🚗",-1),B("span",{class:"btn-text"},"定制",-1)]),8,rx),[[I,o.rentCompleted===2&&!e.rent_customized]])],512),[[I,e[l(30)]("rent",i)]])],512),[[I,e.shouldShowSectionHeader("rent",i)]]),y(B("div",nx,[B("div",{class:"section-header driving-header",onClick:c=>e.handleSectionHeaderClick("driving",i)},[A[65]||(A[65]=B("div",{class:"header-icon"},"🗺️",-1)),A[66]||(A[66]=B("div",{class:l(33)},"路线规划",-1)),A[67]||(A[67]=B("div",{class:"header-subtitle"},"Route Planning",-1)),y(B("div",ax,"▼",512),[[I,e.shouldShowSection("driving",i)]])],8,sx),y(B("div",ix,[y(B("div",{class:"think-area"},R(o.think),513),[[I,o.drivingCompleted===1]]),B("div",{class:"answer-area",innerHTML:e.parseMarkdown(o.driving)},null,8,ox),y(B("div",lx,[y(B("select",{class:"answer-action-input","onUpdate:modelValue":A[16]||(A[16]=c=>e.plan_requirements=c)},[(X(!0),J(OA,null,qA(e.planOptions,c=>(X(),J("option",{key:c.value,value:c.value},R(c.text),9,cx))),128))],512),[[Je,e.plan_requirements]])],512),[[I,o.drivingCompleted===2&&!e.plan_customized]]),y(B("button",{class:"answer-action-btn",onClick:c=>e.handleActionClick("driving",i)},A[68]||(A[68]=[B("span",{class:"btn-icon"},"📍",-1),B("span",{class:"btn-text"},l(34),-1)]),8,Bx),[[I,o.drivingCompleted===2&&!e.plan_customized]])],512),[[I,e.shouldShowSection("driving",i)]])],512),[[I,e.shouldShowSectionHeader("driving",i)]]),y(B("div",xx,[A[69]||(A[69]=B("div",{class:"section-header map-header"},[B("div",{class:l(35)},l(36)),B(l(12),{class:"header-title"},"路线地图"),B("div",{class:"header-subtitle"},"Route Map")],-1)),B("div",{class:sA(["map-wrapper",{"map-maximized":e.maximizedMapIndex===i}])},[e.maximizedMapIndex===i?(X(),J("div",{key:0,class:"map-overlay",onClick:c=>e.toggleMapSize(i)},null,8,ux)):Vt("",!0),B("div",gx,[B("button",{class:"map-control-btn",onClick:c=>e.toggleMapSize(i),title:e.maximizedMapIndex===i?l(37):"最大化地图"},[e[l(38)]===i?(X(),J("span",dx,"✕")):(X(),J("span",wx,"🗖"))],8,hx)]),B("div",{id:"map-container-"+i,class:l(39)},null,8,fx)],2)],512),[[I,e.shouldShowSectionHeader("driving",i)&&"amap"in o&&e.shouldShowSection("driving",i)]]),y(B("div",Qx,[B("div",{class:"section-header view-header",onClick:c=>e.handleSectionHeaderClick(l(40),i)},[A[70]||(A[70]=B("div",{class:"header-icon"},"🏞️",-1)),A[71]||(A[71]=B("div",{class:l(33)},"景点推荐",-1)),A[72]||(A[72]=B("div",{class:"header-subtitle"},"Tourist Attractions",-1)),y(B("div",px,"▼",512),[[I,e.shouldShowSection("view",i)]])],8,Cx),y(B("div",Ux,[B("div",Fx,[y(B("div",{class:"think-area"},R(o.think),513),[[I,o.viewCompleted===1]]),B("div",{class:"answer-area",innerHTML:this.parseMarkdown(`**景点推荐:**

`)},null,8,mx),(X(!0),J(OA,null,qA(o.view,(c,x)=>(X(),J("div",{class:"content-item",key:"view-"+x},[A[73]||(A[73]=B("span",{class:"icon-ori"},"🏞️",-1)),aA(" "+R(c.name)+" ",1),B("img",{src:c.url,alt:"景点图片",class:"view-image"},null,8,vx),B("div",{class:"answer-area",innerHTML:this.parseMarkdown(c.info)},null,8,Ex)]))),128))])],512),[[I,e[l(30)]("view",i)]])],512),[[I,e.shouldShowSectionHeader("view",i)]]),y(B("div",yx,[B("div",{class:l(41),onClick:c=>e[l(42)]("food",i)},[A[74]||(A[74]=B("div",{class:l(35)},"🍜",-1)),A[75]||(A[75]=B("div",{class:"header-title"},"美食推荐",-1)),A[76]||(A[76]=B("div",{class:"header-subtitle"},"Local Cuisine",-1)),y(B("div",Ix,"▼",512),[[I,e.shouldShowSection("food",i)]])],8,Hx),y(B(l(12),bx,[B("div",Sx,[y(B("div",{class:"think-area"},R(o.think),513),[[I,o.foodCompleted===1]]),B("div",{class:"answer-area",innerHTML:this.parseMarkdown(`**美食推荐:**

`)},null,8,Dx),(X(!0),J(OA,null,qA(o[l(43)],(c,x)=>(X(),J("div",{class:"content-item",key:"food-"+x},[A[77]||(A[77]=B("span",{class:"icon-ori"},"🍜",-1)),aA(" "+R(c.name)+" ",1),B("img",{src:c.url,alt:"美食图片",class:"food-image"},null,8,Lx),B("div",{class:"answer-area",innerHTML:this.parseMarkdown(c.info)},null,8,Kx)]))),128))])],512),[[I,e.shouldShowSection("food",i)]])],512),[[I,e.shouldShowSectionHeader("food",i)]]),y(B("div",Tx,[B("div",{class:"section-header hotel-header",onClick:c=>e[l(42)]("hotel",i)},[A[78]||(A[78]=B("div",{class:"header-icon"},"🏨",-1)),A[79]||(A[79]=B("div",{class:"header-title"},"住宿推荐",-1)),A[80]||(A[80]=B("div",{class:"header-subtitle"},"Accommodation",-1)),y(B("div",Ox,"▼",512),[[I,e.shouldShowSection("hotel",i)]])],8,Mx),y(B("div",_x,[B("div",Rx,[y(B(l(12),{class:"think-area"},R(o.think),513),[[I,o.hotelCompleted===1]]),B("div",{class:"answer-area",innerHTML:this.parseMarkdown(`**住宿推荐:**

`)},null,8,Gx),(X(!0),J(OA,null,qA(o.hotel,(c,x)=>(X(),J("div",{class:"content-item",key:"hotel-"+x},[B("a",{href:c.url,target:"_blank",class:"hotel-link"},[A[81]||(A[81]=B("span",{class:"icon-ori"},"🏨",-1)),aA(" "+R("携程直达："+c.name),1)],8,kx),B("div",{class:"answer-area",innerHTML:this.parseMarkdown(c.info)},null,8,Vx)]))),128))]),y(B("div",Nx,[B("input",{value:e[l(44)],type:"text",class:"answer-action-input",placeholder:"请输入您的住宿需求，我们可以根据您的需求重新定制一次...",onInput:A[17]||(A[17]=(...c)=>e.updateHotelRequirements&&e.updateHotelRequirements(...c)),onKeypress:$r(c=>e.handleActionClick("hotel",i),[l(45)])},null,40,Px)],512),[[I,o.hotelCompleted===2&&!e.hotel_customized]]),y(B("button",{class:"answer-action-btn",onClick:c=>e.handleActionClick("hotel",i)},A[82]||(A[82]=[B("span",{class:"btn-icon"},"🏨",-1),B("span",{class:"btn-text"},"定制",-1)]),8,Jx),[[I,o.hotelCompleted===2&&!e.hotel_customized]])],512),[[I,e[l(30)]("hotel",i)]])],512),[[I,e.shouldShowSectionHeader("hotel",i)]]),y(B("div",Xx,[A[83]||(A[83]=B("div",{class:"section-header cost-header"},[B("div",{class:"header-icon"},"💰"),B("div",{class:"header-title"},"费用预算"),B("div",{class:"header-subtitle"},"Budget Planning")],-1)),y(B("div",{class:"think-area"},R(o[l(46)]),513),[[I,o.costCompleted===1]]),B("div",{class:l(47),innerHTML:e.parseMarkdown(o.cost)},null,8,Yx)],512),[[I,e.shouldShowSectionHeader("cost",i)]]),y(B("hr",Wx,null,512),[[I,"cost"in o]])])}),128))],512)]),e.showCompletionModal?(X(),J("div",{key:0,class:"completion-modal-overlay",onClick:A[20]||(A[20]=(...o)=>e.handleCompletionModalConfirm&&e.handleCompletionModalConfirm(...o))},[B("div",{class:"completion-modal",onClick:A[19]||(A[19]=An(()=>{},["stop"]))},[A[92]||(A[92]=B("div",{class:"modal-header"},[B("div",{class:"modal-icon"},"✨"),B("h3",{class:"modal-title"},"规划完成！")],-1)),B("div",Zx,[B("p",zx,[A[85]||(A[85]=aA("您的 ")),B(a(48),null,R(e.dates)+"天"+R(e.plan_mode)+"旅行规划",1),A[86]||(A[86]=aA(" 已经生成完毕！"))]),B("p",qx,[A[87]||(A[87]=aA("从 ")),B("span",jx,R(e.s_address),1),A[88]||(A[88]=aA(" 到 ")),B(a(10),$x,R(e.e_address),1),A[89]||(A[89]=aA(" 的完美行程等您探索"))]),A[90]||(A[90]=UA('<div class="modal-features" data-v-19315b56><div class="feature-item" data-v-19315b56><span class="feature-icon" data-v-19315b56>📍</span><span data-v-19315b56>详细路线规划</span></div><div class="feature-item" data-v-19315b56><span class="feature-icon" data-v-19315b56>🏞️</span><span data-v-19315b56>精选景点推荐</span></div><div class="feature-item" data-v-19315b56><span class="feature-icon" data-v-19315b56>🏨</span><span data-v-19315b56>优质住宿建议</span></div><div class="feature-item" data-v-19315b56><span class="feature-icon" data-v-19315b56>🍜</span><span data-v-19315b56>地道美食指南</span></div></div>',1))]),B("div",Au,[B("button",{class:"modal-confirm-btn",onClick:A[18]||(A[18]=(...o)=>e.handleCompletionModalConfirm&&e.handleCompletionModalConfirm(...o))},A[91]||(A[91]=[B("span",{class:a(49)},"👍",-1)]))])])])):Vt("",!0),e.showFailureModal?(X(),J(a(12),{key:1,class:"failure-modal-overlay",onClick:A[23]||(A[23]=(...o)=>e[a(50)]&&e.handleFailureModalConfirm(...o))},[B("div",{class:"failure-modal",onClick:A[22]||(A[22]=An(()=>{},["stop"]))},[A[97]||(A[97]=B("div",{class:"modal-header failure-header"},[B("div",{class:"modal-icon"},"❌"),B("h3",{class:"modal-title"},"规划失败")],-1)),B("div",eu,[B("p",tu,[A[93]||(A[93]=aA("很抱歉，您的 ")),B("strong",null,R(e.dates)+"天"+R(e.plan_mode)+"旅行规划",1),A[94]||(A[94]=aA(" 生成失败"))]),B("p",ru,R(e[a(51)]),1),A[95]||(A[95]=UA('<div class="failure-suggestions" data-v-19315b56><div class="suggestion-item" data-v-19315b56><span class="suggestion-icon" data-v-19315b56>🔄</span><span data-v-19315b56>检查网络连接后重新尝试</span></div><div class="suggestion-item" data-v-19315b56><span class="suggestion-icon" data-v-19315b56>📝</span><span data-v-19315b56>确认起终点地址填写正确</span></div><div class="suggestion-item" data-v-19315b56><span class="suggestion-icon" data-v-19315b56>⏰</span><span data-v-19315b56>稍后再试，避开网络高峰期</span></div><div class="suggestion-item" data-v-19315b56><span class="suggestion-icon" data-v-19315b56>💬</span><span data-v-19315b56>如持续失败请联系客服</span></div></div>',1))]),B("div",nu,[B("button",{class:"modal-failure-btn",onClick:A[21]||(A[21]=(...o)=>e.handleFailureModalConfirm&&e[a(50)](...o))},A[96]||(A[96]=[B("span",{class:"btn-icon"},"😔",-1),B("span",{class:"btn-text"},"我知道了",-1)]))])])])):Vt("",!0),B(a(12),{class:sA(["floating-scroll-buttons",{show:e.showFloatingButtons}])},[B(a(52),{class:"scroll-btn scroll-to-top",onClick:A[24]||(A[24]=(...o)=>e.scrollToTop&&e.scrollToTop(...o)),title:"滚动到顶部"},A[98]||(A[98]=[B("span",{class:"scroll-btn-icon"},"⬆️",-1)])),B(a(52),{class:"scroll-btn scroll-to-bottom",onClick:A[25]||(A[25]=(...o)=>e.scrollToBottom&&e.scrollToBottom(...o)),title:"滚动到底部"},A[99]||(A[99]=[B("span",{class:"scroll-btn-icon"},"⬇️",-1)]))],2)],64)}const lu=la(Y0,[["render",su],["__scopeId","data-v-19315b56"]]);export{lu as default};
