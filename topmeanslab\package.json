{"name": "topmeanslab", "version": "1.0.0", "private": true, "scripts": {"docs:dev": "vitepress dev", "docs:build": "vitepress build", "docs:build:prod": "NODE_ENV=production vitepress build --mode production", "docs:preview": "vitepress preview", "start:api": "node server/index.js", "start": "concurrently \"npm run docs:dev\" \"npm run start:api\"", "build:production": "NODE_ENV=production vitepress build --mode production", "build:cache-bust": "./scripts/deploy-with-cache-busting.sh", "clear-cache": "rm -rf .vitepress/cache .vitepress/dist node_modules/.vite", "deploy": "npm run build:cache-bust"}, "dependencies": {"@element-plus/icons-vue": "^2.1.0", "axios": "^1.8.4", "bcryptjs": "^3.0.2", "body-parser": "^2.2.0", "cors": "^2.8.5", "dotenv": "^16.5.0", "element-plus": "^2.9.8", "emojis-list": "^3.0.0", "express": "^5.1.0", "express-rate-limit": "^7.5.0", "html2canvas": "^1.4.1", "jsonwebtoken": "^9.0.2", "loglevel": "^1.9.2", "markdown-it": "^14.1.0", "markdown-it-attrs": "^4.3.1", "markdown-it-container": "^4.0.0", "multer": "^1.4.5-lts.2", "mysql2": "^3.14.0", "pinia": "^2.1.0", "vue": "^3.3.0", "vue-toastification": "^2.0.0-rc.5"}, "devDependencies": {"concurrently": "^9.1.2", "install": "^0.13.0", "npm": "^11.2.0", "vite-plugin-javascript-obfuscator": "^3.1.0", "vitepress": "^1.6.3"}}