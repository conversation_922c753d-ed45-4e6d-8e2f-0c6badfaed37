const alipayService = require('../services/alipayService');
const logger = require('../log/logger');

class PaymentController {
  // 创建支付订单
  async createPayment(req, res) {
    try {
      const { amount, subject, serviceType } = req.body;
      const userId = req.user?.userId; // 从认证中间件获取用户ID

      // 参数验证
      if (!amount || amount <= 0) {
        return res.status(400).json({
          success: false,
          message: '支付金额无效'
        });
      }

      if (!userId) {
        return res.status(401).json({
          success: false,
          message: '用户未登录'
        });
      }

      // 创建支付订单
      const result = await alipayService.createPayment({
        amount: parseFloat(amount),
        subject: subject || 'TopMeansLab 服务购买',
        userId,
        serviceType: serviceType || 'premium_service'
      });

      if (result.success) {
        res.json({
          success: true,
          data: result.data
        });
      } else {
        res.status(500).json({
          success: false,
          message: result.message
        });
      }
    } catch (error) {
      logger.error('创建支付订单失败:', error);
      res.status(500).json({
        success: false,
        message: '服务器内部错误'
      });
    }
  }

  // 查询支付状态
  async queryPaymentStatus(req, res) {
    try {
      const { outTradeNo } = req.params;

      if (!outTradeNo) {
        return res.status(400).json({
          success: false,
          message: '订单号不能为空'
        });
      }

      const result = await alipayService.queryPaymentStatus(outTradeNo);

      res.json(result);
    } catch (error) {
      logger.error('查询支付状态失败:', error);
      res.status(500).json({
        success: false,
        message: '服务器内部错误'
      });
    }
  }

  // 处理支付宝异步通知
  async handleAlipayNotify(req, res) {
    try {
      const notifyData = req.body;
      
      logger.info('收到支付宝通知:', notifyData);

      const result = await alipayService.handleNotify(notifyData);

      if (result.success) {
        // 支付成功，可以在这里处理业务逻辑
        // 例如：激活用户服务、发送通知等
        await this.handlePaymentSuccess(result.data);
        
        res.send('success'); // 支付宝要求返回 success
      } else {
        res.send('fail');
      }
    } catch (error) {
      logger.error('处理支付宝通知失败:', error);
      res.send('fail');
    }
  }

  // 处理支付成功后的业务逻辑
  async handlePaymentSuccess(paymentData) {
    try {
      const { outTradeNo, status } = paymentData;
      
      if (status === 'success') {
        const record = alipayService.getPaymentRecord(outTradeNo);
        
        if (record) {
          logger.info('支付成功，处理业务逻辑:', {
            outTradeNo,
            userId: record.userId,
            amount: record.amount,
            serviceType: record.serviceType
          });

          // 这里可以添加具体的业务逻辑
          // 例如：
          // - 激活用户的高级服务
          // - 更新用户账户余额
          // - 发送支付成功通知
          // - 记录到数据库等
        }
      }
    } catch (error) {
      logger.error('处理支付成功业务逻辑失败:', error);
    }
  }

  // 获取支付记录
  async getPaymentRecord(req, res) {
    try {
      const { outTradeNo } = req.params;
      const userId = req.user?.userId;

      if (!outTradeNo) {
        return res.status(400).json({
          success: false,
          message: '订单号不能为空'
        });
      }

      const record = alipayService.getPaymentRecord(outTradeNo);

      if (!record) {
        return res.status(404).json({
          success: false,
          message: '支付记录不存在'
        });
      }

      // 验证用户权限
      if (record.userId !== userId) {
        return res.status(403).json({
          success: false,
          message: '无权访问此支付记录'
        });
      }

      res.json({
        success: true,
        data: {
          outTradeNo: record.outTradeNo,
          amount: record.amount,
          subject: record.subject,
          status: record.status,
          createTime: record.createTime,
          expireTime: record.expireTime
        }
      });
    } catch (error) {
      logger.error('获取支付记录失败:', error);
      res.status(500).json({
        success: false,
        message: '服务器内部错误'
      });
    }
  }

  // 取消支付
  async cancelPayment(req, res) {
    try {
      const { outTradeNo } = req.params;
      const userId = req.user?.userId;

      if (!outTradeNo) {
        return res.status(400).json({
          success: false,
          message: '订单号不能为空'
        });
      }

      const record = alipayService.getPaymentRecord(outTradeNo);

      if (!record) {
        return res.status(404).json({
          success: false,
          message: '支付记录不存在'
        });
      }

      // 验证用户权限
      if (record.userId !== userId) {
        return res.status(403).json({
          success: false,
          message: '无权操作此支付记录'
        });
      }

      // 只能取消待支付状态的订单
      if (record.status !== 'pending') {
        return res.status(400).json({
          success: false,
          message: '订单状态不允许取消'
        });
      }

      // 更新状态为已取消
      record.status = 'cancelled';
      record.cancelTime = new Date();

      logger.info('支付订单已取消:', {
        outTradeNo,
        userId
      });

      res.json({
        success: true,
        message: '支付已取消'
      });
    } catch (error) {
      logger.error('取消支付失败:', error);
      res.status(500).json({
        success: false,
        message: '服务器内部错误'
      });
    }
  }
}

module.exports = new PaymentController();
