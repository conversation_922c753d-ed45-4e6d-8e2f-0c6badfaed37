import{_ as D,u as Ne}from"./theme.CmWpOUCL.js";import{m as aa}from"./TopmeansMarkdownService.HmRBHz74.js";import{c as J,o as X,ao as H,j as c,e as Gt,ap as I,t as G,ab as CA,n as nA,a as sA,aJ as kt,b4 as Pe,F as MA,B as zA,X as qr,Z as jr,_ as ia}from"./framework.B19ydMwb.js";/*!
 * html2canvas 1.4.1 <https://html2canvas.hertzen.com>
 * Copyright (c) 2022 <PERSON><PERSON> <https://hertzen.com>
 * Released under MIT License
 *//*! *****************************************************************************
Copyright (c) Microsoft Corporation.

Permission to use, copy, modify, and/or distribute this software for any
purpose with or without fee is hereby granted.

THE SOFTWARE IS PROVIDED "AS IS" AND THE AUTHOR DISCLAIMS ALL WARRANTIES WITH
REGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY
AND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY SPECIAL, DIRECT,
INDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM
LOSS OF USE, DATA OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE OR
OTHER TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR
PERFORMANCE OF THIS SOFTWARE.
***************************************************************************** */var cr=function(e,A){return cr=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,r){t.__proto__=r}||function(t,r){for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])},cr(e,A)};function fA(e,A){if(typeof A!="function"&&A!==null)throw new TypeError("Class extends value "+String(A)+" is not a constructor or null");cr(e,A);function t(){this.constructor=e}e.prototype=A===null?Object.create(A):(t.prototype=A.prototype,new t)}var xr=function(){return xr=Object.assign||function(A){for(var t,r=1,n=arguments.length;r<n;r++){t=arguments[r];for(var s in t)Object.prototype.hasOwnProperty.call(t,s)&&(A[s]=t[s])}return A},xr.apply(this,arguments)};function AA(e,A,t,r){function n(s){return s instanceof t?s:new t(function(a){a(s)})}return new(t||(t=Promise))(function(s,a){function i(B){try{l(r.next(B))}catch(x){a(x)}}function o(B){try{l(r.throw(B))}catch(x){a(x)}}function l(B){B.done?s(B.value):n(B.value).then(i,o)}l((r=r.apply(e,[])).next())})}function j(e,A){var t={label:0,sent:function(){if(s[0]&1)throw s[1];return s[1]},trys:[],ops:[]},r,n,s,a;return a={next:i(0),throw:i(1),return:i(2)},typeof Symbol=="function"&&(a[Symbol.iterator]=function(){return this}),a;function i(l){return function(B){return o([l,B])}}function o(l){if(r)throw new TypeError("Generator is already executing.");for(;t;)try{if(r=1,n&&(s=l[0]&2?n.return:l[0]?n.throw||((s=n.return)&&s.call(n),0):n.next)&&!(s=s.call(n,l[1])).done)return s;switch(n=0,s&&(l=[l[0]&2,s.value]),l[0]){case 0:case 1:s=l;break;case 4:return t.label++,{value:l[1],done:!1};case 5:t.label++,n=l[1],l=[0];continue;case 7:l=t.ops.pop(),t.trys.pop();continue;default:if(s=t.trys,!(s=s.length>0&&s[s.length-1])&&(l[0]===6||l[0]===2)){t=0;continue}if(l[0]===3&&(!s||l[1]>s[0]&&l[1]<s[3])){t.label=l[1];break}if(l[0]===6&&t.label<s[1]){t.label=s[1],s=l;break}if(s&&t.label<s[2]){t.label=s[2],t.ops.push(l);break}s[2]&&t.ops.pop(),t.trys.pop();continue}l=A.call(e,t)}catch(B){l=[6,B],n=0}finally{r=s=0}if(l[0]&5)throw l[1];return{value:l[0]?l[1]:void 0,done:!0}}}function Je(e,A,t){if(arguments.length===2)for(var r=0,n=A.length,s;r<n;r++)(s||!(r in A))&&(s||(s=Array.prototype.slice.call(A,0,r)),s[r]=A[r]);return e.concat(s||A)}var LA=function(){function e(A,t,r,n){this.left=A,this.top=t,this.width=r,this.height=n}return e.prototype.add=function(A,t,r,n){return new e(this.left+A,this.top+t,this.width+r,this.height+n)},e.fromClientRect=function(A,t){return new e(t.left+A.windowBounds.left,t.top+A.windowBounds.top,t.width,t.height)},e.fromDOMRectList=function(A,t){var r=Array.from(t).find(function(n){return n.width!==0});return r?new e(r.left+A.windowBounds.left,r.top+A.windowBounds.top,r.width,r.height):e.EMPTY},e.EMPTY=new e(0,0,0,0),e}(),bt=function(e,A){return LA.fromClientRect(e,A.getBoundingClientRect())},oa=function(e){var A=e.body,t=e.documentElement;if(!A||!t)throw new Error("Unable to get document size");var r=Math.max(Math.max(A.scrollWidth,t.scrollWidth),Math.max(A.offsetWidth,t.offsetWidth),Math.max(A.clientWidth,t.clientWidth)),n=Math.max(Math.max(A.scrollHeight,t.scrollHeight),Math.max(A.offsetHeight,t.offsetHeight),Math.max(A.clientHeight,t.clientHeight));return new LA(0,0,r,n)},St=function(e){for(var A=[],t=0,r=e.length;t<r;){var n=e.charCodeAt(t++);if(n>=55296&&n<=56319&&t<r){var s=e.charCodeAt(t++);(s&64512)===56320?A.push(((n&1023)<<10)+(s&1023)+65536):(A.push(n),t--)}else A.push(n)}return A},N=function(){for(var e=[],A=0;A<arguments.length;A++)e[A]=arguments[A];if(String.fromCodePoint)return String.fromCodePoint.apply(String,e);var t=e.length;if(!t)return"";for(var r=[],n=-1,s="";++n<t;){var a=e[n];a<=65535?r.push(a):(a-=65536,r.push((a>>10)+55296,a%1024+56320)),(n+1===t||r.length>16384)&&(s+=String.fromCharCode.apply(String,r),r.length=0)}return s},$r="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",la=typeof Uint8Array>"u"?[]:new Uint8Array(256);for(var Xe=0;Xe<$r.length;Xe++)la[$r.charCodeAt(Xe)]=Xe;var An="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",ve=typeof Uint8Array>"u"?[]:new Uint8Array(256);for(var Ye=0;Ye<An.length;Ye++)ve[An.charCodeAt(Ye)]=Ye;var Ba=function(e){var A=e.length*.75,t=e.length,r,n=0,s,a,i,o;e[e.length-1]==="="&&(A--,e[e.length-2]==="="&&A--);var l=typeof ArrayBuffer<"u"&&typeof Uint8Array<"u"&&typeof Uint8Array.prototype.slice<"u"?new ArrayBuffer(A):new Array(A),B=Array.isArray(l)?l:new Uint8Array(l);for(r=0;r<t;r+=4)s=ve[e.charCodeAt(r)],a=ve[e.charCodeAt(r+1)],i=ve[e.charCodeAt(r+2)],o=ve[e.charCodeAt(r+3)],B[n++]=s<<2|a>>4,B[n++]=(a&15)<<4|i>>2,B[n++]=(i&3)<<6|o&63;return l},ca=function(e){for(var A=e.length,t=[],r=0;r<A;r+=2)t.push(e[r+1]<<8|e[r]);return t},xa=function(e){for(var A=e.length,t=[],r=0;r<A;r+=4)t.push(e[r+3]<<24|e[r+2]<<16|e[r+1]<<8|e[r]);return t},ne=5,Pr=11,Vt=2,ua=Pr-ne,rs=65536>>ne,ga=1<<ne,Nt=ga-1,da=1024>>ne,ha=rs+da,wa=ha,fa=32,Qa=wa+fa,Ca=65536>>Pr,pa=1<<ua,Ua=pa-1,en=function(e,A,t){return e.slice?e.slice(A,t):new Uint16Array(Array.prototype.slice.call(e,A,t))},Fa=function(e,A,t){return e.slice?e.slice(A,t):new Uint32Array(Array.prototype.slice.call(e,A,t))},ma=function(e,A){var t=Ba(e),r=Array.isArray(t)?xa(t):new Uint32Array(t),n=Array.isArray(t)?ca(t):new Uint16Array(t),s=24,a=en(n,s/2,r[4]/2),i=r[5]===2?en(n,(s+r[4])/2):Fa(r,Math.ceil((s+r[4])/4));return new va(r[0],r[1],r[2],r[3],a,i)},va=function(){function e(A,t,r,n,s,a){this.initialValue=A,this.errorValue=t,this.highStart=r,this.highValueIndex=n,this.index=s,this.data=a}return e.prototype.get=function(A){var t;if(A>=0){if(A<55296||A>56319&&A<=65535)return t=this.index[A>>ne],t=(t<<Vt)+(A&Nt),this.data[t];if(A<=65535)return t=this.index[rs+(A-55296>>ne)],t=(t<<Vt)+(A&Nt),this.data[t];if(A<this.highStart)return t=Qa-Ca+(A>>Pr),t=this.index[t],t+=A>>ne&Ua,t=this.index[t],t=(t<<Vt)+(A&Nt),this.data[t];if(A<=1114111)return this.data[this.highValueIndex]}return this.errorValue},e}(),tn="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",Ea=typeof Uint8Array>"u"?[]:new Uint8Array(256);for(var We=0;We<tn.length;We++)Ea[tn.charCodeAt(We)]=We;var ya="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",rn=50,Ha=1,ns=2,ss=3,Ia=4,ba=5,nn=7,as=8,sn=9,GA=10,ur=11,an=12,gr=13,Sa=14,Ee=15,dr=16,Ze=17,pe=18,La=19,on=20,hr=21,Ue=22,Pt=23,ie=24,BA=25,ye=26,He=27,oe=28,Da=29,ee=30,Ka=31,ze=32,qe=33,wr=34,fr=35,Qr=36,Oe=37,Cr=38,ht=39,wt=40,Jt=41,is=42,Ta=43,Ma=[9001,65288],os="!",L="×",je="÷",pr=ma(ya),EA=[ee,Qr],Ur=[Ha,ns,ss,ba],ls=[GA,as],ln=[He,ye],Oa=Ur.concat(ls),Bn=[Cr,ht,wt,wr,fr],_a=[Ee,gr],Ra=function(e,A){A===void 0&&(A="strict");var t=[],r=[],n=[];return e.forEach(function(s,a){var i=pr.get(s);if(i>rn?(n.push(!0),i-=rn):n.push(!1),["normal","auto","loose"].indexOf(A)!==-1&&[8208,8211,12316,12448].indexOf(s)!==-1)return r.push(a),t.push(dr);if(i===Ia||i===ur){if(a===0)return r.push(a),t.push(ee);var o=t[a-1];return Oa.indexOf(o)===-1?(r.push(r[a-1]),t.push(o)):(r.push(a),t.push(ee))}if(r.push(a),i===Ka)return t.push(A==="strict"?hr:Oe);if(i===is||i===Da)return t.push(ee);if(i===Ta)return s>=131072&&s<=196605||s>=196608&&s<=262141?t.push(Oe):t.push(ee);t.push(i)}),[r,t,n]},Xt=function(e,A,t,r){var n=r[t];if(Array.isArray(e)?e.indexOf(n)!==-1:e===n)for(var s=t;s<=r.length;){s++;var a=r[s];if(a===A)return!0;if(a!==GA)break}if(n===GA)for(var s=t;s>0;){s--;var i=r[s];if(Array.isArray(e)?e.indexOf(i)!==-1:e===i)for(var o=t;o<=r.length;){o++;var a=r[o];if(a===A)return!0;if(a!==GA)break}if(i!==GA)break}return!1},cn=function(e,A){for(var t=e;t>=0;){var r=A[t];if(r===GA)t--;else return r}return 0},Ga=function(e,A,t,r,n){if(t[r]===0)return L;var s=r-1;if(Array.isArray(n)&&n[s]===!0)return L;var a=s-1,i=s+1,o=A[s],l=a>=0?A[a]:0,B=A[i];if(o===ns&&B===ss)return L;if(Ur.indexOf(o)!==-1)return os;if(Ur.indexOf(B)!==-1||ls.indexOf(B)!==-1)return L;if(cn(s,A)===as)return je;if(pr.get(e[s])===ur||(o===ze||o===qe)&&pr.get(e[i])===ur||o===nn||B===nn||o===sn||[GA,gr,Ee].indexOf(o)===-1&&B===sn||[Ze,pe,La,ie,oe].indexOf(B)!==-1||cn(s,A)===Ue||Xt(Pt,Ue,s,A)||Xt([Ze,pe],hr,s,A)||Xt(an,an,s,A))return L;if(o===GA)return je;if(o===Pt||B===Pt)return L;if(B===dr||o===dr)return je;if([gr,Ee,hr].indexOf(B)!==-1||o===Sa||l===Qr&&_a.indexOf(o)!==-1||o===oe&&B===Qr||B===on||EA.indexOf(B)!==-1&&o===BA||EA.indexOf(o)!==-1&&B===BA||o===He&&[Oe,ze,qe].indexOf(B)!==-1||[Oe,ze,qe].indexOf(o)!==-1&&B===ye||EA.indexOf(o)!==-1&&ln.indexOf(B)!==-1||ln.indexOf(o)!==-1&&EA.indexOf(B)!==-1||[He,ye].indexOf(o)!==-1&&(B===BA||[Ue,Ee].indexOf(B)!==-1&&A[i+1]===BA)||[Ue,Ee].indexOf(o)!==-1&&B===BA||o===BA&&[BA,oe,ie].indexOf(B)!==-1)return L;if([BA,oe,ie,Ze,pe].indexOf(B)!==-1)for(var x=s;x>=0;){var u=A[x];if(u===BA)return L;if([oe,ie].indexOf(u)!==-1)x--;else break}if([He,ye].indexOf(B)!==-1)for(var x=[Ze,pe].indexOf(o)!==-1?a:s;x>=0;){var u=A[x];if(u===BA)return L;if([oe,ie].indexOf(u)!==-1)x--;else break}if(Cr===o&&[Cr,ht,wr,fr].indexOf(B)!==-1||[ht,wr].indexOf(o)!==-1&&[ht,wt].indexOf(B)!==-1||[wt,fr].indexOf(o)!==-1&&B===wt||Bn.indexOf(o)!==-1&&[on,ye].indexOf(B)!==-1||Bn.indexOf(B)!==-1&&o===He||EA.indexOf(o)!==-1&&EA.indexOf(B)!==-1||o===ie&&EA.indexOf(B)!==-1||EA.concat(BA).indexOf(o)!==-1&&B===Ue&&Ma.indexOf(e[i])===-1||EA.concat(BA).indexOf(B)!==-1&&o===pe)return L;if(o===Jt&&B===Jt){for(var h=t[s],g=1;h>0&&(h--,A[h]===Jt);)g++;if(g%2!==0)return L}return o===ze&&B===qe?L:je},ka=function(e,A){A||(A={lineBreak:"normal",wordBreak:"normal"});var t=Ra(e,A.lineBreak),r=t[0],n=t[1],s=t[2];(A.wordBreak==="break-all"||A.wordBreak==="break-word")&&(n=n.map(function(i){return[BA,ee,is].indexOf(i)!==-1?Oe:i}));var a=A.wordBreak==="keep-all"?s.map(function(i,o){return i&&e[o]>=19968&&e[o]<=40959}):void 0;return[r,n,a]},Va=function(){function e(A,t,r,n){this.codePoints=A,this.required=t===os,this.start=r,this.end=n}return e.prototype.slice=function(){return N.apply(void 0,this.codePoints.slice(this.start,this.end))},e}(),Na=function(e,A){var t=St(e),r=ka(t,A),n=r[0],s=r[1],a=r[2],i=t.length,o=0,l=0;return{next:function(){if(l>=i)return{done:!0,value:null};for(var B=L;l<i&&(B=Ga(t,s,n,++l,a))===L;);if(B!==L||l===i){var x=new Va(t,B,o,l);return o=l,{value:x,done:!1}}return{done:!0,value:null}}}},Pa=1,Ja=2,ke=4,xn=8,Ct=10,un=47,Le=92,Xa=9,Ya=32,$e=34,Fe=61,Wa=35,Za=36,za=37,At=39,et=40,me=41,qa=95,iA=45,ja=33,$a=60,Ai=62,ei=64,ti=91,ri=93,ni=61,si=123,tt=63,ai=125,gn=124,ii=126,oi=128,dn=65533,Yt=42,te=43,li=44,Bi=58,ci=59,_e=46,xi=0,ui=8,gi=11,di=14,hi=31,wi=127,pA=-1,Bs=48,cs=97,xs=101,fi=102,Qi=117,Ci=122,us=65,gs=69,ds=70,pi=85,Ui=90,$=function(e){return e>=Bs&&e<=57},Fi=function(e){return e>=55296&&e<=57343},le=function(e){return $(e)||e>=us&&e<=ds||e>=cs&&e<=fi},mi=function(e){return e>=cs&&e<=Ci},vi=function(e){return e>=us&&e<=Ui},Ei=function(e){return mi(e)||vi(e)},yi=function(e){return e>=oi},rt=function(e){return e===Ct||e===Xa||e===Ya},pt=function(e){return Ei(e)||yi(e)||e===qa},hn=function(e){return pt(e)||$(e)||e===iA},Hi=function(e){return e>=xi&&e<=ui||e===gi||e>=di&&e<=hi||e===wi},RA=function(e,A){return e!==Le?!1:A!==Ct},nt=function(e,A,t){return e===iA?pt(A)||RA(A,t):pt(e)?!0:!!(e===Le&&RA(e,A))},Wt=function(e,A,t){return e===te||e===iA?$(A)?!0:A===_e&&$(t):$(e===_e?A:e)},Ii=function(e){var A=0,t=1;(e[A]===te||e[A]===iA)&&(e[A]===iA&&(t=-1),A++);for(var r=[];$(e[A]);)r.push(e[A++]);var n=r.length?parseInt(N.apply(void 0,r),10):0;e[A]===_e&&A++;for(var s=[];$(e[A]);)s.push(e[A++]);var a=s.length,i=a?parseInt(N.apply(void 0,s),10):0;(e[A]===gs||e[A]===xs)&&A++;var o=1;(e[A]===te||e[A]===iA)&&(e[A]===iA&&(o=-1),A++);for(var l=[];$(e[A]);)l.push(e[A++]);var B=l.length?parseInt(N.apply(void 0,l),10):0;return t*(n+i*Math.pow(10,-a))*Math.pow(10,o*B)},bi={type:2},Si={type:3},Li={type:4},Di={type:13},Ki={type:8},Ti={type:21},Mi={type:9},Oi={type:10},_i={type:11},Ri={type:12},Gi={type:14},st={type:23},ki={type:1},Vi={type:25},Ni={type:24},Pi={type:26},Ji={type:27},Xi={type:28},Yi={type:29},Wi={type:31},Fr={type:32},hs=function(){function e(){this._value=[]}return e.prototype.write=function(A){this._value=this._value.concat(St(A))},e.prototype.read=function(){for(var A=[],t=this.consumeToken();t!==Fr;)A.push(t),t=this.consumeToken();return A},e.prototype.consumeToken=function(){var A=this.consumeCodePoint();switch(A){case $e:return this.consumeStringToken($e);case Wa:var t=this.peekCodePoint(0),r=this.peekCodePoint(1),n=this.peekCodePoint(2);if(hn(t)||RA(r,n)){var s=nt(t,r,n)?Ja:Pa,a=this.consumeName();return{type:5,value:a,flags:s}}break;case Za:if(this.peekCodePoint(0)===Fe)return this.consumeCodePoint(),Di;break;case At:return this.consumeStringToken(At);case et:return bi;case me:return Si;case Yt:if(this.peekCodePoint(0)===Fe)return this.consumeCodePoint(),Gi;break;case te:if(Wt(A,this.peekCodePoint(0),this.peekCodePoint(1)))return this.reconsumeCodePoint(A),this.consumeNumericToken();break;case li:return Li;case iA:var i=A,o=this.peekCodePoint(0),l=this.peekCodePoint(1);if(Wt(i,o,l))return this.reconsumeCodePoint(A),this.consumeNumericToken();if(nt(i,o,l))return this.reconsumeCodePoint(A),this.consumeIdentLikeToken();if(o===iA&&l===Ai)return this.consumeCodePoint(),this.consumeCodePoint(),Ni;break;case _e:if(Wt(A,this.peekCodePoint(0),this.peekCodePoint(1)))return this.reconsumeCodePoint(A),this.consumeNumericToken();break;case un:if(this.peekCodePoint(0)===Yt)for(this.consumeCodePoint();;){var B=this.consumeCodePoint();if(B===Yt&&(B=this.consumeCodePoint(),B===un))return this.consumeToken();if(B===pA)return this.consumeToken()}break;case Bi:return Pi;case ci:return Ji;case $a:if(this.peekCodePoint(0)===ja&&this.peekCodePoint(1)===iA&&this.peekCodePoint(2)===iA)return this.consumeCodePoint(),this.consumeCodePoint(),Vi;break;case ei:var x=this.peekCodePoint(0),u=this.peekCodePoint(1),h=this.peekCodePoint(2);if(nt(x,u,h)){var a=this.consumeName();return{type:7,value:a}}break;case ti:return Xi;case Le:if(RA(A,this.peekCodePoint(0)))return this.reconsumeCodePoint(A),this.consumeIdentLikeToken();break;case ri:return Yi;case ni:if(this.peekCodePoint(0)===Fe)return this.consumeCodePoint(),Ki;break;case si:return _i;case ai:return Ri;case Qi:case pi:var g=this.peekCodePoint(0),d=this.peekCodePoint(1);return g===te&&(le(d)||d===tt)&&(this.consumeCodePoint(),this.consumeUnicodeRangeToken()),this.reconsumeCodePoint(A),this.consumeIdentLikeToken();case gn:if(this.peekCodePoint(0)===Fe)return this.consumeCodePoint(),Mi;if(this.peekCodePoint(0)===gn)return this.consumeCodePoint(),Ti;break;case ii:if(this.peekCodePoint(0)===Fe)return this.consumeCodePoint(),Oi;break;case pA:return Fr}return rt(A)?(this.consumeWhiteSpace(),Wi):$(A)?(this.reconsumeCodePoint(A),this.consumeNumericToken()):pt(A)?(this.reconsumeCodePoint(A),this.consumeIdentLikeToken()):{type:6,value:N(A)}},e.prototype.consumeCodePoint=function(){var A=this._value.shift();return typeof A>"u"?-1:A},e.prototype.reconsumeCodePoint=function(A){this._value.unshift(A)},e.prototype.peekCodePoint=function(A){return A>=this._value.length?-1:this._value[A]},e.prototype.consumeUnicodeRangeToken=function(){for(var A=[],t=this.consumeCodePoint();le(t)&&A.length<6;)A.push(t),t=this.consumeCodePoint();for(var r=!1;t===tt&&A.length<6;)A.push(t),t=this.consumeCodePoint(),r=!0;if(r){var n=parseInt(N.apply(void 0,A.map(function(o){return o===tt?Bs:o})),16),s=parseInt(N.apply(void 0,A.map(function(o){return o===tt?ds:o})),16);return{type:30,start:n,end:s}}var a=parseInt(N.apply(void 0,A),16);if(this.peekCodePoint(0)===iA&&le(this.peekCodePoint(1))){this.consumeCodePoint(),t=this.consumeCodePoint();for(var i=[];le(t)&&i.length<6;)i.push(t),t=this.consumeCodePoint();var s=parseInt(N.apply(void 0,i),16);return{type:30,start:a,end:s}}else return{type:30,start:a,end:a}},e.prototype.consumeIdentLikeToken=function(){var A=this.consumeName();return A.toLowerCase()==="url"&&this.peekCodePoint(0)===et?(this.consumeCodePoint(),this.consumeUrlToken()):this.peekCodePoint(0)===et?(this.consumeCodePoint(),{type:19,value:A}):{type:20,value:A}},e.prototype.consumeUrlToken=function(){var A=[];if(this.consumeWhiteSpace(),this.peekCodePoint(0)===pA)return{type:22,value:""};var t=this.peekCodePoint(0);if(t===At||t===$e){var r=this.consumeStringToken(this.consumeCodePoint());return r.type===0&&(this.consumeWhiteSpace(),this.peekCodePoint(0)===pA||this.peekCodePoint(0)===me)?(this.consumeCodePoint(),{type:22,value:r.value}):(this.consumeBadUrlRemnants(),st)}for(;;){var n=this.consumeCodePoint();if(n===pA||n===me)return{type:22,value:N.apply(void 0,A)};if(rt(n))return this.consumeWhiteSpace(),this.peekCodePoint(0)===pA||this.peekCodePoint(0)===me?(this.consumeCodePoint(),{type:22,value:N.apply(void 0,A)}):(this.consumeBadUrlRemnants(),st);if(n===$e||n===At||n===et||Hi(n))return this.consumeBadUrlRemnants(),st;if(n===Le)if(RA(n,this.peekCodePoint(0)))A.push(this.consumeEscapedCodePoint());else return this.consumeBadUrlRemnants(),st;else A.push(n)}},e.prototype.consumeWhiteSpace=function(){for(;rt(this.peekCodePoint(0));)this.consumeCodePoint()},e.prototype.consumeBadUrlRemnants=function(){for(;;){var A=this.consumeCodePoint();if(A===me||A===pA)return;RA(A,this.peekCodePoint(0))&&this.consumeEscapedCodePoint()}},e.prototype.consumeStringSlice=function(A){for(var t=5e4,r="";A>0;){var n=Math.min(t,A);r+=N.apply(void 0,this._value.splice(0,n)),A-=n}return this._value.shift(),r},e.prototype.consumeStringToken=function(A){var t="",r=0;do{var n=this._value[r];if(n===pA||n===void 0||n===A)return t+=this.consumeStringSlice(r),{type:0,value:t};if(n===Ct)return this._value.splice(0,r),ki;if(n===Le){var s=this._value[r+1];s!==pA&&s!==void 0&&(s===Ct?(t+=this.consumeStringSlice(r),r=-1,this._value.shift()):RA(n,s)&&(t+=this.consumeStringSlice(r),t+=N(this.consumeEscapedCodePoint()),r=-1))}r++}while(!0)},e.prototype.consumeNumber=function(){var A=[],t=ke,r=this.peekCodePoint(0);for((r===te||r===iA)&&A.push(this.consumeCodePoint());$(this.peekCodePoint(0));)A.push(this.consumeCodePoint());r=this.peekCodePoint(0);var n=this.peekCodePoint(1);if(r===_e&&$(n))for(A.push(this.consumeCodePoint(),this.consumeCodePoint()),t=xn;$(this.peekCodePoint(0));)A.push(this.consumeCodePoint());r=this.peekCodePoint(0),n=this.peekCodePoint(1);var s=this.peekCodePoint(2);if((r===gs||r===xs)&&((n===te||n===iA)&&$(s)||$(n)))for(A.push(this.consumeCodePoint(),this.consumeCodePoint()),t=xn;$(this.peekCodePoint(0));)A.push(this.consumeCodePoint());return[Ii(A),t]},e.prototype.consumeNumericToken=function(){var A=this.consumeNumber(),t=A[0],r=A[1],n=this.peekCodePoint(0),s=this.peekCodePoint(1),a=this.peekCodePoint(2);if(nt(n,s,a)){var i=this.consumeName();return{type:15,number:t,flags:r,unit:i}}return n===za?(this.consumeCodePoint(),{type:16,number:t,flags:r}):{type:17,number:t,flags:r}},e.prototype.consumeEscapedCodePoint=function(){var A=this.consumeCodePoint();if(le(A)){for(var t=N(A);le(this.peekCodePoint(0))&&t.length<6;)t+=N(this.consumeCodePoint());rt(this.peekCodePoint(0))&&this.consumeCodePoint();var r=parseInt(t,16);return r===0||Fi(r)||r>1114111?dn:r}return A===pA?dn:A},e.prototype.consumeName=function(){for(var A="";;){var t=this.consumeCodePoint();if(hn(t))A+=N(t);else if(RA(t,this.peekCodePoint(0)))A+=N(this.consumeEscapedCodePoint());else return this.reconsumeCodePoint(t),A}},e}(),ws=function(){function e(A){this._tokens=A}return e.create=function(A){var t=new hs;return t.write(A),new e(t.read())},e.parseValue=function(A){return e.create(A).parseComponentValue()},e.parseValues=function(A){return e.create(A).parseComponentValues()},e.prototype.parseComponentValue=function(){for(var A=this.consumeToken();A.type===31;)A=this.consumeToken();if(A.type===32)throw new SyntaxError("Error parsing CSS component value, unexpected EOF");this.reconsumeToken(A);var t=this.consumeComponentValue();do A=this.consumeToken();while(A.type===31);if(A.type===32)return t;throw new SyntaxError("Error parsing CSS component value, multiple values found when expecting only one")},e.prototype.parseComponentValues=function(){for(var A=[];;){var t=this.consumeComponentValue();if(t.type===32)return A;A.push(t),A.push()}},e.prototype.consumeComponentValue=function(){var A=this.consumeToken();switch(A.type){case 11:case 28:case 2:return this.consumeSimpleBlock(A.type);case 19:return this.consumeFunction(A)}return A},e.prototype.consumeSimpleBlock=function(A){for(var t={type:A,values:[]},r=this.consumeToken();;){if(r.type===32||zi(r,A))return t;this.reconsumeToken(r),t.values.push(this.consumeComponentValue()),r=this.consumeToken()}},e.prototype.consumeFunction=function(A){for(var t={name:A.value,values:[],type:18};;){var r=this.consumeToken();if(r.type===32||r.type===3)return t;this.reconsumeToken(r),t.values.push(this.consumeComponentValue())}},e.prototype.consumeToken=function(){var A=this._tokens.shift();return typeof A>"u"?Fr:A},e.prototype.reconsumeToken=function(A){this._tokens.unshift(A)},e}(),Ve=function(e){return e.type===15},Qe=function(e){return e.type===17},O=function(e){return e.type===20},Zi=function(e){return e.type===0},mr=function(e,A){return O(e)&&e.value===A},fs=function(e){return e.type!==31},fe=function(e){return e.type!==31&&e.type!==4},UA=function(e){var A=[],t=[];return e.forEach(function(r){if(r.type===4){if(t.length===0)throw new Error("Error parsing function args, zero tokens for arg");A.push(t),t=[];return}r.type!==31&&t.push(r)}),t.length&&A.push(t),A},zi=function(e,A){return A===11&&e.type===12||A===28&&e.type===29?!0:A===2&&e.type===3},JA=function(e){return e.type===17||e.type===15},P=function(e){return e.type===16||JA(e)},Qs=function(e){return e.length>1?[e[0],e[1]]:[e[0]]},q={type:17,number:0,flags:ke},Jr={type:16,number:50,flags:ke},kA={type:16,number:100,flags:ke},Ie=function(e,A,t){var r=e[0],n=e[1];return[R(r,A),R(typeof n<"u"?n:r,t)]},R=function(e,A){if(e.type===16)return e.number/100*A;if(Ve(e))switch(e.unit){case"rem":case"em":return 16*e.number;case"px":default:return e.number}return e.number},Cs="deg",ps="grad",Us="rad",Fs="turn",Lt={name:"angle",parse:function(e,A){if(A.type===15)switch(A.unit){case Cs:return Math.PI*A.number/180;case ps:return Math.PI/200*A.number;case Us:return A.number;case Fs:return Math.PI*2*A.number}throw new Error("Unsupported angle type")}},ms=function(e){return e.type===15&&(e.unit===Cs||e.unit===ps||e.unit===Us||e.unit===Fs)},vs=function(e){var A=e.filter(O).map(function(t){return t.value}).join(" ");switch(A){case"to bottom right":case"to right bottom":case"left top":case"top left":return[q,q];case"to top":case"bottom":return uA(0);case"to bottom left":case"to left bottom":case"right top":case"top right":return[q,kA];case"to right":case"left":return uA(90);case"to top left":case"to left top":case"right bottom":case"bottom right":return[kA,kA];case"to bottom":case"top":return uA(180);case"to top right":case"to right top":case"left bottom":case"bottom left":return[kA,q];case"to left":case"right":return uA(270)}return 0},uA=function(e){return Math.PI*e/180},NA={name:"color",parse:function(e,A){if(A.type===18){var t=qi[A.name];if(typeof t>"u")throw new Error('Attempting to parse an unsupported color function "'+A.name+'"');return t(e,A.values)}if(A.type===5){if(A.value.length===3){var r=A.value.substring(0,1),n=A.value.substring(1,2),s=A.value.substring(2,3);return VA(parseInt(r+r,16),parseInt(n+n,16),parseInt(s+s,16),1)}if(A.value.length===4){var r=A.value.substring(0,1),n=A.value.substring(1,2),s=A.value.substring(2,3),a=A.value.substring(3,4);return VA(parseInt(r+r,16),parseInt(n+n,16),parseInt(s+s,16),parseInt(a+a,16)/255)}if(A.value.length===6){var r=A.value.substring(0,2),n=A.value.substring(2,4),s=A.value.substring(4,6);return VA(parseInt(r,16),parseInt(n,16),parseInt(s,16),1)}if(A.value.length===8){var r=A.value.substring(0,2),n=A.value.substring(2,4),s=A.value.substring(4,6),a=A.value.substring(6,8);return VA(parseInt(r,16),parseInt(n,16),parseInt(s,16),parseInt(a,16)/255)}}if(A.type===20){var i=SA[A.value.toUpperCase()];if(typeof i<"u")return i}return SA.TRANSPARENT}},PA=function(e){return(255&e)===0},W=function(e){var A=255&e,t=255&e>>8,r=255&e>>16,n=255&e>>24;return A<255?"rgba("+n+","+r+","+t+","+A/255+")":"rgb("+n+","+r+","+t+")"},VA=function(e,A,t,r){return(e<<24|A<<16|t<<8|Math.round(r*255)<<0)>>>0},wn=function(e,A){if(e.type===17)return e.number;if(e.type===16){var t=A===3?1:255;return A===3?e.number/100*t:Math.round(e.number/100*t)}return 0},fn=function(e,A){var t=A.filter(fe);if(t.length===3){var r=t.map(wn),n=r[0],s=r[1],a=r[2];return VA(n,s,a,1)}if(t.length===4){var i=t.map(wn),n=i[0],s=i[1],a=i[2],o=i[3];return VA(n,s,a,o)}return 0};function Zt(e,A,t){return t<0&&(t+=1),t>=1&&(t-=1),t<1/6?(A-e)*t*6+e:t<1/2?A:t<2/3?(A-e)*6*(2/3-t)+e:e}var Qn=function(e,A){var t=A.filter(fe),r=t[0],n=t[1],s=t[2],a=t[3],i=(r.type===17?uA(r.number):Lt.parse(e,r))/(Math.PI*2),o=P(n)?n.number/100:0,l=P(s)?s.number/100:0,B=typeof a<"u"&&P(a)?R(a,1):1;if(o===0)return VA(l*255,l*255,l*255,1);var x=l<=.5?l*(o+1):l+o-l*o,u=l*2-x,h=Zt(u,x,i+1/3),g=Zt(u,x,i),d=Zt(u,x,i-1/3);return VA(h*255,g*255,d*255,B)},qi={hsl:Qn,hsla:Qn,rgb:fn,rgba:fn},De=function(e,A){return NA.parse(e,ws.create(A).parseComponentValue())},SA={ALICEBLUE:4042850303,ANTIQUEWHITE:4209760255,AQUA:16777215,AQUAMARINE:2147472639,AZURE:4043309055,BEIGE:4126530815,BISQUE:4293182719,BLACK:255,BLANCHEDALMOND:4293643775,BLUE:65535,BLUEVIOLET:2318131967,BROWN:2771004159,BURLYWOOD:3736635391,CADETBLUE:1604231423,CHARTREUSE:2147418367,CHOCOLATE:3530104575,CORAL:4286533887,CORNFLOWERBLUE:1687547391,CORNSILK:4294499583,CRIMSON:3692313855,CYAN:16777215,DARKBLUE:35839,DARKCYAN:9145343,DARKGOLDENROD:3095837695,DARKGRAY:2846468607,DARKGREEN:6553855,DARKGREY:2846468607,DARKKHAKI:3182914559,DARKMAGENTA:2332068863,DARKOLIVEGREEN:1433087999,DARKORANGE:4287365375,DARKORCHID:2570243327,DARKRED:2332033279,DARKSALMON:3918953215,DARKSEAGREEN:2411499519,DARKSLATEBLUE:1211993087,DARKSLATEGRAY:793726975,DARKSLATEGREY:793726975,DARKTURQUOISE:13554175,DARKVIOLET:2483082239,DEEPPINK:4279538687,DEEPSKYBLUE:12582911,DIMGRAY:1768516095,DIMGREY:1768516095,DODGERBLUE:512819199,FIREBRICK:2988581631,FLORALWHITE:4294635775,FORESTGREEN:579543807,FUCHSIA:4278255615,GAINSBORO:3705462015,GHOSTWHITE:4177068031,GOLD:4292280575,GOLDENROD:3668254975,GRAY:2155905279,GREEN:8388863,GREENYELLOW:2919182335,GREY:2155905279,HONEYDEW:4043305215,HOTPINK:4285117695,INDIANRED:3445382399,INDIGO:1258324735,IVORY:4294963455,KHAKI:4041641215,LAVENDER:3873897215,LAVENDERBLUSH:4293981695,LAWNGREEN:2096890111,LEMONCHIFFON:4294626815,LIGHTBLUE:2916673279,LIGHTCORAL:4034953471,LIGHTCYAN:3774873599,LIGHTGOLDENRODYELLOW:4210742015,LIGHTGRAY:3553874943,LIGHTGREEN:2431553791,LIGHTGREY:3553874943,LIGHTPINK:4290167295,LIGHTSALMON:4288707327,LIGHTSEAGREEN:548580095,LIGHTSKYBLUE:2278488831,LIGHTSLATEGRAY:2005441023,LIGHTSLATEGREY:2005441023,LIGHTSTEELBLUE:2965692159,LIGHTYELLOW:4294959359,LIME:16711935,LIMEGREEN:852308735,LINEN:4210091775,MAGENTA:4278255615,MAROON:2147483903,MEDIUMAQUAMARINE:1724754687,MEDIUMBLUE:52735,MEDIUMORCHID:3126187007,MEDIUMPURPLE:2473647103,MEDIUMSEAGREEN:1018393087,MEDIUMSLATEBLUE:2070474495,MEDIUMSPRINGGREEN:16423679,MEDIUMTURQUOISE:1221709055,MEDIUMVIOLETRED:3340076543,MIDNIGHTBLUE:421097727,MINTCREAM:4127193855,MISTYROSE:4293190143,MOCCASIN:4293178879,NAVAJOWHITE:4292783615,NAVY:33023,OLDLACE:4260751103,OLIVE:2155872511,OLIVEDRAB:1804477439,ORANGE:4289003775,ORANGERED:4282712319,ORCHID:3664828159,PALEGOLDENROD:4008225535,PALEGREEN:2566625535,PALETURQUOISE:2951671551,PALEVIOLETRED:3681588223,PAPAYAWHIP:4293907967,PEACHPUFF:4292524543,PERU:3448061951,PINK:4290825215,PLUM:3718307327,POWDERBLUE:2967529215,PURPLE:2147516671,REBECCAPURPLE:1714657791,RED:4278190335,ROSYBROWN:3163525119,ROYALBLUE:1097458175,SADDLEBROWN:2336560127,SALMON:4202722047,SANDYBROWN:4104413439,SEAGREEN:780883967,SEASHELL:4294307583,SIENNA:2689740287,SILVER:3233857791,SKYBLUE:2278484991,SLATEBLUE:1784335871,SLATEGRAY:1887473919,SLATEGREY:1887473919,SNOW:4294638335,SPRINGGREEN:16744447,STEELBLUE:1182971135,TAN:3535047935,TEAL:8421631,THISTLE:3636451583,TOMATO:4284696575,TRANSPARENT:0,TURQUOISE:1088475391,VIOLET:4001558271,WHEAT:4125012991,WHITE:4294967295,WHITESMOKE:4126537215,YELLOW:4294902015,YELLOWGREEN:2597139199},ji={name:"background-clip",initialValue:"border-box",prefix:!1,type:1,parse:function(e,A){return A.map(function(t){if(O(t))switch(t.value){case"padding-box":return 1;case"content-box":return 2}return 0})}},$i={name:"background-color",initialValue:"transparent",prefix:!1,type:3,format:"color"},Dt=function(e,A){var t=NA.parse(e,A[0]),r=A[1];return r&&P(r)?{color:t,stop:r}:{color:t,stop:null}},Cn=function(e,A){var t=e[0],r=e[e.length-1];t.stop===null&&(t.stop=q),r.stop===null&&(r.stop=kA);for(var n=[],s=0,a=0;a<e.length;a++){var i=e[a].stop;if(i!==null){var o=R(i,A);o>s?n.push(o):n.push(s),s=o}else n.push(null)}for(var l=null,a=0;a<n.length;a++){var B=n[a];if(B===null)l===null&&(l=a);else if(l!==null){for(var x=a-l,u=n[l-1],h=(B-u)/(x+1),g=1;g<=x;g++)n[l+g-1]=h*g;l=null}}return e.map(function(d,F){var f=d.color;return{color:f,stop:Math.max(Math.min(1,n[F]/A),0)}})},Ao=function(e,A,t){var r=A/2,n=t/2,s=R(e[0],A)-r,a=n-R(e[1],t);return(Math.atan2(a,s)+Math.PI*2)%(Math.PI*2)},eo=function(e,A,t){var r=typeof e=="number"?e:Ao(e,A,t),n=Math.abs(A*Math.sin(r))+Math.abs(t*Math.cos(r)),s=A/2,a=t/2,i=n/2,o=Math.sin(r-Math.PI/2)*i,l=Math.cos(r-Math.PI/2)*i;return[n,s-l,s+l,a-o,a+o]},hA=function(e,A){return Math.sqrt(e*e+A*A)},pn=function(e,A,t,r,n){var s=[[0,0],[0,A],[e,0],[e,A]];return s.reduce(function(a,i){var o=i[0],l=i[1],B=hA(t-o,r-l);return(n?B<a.optimumDistance:B>a.optimumDistance)?{optimumCorner:i,optimumDistance:B}:a},{optimumDistance:n?1/0:-1/0,optimumCorner:null}).optimumCorner},to=function(e,A,t,r,n){var s=0,a=0;switch(e.size){case 0:e.shape===0?s=a=Math.min(Math.abs(A),Math.abs(A-r),Math.abs(t),Math.abs(t-n)):e.shape===1&&(s=Math.min(Math.abs(A),Math.abs(A-r)),a=Math.min(Math.abs(t),Math.abs(t-n)));break;case 2:if(e.shape===0)s=a=Math.min(hA(A,t),hA(A,t-n),hA(A-r,t),hA(A-r,t-n));else if(e.shape===1){var i=Math.min(Math.abs(t),Math.abs(t-n))/Math.min(Math.abs(A),Math.abs(A-r)),o=pn(r,n,A,t,!0),l=o[0],B=o[1];s=hA(l-A,(B-t)/i),a=i*s}break;case 1:e.shape===0?s=a=Math.max(Math.abs(A),Math.abs(A-r),Math.abs(t),Math.abs(t-n)):e.shape===1&&(s=Math.max(Math.abs(A),Math.abs(A-r)),a=Math.max(Math.abs(t),Math.abs(t-n)));break;case 3:if(e.shape===0)s=a=Math.max(hA(A,t),hA(A,t-n),hA(A-r,t),hA(A-r,t-n));else if(e.shape===1){var i=Math.max(Math.abs(t),Math.abs(t-n))/Math.max(Math.abs(A),Math.abs(A-r)),x=pn(r,n,A,t,!1),l=x[0],B=x[1];s=hA(l-A,(B-t)/i),a=i*s}break}return Array.isArray(e.size)&&(s=R(e.size[0],r),a=e.size.length===2?R(e.size[1],n):s),[s,a]},ro=function(e,A){var t=uA(180),r=[];return UA(A).forEach(function(n,s){if(s===0){var a=n[0];if(a.type===20&&a.value==="to"){t=vs(n);return}else if(ms(a)){t=Lt.parse(e,a);return}}var i=Dt(e,n);r.push(i)}),{angle:t,stops:r,type:1}},at=function(e,A){var t=uA(180),r=[];return UA(A).forEach(function(n,s){if(s===0){var a=n[0];if(a.type===20&&["top","left","right","bottom"].indexOf(a.value)!==-1){t=vs(n);return}else if(ms(a)){t=(Lt.parse(e,a)+uA(270))%uA(360);return}}var i=Dt(e,n);r.push(i)}),{angle:t,stops:r,type:1}},no=function(e,A){var t=uA(180),r=[],n=1,s=0,a=3,i=[];return UA(A).forEach(function(o,l){var B=o[0];if(l===0){if(O(B)&&B.value==="linear"){n=1;return}else if(O(B)&&B.value==="radial"){n=2;return}}if(B.type===18){if(B.name==="from"){var x=NA.parse(e,B.values[0]);r.push({stop:q,color:x})}else if(B.name==="to"){var x=NA.parse(e,B.values[0]);r.push({stop:kA,color:x})}else if(B.name==="color-stop"){var u=B.values.filter(fe);if(u.length===2){var x=NA.parse(e,u[1]),h=u[0];Qe(h)&&r.push({stop:{type:16,number:h.number*100,flags:h.flags},color:x})}}}}),n===1?{angle:(t+uA(180))%uA(360),stops:r,type:n}:{size:a,shape:s,stops:r,position:i,type:n}},Es="closest-side",ys="farthest-side",Hs="closest-corner",Is="farthest-corner",bs="circle",Ss="ellipse",Ls="cover",Ds="contain",so=function(e,A){var t=0,r=3,n=[],s=[];return UA(A).forEach(function(a,i){var o=!0;if(i===0){var l=!1;o=a.reduce(function(x,u){if(l)if(O(u))switch(u.value){case"center":return s.push(Jr),x;case"top":case"left":return s.push(q),x;case"right":case"bottom":return s.push(kA),x}else(P(u)||JA(u))&&s.push(u);else if(O(u))switch(u.value){case bs:return t=0,!1;case Ss:return t=1,!1;case"at":return l=!0,!1;case Es:return r=0,!1;case Ls:case ys:return r=1,!1;case Ds:case Hs:return r=2,!1;case Is:return r=3,!1}else if(JA(u)||P(u))return Array.isArray(r)||(r=[]),r.push(u),!1;return x},o)}if(o){var B=Dt(e,a);n.push(B)}}),{size:r,shape:t,stops:n,position:s,type:2}},it=function(e,A){var t=0,r=3,n=[],s=[];return UA(A).forEach(function(a,i){var o=!0;if(i===0?o=a.reduce(function(B,x){if(O(x))switch(x.value){case"center":return s.push(Jr),!1;case"top":case"left":return s.push(q),!1;case"right":case"bottom":return s.push(kA),!1}else if(P(x)||JA(x))return s.push(x),!1;return B},o):i===1&&(o=a.reduce(function(B,x){if(O(x))switch(x.value){case bs:return t=0,!1;case Ss:return t=1,!1;case Ds:case Es:return r=0,!1;case ys:return r=1,!1;case Hs:return r=2,!1;case Ls:case Is:return r=3,!1}else if(JA(x)||P(x))return Array.isArray(r)||(r=[]),r.push(x),!1;return B},o)),o){var l=Dt(e,a);n.push(l)}}),{size:r,shape:t,stops:n,position:s,type:2}},ao=function(e){return e.type===1},io=function(e){return e.type===2},Xr={name:"image",parse:function(e,A){if(A.type===22){var t={url:A.value,type:0};return e.cache.addImage(A.value),t}if(A.type===18){var r=Ks[A.name];if(typeof r>"u")throw new Error('Attempting to parse an unsupported image function "'+A.name+'"');return r(e,A.values)}throw new Error("Unsupported image type "+A.type)}};function oo(e){return!(e.type===20&&e.value==="none")&&(e.type!==18||!!Ks[e.name])}var Ks={"linear-gradient":ro,"-moz-linear-gradient":at,"-ms-linear-gradient":at,"-o-linear-gradient":at,"-webkit-linear-gradient":at,"radial-gradient":so,"-moz-radial-gradient":it,"-ms-radial-gradient":it,"-o-radial-gradient":it,"-webkit-radial-gradient":it,"-webkit-gradient":no},lo={name:"background-image",initialValue:"none",type:1,prefix:!1,parse:function(e,A){if(A.length===0)return[];var t=A[0];return t.type===20&&t.value==="none"?[]:A.filter(function(r){return fe(r)&&oo(r)}).map(function(r){return Xr.parse(e,r)})}},Bo={name:"background-origin",initialValue:"border-box",prefix:!1,type:1,parse:function(e,A){return A.map(function(t){if(O(t))switch(t.value){case"padding-box":return 1;case"content-box":return 2}return 0})}},co={name:"background-position",initialValue:"0% 0%",type:1,prefix:!1,parse:function(e,A){return UA(A).map(function(t){return t.filter(P)}).map(Qs)}},xo={name:"background-repeat",initialValue:"repeat",prefix:!1,type:1,parse:function(e,A){return UA(A).map(function(t){return t.filter(O).map(function(r){return r.value}).join(" ")}).map(uo)}},uo=function(e){switch(e){case"no-repeat":return 1;case"repeat-x":case"repeat no-repeat":return 2;case"repeat-y":case"no-repeat repeat":return 3;case"repeat":default:return 0}},he;(function(e){e.AUTO="auto",e.CONTAIN="contain",e.COVER="cover"})(he||(he={}));var go={name:"background-size",initialValue:"0",prefix:!1,type:1,parse:function(e,A){return UA(A).map(function(t){return t.filter(ho)})}},ho=function(e){return O(e)||P(e)},Kt=function(e){return{name:"border-"+e+"-color",initialValue:"transparent",prefix:!1,type:3,format:"color"}},wo=Kt("top"),fo=Kt("right"),Qo=Kt("bottom"),Co=Kt("left"),Tt=function(e){return{name:"border-radius-"+e,initialValue:"0 0",prefix:!1,type:1,parse:function(A,t){return Qs(t.filter(P))}}},po=Tt("top-left"),Uo=Tt("top-right"),Fo=Tt("bottom-right"),mo=Tt("bottom-left"),Mt=function(e){return{name:"border-"+e+"-style",initialValue:"solid",prefix:!1,type:2,parse:function(A,t){switch(t){case"none":return 0;case"dashed":return 2;case"dotted":return 3;case"double":return 4}return 1}}},vo=Mt("top"),Eo=Mt("right"),yo=Mt("bottom"),Ho=Mt("left"),Ot=function(e){return{name:"border-"+e+"-width",initialValue:"0",type:0,prefix:!1,parse:function(A,t){return Ve(t)?t.number:0}}},Io=Ot("top"),bo=Ot("right"),So=Ot("bottom"),Lo=Ot("left"),Do={name:"color",initialValue:"transparent",prefix:!1,type:3,format:"color"},Ko={name:"direction",initialValue:"ltr",prefix:!1,type:2,parse:function(e,A){switch(A){case"rtl":return 1;case"ltr":default:return 0}}},To={name:"display",initialValue:"inline-block",prefix:!1,type:1,parse:function(e,A){return A.filter(O).reduce(function(t,r){return t|Mo(r.value)},0)}},Mo=function(e){switch(e){case"block":case"-webkit-box":return 2;case"inline":return 4;case"run-in":return 8;case"flow":return 16;case"flow-root":return 32;case"table":return 64;case"flex":case"-webkit-flex":return 128;case"grid":case"-ms-grid":return 256;case"ruby":return 512;case"subgrid":return 1024;case"list-item":return 2048;case"table-row-group":return 4096;case"table-header-group":return 8192;case"table-footer-group":return 16384;case"table-row":return 32768;case"table-cell":return 65536;case"table-column-group":return 131072;case"table-column":return 262144;case"table-caption":return 524288;case"ruby-base":return 1048576;case"ruby-text":return 2097152;case"ruby-base-container":return 4194304;case"ruby-text-container":return 8388608;case"contents":return 16777216;case"inline-block":return 33554432;case"inline-list-item":return 67108864;case"inline-table":return 134217728;case"inline-flex":return 268435456;case"inline-grid":return 536870912}return 0},Oo={name:"float",initialValue:"none",prefix:!1,type:2,parse:function(e,A){switch(A){case"left":return 1;case"right":return 2;case"inline-start":return 3;case"inline-end":return 4}return 0}},_o={name:"letter-spacing",initialValue:"0",prefix:!1,type:0,parse:function(e,A){return A.type===20&&A.value==="normal"?0:A.type===17||A.type===15?A.number:0}},Ut;(function(e){e.NORMAL="normal",e.STRICT="strict"})(Ut||(Ut={}));var Ro={name:"line-break",initialValue:"normal",prefix:!1,type:2,parse:function(e,A){switch(A){case"strict":return Ut.STRICT;case"normal":default:return Ut.NORMAL}}},Go={name:"line-height",initialValue:"normal",prefix:!1,type:4},Un=function(e,A){return O(e)&&e.value==="normal"?1.2*A:e.type===17?A*e.number:P(e)?R(e,A):A},ko={name:"list-style-image",initialValue:"none",type:0,prefix:!1,parse:function(e,A){return A.type===20&&A.value==="none"?null:Xr.parse(e,A)}},Vo={name:"list-style-position",initialValue:"outside",prefix:!1,type:2,parse:function(e,A){switch(A){case"inside":return 0;case"outside":default:return 1}}},vr={name:"list-style-type",initialValue:"none",prefix:!1,type:2,parse:function(e,A){switch(A){case"disc":return 0;case"circle":return 1;case"square":return 2;case"decimal":return 3;case"cjk-decimal":return 4;case"decimal-leading-zero":return 5;case"lower-roman":return 6;case"upper-roman":return 7;case"lower-greek":return 8;case"lower-alpha":return 9;case"upper-alpha":return 10;case"arabic-indic":return 11;case"armenian":return 12;case"bengali":return 13;case"cambodian":return 14;case"cjk-earthly-branch":return 15;case"cjk-heavenly-stem":return 16;case"cjk-ideographic":return 17;case"devanagari":return 18;case"ethiopic-numeric":return 19;case"georgian":return 20;case"gujarati":return 21;case"gurmukhi":return 22;case"hebrew":return 22;case"hiragana":return 23;case"hiragana-iroha":return 24;case"japanese-formal":return 25;case"japanese-informal":return 26;case"kannada":return 27;case"katakana":return 28;case"katakana-iroha":return 29;case"khmer":return 30;case"korean-hangul-formal":return 31;case"korean-hanja-formal":return 32;case"korean-hanja-informal":return 33;case"lao":return 34;case"lower-armenian":return 35;case"malayalam":return 36;case"mongolian":return 37;case"myanmar":return 38;case"oriya":return 39;case"persian":return 40;case"simp-chinese-formal":return 41;case"simp-chinese-informal":return 42;case"tamil":return 43;case"telugu":return 44;case"thai":return 45;case"tibetan":return 46;case"trad-chinese-formal":return 47;case"trad-chinese-informal":return 48;case"upper-armenian":return 49;case"disclosure-open":return 50;case"disclosure-closed":return 51;case"none":default:return-1}}},_t=function(e){return{name:"margin-"+e,initialValue:"0",prefix:!1,type:4}},No=_t("top"),Po=_t("right"),Jo=_t("bottom"),Xo=_t("left"),Yo={name:"overflow",initialValue:"visible",prefix:!1,type:1,parse:function(e,A){return A.filter(O).map(function(t){switch(t.value){case"hidden":return 1;case"scroll":return 2;case"clip":return 3;case"auto":return 4;case"visible":default:return 0}})}},Wo={name:"overflow-wrap",initialValue:"normal",prefix:!1,type:2,parse:function(e,A){switch(A){case"break-word":return"break-word";case"normal":default:return"normal"}}},Rt=function(e){return{name:"padding-"+e,initialValue:"0",prefix:!1,type:3,format:"length-percentage"}},Zo=Rt("top"),zo=Rt("right"),qo=Rt("bottom"),jo=Rt("left"),$o={name:"text-align",initialValue:"left",prefix:!1,type:2,parse:function(e,A){switch(A){case"right":return 2;case"center":case"justify":return 1;case"left":default:return 0}}},Al={name:"position",initialValue:"static",prefix:!1,type:2,parse:function(e,A){switch(A){case"relative":return 1;case"absolute":return 2;case"fixed":return 3;case"sticky":return 4}return 0}},el={name:"text-shadow",initialValue:"none",type:1,prefix:!1,parse:function(e,A){return A.length===1&&mr(A[0],"none")?[]:UA(A).map(function(t){for(var r={color:SA.TRANSPARENT,offsetX:q,offsetY:q,blur:q},n=0,s=0;s<t.length;s++){var a=t[s];JA(a)?(n===0?r.offsetX=a:n===1?r.offsetY=a:r.blur=a,n++):r.color=NA.parse(e,a)}return r})}},tl={name:"text-transform",initialValue:"none",prefix:!1,type:2,parse:function(e,A){switch(A){case"uppercase":return 2;case"lowercase":return 1;case"capitalize":return 3}return 0}},rl={name:"transform",initialValue:"none",prefix:!0,type:0,parse:function(e,A){if(A.type===20&&A.value==="none")return null;if(A.type===18){var t=al[A.name];if(typeof t>"u")throw new Error('Attempting to parse an unsupported transform function "'+A.name+'"');return t(A.values)}return null}},nl=function(e){var A=e.filter(function(t){return t.type===17}).map(function(t){return t.number});return A.length===6?A:null},sl=function(e){var A=e.filter(function(o){return o.type===17}).map(function(o){return o.number}),t=A[0],r=A[1];A[2],A[3];var n=A[4],s=A[5];A[6],A[7],A[8],A[9],A[10],A[11];var a=A[12],i=A[13];return A[14],A[15],A.length===16?[t,r,n,s,a,i]:null},al={matrix:nl,matrix3d:sl},Fn={type:16,number:50,flags:ke},il=[Fn,Fn],ol={name:"transform-origin",initialValue:"50% 50%",prefix:!0,type:1,parse:function(e,A){var t=A.filter(P);return t.length!==2?il:[t[0],t[1]]}},ll={name:"visible",initialValue:"none",prefix:!1,type:2,parse:function(e,A){switch(A){case"hidden":return 1;case"collapse":return 2;case"visible":default:return 0}}},Ke;(function(e){e.NORMAL="normal",e.BREAK_ALL="break-all",e.KEEP_ALL="keep-all"})(Ke||(Ke={}));var Bl={name:"word-break",initialValue:"normal",prefix:!1,type:2,parse:function(e,A){switch(A){case"break-all":return Ke.BREAK_ALL;case"keep-all":return Ke.KEEP_ALL;case"normal":default:return Ke.NORMAL}}},cl={name:"z-index",initialValue:"auto",prefix:!1,type:0,parse:function(e,A){if(A.type===20)return{auto:!0,order:0};if(Qe(A))return{auto:!1,order:A.number};throw new Error("Invalid z-index number parsed")}},Ts={name:"time",parse:function(e,A){if(A.type===15)switch(A.unit.toLowerCase()){case"s":return 1e3*A.number;case"ms":return A.number}throw new Error("Unsupported time type")}},xl={name:"opacity",initialValue:"1",type:0,prefix:!1,parse:function(e,A){return Qe(A)?A.number:1}},ul={name:"text-decoration-color",initialValue:"transparent",prefix:!1,type:3,format:"color"},gl={name:"text-decoration-line",initialValue:"none",prefix:!1,type:1,parse:function(e,A){return A.filter(O).map(function(t){switch(t.value){case"underline":return 1;case"overline":return 2;case"line-through":return 3;case"none":return 4}return 0}).filter(function(t){return t!==0})}},dl={name:"font-family",initialValue:"",prefix:!1,type:1,parse:function(e,A){var t=[],r=[];return A.forEach(function(n){switch(n.type){case 20:case 0:t.push(n.value);break;case 17:t.push(n.number.toString());break;case 4:r.push(t.join(" ")),t.length=0;break}}),t.length&&r.push(t.join(" ")),r.map(function(n){return n.indexOf(" ")===-1?n:"'"+n+"'"})}},hl={name:"font-size",initialValue:"0",prefix:!1,type:3,format:"length"},wl={name:"font-weight",initialValue:"normal",type:0,prefix:!1,parse:function(e,A){if(Qe(A))return A.number;if(O(A))switch(A.value){case"bold":return 700;case"normal":default:return 400}return 400}},fl={name:"font-variant",initialValue:"none",type:1,prefix:!1,parse:function(e,A){return A.filter(O).map(function(t){return t.value})}},Ql={name:"font-style",initialValue:"normal",prefix:!1,type:2,parse:function(e,A){switch(A){case"oblique":return"oblique";case"italic":return"italic";case"normal":default:return"normal"}}},Y=function(e,A){return(e&A)!==0},Cl={name:"content",initialValue:"none",type:1,prefix:!1,parse:function(e,A){if(A.length===0)return[];var t=A[0];return t.type===20&&t.value==="none"?[]:A}},pl={name:"counter-increment",initialValue:"none",prefix:!0,type:1,parse:function(e,A){if(A.length===0)return null;var t=A[0];if(t.type===20&&t.value==="none")return null;for(var r=[],n=A.filter(fs),s=0;s<n.length;s++){var a=n[s],i=n[s+1];if(a.type===20){var o=i&&Qe(i)?i.number:1;r.push({counter:a.value,increment:o})}}return r}},Ul={name:"counter-reset",initialValue:"none",prefix:!0,type:1,parse:function(e,A){if(A.length===0)return[];for(var t=[],r=A.filter(fs),n=0;n<r.length;n++){var s=r[n],a=r[n+1];if(O(s)&&s.value!=="none"){var i=a&&Qe(a)?a.number:0;t.push({counter:s.value,reset:i})}}return t}},Fl={name:"duration",initialValue:"0s",prefix:!1,type:1,parse:function(e,A){return A.filter(Ve).map(function(t){return Ts.parse(e,t)})}},ml={name:"quotes",initialValue:"none",prefix:!0,type:1,parse:function(e,A){if(A.length===0)return null;var t=A[0];if(t.type===20&&t.value==="none")return null;var r=[],n=A.filter(Zi);if(n.length%2!==0)return null;for(var s=0;s<n.length;s+=2){var a=n[s].value,i=n[s+1].value;r.push({open:a,close:i})}return r}},mn=function(e,A,t){if(!e)return"";var r=e[Math.min(A,e.length-1)];return r?t?r.open:r.close:""},vl={name:"box-shadow",initialValue:"none",type:1,prefix:!1,parse:function(e,A){return A.length===1&&mr(A[0],"none")?[]:UA(A).map(function(t){for(var r={color:255,offsetX:q,offsetY:q,blur:q,spread:q,inset:!1},n=0,s=0;s<t.length;s++){var a=t[s];mr(a,"inset")?r.inset=!0:JA(a)?(n===0?r.offsetX=a:n===1?r.offsetY=a:n===2?r.blur=a:r.spread=a,n++):r.color=NA.parse(e,a)}return r})}},El={name:"paint-order",initialValue:"normal",prefix:!1,type:1,parse:function(e,A){var t=[0,1,2],r=[];return A.filter(O).forEach(function(n){switch(n.value){case"stroke":r.push(1);break;case"fill":r.push(0);break;case"markers":r.push(2);break}}),t.forEach(function(n){r.indexOf(n)===-1&&r.push(n)}),r}},yl={name:"-webkit-text-stroke-color",initialValue:"currentcolor",prefix:!1,type:3,format:"color"},Hl={name:"-webkit-text-stroke-width",initialValue:"0",type:0,prefix:!1,parse:function(e,A){return Ve(A)?A.number:0}},Il=function(){function e(A,t){var r,n;this.animationDuration=p(A,Fl,t.animationDuration),this.backgroundClip=p(A,ji,t.backgroundClip),this.backgroundColor=p(A,$i,t.backgroundColor),this.backgroundImage=p(A,lo,t.backgroundImage),this.backgroundOrigin=p(A,Bo,t.backgroundOrigin),this.backgroundPosition=p(A,co,t.backgroundPosition),this.backgroundRepeat=p(A,xo,t.backgroundRepeat),this.backgroundSize=p(A,go,t.backgroundSize),this.borderTopColor=p(A,wo,t.borderTopColor),this.borderRightColor=p(A,fo,t.borderRightColor),this.borderBottomColor=p(A,Qo,t.borderBottomColor),this.borderLeftColor=p(A,Co,t.borderLeftColor),this.borderTopLeftRadius=p(A,po,t.borderTopLeftRadius),this.borderTopRightRadius=p(A,Uo,t.borderTopRightRadius),this.borderBottomRightRadius=p(A,Fo,t.borderBottomRightRadius),this.borderBottomLeftRadius=p(A,mo,t.borderBottomLeftRadius),this.borderTopStyle=p(A,vo,t.borderTopStyle),this.borderRightStyle=p(A,Eo,t.borderRightStyle),this.borderBottomStyle=p(A,yo,t.borderBottomStyle),this.borderLeftStyle=p(A,Ho,t.borderLeftStyle),this.borderTopWidth=p(A,Io,t.borderTopWidth),this.borderRightWidth=p(A,bo,t.borderRightWidth),this.borderBottomWidth=p(A,So,t.borderBottomWidth),this.borderLeftWidth=p(A,Lo,t.borderLeftWidth),this.boxShadow=p(A,vl,t.boxShadow),this.color=p(A,Do,t.color),this.direction=p(A,Ko,t.direction),this.display=p(A,To,t.display),this.float=p(A,Oo,t.cssFloat),this.fontFamily=p(A,dl,t.fontFamily),this.fontSize=p(A,hl,t.fontSize),this.fontStyle=p(A,Ql,t.fontStyle),this.fontVariant=p(A,fl,t.fontVariant),this.fontWeight=p(A,wl,t.fontWeight),this.letterSpacing=p(A,_o,t.letterSpacing),this.lineBreak=p(A,Ro,t.lineBreak),this.lineHeight=p(A,Go,t.lineHeight),this.listStyleImage=p(A,ko,t.listStyleImage),this.listStylePosition=p(A,Vo,t.listStylePosition),this.listStyleType=p(A,vr,t.listStyleType),this.marginTop=p(A,No,t.marginTop),this.marginRight=p(A,Po,t.marginRight),this.marginBottom=p(A,Jo,t.marginBottom),this.marginLeft=p(A,Xo,t.marginLeft),this.opacity=p(A,xl,t.opacity);var s=p(A,Yo,t.overflow);this.overflowX=s[0],this.overflowY=s[s.length>1?1:0],this.overflowWrap=p(A,Wo,t.overflowWrap),this.paddingTop=p(A,Zo,t.paddingTop),this.paddingRight=p(A,zo,t.paddingRight),this.paddingBottom=p(A,qo,t.paddingBottom),this.paddingLeft=p(A,jo,t.paddingLeft),this.paintOrder=p(A,El,t.paintOrder),this.position=p(A,Al,t.position),this.textAlign=p(A,$o,t.textAlign),this.textDecorationColor=p(A,ul,(r=t.textDecorationColor)!==null&&r!==void 0?r:t.color),this.textDecorationLine=p(A,gl,(n=t.textDecorationLine)!==null&&n!==void 0?n:t.textDecoration),this.textShadow=p(A,el,t.textShadow),this.textTransform=p(A,tl,t.textTransform),this.transform=p(A,rl,t.transform),this.transformOrigin=p(A,ol,t.transformOrigin),this.visibility=p(A,ll,t.visibility),this.webkitTextStrokeColor=p(A,yl,t.webkitTextStrokeColor),this.webkitTextStrokeWidth=p(A,Hl,t.webkitTextStrokeWidth),this.wordBreak=p(A,Bl,t.wordBreak),this.zIndex=p(A,cl,t.zIndex)}return e.prototype.isVisible=function(){return this.display>0&&this.opacity>0&&this.visibility===0},e.prototype.isTransparent=function(){return PA(this.backgroundColor)},e.prototype.isTransformed=function(){return this.transform!==null},e.prototype.isPositioned=function(){return this.position!==0},e.prototype.isPositionedWithZIndex=function(){return this.isPositioned()&&!this.zIndex.auto},e.prototype.isFloating=function(){return this.float!==0},e.prototype.isInlineLevel=function(){return Y(this.display,4)||Y(this.display,33554432)||Y(this.display,268435456)||Y(this.display,536870912)||Y(this.display,67108864)||Y(this.display,134217728)},e}(),bl=function(){function e(A,t){this.content=p(A,Cl,t.content),this.quotes=p(A,ml,t.quotes)}return e}(),vn=function(){function e(A,t){this.counterIncrement=p(A,pl,t.counterIncrement),this.counterReset=p(A,Ul,t.counterReset)}return e}(),p=function(e,A,t){var r=new hs,n=t!==null&&typeof t<"u"?t.toString():A.initialValue;r.write(n);var s=new ws(r.read());switch(A.type){case 2:var a=s.parseComponentValue();return A.parse(e,O(a)?a.value:A.initialValue);case 0:return A.parse(e,s.parseComponentValue());case 1:return A.parse(e,s.parseComponentValues());case 4:return s.parseComponentValue();case 3:switch(A.format){case"angle":return Lt.parse(e,s.parseComponentValue());case"color":return NA.parse(e,s.parseComponentValue());case"image":return Xr.parse(e,s.parseComponentValue());case"length":var i=s.parseComponentValue();return JA(i)?i:q;case"length-percentage":var o=s.parseComponentValue();return P(o)?o:q;case"time":return Ts.parse(e,s.parseComponentValue())}break}},Sl="data-html2canvas-debug",Ll=function(e){var A=e.getAttribute(Sl);switch(A){case"all":return 1;case"clone":return 2;case"parse":return 3;case"render":return 4;default:return 0}},Er=function(e,A){var t=Ll(e);return t===1||A===t},FA=function(){function e(A,t){if(this.context=A,this.textNodes=[],this.elements=[],this.flags=0,Er(t,3))debugger;this.styles=new Il(A,window.getComputedStyle(t,null)),Ir(t)&&(this.styles.animationDuration.some(function(r){return r>0})&&(t.style.animationDuration="0s"),this.styles.transform!==null&&(t.style.transform="none")),this.bounds=bt(this.context,t),Er(t,4)&&(this.flags|=16)}return e}(),Dl="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",En="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",be=typeof Uint8Array>"u"?[]:new Uint8Array(256);for(var ot=0;ot<En.length;ot++)be[En.charCodeAt(ot)]=ot;var Kl=function(e){var A=e.length*.75,t=e.length,r,n=0,s,a,i,o;e[e.length-1]==="="&&(A--,e[e.length-2]==="="&&A--);var l=typeof ArrayBuffer<"u"&&typeof Uint8Array<"u"&&typeof Uint8Array.prototype.slice<"u"?new ArrayBuffer(A):new Array(A),B=Array.isArray(l)?l:new Uint8Array(l);for(r=0;r<t;r+=4)s=be[e.charCodeAt(r)],a=be[e.charCodeAt(r+1)],i=be[e.charCodeAt(r+2)],o=be[e.charCodeAt(r+3)],B[n++]=s<<2|a>>4,B[n++]=(a&15)<<4|i>>2,B[n++]=(i&3)<<6|o&63;return l},Tl=function(e){for(var A=e.length,t=[],r=0;r<A;r+=2)t.push(e[r+1]<<8|e[r]);return t},Ml=function(e){for(var A=e.length,t=[],r=0;r<A;r+=4)t.push(e[r+3]<<24|e[r+2]<<16|e[r+1]<<8|e[r]);return t},se=5,Yr=11,zt=2,Ol=Yr-se,Ms=65536>>se,_l=1<<se,qt=_l-1,Rl=1024>>se,Gl=Ms+Rl,kl=Gl,Vl=32,Nl=kl+Vl,Pl=65536>>Yr,Jl=1<<Ol,Xl=Jl-1,yn=function(e,A,t){return e.slice?e.slice(A,t):new Uint16Array(Array.prototype.slice.call(e,A,t))},Yl=function(e,A,t){return e.slice?e.slice(A,t):new Uint32Array(Array.prototype.slice.call(e,A,t))},Wl=function(e,A){var t=Kl(e),r=Array.isArray(t)?Ml(t):new Uint32Array(t),n=Array.isArray(t)?Tl(t):new Uint16Array(t),s=24,a=yn(n,s/2,r[4]/2),i=r[5]===2?yn(n,(s+r[4])/2):Yl(r,Math.ceil((s+r[4])/4));return new Zl(r[0],r[1],r[2],r[3],a,i)},Zl=function(){function e(A,t,r,n,s,a){this.initialValue=A,this.errorValue=t,this.highStart=r,this.highValueIndex=n,this.index=s,this.data=a}return e.prototype.get=function(A){var t;if(A>=0){if(A<55296||A>56319&&A<=65535)return t=this.index[A>>se],t=(t<<zt)+(A&qt),this.data[t];if(A<=65535)return t=this.index[Ms+(A-55296>>se)],t=(t<<zt)+(A&qt),this.data[t];if(A<this.highStart)return t=Nl-Pl+(A>>Yr),t=this.index[t],t+=A>>se&Xl,t=this.index[t],t=(t<<zt)+(A&qt),this.data[t];if(A<=1114111)return this.data[this.highValueIndex]}return this.errorValue},e}(),Hn="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",zl=typeof Uint8Array>"u"?[]:new Uint8Array(256);for(var lt=0;lt<Hn.length;lt++)zl[Hn.charCodeAt(lt)]=lt;var ql=1,jt=2,$t=3,In=4,bn=5,jl=7,Sn=8,Ar=9,er=10,Ln=11,Dn=12,Kn=13,Tn=14,tr=15,$l=function(e){for(var A=[],t=0,r=e.length;t<r;){var n=e.charCodeAt(t++);if(n>=55296&&n<=56319&&t<r){var s=e.charCodeAt(t++);(s&64512)===56320?A.push(((n&1023)<<10)+(s&1023)+65536):(A.push(n),t--)}else A.push(n)}return A},AB=function(){for(var e=[],A=0;A<arguments.length;A++)e[A]=arguments[A];if(String.fromCodePoint)return String.fromCodePoint.apply(String,e);var t=e.length;if(!t)return"";for(var r=[],n=-1,s="";++n<t;){var a=e[n];a<=65535?r.push(a):(a-=65536,r.push((a>>10)+55296,a%1024+56320)),(n+1===t||r.length>16384)&&(s+=String.fromCharCode.apply(String,r),r.length=0)}return s},eB=Wl(Dl),cA="×",rr="÷",tB=function(e){return eB.get(e)},rB=function(e,A,t){var r=t-2,n=A[r],s=A[t-1],a=A[t];if(s===jt&&a===$t)return cA;if(s===jt||s===$t||s===In||a===jt||a===$t||a===In)return rr;if(s===Sn&&[Sn,Ar,Ln,Dn].indexOf(a)!==-1||(s===Ln||s===Ar)&&(a===Ar||a===er)||(s===Dn||s===er)&&a===er||a===Kn||a===bn||a===jl||s===ql)return cA;if(s===Kn&&a===Tn){for(;n===bn;)n=A[--r];if(n===Tn)return cA}if(s===tr&&a===tr){for(var i=0;n===tr;)i++,n=A[--r];if(i%2===0)return cA}return rr},nB=function(e){var A=$l(e),t=A.length,r=0,n=0,s=A.map(tB);return{next:function(){if(r>=t)return{done:!0,value:null};for(var a=cA;r<t&&(a=rB(A,s,++r))===cA;);if(a!==cA||r===t){var i=AB.apply(null,A.slice(n,r));return n=r,{value:i,done:!1}}return{done:!0,value:null}}}},sB=function(e){for(var A=nB(e),t=[],r;!(r=A.next()).done;)r.value&&t.push(r.value.slice());return t},aB=function(e){var A=123;if(e.createRange){var t=e.createRange();if(t.getBoundingClientRect){var r=e.createElement("boundtest");r.style.height=A+"px",r.style.display="block",e.body.appendChild(r),t.selectNode(r);var n=t.getBoundingClientRect(),s=Math.round(n.height);if(e.body.removeChild(r),s===A)return!0}}return!1},iB=function(e){var A=e.createElement("boundtest");A.style.width="50px",A.style.display="block",A.style.fontSize="12px",A.style.letterSpacing="0px",A.style.wordSpacing="0px",e.body.appendChild(A);var t=e.createRange();A.innerHTML=typeof"".repeat=="function"?"&#128104;".repeat(10):"";var r=A.firstChild,n=St(r.data).map(function(o){return N(o)}),s=0,a={},i=n.every(function(o,l){t.setStart(r,s),t.setEnd(r,s+o.length);var B=t.getBoundingClientRect();s+=o.length;var x=B.x>a.x||B.y>a.y;return a=B,l===0?!0:x});return e.body.removeChild(A),i},oB=function(){return typeof new Image().crossOrigin<"u"},lB=function(){return typeof new XMLHttpRequest().responseType=="string"},BB=function(e){var A=new Image,t=e.createElement("canvas"),r=t.getContext("2d");if(!r)return!1;A.src="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg'></svg>";try{r.drawImage(A,0,0),t.toDataURL()}catch{return!1}return!0},Mn=function(e){return e[0]===0&&e[1]===255&&e[2]===0&&e[3]===255},cB=function(e){var A=e.createElement("canvas"),t=100;A.width=t,A.height=t;var r=A.getContext("2d");if(!r)return Promise.reject(!1);r.fillStyle="rgb(0, 255, 0)",r.fillRect(0,0,t,t);var n=new Image,s=A.toDataURL();n.src=s;var a=yr(t,t,0,0,n);return r.fillStyle="red",r.fillRect(0,0,t,t),On(a).then(function(i){r.drawImage(i,0,0);var o=r.getImageData(0,0,t,t).data;r.fillStyle="red",r.fillRect(0,0,t,t);var l=e.createElement("div");return l.style.backgroundImage="url("+s+")",l.style.height=t+"px",Mn(o)?On(yr(t,t,0,0,l)):Promise.reject(!1)}).then(function(i){return r.drawImage(i,0,0),Mn(r.getImageData(0,0,t,t).data)}).catch(function(){return!1})},yr=function(e,A,t,r,n){var s="http://www.w3.org/2000/svg",a=document.createElementNS(s,"svg"),i=document.createElementNS(s,"foreignObject");return a.setAttributeNS(null,"width",e.toString()),a.setAttributeNS(null,"height",A.toString()),i.setAttributeNS(null,"width","100%"),i.setAttributeNS(null,"height","100%"),i.setAttributeNS(null,"x",t.toString()),i.setAttributeNS(null,"y",r.toString()),i.setAttributeNS(null,"externalResourcesRequired","true"),a.appendChild(i),i.appendChild(n),a},On=function(e){return new Promise(function(A,t){var r=new Image;r.onload=function(){return A(r)},r.onerror=t,r.src="data:image/svg+xml;charset=utf-8,"+encodeURIComponent(new XMLSerializer().serializeToString(e))})},z={get SUPPORT_RANGE_BOUNDS(){var e=aB(document);return Object.defineProperty(z,"SUPPORT_RANGE_BOUNDS",{value:e}),e},get SUPPORT_WORD_BREAKING(){var e=z.SUPPORT_RANGE_BOUNDS&&iB(document);return Object.defineProperty(z,"SUPPORT_WORD_BREAKING",{value:e}),e},get SUPPORT_SVG_DRAWING(){var e=BB(document);return Object.defineProperty(z,"SUPPORT_SVG_DRAWING",{value:e}),e},get SUPPORT_FOREIGNOBJECT_DRAWING(){var e=typeof Array.from=="function"&&typeof window.fetch=="function"?cB(document):Promise.resolve(!1);return Object.defineProperty(z,"SUPPORT_FOREIGNOBJECT_DRAWING",{value:e}),e},get SUPPORT_CORS_IMAGES(){var e=oB();return Object.defineProperty(z,"SUPPORT_CORS_IMAGES",{value:e}),e},get SUPPORT_RESPONSE_TYPE(){var e=lB();return Object.defineProperty(z,"SUPPORT_RESPONSE_TYPE",{value:e}),e},get SUPPORT_CORS_XHR(){var e="withCredentials"in new XMLHttpRequest;return Object.defineProperty(z,"SUPPORT_CORS_XHR",{value:e}),e},get SUPPORT_NATIVE_TEXT_SEGMENTATION(){var e=!!(typeof Intl<"u"&&Intl.Segmenter);return Object.defineProperty(z,"SUPPORT_NATIVE_TEXT_SEGMENTATION",{value:e}),e}},Te=function(){function e(A,t){this.text=A,this.bounds=t}return e}(),xB=function(e,A,t,r){var n=dB(A,t),s=[],a=0;return n.forEach(function(i){if(t.textDecorationLine.length||i.trim().length>0)if(z.SUPPORT_RANGE_BOUNDS){var o=_n(r,a,i.length).getClientRects();if(o.length>1){var l=Wr(i),B=0;l.forEach(function(u){s.push(new Te(u,LA.fromDOMRectList(e,_n(r,B+a,u.length).getClientRects()))),B+=u.length})}else s.push(new Te(i,LA.fromDOMRectList(e,o)))}else{var x=r.splitText(i.length);s.push(new Te(i,uB(e,r))),r=x}else z.SUPPORT_RANGE_BOUNDS||(r=r.splitText(i.length));a+=i.length}),s},uB=function(e,A){var t=A.ownerDocument;if(t){var r=t.createElement("html2canvaswrapper");r.appendChild(A.cloneNode(!0));var n=A.parentNode;if(n){n.replaceChild(r,A);var s=bt(e,r);return r.firstChild&&n.replaceChild(r.firstChild,r),s}}return LA.EMPTY},_n=function(e,A,t){var r=e.ownerDocument;if(!r)throw new Error("Node has no owner document");var n=r.createRange();return n.setStart(e,A),n.setEnd(e,A+t),n},Wr=function(e){if(z.SUPPORT_NATIVE_TEXT_SEGMENTATION){var A=new Intl.Segmenter(void 0,{granularity:"grapheme"});return Array.from(A.segment(e)).map(function(t){return t.segment})}return sB(e)},gB=function(e,A){if(z.SUPPORT_NATIVE_TEXT_SEGMENTATION){var t=new Intl.Segmenter(void 0,{granularity:"word"});return Array.from(t.segment(e)).map(function(r){return r.segment})}return wB(e,A)},dB=function(e,A){return A.letterSpacing!==0?Wr(e):gB(e,A)},hB=[32,160,4961,65792,65793,4153,4241],wB=function(e,A){for(var t=Na(e,{lineBreak:A.lineBreak,wordBreak:A.overflowWrap==="break-word"?"break-word":A.wordBreak}),r=[],n,s=function(){if(n.value){var a=n.value.slice(),i=St(a),o="";i.forEach(function(l){hB.indexOf(l)===-1?o+=N(l):(o.length&&r.push(o),r.push(N(l)),o="")}),o.length&&r.push(o)}};!(n=t.next()).done;)s();return r},fB=function(){function e(A,t,r){this.text=QB(t.data,r.textTransform),this.textBounds=xB(A,this.text,r,t)}return e}(),QB=function(e,A){switch(A){case 1:return e.toLowerCase();case 3:return e.replace(CB,pB);case 2:return e.toUpperCase();default:return e}},CB=/(^|\s|:|-|\(|\))([a-z])/g,pB=function(e,A,t){return e.length>0?A+t.toUpperCase():e},Os=function(e){fA(A,e);function A(t,r){var n=e.call(this,t,r)||this;return n.src=r.currentSrc||r.src,n.intrinsicWidth=r.naturalWidth,n.intrinsicHeight=r.naturalHeight,n.context.cache.addImage(n.src),n}return A}(FA),_s=function(e){fA(A,e);function A(t,r){var n=e.call(this,t,r)||this;return n.canvas=r,n.intrinsicWidth=r.width,n.intrinsicHeight=r.height,n}return A}(FA),Rs=function(e){fA(A,e);function A(t,r){var n=e.call(this,t,r)||this,s=new XMLSerializer,a=bt(t,r);return r.setAttribute("width",a.width+"px"),r.setAttribute("height",a.height+"px"),n.svg="data:image/svg+xml,"+encodeURIComponent(s.serializeToString(r)),n.intrinsicWidth=r.width.baseVal.value,n.intrinsicHeight=r.height.baseVal.value,n.context.cache.addImage(n.svg),n}return A}(FA),Gs=function(e){fA(A,e);function A(t,r){var n=e.call(this,t,r)||this;return n.value=r.value,n}return A}(FA),Hr=function(e){fA(A,e);function A(t,r){var n=e.call(this,t,r)||this;return n.start=r.start,n.reversed=typeof r.reversed=="boolean"&&r.reversed===!0,n}return A}(FA),UB=[{type:15,flags:0,unit:"px",number:3}],FB=[{type:16,flags:0,number:50}],mB=function(e){return e.width>e.height?new LA(e.left+(e.width-e.height)/2,e.top,e.height,e.height):e.width<e.height?new LA(e.left,e.top+(e.height-e.width)/2,e.width,e.width):e},vB=function(e){var A=e.type===EB?new Array(e.value.length+1).join("•"):e.value;return A.length===0?e.placeholder||"":A},Ft="checkbox",mt="radio",EB="password",Rn=707406591,Zr=function(e){fA(A,e);function A(t,r){var n=e.call(this,t,r)||this;switch(n.type=r.type.toLowerCase(),n.checked=r.checked,n.value=vB(r),(n.type===Ft||n.type===mt)&&(n.styles.backgroundColor=3739148031,n.styles.borderTopColor=n.styles.borderRightColor=n.styles.borderBottomColor=n.styles.borderLeftColor=2779096575,n.styles.borderTopWidth=n.styles.borderRightWidth=n.styles.borderBottomWidth=n.styles.borderLeftWidth=1,n.styles.borderTopStyle=n.styles.borderRightStyle=n.styles.borderBottomStyle=n.styles.borderLeftStyle=1,n.styles.backgroundClip=[0],n.styles.backgroundOrigin=[0],n.bounds=mB(n.bounds)),n.type){case Ft:n.styles.borderTopRightRadius=n.styles.borderTopLeftRadius=n.styles.borderBottomRightRadius=n.styles.borderBottomLeftRadius=UB;break;case mt:n.styles.borderTopRightRadius=n.styles.borderTopLeftRadius=n.styles.borderBottomRightRadius=n.styles.borderBottomLeftRadius=FB;break}return n}return A}(FA),ks=function(e){fA(A,e);function A(t,r){var n=e.call(this,t,r)||this,s=r.options[r.selectedIndex||0];return n.value=s&&s.text||"",n}return A}(FA),Vs=function(e){fA(A,e);function A(t,r){var n=e.call(this,t,r)||this;return n.value=r.value,n}return A}(FA),Ns=function(e){fA(A,e);function A(t,r){var n=e.call(this,t,r)||this;n.src=r.src,n.width=parseInt(r.width,10)||0,n.height=parseInt(r.height,10)||0,n.backgroundColor=n.styles.backgroundColor;try{if(r.contentWindow&&r.contentWindow.document&&r.contentWindow.document.documentElement){n.tree=Js(t,r.contentWindow.document.documentElement);var s=r.contentWindow.document.documentElement?De(t,getComputedStyle(r.contentWindow.document.documentElement).backgroundColor):SA.TRANSPARENT,a=r.contentWindow.document.body?De(t,getComputedStyle(r.contentWindow.document.body).backgroundColor):SA.TRANSPARENT;n.backgroundColor=PA(s)?PA(a)?n.styles.backgroundColor:a:s}}catch{}return n}return A}(FA),yB=["OL","UL","MENU"],ft=function(e,A,t,r){for(var n=A.firstChild,s=void 0;n;n=s)if(s=n.nextSibling,Xs(n)&&n.data.trim().length>0)t.textNodes.push(new fB(e,n,t.styles));else if(de(n))if(zs(n)&&n.assignedNodes)n.assignedNodes().forEach(function(i){return ft(e,i,t,r)});else{var a=Ps(e,n);a.styles.isVisible()&&(HB(n,a,r)?a.flags|=4:IB(a.styles)&&(a.flags|=2),yB.indexOf(n.tagName)!==-1&&(a.flags|=8),t.elements.push(a),n.slot,n.shadowRoot?ft(e,n.shadowRoot,a,r):!vt(n)&&!Ys(n)&&!Et(n)&&ft(e,n,a,r))}},Ps=function(e,A){return br(A)?new Os(e,A):Ws(A)?new _s(e,A):Ys(A)?new Rs(e,A):bB(A)?new Gs(e,A):SB(A)?new Hr(e,A):LB(A)?new Zr(e,A):Et(A)?new ks(e,A):vt(A)?new Vs(e,A):Zs(A)?new Ns(e,A):new FA(e,A)},Js=function(e,A){var t=Ps(e,A);return t.flags|=4,ft(e,A,t,t),t},HB=function(e,A,t){return A.styles.isPositionedWithZIndex()||A.styles.opacity<1||A.styles.isTransformed()||zr(e)&&t.styles.isTransparent()},IB=function(e){return e.isPositioned()||e.isFloating()},Xs=function(e){return e.nodeType===Node.TEXT_NODE},de=function(e){return e.nodeType===Node.ELEMENT_NODE},Ir=function(e){return de(e)&&typeof e.style<"u"&&!Qt(e)},Qt=function(e){return typeof e.className=="object"},bB=function(e){return e.tagName==="LI"},SB=function(e){return e.tagName==="OL"},LB=function(e){return e.tagName==="INPUT"},DB=function(e){return e.tagName==="HTML"},Ys=function(e){return e.tagName==="svg"},zr=function(e){return e.tagName==="BODY"},Ws=function(e){return e.tagName==="CANVAS"},Gn=function(e){return e.tagName==="VIDEO"},br=function(e){return e.tagName==="IMG"},Zs=function(e){return e.tagName==="IFRAME"},kn=function(e){return e.tagName==="STYLE"},KB=function(e){return e.tagName==="SCRIPT"},vt=function(e){return e.tagName==="TEXTAREA"},Et=function(e){return e.tagName==="SELECT"},zs=function(e){return e.tagName==="SLOT"},Vn=function(e){return e.tagName.indexOf("-")>0},TB=function(){function e(){this.counters={}}return e.prototype.getCounterValue=function(A){var t=this.counters[A];return t&&t.length?t[t.length-1]:1},e.prototype.getCounterValues=function(A){var t=this.counters[A];return t||[]},e.prototype.pop=function(A){var t=this;A.forEach(function(r){return t.counters[r].pop()})},e.prototype.parse=function(A){var t=this,r=A.counterIncrement,n=A.counterReset,s=!0;r!==null&&r.forEach(function(i){var o=t.counters[i.counter];o&&i.increment!==0&&(s=!1,o.length||o.push(1),o[Math.max(0,o.length-1)]+=i.increment)});var a=[];return s&&n.forEach(function(i){var o=t.counters[i.counter];a.push(i.counter),o||(o=t.counters[i.counter]=[]),o.push(i.reset)}),a},e}(),Nn={integers:[1e3,900,500,400,100,90,50,40,10,9,5,4,1],values:["M","CM","D","CD","C","XC","L","XL","X","IX","V","IV","I"]},Pn={integers:[9e3,8e3,7e3,6e3,5e3,4e3,3e3,2e3,1e3,900,800,700,600,500,400,300,200,100,90,80,70,60,50,40,30,20,10,9,8,7,6,5,4,3,2,1],values:["Ք","Փ","Ւ","Ց","Ր","Տ","Վ","Ս","Ռ","Ջ","Պ","Չ","Ո","Շ","Ն","Յ","Մ","Ճ","Ղ","Ձ","Հ","Կ","Ծ","Խ","Լ","Ի","Ժ","Թ","Ը","Է","Զ","Ե","Դ","Գ","Բ","Ա"]},MB={integers:[1e4,9e3,8e3,7e3,6e3,5e3,4e3,3e3,2e3,1e3,400,300,200,100,90,80,70,60,50,40,30,20,19,18,17,16,15,10,9,8,7,6,5,4,3,2,1],values:["י׳","ט׳","ח׳","ז׳","ו׳","ה׳","ד׳","ג׳","ב׳","א׳","ת","ש","ר","ק","צ","פ","ע","ס","נ","מ","ל","כ","יט","יח","יז","טז","טו","י","ט","ח","ז","ו","ה","ד","ג","ב","א"]},OB={integers:[1e4,9e3,8e3,7e3,6e3,5e3,4e3,3e3,2e3,1e3,900,800,700,600,500,400,300,200,100,90,80,70,60,50,40,30,20,10,9,8,7,6,5,4,3,2,1],values:["ჵ","ჰ","ჯ","ჴ","ხ","ჭ","წ","ძ","ც","ჩ","შ","ყ","ღ","ქ","ფ","ჳ","ტ","ს","რ","ჟ","პ","ო","ჲ","ნ","მ","ლ","კ","ი","თ","ჱ","ზ","ვ","ე","დ","გ","ბ","ა"]},Be=function(e,A,t,r,n,s){return e<A||e>t?Re(e,n,s.length>0):r.integers.reduce(function(a,i,o){for(;e>=i;)e-=i,a+=r.values[o];return a},"")+s},qs=function(e,A,t,r){var n="";do t||e--,n=r(e)+n,e/=A;while(e*A>=A);return n},V=function(e,A,t,r,n){var s=t-A+1;return(e<0?"-":"")+(qs(Math.abs(e),s,r,function(a){return N(Math.floor(a%s)+A)})+n)},qA=function(e,A,t){t===void 0&&(t=". ");var r=A.length;return qs(Math.abs(e),r,!1,function(n){return A[Math.floor(n%r)]})+t},ue=1,OA=2,_A=4,Se=8,yA=function(e,A,t,r,n,s){if(e<-9999||e>9999)return Re(e,4,n.length>0);var a=Math.abs(e),i=n;if(a===0)return A[0]+i;for(var o=0;a>0&&o<=4;o++){var l=a%10;l===0&&Y(s,ue)&&i!==""?i=A[l]+i:l>1||l===1&&o===0||l===1&&o===1&&Y(s,OA)||l===1&&o===1&&Y(s,_A)&&e>100||l===1&&o>1&&Y(s,Se)?i=A[l]+(o>0?t[o-1]:"")+i:l===1&&o>0&&(i=t[o-1]+i),a=Math.floor(a/10)}return(e<0?r:"")+i},Jn="十百千萬",Xn="拾佰仟萬",Yn="マイナス",nr="마이너스",Re=function(e,A,t){var r=t?". ":"",n=t?"、":"",s=t?", ":"",a=t?" ":"";switch(A){case 0:return"•"+a;case 1:return"◦"+a;case 2:return"◾"+a;case 5:var i=V(e,48,57,!0,r);return i.length<4?"0"+i:i;case 4:return qA(e,"〇一二三四五六七八九",n);case 6:return Be(e,1,3999,Nn,3,r).toLowerCase();case 7:return Be(e,1,3999,Nn,3,r);case 8:return V(e,945,969,!1,r);case 9:return V(e,97,122,!1,r);case 10:return V(e,65,90,!1,r);case 11:return V(e,1632,1641,!0,r);case 12:case 49:return Be(e,1,9999,Pn,3,r);case 35:return Be(e,1,9999,Pn,3,r).toLowerCase();case 13:return V(e,2534,2543,!0,r);case 14:case 30:return V(e,6112,6121,!0,r);case 15:return qA(e,"子丑寅卯辰巳午未申酉戌亥",n);case 16:return qA(e,"甲乙丙丁戊己庚辛壬癸",n);case 17:case 48:return yA(e,"零一二三四五六七八九",Jn,"負",n,OA|_A|Se);case 47:return yA(e,"零壹貳參肆伍陸柒捌玖",Xn,"負",n,ue|OA|_A|Se);case 42:return yA(e,"零一二三四五六七八九",Jn,"负",n,OA|_A|Se);case 41:return yA(e,"零壹贰叁肆伍陆柒捌玖",Xn,"负",n,ue|OA|_A|Se);case 26:return yA(e,"〇一二三四五六七八九","十百千万",Yn,n,0);case 25:return yA(e,"零壱弐参四伍六七八九","拾百千万",Yn,n,ue|OA|_A);case 31:return yA(e,"영일이삼사오육칠팔구","십백천만",nr,s,ue|OA|_A);case 33:return yA(e,"零一二三四五六七八九","十百千萬",nr,s,0);case 32:return yA(e,"零壹貳參四五六七八九","拾百千",nr,s,ue|OA|_A);case 18:return V(e,2406,2415,!0,r);case 20:return Be(e,1,19999,OB,3,r);case 21:return V(e,2790,2799,!0,r);case 22:return V(e,2662,2671,!0,r);case 22:return Be(e,1,10999,MB,3,r);case 23:return qA(e,"あいうえおかきくけこさしすせそたちつてとなにぬねのはひふへほまみむめもやゆよらりるれろわゐゑをん");case 24:return qA(e,"いろはにほへとちりぬるをわかよたれそつねならむうゐのおくやまけふこえてあさきゆめみしゑひもせす");case 27:return V(e,3302,3311,!0,r);case 28:return qA(e,"アイウエオカキクケコサシスセソタチツテトナニヌネノハヒフヘホマミムメモヤユヨラリルレロワヰヱヲン",n);case 29:return qA(e,"イロハニホヘトチリヌルヲワカヨタレソツネナラムウヰノオクヤマケフコエテアサキユメミシヱヒモセス",n);case 34:return V(e,3792,3801,!0,r);case 37:return V(e,6160,6169,!0,r);case 38:return V(e,4160,4169,!0,r);case 39:return V(e,2918,2927,!0,r);case 40:return V(e,1776,1785,!0,r);case 43:return V(e,3046,3055,!0,r);case 44:return V(e,3174,3183,!0,r);case 45:return V(e,3664,3673,!0,r);case 46:return V(e,3872,3881,!0,r);case 3:default:return V(e,48,57,!0,r)}},js="data-html2canvas-ignore",Wn=function(){function e(A,t,r){if(this.context=A,this.options=r,this.scrolledElements=[],this.referenceElement=t,this.counters=new TB,this.quoteDepth=0,!t.ownerDocument)throw new Error("Cloned element does not have an owner document");this.documentElement=this.cloneNode(t.ownerDocument.documentElement,!1)}return e.prototype.toIFrame=function(A,t){var r=this,n=_B(A,t);if(!n.contentWindow)return Promise.reject("Unable to find iframe window");var s=A.defaultView.pageXOffset,a=A.defaultView.pageYOffset,i=n.contentWindow,o=i.document,l=kB(n).then(function(){return AA(r,void 0,void 0,function(){var B,x;return j(this,function(u){switch(u.label){case 0:return this.scrolledElements.forEach(JB),i&&(i.scrollTo(t.left,t.top),/(iPad|iPhone|iPod)/g.test(navigator.userAgent)&&(i.scrollY!==t.top||i.scrollX!==t.left)&&(this.context.logger.warn("Unable to restore scroll position for cloned document"),this.context.windowBounds=this.context.windowBounds.add(i.scrollX-t.left,i.scrollY-t.top,0,0))),B=this.options.onclone,x=this.clonedReferenceElement,typeof x>"u"?[2,Promise.reject("Error finding the "+this.referenceElement.nodeName+" in the cloned document")]:o.fonts&&o.fonts.ready?[4,o.fonts.ready]:[3,2];case 1:u.sent(),u.label=2;case 2:return/(AppleWebKit)/g.test(navigator.userAgent)?[4,GB(o)]:[3,4];case 3:u.sent(),u.label=4;case 4:return typeof B=="function"?[2,Promise.resolve().then(function(){return B(o,x)}).then(function(){return n})]:[2,n]}})})});return o.open(),o.write(NB(document.doctype)+"<html></html>"),PB(this.referenceElement.ownerDocument,s,a),o.replaceChild(o.adoptNode(this.documentElement),o.documentElement),o.close(),l},e.prototype.createElementClone=function(A){if(Er(A,2))debugger;if(Ws(A))return this.createCanvasClone(A);if(Gn(A))return this.createVideoClone(A);if(kn(A))return this.createStyleClone(A);var t=A.cloneNode(!1);return br(t)&&(br(A)&&A.currentSrc&&A.currentSrc!==A.src&&(t.src=A.currentSrc,t.srcset=""),t.loading==="lazy"&&(t.loading="eager")),Vn(t)?this.createCustomElementClone(t):t},e.prototype.createCustomElementClone=function(A){var t=document.createElement("html2canvascustomelement");return sr(A.style,t),t},e.prototype.createStyleClone=function(A){try{var t=A.sheet;if(t&&t.cssRules){var r=[].slice.call(t.cssRules,0).reduce(function(s,a){return a&&typeof a.cssText=="string"?s+a.cssText:s},""),n=A.cloneNode(!1);return n.textContent=r,n}}catch(s){if(this.context.logger.error("Unable to access cssRules property",s),s.name!=="SecurityError")throw s}return A.cloneNode(!1)},e.prototype.createCanvasClone=function(A){var t;if(this.options.inlineImages&&A.ownerDocument){var r=A.ownerDocument.createElement("img");try{return r.src=A.toDataURL(),r}catch{this.context.logger.info("Unable to inline canvas contents, canvas is tainted",A)}}var n=A.cloneNode(!1);try{n.width=A.width,n.height=A.height;var s=A.getContext("2d"),a=n.getContext("2d");if(a)if(!this.options.allowTaint&&s)a.putImageData(s.getImageData(0,0,A.width,A.height),0,0);else{var i=(t=A.getContext("webgl2"))!==null&&t!==void 0?t:A.getContext("webgl");if(i){var o=i.getContextAttributes();(o==null?void 0:o.preserveDrawingBuffer)===!1&&this.context.logger.warn("Unable to clone WebGL context as it has preserveDrawingBuffer=false",A)}a.drawImage(A,0,0)}return n}catch{this.context.logger.info("Unable to clone canvas as it is tainted",A)}return n},e.prototype.createVideoClone=function(A){var t=A.ownerDocument.createElement("canvas");t.width=A.offsetWidth,t.height=A.offsetHeight;var r=t.getContext("2d");try{return r&&(r.drawImage(A,0,0,t.width,t.height),this.options.allowTaint||r.getImageData(0,0,t.width,t.height)),t}catch{this.context.logger.info("Unable to clone video as it is tainted",A)}var n=A.ownerDocument.createElement("canvas");return n.width=A.offsetWidth,n.height=A.offsetHeight,n},e.prototype.appendChildNode=function(A,t,r){(!de(t)||!KB(t)&&!t.hasAttribute(js)&&(typeof this.options.ignoreElements!="function"||!this.options.ignoreElements(t)))&&(!this.options.copyStyles||!de(t)||!kn(t))&&A.appendChild(this.cloneNode(t,r))},e.prototype.cloneChildNodes=function(A,t,r){for(var n=this,s=A.shadowRoot?A.shadowRoot.firstChild:A.firstChild;s;s=s.nextSibling)if(de(s)&&zs(s)&&typeof s.assignedNodes=="function"){var a=s.assignedNodes();a.length&&a.forEach(function(i){return n.appendChildNode(t,i,r)})}else this.appendChildNode(t,s,r)},e.prototype.cloneNode=function(A,t){if(Xs(A))return document.createTextNode(A.data);if(!A.ownerDocument)return A.cloneNode(!1);var r=A.ownerDocument.defaultView;if(r&&de(A)&&(Ir(A)||Qt(A))){var n=this.createElementClone(A);n.style.transitionProperty="none";var s=r.getComputedStyle(A),a=r.getComputedStyle(A,":before"),i=r.getComputedStyle(A,":after");this.referenceElement===A&&Ir(n)&&(this.clonedReferenceElement=n),zr(n)&&WB(n);var o=this.counters.parse(new vn(this.context,s)),l=this.resolvePseudoContent(A,n,a,Me.BEFORE);Vn(A)&&(t=!0),Gn(A)||this.cloneChildNodes(A,n,t),l&&n.insertBefore(l,n.firstChild);var B=this.resolvePseudoContent(A,n,i,Me.AFTER);return B&&n.appendChild(B),this.counters.pop(o),(s&&(this.options.copyStyles||Qt(A))&&!Zs(A)||t)&&sr(s,n),(A.scrollTop!==0||A.scrollLeft!==0)&&this.scrolledElements.push([n,A.scrollLeft,A.scrollTop]),(vt(A)||Et(A))&&(vt(n)||Et(n))&&(n.value=A.value),n}return A.cloneNode(!1)},e.prototype.resolvePseudoContent=function(A,t,r,n){var s=this;if(r){var a=r.content,i=t.ownerDocument;if(!(!i||!a||a==="none"||a==="-moz-alt-content"||r.display==="none")){this.counters.parse(new vn(this.context,r));var o=new bl(this.context,r),l=i.createElement("html2canvaspseudoelement");sr(r,l),o.content.forEach(function(x){if(x.type===0)l.appendChild(i.createTextNode(x.value));else if(x.type===22){var u=i.createElement("img");u.src=x.value,u.style.opacity="1",l.appendChild(u)}else if(x.type===18){if(x.name==="attr"){var h=x.values.filter(O);h.length&&l.appendChild(i.createTextNode(A.getAttribute(h[0].value)||""))}else if(x.name==="counter"){var g=x.values.filter(fe),d=g[0],F=g[1];if(d&&O(d)){var f=s.counters.getCounterValue(d.value),w=F&&O(F)?vr.parse(s.context,F.value):3;l.appendChild(i.createTextNode(Re(f,w,!1)))}}else if(x.name==="counters"){var v=x.values.filter(fe),d=v[0],E=v[1],F=v[2];if(d&&O(d)){var m=s.counters.getCounterValues(d.value),Q=F&&O(F)?vr.parse(s.context,F.value):3,b=E&&E.type===0?E.value:"",S=m.map(function(eA){return Re(eA,Q,!1)}).join(b);l.appendChild(i.createTextNode(S))}}}else if(x.type===20)switch(x.value){case"open-quote":l.appendChild(i.createTextNode(mn(o.quotes,s.quoteDepth++,!0)));break;case"close-quote":l.appendChild(i.createTextNode(mn(o.quotes,--s.quoteDepth,!1)));break;default:l.appendChild(i.createTextNode(x.value))}}),l.className=Sr+" "+Lr;var B=n===Me.BEFORE?" "+Sr:" "+Lr;return Qt(t)?t.className.baseValue+=B:t.className+=B,l}}},e.destroy=function(A){return A.parentNode?(A.parentNode.removeChild(A),!0):!1},e}(),Me;(function(e){e[e.BEFORE=0]="BEFORE",e[e.AFTER=1]="AFTER"})(Me||(Me={}));var _B=function(e,A){var t=e.createElement("iframe");return t.className="html2canvas-container",t.style.visibility="hidden",t.style.position="fixed",t.style.left="-10000px",t.style.top="0px",t.style.border="0",t.width=A.width.toString(),t.height=A.height.toString(),t.scrolling="no",t.setAttribute(js,"true"),e.body.appendChild(t),t},RB=function(e){return new Promise(function(A){if(e.complete){A();return}if(!e.src){A();return}e.onload=A,e.onerror=A})},GB=function(e){return Promise.all([].slice.call(e.images,0).map(RB))},kB=function(e){return new Promise(function(A,t){var r=e.contentWindow;if(!r)return t("No window assigned for iframe");var n=r.document;r.onload=e.onload=function(){r.onload=e.onload=null;var s=setInterval(function(){n.body.childNodes.length>0&&n.readyState==="complete"&&(clearInterval(s),A(e))},50)}})},VB=["all","d","content"],sr=function(e,A){for(var t=e.length-1;t>=0;t--){var r=e.item(t);VB.indexOf(r)===-1&&A.style.setProperty(r,e.getPropertyValue(r))}return A},NB=function(e){var A="";return e&&(A+="<!DOCTYPE ",e.name&&(A+=e.name),e.internalSubset&&(A+=e.internalSubset),e.publicId&&(A+='"'+e.publicId+'"'),e.systemId&&(A+='"'+e.systemId+'"'),A+=">"),A},PB=function(e,A,t){e&&e.defaultView&&(A!==e.defaultView.pageXOffset||t!==e.defaultView.pageYOffset)&&e.defaultView.scrollTo(A,t)},JB=function(e){var A=e[0],t=e[1],r=e[2];A.scrollLeft=t,A.scrollTop=r},XB=":before",YB=":after",Sr="___html2canvas___pseudoelement_before",Lr="___html2canvas___pseudoelement_after",Zn=`{
    content: "" !important;
    display: none !important;
}`,WB=function(e){ZB(e,"."+Sr+XB+Zn+`
         .`+Lr+YB+Zn)},ZB=function(e,A){var t=e.ownerDocument;if(t){var r=t.createElement("style");r.textContent=A,e.appendChild(r)}},$s=function(){function e(){}return e.getOrigin=function(A){var t=e._link;return t?(t.href=A,t.href=t.href,t.protocol+t.hostname+t.port):"about:blank"},e.isSameOrigin=function(A){return e.getOrigin(A)===e._origin},e.setContext=function(A){e._link=A.document.createElement("a"),e._origin=e.getOrigin(A.location.href)},e._origin="about:blank",e}(),zB=function(){function e(A,t){this.context=A,this._options=t,this._cache={}}return e.prototype.addImage=function(A){var t=Promise.resolve();return this.has(A)||(ir(A)||Ac(A))&&(this._cache[A]=this.loadImage(A)).catch(function(){}),t},e.prototype.match=function(A){return this._cache[A]},e.prototype.loadImage=function(A){return AA(this,void 0,void 0,function(){var t,r,n,s,a=this;return j(this,function(i){switch(i.label){case 0:return t=$s.isSameOrigin(A),r=!ar(A)&&this._options.useCORS===!0&&z.SUPPORT_CORS_IMAGES&&!t,n=!ar(A)&&!t&&!ir(A)&&typeof this._options.proxy=="string"&&z.SUPPORT_CORS_XHR&&!r,!t&&this._options.allowTaint===!1&&!ar(A)&&!ir(A)&&!n&&!r?[2]:(s=A,n?[4,this.proxy(s)]:[3,2]);case 1:s=i.sent(),i.label=2;case 2:return this.context.logger.debug("Added image "+A.substring(0,256)),[4,new Promise(function(o,l){var B=new Image;B.onload=function(){return o(B)},B.onerror=l,(ec(s)||r)&&(B.crossOrigin="anonymous"),B.src=s,B.complete===!0&&setTimeout(function(){return o(B)},500),a._options.imageTimeout>0&&setTimeout(function(){return l("Timed out ("+a._options.imageTimeout+"ms) loading image")},a._options.imageTimeout)})];case 3:return[2,i.sent()]}})})},e.prototype.has=function(A){return typeof this._cache[A]<"u"},e.prototype.keys=function(){return Promise.resolve(Object.keys(this._cache))},e.prototype.proxy=function(A){var t=this,r=this._options.proxy;if(!r)throw new Error("No proxy defined");var n=A.substring(0,256);return new Promise(function(s,a){var i=z.SUPPORT_RESPONSE_TYPE?"blob":"text",o=new XMLHttpRequest;o.onload=function(){if(o.status===200)if(i==="text")s(o.response);else{var x=new FileReader;x.addEventListener("load",function(){return s(x.result)},!1),x.addEventListener("error",function(u){return a(u)},!1),x.readAsDataURL(o.response)}else a("Failed to proxy resource "+n+" with status code "+o.status)},o.onerror=a;var l=r.indexOf("?")>-1?"&":"?";if(o.open("GET",""+r+l+"url="+encodeURIComponent(A)+"&responseType="+i),i!=="text"&&o instanceof XMLHttpRequest&&(o.responseType=i),t._options.imageTimeout){var B=t._options.imageTimeout;o.timeout=B,o.ontimeout=function(){return a("Timed out ("+B+"ms) proxying "+n)}}o.send()})},e}(),qB=/^data:image\/svg\+xml/i,jB=/^data:image\/.*;base64,/i,$B=/^data:image\/.*/i,Ac=function(e){return z.SUPPORT_SVG_DRAWING||!tc(e)},ar=function(e){return $B.test(e)},ec=function(e){return jB.test(e)},ir=function(e){return e.substr(0,4)==="blob"},tc=function(e){return e.substr(-3).toLowerCase()==="svg"||qB.test(e)},C=function(){function e(A,t){this.type=0,this.x=A,this.y=t}return e.prototype.add=function(A,t){return new e(this.x+A,this.y+t)},e}(),ce=function(e,A,t){return new C(e.x+(A.x-e.x)*t,e.y+(A.y-e.y)*t)},Bt=function(){function e(A,t,r,n){this.type=1,this.start=A,this.startControl=t,this.endControl=r,this.end=n}return e.prototype.subdivide=function(A,t){var r=ce(this.start,this.startControl,A),n=ce(this.startControl,this.endControl,A),s=ce(this.endControl,this.end,A),a=ce(r,n,A),i=ce(n,s,A),o=ce(a,i,A);return t?new e(this.start,r,a,o):new e(o,i,s,this.end)},e.prototype.add=function(A,t){return new e(this.start.add(A,t),this.startControl.add(A,t),this.endControl.add(A,t),this.end.add(A,t))},e.prototype.reverse=function(){return new e(this.end,this.endControl,this.startControl,this.start)},e}(),xA=function(e){return e.type===1},rc=function(){function e(A){var t=A.styles,r=A.bounds,n=Ie(t.borderTopLeftRadius,r.width,r.height),s=n[0],a=n[1],i=Ie(t.borderTopRightRadius,r.width,r.height),o=i[0],l=i[1],B=Ie(t.borderBottomRightRadius,r.width,r.height),x=B[0],u=B[1],h=Ie(t.borderBottomLeftRadius,r.width,r.height),g=h[0],d=h[1],F=[];F.push((s+o)/r.width),F.push((g+x)/r.width),F.push((a+d)/r.height),F.push((l+u)/r.height);var f=Math.max.apply(Math,F);f>1&&(s/=f,a/=f,o/=f,l/=f,x/=f,u/=f,g/=f,d/=f);var w=r.width-o,v=r.height-u,E=r.width-x,m=r.height-d,Q=t.borderTopWidth,b=t.borderRightWidth,S=t.borderBottomWidth,y=t.borderLeftWidth,_=R(t.paddingTop,A.bounds.width),eA=R(t.paddingRight,A.bounds.width),oA=R(t.paddingBottom,A.bounds.width),T=R(t.paddingLeft,A.bounds.width);this.topLeftBorderDoubleOuterBox=s>0||a>0?k(r.left+y/3,r.top+Q/3,s-y/3,a-Q/3,K.TOP_LEFT):new C(r.left+y/3,r.top+Q/3),this.topRightBorderDoubleOuterBox=s>0||a>0?k(r.left+w,r.top+Q/3,o-b/3,l-Q/3,K.TOP_RIGHT):new C(r.left+r.width-b/3,r.top+Q/3),this.bottomRightBorderDoubleOuterBox=x>0||u>0?k(r.left+E,r.top+v,x-b/3,u-S/3,K.BOTTOM_RIGHT):new C(r.left+r.width-b/3,r.top+r.height-S/3),this.bottomLeftBorderDoubleOuterBox=g>0||d>0?k(r.left+y/3,r.top+m,g-y/3,d-S/3,K.BOTTOM_LEFT):new C(r.left+y/3,r.top+r.height-S/3),this.topLeftBorderDoubleInnerBox=s>0||a>0?k(r.left+y*2/3,r.top+Q*2/3,s-y*2/3,a-Q*2/3,K.TOP_LEFT):new C(r.left+y*2/3,r.top+Q*2/3),this.topRightBorderDoubleInnerBox=s>0||a>0?k(r.left+w,r.top+Q*2/3,o-b*2/3,l-Q*2/3,K.TOP_RIGHT):new C(r.left+r.width-b*2/3,r.top+Q*2/3),this.bottomRightBorderDoubleInnerBox=x>0||u>0?k(r.left+E,r.top+v,x-b*2/3,u-S*2/3,K.BOTTOM_RIGHT):new C(r.left+r.width-b*2/3,r.top+r.height-S*2/3),this.bottomLeftBorderDoubleInnerBox=g>0||d>0?k(r.left+y*2/3,r.top+m,g-y*2/3,d-S*2/3,K.BOTTOM_LEFT):new C(r.left+y*2/3,r.top+r.height-S*2/3),this.topLeftBorderStroke=s>0||a>0?k(r.left+y/2,r.top+Q/2,s-y/2,a-Q/2,K.TOP_LEFT):new C(r.left+y/2,r.top+Q/2),this.topRightBorderStroke=s>0||a>0?k(r.left+w,r.top+Q/2,o-b/2,l-Q/2,K.TOP_RIGHT):new C(r.left+r.width-b/2,r.top+Q/2),this.bottomRightBorderStroke=x>0||u>0?k(r.left+E,r.top+v,x-b/2,u-S/2,K.BOTTOM_RIGHT):new C(r.left+r.width-b/2,r.top+r.height-S/2),this.bottomLeftBorderStroke=g>0||d>0?k(r.left+y/2,r.top+m,g-y/2,d-S/2,K.BOTTOM_LEFT):new C(r.left+y/2,r.top+r.height-S/2),this.topLeftBorderBox=s>0||a>0?k(r.left,r.top,s,a,K.TOP_LEFT):new C(r.left,r.top),this.topRightBorderBox=o>0||l>0?k(r.left+w,r.top,o,l,K.TOP_RIGHT):new C(r.left+r.width,r.top),this.bottomRightBorderBox=x>0||u>0?k(r.left+E,r.top+v,x,u,K.BOTTOM_RIGHT):new C(r.left+r.width,r.top+r.height),this.bottomLeftBorderBox=g>0||d>0?k(r.left,r.top+m,g,d,K.BOTTOM_LEFT):new C(r.left,r.top+r.height),this.topLeftPaddingBox=s>0||a>0?k(r.left+y,r.top+Q,Math.max(0,s-y),Math.max(0,a-Q),K.TOP_LEFT):new C(r.left+y,r.top+Q),this.topRightPaddingBox=o>0||l>0?k(r.left+Math.min(w,r.width-b),r.top+Q,w>r.width+b?0:Math.max(0,o-b),Math.max(0,l-Q),K.TOP_RIGHT):new C(r.left+r.width-b,r.top+Q),this.bottomRightPaddingBox=x>0||u>0?k(r.left+Math.min(E,r.width-y),r.top+Math.min(v,r.height-S),Math.max(0,x-b),Math.max(0,u-S),K.BOTTOM_RIGHT):new C(r.left+r.width-b,r.top+r.height-S),this.bottomLeftPaddingBox=g>0||d>0?k(r.left+y,r.top+Math.min(m,r.height-S),Math.max(0,g-y),Math.max(0,d-S),K.BOTTOM_LEFT):new C(r.left+y,r.top+r.height-S),this.topLeftContentBox=s>0||a>0?k(r.left+y+T,r.top+Q+_,Math.max(0,s-(y+T)),Math.max(0,a-(Q+_)),K.TOP_LEFT):new C(r.left+y+T,r.top+Q+_),this.topRightContentBox=o>0||l>0?k(r.left+Math.min(w,r.width+y+T),r.top+Q+_,w>r.width+y+T?0:o-y+T,l-(Q+_),K.TOP_RIGHT):new C(r.left+r.width-(b+eA),r.top+Q+_),this.bottomRightContentBox=x>0||u>0?k(r.left+Math.min(E,r.width-(y+T)),r.top+Math.min(v,r.height+Q+_),Math.max(0,x-(b+eA)),u-(S+oA),K.BOTTOM_RIGHT):new C(r.left+r.width-(b+eA),r.top+r.height-(S+oA)),this.bottomLeftContentBox=g>0||d>0?k(r.left+y+T,r.top+m,Math.max(0,g-(y+T)),d-(S+oA),K.BOTTOM_LEFT):new C(r.left+y+T,r.top+r.height-(S+oA))}return e}(),K;(function(e){e[e.TOP_LEFT=0]="TOP_LEFT",e[e.TOP_RIGHT=1]="TOP_RIGHT",e[e.BOTTOM_RIGHT=2]="BOTTOM_RIGHT",e[e.BOTTOM_LEFT=3]="BOTTOM_LEFT"})(K||(K={}));var k=function(e,A,t,r,n){var s=4*((Math.sqrt(2)-1)/3),a=t*s,i=r*s,o=e+t,l=A+r;switch(n){case K.TOP_LEFT:return new Bt(new C(e,l),new C(e,l-i),new C(o-a,A),new C(o,A));case K.TOP_RIGHT:return new Bt(new C(e,A),new C(e+a,A),new C(o,l-i),new C(o,l));case K.BOTTOM_RIGHT:return new Bt(new C(o,A),new C(o,A+i),new C(e+a,l),new C(e,l));case K.BOTTOM_LEFT:default:return new Bt(new C(o,l),new C(o-a,l),new C(e,A+i),new C(e,A))}},yt=function(e){return[e.topLeftBorderBox,e.topRightBorderBox,e.bottomRightBorderBox,e.bottomLeftBorderBox]},nc=function(e){return[e.topLeftContentBox,e.topRightContentBox,e.bottomRightContentBox,e.bottomLeftContentBox]},Ht=function(e){return[e.topLeftPaddingBox,e.topRightPaddingBox,e.bottomRightPaddingBox,e.bottomLeftPaddingBox]},sc=function(){function e(A,t,r){this.offsetX=A,this.offsetY=t,this.matrix=r,this.type=0,this.target=6}return e}(),ct=function(){function e(A,t){this.path=A,this.target=t,this.type=1}return e}(),ac=function(){function e(A){this.opacity=A,this.type=2,this.target=6}return e}(),ic=function(e){return e.type===0},Aa=function(e){return e.type===1},oc=function(e){return e.type===2},zn=function(e,A){return e.length===A.length?e.some(function(t,r){return t===A[r]}):!1},lc=function(e,A,t,r,n){return e.map(function(s,a){switch(a){case 0:return s.add(A,t);case 1:return s.add(A+r,t);case 2:return s.add(A+r,t+n);case 3:return s.add(A,t+n)}return s})},ea=function(){function e(A){this.element=A,this.inlineLevel=[],this.nonInlineLevel=[],this.negativeZIndex=[],this.zeroOrAutoZIndexOrTransformedOrOpacity=[],this.positiveZIndex=[],this.nonPositionedFloats=[],this.nonPositionedInlineLevel=[]}return e}(),ta=function(){function e(A,t){if(this.container=A,this.parent=t,this.effects=[],this.curves=new rc(this.container),this.container.styles.opacity<1&&this.effects.push(new ac(this.container.styles.opacity)),this.container.styles.transform!==null){var r=this.container.bounds.left+this.container.styles.transformOrigin[0].number,n=this.container.bounds.top+this.container.styles.transformOrigin[1].number,s=this.container.styles.transform;this.effects.push(new sc(r,n,s))}if(this.container.styles.overflowX!==0){var a=yt(this.curves),i=Ht(this.curves);zn(a,i)?this.effects.push(new ct(a,6)):(this.effects.push(new ct(a,2)),this.effects.push(new ct(i,4)))}}return e.prototype.getEffects=function(A){for(var t=[2,3].indexOf(this.container.styles.position)===-1,r=this.parent,n=this.effects.slice(0);r;){var s=r.effects.filter(function(o){return!Aa(o)});if(t||r.container.styles.position!==0||!r.parent){if(n.unshift.apply(n,s),t=[2,3].indexOf(r.container.styles.position)===-1,r.container.styles.overflowX!==0){var a=yt(r.curves),i=Ht(r.curves);zn(a,i)||n.unshift(new ct(i,6))}}else n.unshift.apply(n,s);r=r.parent}return n.filter(function(o){return Y(o.target,A)})},e}(),Dr=function(e,A,t,r){e.container.elements.forEach(function(n){var s=Y(n.flags,4),a=Y(n.flags,2),i=new ta(n,e);Y(n.styles.display,2048)&&r.push(i);var o=Y(n.flags,8)?[]:r;if(s||a){var l=s||n.styles.isPositioned()?t:A,B=new ea(i);if(n.styles.isPositioned()||n.styles.opacity<1||n.styles.isTransformed()){var x=n.styles.zIndex.order;if(x<0){var u=0;l.negativeZIndex.some(function(g,d){return x>g.element.container.styles.zIndex.order?(u=d,!1):u>0}),l.negativeZIndex.splice(u,0,B)}else if(x>0){var h=0;l.positiveZIndex.some(function(g,d){return x>=g.element.container.styles.zIndex.order?(h=d+1,!1):h>0}),l.positiveZIndex.splice(h,0,B)}else l.zeroOrAutoZIndexOrTransformedOrOpacity.push(B)}else n.styles.isFloating()?l.nonPositionedFloats.push(B):l.nonPositionedInlineLevel.push(B);Dr(i,B,s?B:t,o)}else n.styles.isInlineLevel()?A.inlineLevel.push(i):A.nonInlineLevel.push(i),Dr(i,A,t,o);Y(n.flags,8)&&ra(n,o)})},ra=function(e,A){for(var t=e instanceof Hr?e.start:1,r=e instanceof Hr?e.reversed:!1,n=0;n<A.length;n++){var s=A[n];s.container instanceof Gs&&typeof s.container.value=="number"&&s.container.value!==0&&(t=s.container.value),s.listValue=Re(t,s.container.styles.listStyleType,!0),t+=r?-1:1}},Bc=function(e){var A=new ta(e,null),t=new ea(A),r=[];return Dr(A,t,t,r),ra(A.container,r),t},qn=function(e,A){switch(A){case 0:return gA(e.topLeftBorderBox,e.topLeftPaddingBox,e.topRightBorderBox,e.topRightPaddingBox);case 1:return gA(e.topRightBorderBox,e.topRightPaddingBox,e.bottomRightBorderBox,e.bottomRightPaddingBox);case 2:return gA(e.bottomRightBorderBox,e.bottomRightPaddingBox,e.bottomLeftBorderBox,e.bottomLeftPaddingBox);case 3:default:return gA(e.bottomLeftBorderBox,e.bottomLeftPaddingBox,e.topLeftBorderBox,e.topLeftPaddingBox)}},cc=function(e,A){switch(A){case 0:return gA(e.topLeftBorderBox,e.topLeftBorderDoubleOuterBox,e.topRightBorderBox,e.topRightBorderDoubleOuterBox);case 1:return gA(e.topRightBorderBox,e.topRightBorderDoubleOuterBox,e.bottomRightBorderBox,e.bottomRightBorderDoubleOuterBox);case 2:return gA(e.bottomRightBorderBox,e.bottomRightBorderDoubleOuterBox,e.bottomLeftBorderBox,e.bottomLeftBorderDoubleOuterBox);case 3:default:return gA(e.bottomLeftBorderBox,e.bottomLeftBorderDoubleOuterBox,e.topLeftBorderBox,e.topLeftBorderDoubleOuterBox)}},xc=function(e,A){switch(A){case 0:return gA(e.topLeftBorderDoubleInnerBox,e.topLeftPaddingBox,e.topRightBorderDoubleInnerBox,e.topRightPaddingBox);case 1:return gA(e.topRightBorderDoubleInnerBox,e.topRightPaddingBox,e.bottomRightBorderDoubleInnerBox,e.bottomRightPaddingBox);case 2:return gA(e.bottomRightBorderDoubleInnerBox,e.bottomRightPaddingBox,e.bottomLeftBorderDoubleInnerBox,e.bottomLeftPaddingBox);case 3:default:return gA(e.bottomLeftBorderDoubleInnerBox,e.bottomLeftPaddingBox,e.topLeftBorderDoubleInnerBox,e.topLeftPaddingBox)}},uc=function(e,A){switch(A){case 0:return xt(e.topLeftBorderStroke,e.topRightBorderStroke);case 1:return xt(e.topRightBorderStroke,e.bottomRightBorderStroke);case 2:return xt(e.bottomRightBorderStroke,e.bottomLeftBorderStroke);case 3:default:return xt(e.bottomLeftBorderStroke,e.topLeftBorderStroke)}},xt=function(e,A){var t=[];return xA(e)?t.push(e.subdivide(.5,!1)):t.push(e),xA(A)?t.push(A.subdivide(.5,!0)):t.push(A),t},gA=function(e,A,t,r){var n=[];return xA(e)?n.push(e.subdivide(.5,!1)):n.push(e),xA(t)?n.push(t.subdivide(.5,!0)):n.push(t),xA(r)?n.push(r.subdivide(.5,!0).reverse()):n.push(r),xA(A)?n.push(A.subdivide(.5,!1).reverse()):n.push(A),n},na=function(e){var A=e.bounds,t=e.styles;return A.add(t.borderLeftWidth,t.borderTopWidth,-(t.borderRightWidth+t.borderLeftWidth),-(t.borderTopWidth+t.borderBottomWidth))},It=function(e){var A=e.styles,t=e.bounds,r=R(A.paddingLeft,t.width),n=R(A.paddingRight,t.width),s=R(A.paddingTop,t.width),a=R(A.paddingBottom,t.width);return t.add(r+A.borderLeftWidth,s+A.borderTopWidth,-(A.borderRightWidth+A.borderLeftWidth+r+n),-(A.borderTopWidth+A.borderBottomWidth+s+a))},gc=function(e,A){return e===0?A.bounds:e===2?It(A):na(A)},dc=function(e,A){return e===0?A.bounds:e===2?It(A):na(A)},or=function(e,A,t){var r=gc(ge(e.styles.backgroundOrigin,A),e),n=dc(ge(e.styles.backgroundClip,A),e),s=hc(ge(e.styles.backgroundSize,A),t,r),a=s[0],i=s[1],o=Ie(ge(e.styles.backgroundPosition,A),r.width-a,r.height-i),l=wc(ge(e.styles.backgroundRepeat,A),o,s,r,n),B=Math.round(r.left+o[0]),x=Math.round(r.top+o[1]);return[l,B,x,a,i]},xe=function(e){return O(e)&&e.value===he.AUTO},ut=function(e){return typeof e=="number"},hc=function(e,A,t){var r=A[0],n=A[1],s=A[2],a=e[0],i=e[1];if(!a)return[0,0];if(P(a)&&i&&P(i))return[R(a,t.width),R(i,t.height)];var o=ut(s);if(O(a)&&(a.value===he.CONTAIN||a.value===he.COVER)){if(ut(s)){var l=t.width/t.height;return l<s!=(a.value===he.COVER)?[t.width,t.width/s]:[t.height*s,t.height]}return[t.width,t.height]}var B=ut(r),x=ut(n),u=B||x;if(xe(a)&&(!i||xe(i))){if(B&&x)return[r,n];if(!o&&!u)return[t.width,t.height];if(u&&o){var h=B?r:n*s,g=x?n:r/s;return[h,g]}var d=B?r:t.width,F=x?n:t.height;return[d,F]}if(o){var f=0,w=0;return P(a)?f=R(a,t.width):P(i)&&(w=R(i,t.height)),xe(a)?f=w*s:(!i||xe(i))&&(w=f/s),[f,w]}var v=null,E=null;if(P(a)?v=R(a,t.width):i&&P(i)&&(E=R(i,t.height)),v!==null&&(!i||xe(i))&&(E=B&&x?v/r*n:t.height),E!==null&&xe(a)&&(v=B&&x?E/n*r:t.width),v!==null&&E!==null)return[v,E];throw new Error("Unable to calculate background-size for element")},ge=function(e,A){var t=e[A];return typeof t>"u"?e[0]:t},wc=function(e,A,t,r,n){var s=A[0],a=A[1],i=t[0],o=t[1];switch(e){case 2:return[new C(Math.round(r.left),Math.round(r.top+a)),new C(Math.round(r.left+r.width),Math.round(r.top+a)),new C(Math.round(r.left+r.width),Math.round(o+r.top+a)),new C(Math.round(r.left),Math.round(o+r.top+a))];case 3:return[new C(Math.round(r.left+s),Math.round(r.top)),new C(Math.round(r.left+s+i),Math.round(r.top)),new C(Math.round(r.left+s+i),Math.round(r.height+r.top)),new C(Math.round(r.left+s),Math.round(r.height+r.top))];case 1:return[new C(Math.round(r.left+s),Math.round(r.top+a)),new C(Math.round(r.left+s+i),Math.round(r.top+a)),new C(Math.round(r.left+s+i),Math.round(r.top+a+o)),new C(Math.round(r.left+s),Math.round(r.top+a+o))];default:return[new C(Math.round(n.left),Math.round(n.top)),new C(Math.round(n.left+n.width),Math.round(n.top)),new C(Math.round(n.left+n.width),Math.round(n.height+n.top)),new C(Math.round(n.left),Math.round(n.height+n.top))]}},fc="data:image/gif;base64,R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7",jn="Hidden Text",Qc=function(){function e(A){this._data={},this._document=A}return e.prototype.parseMetrics=function(A,t){var r=this._document.createElement("div"),n=this._document.createElement("img"),s=this._document.createElement("span"),a=this._document.body;r.style.visibility="hidden",r.style.fontFamily=A,r.style.fontSize=t,r.style.margin="0",r.style.padding="0",r.style.whiteSpace="nowrap",a.appendChild(r),n.src=fc,n.width=1,n.height=1,n.style.margin="0",n.style.padding="0",n.style.verticalAlign="baseline",s.style.fontFamily=A,s.style.fontSize=t,s.style.margin="0",s.style.padding="0",s.appendChild(this._document.createTextNode(jn)),r.appendChild(s),r.appendChild(n);var i=n.offsetTop-s.offsetTop+2;r.removeChild(s),r.appendChild(this._document.createTextNode(jn)),r.style.lineHeight="normal",n.style.verticalAlign="super";var o=n.offsetTop-r.offsetTop+2;return a.removeChild(r),{baseline:i,middle:o}},e.prototype.getMetrics=function(A,t){var r=A+" "+t;return typeof this._data[r]>"u"&&(this._data[r]=this.parseMetrics(A,t)),this._data[r]},e}(),sa=function(){function e(A,t){this.context=A,this.options=t}return e}(),Cc=1e4,pc=function(e){fA(A,e);function A(t,r){var n=e.call(this,t,r)||this;return n._activeEffects=[],n.canvas=r.canvas?r.canvas:document.createElement("canvas"),n.ctx=n.canvas.getContext("2d"),r.canvas||(n.canvas.width=Math.floor(r.width*r.scale),n.canvas.height=Math.floor(r.height*r.scale),n.canvas.style.width=r.width+"px",n.canvas.style.height=r.height+"px"),n.fontMetrics=new Qc(document),n.ctx.scale(n.options.scale,n.options.scale),n.ctx.translate(-r.x,-r.y),n.ctx.textBaseline="bottom",n._activeEffects=[],n.context.logger.debug("Canvas renderer initialized ("+r.width+"x"+r.height+") with scale "+r.scale),n}return A.prototype.applyEffects=function(t){for(var r=this;this._activeEffects.length;)this.popEffect();t.forEach(function(n){return r.applyEffect(n)})},A.prototype.applyEffect=function(t){this.ctx.save(),oc(t)&&(this.ctx.globalAlpha=t.opacity),ic(t)&&(this.ctx.translate(t.offsetX,t.offsetY),this.ctx.transform(t.matrix[0],t.matrix[1],t.matrix[2],t.matrix[3],t.matrix[4],t.matrix[5]),this.ctx.translate(-t.offsetX,-t.offsetY)),Aa(t)&&(this.path(t.path),this.ctx.clip()),this._activeEffects.push(t)},A.prototype.popEffect=function(){this._activeEffects.pop(),this.ctx.restore()},A.prototype.renderStack=function(t){return AA(this,void 0,void 0,function(){var r;return j(this,function(n){switch(n.label){case 0:return r=t.element.container.styles,r.isVisible()?[4,this.renderStackContent(t)]:[3,2];case 1:n.sent(),n.label=2;case 2:return[2]}})})},A.prototype.renderNode=function(t){return AA(this,void 0,void 0,function(){return j(this,function(r){switch(r.label){case 0:if(Y(t.container.flags,16))debugger;return t.container.styles.isVisible()?[4,this.renderNodeBackgroundAndBorders(t)]:[3,3];case 1:return r.sent(),[4,this.renderNodeContent(t)];case 2:r.sent(),r.label=3;case 3:return[2]}})})},A.prototype.renderTextWithLetterSpacing=function(t,r,n){var s=this;if(r===0)this.ctx.fillText(t.text,t.bounds.left,t.bounds.top+n);else{var a=Wr(t.text);a.reduce(function(i,o){return s.ctx.fillText(o,i,t.bounds.top+n),i+s.ctx.measureText(o).width},t.bounds.left)}},A.prototype.createFontStyle=function(t){var r=t.fontVariant.filter(function(a){return a==="normal"||a==="small-caps"}).join(""),n=Ec(t.fontFamily).join(", "),s=Ve(t.fontSize)?""+t.fontSize.number+t.fontSize.unit:t.fontSize.number+"px";return[[t.fontStyle,r,t.fontWeight,s,n].join(" "),n,s]},A.prototype.renderTextNode=function(t,r){return AA(this,void 0,void 0,function(){var n,s,a,i,o,l,B,x,u=this;return j(this,function(h){return n=this.createFontStyle(r),s=n[0],a=n[1],i=n[2],this.ctx.font=s,this.ctx.direction=r.direction===1?"rtl":"ltr",this.ctx.textAlign="left",this.ctx.textBaseline="alphabetic",o=this.fontMetrics.getMetrics(a,i),l=o.baseline,B=o.middle,x=r.paintOrder,t.textBounds.forEach(function(g){x.forEach(function(d){switch(d){case 0:u.ctx.fillStyle=W(r.color),u.renderTextWithLetterSpacing(g,r.letterSpacing,l);var F=r.textShadow;F.length&&g.text.trim().length&&(F.slice(0).reverse().forEach(function(f){u.ctx.shadowColor=W(f.color),u.ctx.shadowOffsetX=f.offsetX.number*u.options.scale,u.ctx.shadowOffsetY=f.offsetY.number*u.options.scale,u.ctx.shadowBlur=f.blur.number,u.renderTextWithLetterSpacing(g,r.letterSpacing,l)}),u.ctx.shadowColor="",u.ctx.shadowOffsetX=0,u.ctx.shadowOffsetY=0,u.ctx.shadowBlur=0),r.textDecorationLine.length&&(u.ctx.fillStyle=W(r.textDecorationColor||r.color),r.textDecorationLine.forEach(function(f){switch(f){case 1:u.ctx.fillRect(g.bounds.left,Math.round(g.bounds.top+l),g.bounds.width,1);break;case 2:u.ctx.fillRect(g.bounds.left,Math.round(g.bounds.top),g.bounds.width,1);break;case 3:u.ctx.fillRect(g.bounds.left,Math.ceil(g.bounds.top+B),g.bounds.width,1);break}}));break;case 1:r.webkitTextStrokeWidth&&g.text.trim().length&&(u.ctx.strokeStyle=W(r.webkitTextStrokeColor),u.ctx.lineWidth=r.webkitTextStrokeWidth,u.ctx.lineJoin=window.chrome?"miter":"round",u.ctx.strokeText(g.text,g.bounds.left,g.bounds.top+l)),u.ctx.strokeStyle="",u.ctx.lineWidth=0,u.ctx.lineJoin="miter";break}})}),[2]})})},A.prototype.renderReplacedElement=function(t,r,n){if(n&&t.intrinsicWidth>0&&t.intrinsicHeight>0){var s=It(t),a=Ht(r);this.path(a),this.ctx.save(),this.ctx.clip(),this.ctx.drawImage(n,0,0,t.intrinsicWidth,t.intrinsicHeight,s.left,s.top,s.width,s.height),this.ctx.restore()}},A.prototype.renderNodeContent=function(t){return AA(this,void 0,void 0,function(){var r,n,s,a,i,o,w,w,l,B,x,u,E,h,g,m,d,F,f,w,v,E,m;return j(this,function(Q){switch(Q.label){case 0:this.applyEffects(t.getEffects(4)),r=t.container,n=t.curves,s=r.styles,a=0,i=r.textNodes,Q.label=1;case 1:return a<i.length?(o=i[a],[4,this.renderTextNode(o,s)]):[3,4];case 2:Q.sent(),Q.label=3;case 3:return a++,[3,1];case 4:if(!(r instanceof Os))return[3,8];Q.label=5;case 5:return Q.trys.push([5,7,,8]),[4,this.context.cache.match(r.src)];case 6:return w=Q.sent(),this.renderReplacedElement(r,n,w),[3,8];case 7:return Q.sent(),this.context.logger.error("Error loading image "+r.src),[3,8];case 8:if(r instanceof _s&&this.renderReplacedElement(r,n,r.canvas),!(r instanceof Rs))return[3,12];Q.label=9;case 9:return Q.trys.push([9,11,,12]),[4,this.context.cache.match(r.svg)];case 10:return w=Q.sent(),this.renderReplacedElement(r,n,w),[3,12];case 11:return Q.sent(),this.context.logger.error("Error loading svg "+r.svg.substring(0,255)),[3,12];case 12:return r instanceof Ns&&r.tree?(l=new A(this.context,{scale:this.options.scale,backgroundColor:r.backgroundColor,x:0,y:0,width:r.width,height:r.height}),[4,l.render(r.tree)]):[3,14];case 13:B=Q.sent(),r.width&&r.height&&this.ctx.drawImage(B,0,0,r.width,r.height,r.bounds.left,r.bounds.top,r.bounds.width,r.bounds.height),Q.label=14;case 14:if(r instanceof Zr&&(x=Math.min(r.bounds.width,r.bounds.height),r.type===Ft?r.checked&&(this.ctx.save(),this.path([new C(r.bounds.left+x*.39363,r.bounds.top+x*.79),new C(r.bounds.left+x*.16,r.bounds.top+x*.5549),new C(r.bounds.left+x*.27347,r.bounds.top+x*.44071),new C(r.bounds.left+x*.39694,r.bounds.top+x*.5649),new C(r.bounds.left+x*.72983,r.bounds.top+x*.23),new C(r.bounds.left+x*.84,r.bounds.top+x*.34085),new C(r.bounds.left+x*.39363,r.bounds.top+x*.79)]),this.ctx.fillStyle=W(Rn),this.ctx.fill(),this.ctx.restore()):r.type===mt&&r.checked&&(this.ctx.save(),this.ctx.beginPath(),this.ctx.arc(r.bounds.left+x/2,r.bounds.top+x/2,x/4,0,Math.PI*2,!0),this.ctx.fillStyle=W(Rn),this.ctx.fill(),this.ctx.restore())),Uc(r)&&r.value.length){switch(u=this.createFontStyle(s),E=u[0],h=u[1],g=this.fontMetrics.getMetrics(E,h).baseline,this.ctx.font=E,this.ctx.fillStyle=W(s.color),this.ctx.textBaseline="alphabetic",this.ctx.textAlign=mc(r.styles.textAlign),m=It(r),d=0,r.styles.textAlign){case 1:d+=m.width/2;break;case 2:d+=m.width;break}F=m.add(d,0,0,-m.height/2+1),this.ctx.save(),this.path([new C(m.left,m.top),new C(m.left+m.width,m.top),new C(m.left+m.width,m.top+m.height),new C(m.left,m.top+m.height)]),this.ctx.clip(),this.renderTextWithLetterSpacing(new Te(r.value,F),s.letterSpacing,g),this.ctx.restore(),this.ctx.textBaseline="alphabetic",this.ctx.textAlign="left"}if(!Y(r.styles.display,2048))return[3,20];if(r.styles.listStyleImage===null)return[3,19];if(f=r.styles.listStyleImage,f.type!==0)return[3,18];w=void 0,v=f.url,Q.label=15;case 15:return Q.trys.push([15,17,,18]),[4,this.context.cache.match(v)];case 16:return w=Q.sent(),this.ctx.drawImage(w,r.bounds.left-(w.width+10),r.bounds.top),[3,18];case 17:return Q.sent(),this.context.logger.error("Error loading list-style-image "+v),[3,18];case 18:return[3,20];case 19:t.listValue&&r.styles.listStyleType!==-1&&(E=this.createFontStyle(s)[0],this.ctx.font=E,this.ctx.fillStyle=W(s.color),this.ctx.textBaseline="middle",this.ctx.textAlign="right",m=new LA(r.bounds.left,r.bounds.top+R(r.styles.paddingTop,r.bounds.width),r.bounds.width,Un(s.lineHeight,s.fontSize.number)/2+1),this.renderTextWithLetterSpacing(new Te(t.listValue,m),s.letterSpacing,Un(s.lineHeight,s.fontSize.number)/2+2),this.ctx.textBaseline="bottom",this.ctx.textAlign="left"),Q.label=20;case 20:return[2]}})})},A.prototype.renderStackContent=function(t){return AA(this,void 0,void 0,function(){var r,n,f,s,a,f,i,o,f,l,B,f,x,u,f,h,g,f,d,F,f;return j(this,function(w){switch(w.label){case 0:if(Y(t.element.container.flags,16))debugger;return[4,this.renderNodeBackgroundAndBorders(t.element)];case 1:w.sent(),r=0,n=t.negativeZIndex,w.label=2;case 2:return r<n.length?(f=n[r],[4,this.renderStack(f)]):[3,5];case 3:w.sent(),w.label=4;case 4:return r++,[3,2];case 5:return[4,this.renderNodeContent(t.element)];case 6:w.sent(),s=0,a=t.nonInlineLevel,w.label=7;case 7:return s<a.length?(f=a[s],[4,this.renderNode(f)]):[3,10];case 8:w.sent(),w.label=9;case 9:return s++,[3,7];case 10:i=0,o=t.nonPositionedFloats,w.label=11;case 11:return i<o.length?(f=o[i],[4,this.renderStack(f)]):[3,14];case 12:w.sent(),w.label=13;case 13:return i++,[3,11];case 14:l=0,B=t.nonPositionedInlineLevel,w.label=15;case 15:return l<B.length?(f=B[l],[4,this.renderStack(f)]):[3,18];case 16:w.sent(),w.label=17;case 17:return l++,[3,15];case 18:x=0,u=t.inlineLevel,w.label=19;case 19:return x<u.length?(f=u[x],[4,this.renderNode(f)]):[3,22];case 20:w.sent(),w.label=21;case 21:return x++,[3,19];case 22:h=0,g=t.zeroOrAutoZIndexOrTransformedOrOpacity,w.label=23;case 23:return h<g.length?(f=g[h],[4,this.renderStack(f)]):[3,26];case 24:w.sent(),w.label=25;case 25:return h++,[3,23];case 26:d=0,F=t.positiveZIndex,w.label=27;case 27:return d<F.length?(f=F[d],[4,this.renderStack(f)]):[3,30];case 28:w.sent(),w.label=29;case 29:return d++,[3,27];case 30:return[2]}})})},A.prototype.mask=function(t){this.ctx.beginPath(),this.ctx.moveTo(0,0),this.ctx.lineTo(this.canvas.width,0),this.ctx.lineTo(this.canvas.width,this.canvas.height),this.ctx.lineTo(0,this.canvas.height),this.ctx.lineTo(0,0),this.formatPath(t.slice(0).reverse()),this.ctx.closePath()},A.prototype.path=function(t){this.ctx.beginPath(),this.formatPath(t),this.ctx.closePath()},A.prototype.formatPath=function(t){var r=this;t.forEach(function(n,s){var a=xA(n)?n.start:n;s===0?r.ctx.moveTo(a.x,a.y):r.ctx.lineTo(a.x,a.y),xA(n)&&r.ctx.bezierCurveTo(n.startControl.x,n.startControl.y,n.endControl.x,n.endControl.y,n.end.x,n.end.y)})},A.prototype.renderRepeat=function(t,r,n,s){this.path(t),this.ctx.fillStyle=r,this.ctx.translate(n,s),this.ctx.fill(),this.ctx.translate(-n,-s)},A.prototype.resizeImage=function(t,r,n){var s;if(t.width===r&&t.height===n)return t;var a=(s=this.canvas.ownerDocument)!==null&&s!==void 0?s:document,i=a.createElement("canvas");i.width=Math.max(1,r),i.height=Math.max(1,n);var o=i.getContext("2d");return o.drawImage(t,0,0,t.width,t.height,0,0,r,n),i},A.prototype.renderBackgroundImage=function(t){return AA(this,void 0,void 0,function(){var r,n,s,a,i,o;return j(this,function(l){switch(l.label){case 0:r=t.styles.backgroundImage.length-1,n=function(B){var x,u,h,_,tA,rA,T,Z,S,g,_,tA,rA,T,Z,d,F,f,w,v,E,m,Q,b,S,y,_,eA,oA,T,Z,KA,tA,rA,XA,QA,TA,YA,WA,mA,ZA,vA;return j(this,function(ae){switch(ae.label){case 0:if(B.type!==0)return[3,5];x=void 0,u=B.url,ae.label=1;case 1:return ae.trys.push([1,3,,4]),[4,s.context.cache.match(u)];case 2:return x=ae.sent(),[3,4];case 3:return ae.sent(),s.context.logger.error("Error loading background-image "+u),[3,4];case 4:return x&&(h=or(t,r,[x.width,x.height,x.width/x.height]),_=h[0],tA=h[1],rA=h[2],T=h[3],Z=h[4],S=s.ctx.createPattern(s.resizeImage(x,T,Z),"repeat"),s.renderRepeat(_,S,tA,rA)),[3,6];case 5:ao(B)?(g=or(t,r,[null,null,null]),_=g[0],tA=g[1],rA=g[2],T=g[3],Z=g[4],d=eo(B.angle,T,Z),F=d[0],f=d[1],w=d[2],v=d[3],E=d[4],m=document.createElement("canvas"),m.width=T,m.height=Z,Q=m.getContext("2d"),b=Q.createLinearGradient(f,v,w,E),Cn(B.stops,F).forEach(function(Ce){return b.addColorStop(Ce.stop,W(Ce.color))}),Q.fillStyle=b,Q.fillRect(0,0,T,Z),T>0&&Z>0&&(S=s.ctx.createPattern(m,"repeat"),s.renderRepeat(_,S,tA,rA))):io(B)&&(y=or(t,r,[null,null,null]),_=y[0],eA=y[1],oA=y[2],T=y[3],Z=y[4],KA=B.position.length===0?[Jr]:B.position,tA=R(KA[0],T),rA=R(KA[KA.length-1],Z),XA=to(B,tA,rA,T,Z),QA=XA[0],TA=XA[1],QA>0&&TA>0&&(YA=s.ctx.createRadialGradient(eA+tA,oA+rA,0,eA+tA,oA+rA,QA),Cn(B.stops,QA*2).forEach(function(Ce){return YA.addColorStop(Ce.stop,W(Ce.color))}),s.path(_),s.ctx.fillStyle=YA,QA!==TA?(WA=t.bounds.left+.5*t.bounds.width,mA=t.bounds.top+.5*t.bounds.height,ZA=TA/QA,vA=1/ZA,s.ctx.save(),s.ctx.translate(WA,mA),s.ctx.transform(1,0,0,ZA,0,0),s.ctx.translate(-WA,-mA),s.ctx.fillRect(eA,vA*(oA-mA)+mA,T,Z*vA),s.ctx.restore()):s.ctx.fill())),ae.label=6;case 6:return r--,[2]}})},s=this,a=0,i=t.styles.backgroundImage.slice(0).reverse(),l.label=1;case 1:return a<i.length?(o=i[a],[5,n(o)]):[3,4];case 2:l.sent(),l.label=3;case 3:return a++,[3,1];case 4:return[2]}})})},A.prototype.renderSolidBorder=function(t,r,n){return AA(this,void 0,void 0,function(){return j(this,function(s){return this.path(qn(n,r)),this.ctx.fillStyle=W(t),this.ctx.fill(),[2]})})},A.prototype.renderDoubleBorder=function(t,r,n,s){return AA(this,void 0,void 0,function(){var a,i;return j(this,function(o){switch(o.label){case 0:return r<3?[4,this.renderSolidBorder(t,n,s)]:[3,2];case 1:return o.sent(),[2];case 2:return a=cc(s,n),this.path(a),this.ctx.fillStyle=W(t),this.ctx.fill(),i=xc(s,n),this.path(i),this.ctx.fill(),[2]}})})},A.prototype.renderNodeBackgroundAndBorders=function(t){return AA(this,void 0,void 0,function(){var r,n,s,a,i,o,l,B,x=this;return j(this,function(u){switch(u.label){case 0:return this.applyEffects(t.getEffects(2)),r=t.container.styles,n=!PA(r.backgroundColor)||r.backgroundImage.length,s=[{style:r.borderTopStyle,color:r.borderTopColor,width:r.borderTopWidth},{style:r.borderRightStyle,color:r.borderRightColor,width:r.borderRightWidth},{style:r.borderBottomStyle,color:r.borderBottomColor,width:r.borderBottomWidth},{style:r.borderLeftStyle,color:r.borderLeftColor,width:r.borderLeftWidth}],a=Fc(ge(r.backgroundClip,0),t.curves),n||r.boxShadow.length?(this.ctx.save(),this.path(a),this.ctx.clip(),PA(r.backgroundColor)||(this.ctx.fillStyle=W(r.backgroundColor),this.ctx.fill()),[4,this.renderBackgroundImage(t.container)]):[3,2];case 1:u.sent(),this.ctx.restore(),r.boxShadow.slice(0).reverse().forEach(function(h){x.ctx.save();var g=yt(t.curves),d=h.inset?0:Cc,F=lc(g,-d+(h.inset?1:-1)*h.spread.number,(h.inset?1:-1)*h.spread.number,h.spread.number*(h.inset?-2:2),h.spread.number*(h.inset?-2:2));h.inset?(x.path(g),x.ctx.clip(),x.mask(F)):(x.mask(g),x.ctx.clip(),x.path(F)),x.ctx.shadowOffsetX=h.offsetX.number+d,x.ctx.shadowOffsetY=h.offsetY.number,x.ctx.shadowColor=W(h.color),x.ctx.shadowBlur=h.blur.number,x.ctx.fillStyle=h.inset?W(h.color):"rgba(0,0,0,1)",x.ctx.fill(),x.ctx.restore()}),u.label=2;case 2:i=0,o=0,l=s,u.label=3;case 3:return o<l.length?(B=l[o],B.style!==0&&!PA(B.color)&&B.width>0?B.style!==2?[3,5]:[4,this.renderDashedDottedBorder(B.color,B.width,i,t.curves,2)]:[3,11]):[3,13];case 4:return u.sent(),[3,11];case 5:return B.style!==3?[3,7]:[4,this.renderDashedDottedBorder(B.color,B.width,i,t.curves,3)];case 6:return u.sent(),[3,11];case 7:return B.style!==4?[3,9]:[4,this.renderDoubleBorder(B.color,B.width,i,t.curves)];case 8:return u.sent(),[3,11];case 9:return[4,this.renderSolidBorder(B.color,i,t.curves)];case 10:u.sent(),u.label=11;case 11:i++,u.label=12;case 12:return o++,[3,3];case 13:return[2]}})})},A.prototype.renderDashedDottedBorder=function(t,r,n,s,a){return AA(this,void 0,void 0,function(){var i,o,l,B,x,u,h,g,d,F,f,w,v,E,m,Q,m,Q;return j(this,function(b){return this.ctx.save(),i=uc(s,n),o=qn(s,n),a===2&&(this.path(o),this.ctx.clip()),xA(o[0])?(l=o[0].start.x,B=o[0].start.y):(l=o[0].x,B=o[0].y),xA(o[1])?(x=o[1].end.x,u=o[1].end.y):(x=o[1].x,u=o[1].y),n===0||n===2?h=Math.abs(l-x):h=Math.abs(B-u),this.ctx.beginPath(),a===3?this.formatPath(i):this.formatPath(o.slice(0,2)),g=r<3?r*3:r*2,d=r<3?r*2:r,a===3&&(g=r,d=r),F=!0,h<=g*2?F=!1:h<=g*2+d?(f=h/(2*g+d),g*=f,d*=f):(w=Math.floor((h+d)/(g+d)),v=(h-w*g)/(w-1),E=(h-(w+1)*g)/w,d=E<=0||Math.abs(d-v)<Math.abs(d-E)?v:E),F&&(a===3?this.ctx.setLineDash([0,g+d]):this.ctx.setLineDash([g,d])),a===3?(this.ctx.lineCap="round",this.ctx.lineWidth=r):this.ctx.lineWidth=r*2+1.1,this.ctx.strokeStyle=W(t),this.ctx.stroke(),this.ctx.setLineDash([]),a===2&&(xA(o[0])&&(m=o[3],Q=o[0],this.ctx.beginPath(),this.formatPath([new C(m.end.x,m.end.y),new C(Q.start.x,Q.start.y)]),this.ctx.stroke()),xA(o[1])&&(m=o[1],Q=o[2],this.ctx.beginPath(),this.formatPath([new C(m.end.x,m.end.y),new C(Q.start.x,Q.start.y)]),this.ctx.stroke())),this.ctx.restore(),[2]})})},A.prototype.render=function(t){return AA(this,void 0,void 0,function(){var r;return j(this,function(n){switch(n.label){case 0:return this.options.backgroundColor&&(this.ctx.fillStyle=W(this.options.backgroundColor),this.ctx.fillRect(this.options.x,this.options.y,this.options.width,this.options.height)),r=Bc(t),[4,this.renderStack(r)];case 1:return n.sent(),this.applyEffects([]),[2,this.canvas]}})})},A}(sa),Uc=function(e){return e instanceof Vs||e instanceof ks?!0:e instanceof Zr&&e.type!==mt&&e.type!==Ft},Fc=function(e,A){switch(e){case 0:return yt(A);case 2:return nc(A);case 1:default:return Ht(A)}},mc=function(e){switch(e){case 1:return"center";case 2:return"right";case 0:default:return"left"}},vc=["-apple-system","system-ui"],Ec=function(e){return/iPhone OS 15_(0|1)/.test(window.navigator.userAgent)?e.filter(function(A){return vc.indexOf(A)===-1}):e},yc=function(e){fA(A,e);function A(t,r){var n=e.call(this,t,r)||this;return n.canvas=r.canvas?r.canvas:document.createElement("canvas"),n.ctx=n.canvas.getContext("2d"),n.options=r,n.canvas.width=Math.floor(r.width*r.scale),n.canvas.height=Math.floor(r.height*r.scale),n.canvas.style.width=r.width+"px",n.canvas.style.height=r.height+"px",n.ctx.scale(n.options.scale,n.options.scale),n.ctx.translate(-r.x,-r.y),n.context.logger.debug("EXPERIMENTAL ForeignObject renderer initialized ("+r.width+"x"+r.height+" at "+r.x+","+r.y+") with scale "+r.scale),n}return A.prototype.render=function(t){return AA(this,void 0,void 0,function(){var r,n;return j(this,function(s){switch(s.label){case 0:return r=yr(this.options.width*this.options.scale,this.options.height*this.options.scale,this.options.scale,this.options.scale,t),[4,Hc(r)];case 1:return n=s.sent(),this.options.backgroundColor&&(this.ctx.fillStyle=W(this.options.backgroundColor),this.ctx.fillRect(0,0,this.options.width*this.options.scale,this.options.height*this.options.scale)),this.ctx.drawImage(n,-this.options.x*this.options.scale,-this.options.y*this.options.scale),[2,this.canvas]}})})},A}(sa),Hc=function(e){return new Promise(function(A,t){var r=new Image;r.onload=function(){A(r)},r.onerror=t,r.src="data:image/svg+xml;charset=utf-8,"+encodeURIComponent(new XMLSerializer().serializeToString(e))})},Ic=function(){function e(A){var t=A.id,r=A.enabled;this.id=t,this.enabled=r,this.start=Date.now()}return e.prototype.debug=function(){for(var A=[],t=0;t<arguments.length;t++)A[t]=arguments[t];this.enabled&&(typeof window<"u"&&window.console&&typeof console.debug=="function"?console.debug.apply(console,Je([this.id,this.getTime()+"ms"],A)):this.info.apply(this,A))},e.prototype.getTime=function(){return Date.now()-this.start},e.prototype.info=function(){for(var A=[],t=0;t<arguments.length;t++)A[t]=arguments[t];this.enabled&&typeof window<"u"&&window.console&&typeof console.info=="function"&&console.info.apply(console,Je([this.id,this.getTime()+"ms"],A))},e.prototype.warn=function(){for(var A=[],t=0;t<arguments.length;t++)A[t]=arguments[t];this.enabled&&(typeof window<"u"&&window.console&&typeof console.warn=="function"?console.warn.apply(console,Je([this.id,this.getTime()+"ms"],A)):this.info.apply(this,A))},e.prototype.error=function(){for(var A=[],t=0;t<arguments.length;t++)A[t]=arguments[t];this.enabled&&(typeof window<"u"&&window.console&&typeof console.error=="function"?console.error.apply(console,Je([this.id,this.getTime()+"ms"],A)):this.info.apply(this,A))},e.instances={},e}(),bc=function(){function e(A,t){var r;this.windowBounds=t,this.instanceName="#"+e.instanceCount++,this.logger=new Ic({id:this.instanceName,enabled:A.logging}),this.cache=(r=A.cache)!==null&&r!==void 0?r:new zB(this,A)}return e.instanceCount=1,e}(),Sc=function(e,A){return A===void 0&&(A={}),Lc(e,A)};typeof window<"u"&&$s.setContext(window);var Lc=function(e,A){return AA(void 0,void 0,void 0,function(){var t,r,n,s,a,i,o,l,B,x,u,h,g,d,F,f,w,v,E,m,b,Q,b,S,y,_,eA,oA,T,Z,KA,tA,rA,XA,QA,TA,YA,WA,mA,ZA;return j(this,function(vA){switch(vA.label){case 0:if(!e||typeof e!="object")return[2,Promise.reject("Invalid element provided as first argument")];if(t=e.ownerDocument,!t)throw new Error("Element is not attached to a Document");if(r=t.defaultView,!r)throw new Error("Document is not attached to a Window");return n={allowTaint:(S=A.allowTaint)!==null&&S!==void 0?S:!1,imageTimeout:(y=A.imageTimeout)!==null&&y!==void 0?y:15e3,proxy:A.proxy,useCORS:(_=A.useCORS)!==null&&_!==void 0?_:!1},s=xr({logging:(eA=A.logging)!==null&&eA!==void 0?eA:!0,cache:A.cache},n),a={windowWidth:(oA=A.windowWidth)!==null&&oA!==void 0?oA:r.innerWidth,windowHeight:(T=A.windowHeight)!==null&&T!==void 0?T:r.innerHeight,scrollX:(Z=A.scrollX)!==null&&Z!==void 0?Z:r.pageXOffset,scrollY:(KA=A.scrollY)!==null&&KA!==void 0?KA:r.pageYOffset},i=new LA(a.scrollX,a.scrollY,a.windowWidth,a.windowHeight),o=new bc(s,i),l=(tA=A.foreignObjectRendering)!==null&&tA!==void 0?tA:!1,B={allowTaint:(rA=A.allowTaint)!==null&&rA!==void 0?rA:!1,onclone:A.onclone,ignoreElements:A.ignoreElements,inlineImages:l,copyStyles:l},o.logger.debug("Starting document clone with size "+i.width+"x"+i.height+" scrolled to "+-i.left+","+-i.top),x=new Wn(o,e,B),u=x.clonedReferenceElement,u?[4,x.toIFrame(t,i)]:[2,Promise.reject("Unable to find element in cloned iframe")];case 1:return h=vA.sent(),g=zr(u)||DB(u)?oa(u.ownerDocument):bt(o,u),d=g.width,F=g.height,f=g.left,w=g.top,v=Dc(o,u,A.backgroundColor),E={canvas:A.canvas,backgroundColor:v,scale:(QA=(XA=A.scale)!==null&&XA!==void 0?XA:r.devicePixelRatio)!==null&&QA!==void 0?QA:1,x:((TA=A.x)!==null&&TA!==void 0?TA:0)+f,y:((YA=A.y)!==null&&YA!==void 0?YA:0)+w,width:(WA=A.width)!==null&&WA!==void 0?WA:Math.ceil(d),height:(mA=A.height)!==null&&mA!==void 0?mA:Math.ceil(F)},l?(o.logger.debug("Document cloned, using foreign object rendering"),b=new yc(o,E),[4,b.render(u)]):[3,3];case 2:return m=vA.sent(),[3,5];case 3:return o.logger.debug("Document cloned, element located at "+f+","+w+" with size "+d+"x"+F+" using computed rendering"),o.logger.debug("Starting DOM parsing"),Q=Js(o,u),v===Q.styles.backgroundColor&&(Q.styles.backgroundColor=SA.TRANSPARENT),o.logger.debug("Starting renderer for element at "+E.x+","+E.y+" with size "+E.width+"x"+E.height),b=new pc(o,E),[4,b.render(Q)];case 4:m=vA.sent(),vA.label=5;case 5:return(!((ZA=A.removeContainer)!==null&&ZA!==void 0)||ZA)&&(Wn.destroy(h)||o.logger.error("Cannot detach cloned iframe as it is not in the DOM anymore")),o.logger.debug("Finished rendering"),[2,m]}})})},Dc=function(e,A,t){var r=A.ownerDocument,n=r.documentElement?De(e,getComputedStyle(r.documentElement).backgroundColor):SA.TRANSPARENT,s=r.body?De(e,getComputedStyle(r.body).backgroundColor):SA.TRANSPARENT,a=typeof t=="string"?De(e,t):t===null?SA.TRANSPARENT:4294967295;return A===r.documentElement?PA(n)?PA(s)?a:s:n:a};function M(e,A){const t=Kr();return M=function(r,n){return r=r-0,t[r]},M(e,A)}function Kr(){const e=["include","json",".custom-suggest-item","updateSelection","selectSuggestion","hideSuggestions","none","trim","custom-amap-suggest",`
            position: fixed;
            background: var(--vp-c-bg);
            border: 2px solid var(--vp-c-divider);
            border-radius: 12px;
            box-shadow: 0 12px 40px rgba(0, 0, 0, 0.2);
            z-index: 99999;
            max-height: 300px;
            overflow-y: auto;
            display: none;
            backdrop-filter: blur(15px);
            -webkit-backdrop-filter: blur(15px);
            min-width: 300px;
        `,"min","search","positionSuggestContainer",`
                font-weight: 500;
                color: var(--vp-c-text-1);
                font-size: 0.875rem;
            `,"createElement","textContent","display","background","classList","var(--vp-c-bg-soft)","div:last-child","style","setProperty","querySelectorAll","important","complete","canvas","LngLat","获取驾车数据失败：","地图初始化失败: ","message","selectedDayIndex","hidden","length","toDataURL","expandedSectionType","map-container-","block","visibility","地图截图失败: 画布尺寸为 ","/api/save_amap_img","POST","destroy","销毁地图实例 ","forEach","start-tipinput","end-tipinput","warn","sqrt","isArray","map","error"];return Kr=function(){return e},Kr()}const lr="http://localhost:3999";class Kc{constructor(){this.AMap=null,this.mapInstances=[]}async initialize(){const A=M;if(typeof window>"u")return;let t="",r="";try{const n=await fetch(lr+"/api/amap_keys",{method:"GET",credentials:A(0)});if(!n.ok)throw D.error("获取API密钥失败，请检查网络连接",n),new Error("获取API密钥失败，请检查网络连接");const{AMAP_CODE:s,AMAP_KEY:a}=await n[A(1)]();t=s,r=a}catch(n){throw D.error("获取API密钥异常，请检查网络连接",n),n}window._AMapSecurityConfig={securityJsCode:t};try{return this.AMap=await this.loadAMapScript(r),this.AMap}catch(n){throw new Error("地图初始化失败: "+n.message)}}setupAutoComplete(A,t,r,n){if(!this.AMap)throw new Error("地图服务未初始化");this.createCustomAutoComplete(A,r),this.createCustomAutoComplete(t,n)}createCustomAutoComplete(A,t){const r=document.getElementById(A);if(!r)return;const n=this.createSuggestContainer(A);this.AMap.plugin(["AMap.PlaceSearch"],()=>{const s=new this.AMap.PlaceSearch({pageSize:10,pageIndex:1,citylimit:!1,extensions:"all"});let a=null,i=-1;const o=h=>{const g=h.target.value.trim();if(g.length<2){this.hideSuggestions(n);return}a&&clearTimeout(a),a=setTimeout(()=>{this.searchPlaces(s,g,n,t,r)},300)},l=h=>{const g=M,d=n.querySelectorAll(g(2));switch(h.key){case"ArrowDown":h.preventDefault(),i=Math.min(i+1,d.length-1),this.updateSelection(d,i);break;case"ArrowUp":h.preventDefault(),i=Math.max(i-1,-1),this[g(3)](d,i);break;case"Enter":h.preventDefault(),i>=0&&d[i]&&this[g(4)](d[i],t,r,n);break;case"Escape":this[g(5)](n),r.blur();break}},B=()=>{setTimeout(()=>{!n.matches(":hover")&&this.hideSuggestions(n)},150)},x=()=>{n.style.display!=="none"&&this.positionSuggestContainer(n,r)},u=()=>{const h=M;n.style.display!==h(6)&&this.positionSuggestContainer(n,r)};r.addEventListener("input",o),r.addEventListener("keydown",l),r.addEventListener("blur",B),r.addEventListener("focus",()=>{const h=M;if(r.value.trim().length>=2){const g=r.value[h(7)]();this.searchPlaces(s,g,n,t,r)}}),window.addEventListener("resize",x),window.addEventListener("scroll",u,!0),r._cleanup=()=>{r.removeEventListener("input",o),r.removeEventListener("keydown",l),r.removeEventListener("blur",B),window.removeEventListener("resize",x),window.removeEventListener("scroll",u,!0),a&&clearTimeout(a),n.parentNode&&n.parentNode.removeChild(n)}})}createSuggestContainer(A){const t=M;document.getElementById(A);const r=document.createElement("div");return r.className=t(8),r.id=A+"-suggest",r.style.cssText=t(9),document.body.appendChild(r),r}positionSuggestContainer(A,t){const r=M,n=t.getBoundingClientRect(),s=window.innerHeight,a=300,i=s-n.bottom,o=n.top;i>=a||i>=o?(A.style.top=n.bottom+4+"px",A.style.maxHeight=Math.min(a,i-20)+"px"):(A.style.top=n.top-Math[r(10)](a,o-20)+"px",A.style.maxHeight=Math[r(10)](a,o-20)+"px"),A.style.left=n.left+"px",A.style.width=Math.max(n.width,300)+"px"}searchPlaces(A,t,r,n,s){A[M(11)](t,(i,o)=>{i==="complete"&&o.poiList&&o.poiList.pois?this.showSuggestions(r,o.poiList.pois,n,s):this.hideSuggestions(r)})}showSuggestions(A,t,r,n){const s=M;A.innerHTML="",this[s(12)](A,n),t.slice(0,8).forEach((a,i)=>{const o=s,l=document.createElement("div");l.className="custom-suggest-item",l.style.cssText=`
                padding: 12px 16px;
                cursor: pointer;
                border-bottom: 1px solid var(--vp-c-divider-light);
                transition: all 0.2s ease;
                display: flex;
                flex-direction: column;
                gap: 4px;
            `;const B=document.createElement("div");B.style.cssText=o(13),B.textContent=a.name;const x=document[o(14)]("div");x.style.cssText=`
                color: var(--vp-c-text-2);
                font-size: 0.8rem;
                line-height: 1.3;
            `,x[o(15)]=a.address||a.pname+a.cityname+a.adname,l.appendChild(B),l.appendChild(x),l.addEventListener("mouseenter",()=>{this.clearSelection(A)}),l.addEventListener("click",u=>{u.preventDefault(),u.stopPropagation(),this.selectSuggestion(l,r,n,A,a)}),(i===t.length-1||i===7)&&(l.style.borderBottom="none"),A.appendChild(l)}),A.style.display="block",this.updateThemeStyles(A)}hideSuggestions(A){const t=M;A.style[t(16)]="none",A.innerHTML=""}updateSelection(A,t){this.clearSelection(A[0].parentNode),t>=0&&A[t]&&(A[t].classList.add("selected"),A[t].scrollIntoView({block:"nearest",behavior:"smooth"}))}clearSelection(A){A.querySelectorAll(".custom-suggest-item").forEach(r=>{const n=M;r.style.removeProperty(n(17)),r.style.removeProperty("color"),r[n(18)].remove("selected")})}selectSuggestion(A,t,r,n,s=null){const a=M;if(!s){const o=A.querySelector("div:first-child")[a(15)],l=A.querySelector("div:last-child").textContent;s={name:o,address:l}}r.value=s.name;const i=this.extractLocationInfo(s);t(s.name,i),this.hideSuggestions(n),r.blur()}updateThemeStyles(A){const t=M;document.documentElement.classList.contains("dark")?(A.style.setProperty("background",t(19),"important"),A.style.setProperty("border-color","var(--vp-c-divider)","important"),A.style.setProperty("box-shadow","0 12px 40px rgba(0, 0, 0, 0.5)","important"),A.querySelectorAll(".custom-suggest-item").forEach(s=>{const a=t,i=s.querySelector("div:first-child"),o=s.querySelector(a(20));i&&i[a(21)].setProperty("color","var(--vp-c-text-1)","important"),o&&o.style.setProperty("color","var(--vp-c-text-2)","important")})):(A.style.setProperty("background","var(--vp-c-bg)","important"),A.style[t(22)]("border-color","var(--vp-c-divider)","important"),A.style.setProperty("box-shadow","0 12px 40px rgba(0, 0, 0, 0.2)","important"),A[t(23)](".custom-suggest-item").forEach(s=>{const a=t,i=s.querySelector("div:first-child"),o=s.querySelector("div:last-child");i&&i.style.setProperty("color","var(--vp-c-text-1)","important"),o&&o.style.setProperty("color","var(--vp-c-text-2)",a(24))}))}extractLocationInfo(A){var n,s;if(!A)return null;const t=A.district||A.address||"",r=this.parseAddress(t);return{lng:((n=A.location)==null?void 0:n.lng)||null,lat:((s=A.location)==null?void 0:s.lat)||null,province:r.province||null,city:r.city||null,district:r.district||null,address:t,adcode:A.adcode||null}}parseAddress(A){if(!A)return{province:null,city:null,district:null};const t=/(.*?)(省|自治区|市|特别行政区)/,r=/(.*?)(市|地区|州|盟)/,n=/(.*?)(区|县|市|旗)/;let s=null,a=null,i=null;const o=A.match(t);o&&(s=o[0],A=A.replace(s,""));const l=A.match(r);l&&(a=l[0],A=A.replace(a,""));const B=A.match(n);return B&&(i=B[0]),{province:s,city:a,district:i}}loadAMapScript(A){return new Promise(async(t,r)=>{if(window.AMap)return t(window.AMap);try{const n=await fetch(lr+"/api/amap",{method:"GET",credentials:"include"});if(!n.ok)throw new Error("地图脚本加载失败，请检查网络连接");const{scriptUrl:s}=await n.json(),a=document.createElement("script");a.src=s,a.onload=()=>t(window.AMap),a.onerror=i=>r(new Error("地图脚本加载失败，请检查网络连接",{cause:i})),document.head.appendChild(a)}catch(n){r(new Error("地图脚本加载失败，请检查网络连接",{cause:n}))}})}async getGeocodePosition(A,t){return new Promise((r,n)=>{A.getLocation(t,(s,a)=>{s===M(25)&&a.geocodes.length?r(a.geocodes[0].location):n(new Error("无法解析地址: "+t))})})}async initMap(A,t,r,n,s){const a=M;if(!this.AMap)throw new Error("地图服务未初始化");const{s_lng:i,s_lat:o}=t,{e_lng:l,e_lat:B}=r;try{const x=new this.AMap.Map("map-container-"+A,{renderer:a(26),resizeEnable:!0,viewMode:"2D",crossOrigin:"anonymous",WebGLParams:{preserveDrawingBuffer:!0}});for(;this.mapInstances.length<=A;)this.mapInstances.push(null);this.mapInstances[A]=x;const u=new this.AMap.Driving({map:x,panel:"",renderer:a(26),policy:n});return await new Promise((h,g)=>{const d=a,F=setTimeout(()=>{g(new Error("路线规划超时: "+A))},15e3);u.search(new AMap.LngLat(i,o),new AMap[d(27)](l,B),{waypoints:s},function(f,w){const v=d;clearTimeout(F),f!=="complete"?(D.error(v(28)+w),g(new Error("路线规划失败: "+w))):setTimeout(()=>{h()},1e3)})}),x}catch(x){throw D.error("地图初始化失败:",x),new Error(a(29)+x.message)}}async drivingPlanning(A,t,r,n=0,s=[]){const a=M;try{if(!document.getElementById("map-container-"+A))throw D.error("地图容器未找到"),new Error("地图容器未找到");this.destroyMapInstance(A),await this.initMap(A,t,r,n,s)}catch(i){throw D.error("路线生成错误",i),new Error(i[a(30)]||"路线生成失败")}}async saveMapAsImage(A,t,r,n=null){const s=M;let a=null,i=null,o=!1;try{const l=document.getElementById("map-container-"+A);if(!l)throw new Error("地图容器未找到");const B=this.mapInstances[A];if(!B)throw new Error("地图实例 "+A+" 未找到");const x=l.closest(".planning-box");if(x&&n){const f=window.getComputedStyle(x);(f.display==="none"||f.visibility==="hidden")&&(a=n.expandedSectionType,i=n.selectedDayIndex,o=!0,n.expandedSectionType="driving",n[s(31)]=A,await n.$nextTick(),await new Promise(v=>setTimeout(v,1e3)))}const u=l.getBoundingClientRect();if(u.width===0||u.height===0){const f=[];let w=l;for(;w&&w!==document.body;){const v=window.getComputedStyle(w);v.display==="none"&&(f.push({element:w,originalDisplay:w.style.display}),w.style.display="block"),v.visibility===s(32)&&(f.push({element:w,originalVisibility:w.style.visibility}),w.style.visibility="visible"),w=w.parentElement}await new Promise(v=>setTimeout(v,500)),typeof B.resize=="function"&&(B.resize(),await new Promise(v=>setTimeout(v,1e3)));try{const v=await this.captureMapCanvas(l,A),E=v.toDataURL("image/png");if(!E||E[s(33)]<1e3)throw new Error("地图图片数据无效或过小");return await this.uploadMapImage(E,t,r,A)}finally{f.forEach(({element:v,originalDisplay:E,originalVisibility:m})=>{E!==void 0&&(v.style.display=E),m!==void 0&&(v.style.visibility=m)})}}await new Promise(f=>setTimeout(f,2e3)),!l.querySelector(".amap-container")&&await new Promise(f=>setTimeout(f,3e3)),typeof B.resize=="function"&&(B.resize(),await new Promise(f=>setTimeout(f,500)));const g=await this.captureMapCanvas(l,A),d=g[s(34)]("image/png");if(!d||d[s(33)]<1e3)throw new Error("地图图片数据无效或过小");return await this.uploadMapImage(d,t,r,A)}catch(l){throw D.error("保存地图为图片失败:",l),l}finally{o&&n&&setTimeout(()=>{const l=s;n[l(35)]=a,n.selectedDayIndex=i},500)}}async captureMapCanvas(A,t){const r=M,n=await Sc(A,{useCORS:!0,allowTaint:!0,logging:!1,scale:2,backgroundColor:"#f5f5f5",onclone:s=>{const a=M,i=s.getElementById(a(36)+t);i&&(i.style.display="block",i.style.visibility="visible",i.style.opacity="1",i.style.position="relative",i.style.zIndex="1");let o=i==null?void 0:i.parentElement;for(;o&&o!==s.body;)o.style.display=a(37),o.style[a(38)]="visible",o.style.opacity="1",o=o.parentElement},ignoreElements:s=>{const a=M;return s.classList.contains("map-controls")||s[a(18)].contains("amap-copyright")||s.classList.contains("header-toggle")||s.id==="some-obstructive-element"}});if(!n||n.width===0||n.height===0)throw new Error(r(39)+((n==null?void 0:n.width)||0)+"x"+((n==null?void 0:n.height)||0));return n}async uploadMapImage(A,t,r,n){const s=M,a=await fetch(lr+s(40),{method:s(41),headers:{"Content-Type":"application/json"},body:JSON.stringify({image:A,user:t,filename:"map-"+r+"-"+n+".png"})});if(!a.ok)throw D.error("图片保存失败，请检查网络连接",a),new Error("图片保存失败");return await a.json()}destroyMapInstance(A){const t=M;if(this.mapInstances[A]){try{this.mapInstances[A][t(42)]()}catch(r){D.warn(t(43)+A+" 时出错:",r)}this.mapInstances[A]=null}}cleanup(){const A=M;this.mapInstances[A(44)]((t,r)=>{if(t&&t.destroy)try{t.destroy()}catch(n){D.warn("清理地图实例 "+r+" 时出错:",n)}}),this.mapInstances=[],this.cleanupCustomAutoComplete()}cleanupCustomAutoComplete(){const A=M;[A(45),A(46)].forEach(r=>{const n=document.getElementById(r);n&&n._cleanup&&(n._cleanup(),delete n._cleanup);const s=document.getElementById(r+"-suggest");s&&s.parentNode&&s.parentNode.removeChild(s)})}getMapInstance(A){return this.mapInstances[A]}getAllMapInstances(){return this.mapInstances}async getAccurateCoordinates(A,t,r){try{return this.AMap?new Promise((n,s)=>{const a=setTimeout(()=>{console[M(47)]("地理编码超时: "+A+"，返回空对象"),n({})},1e4);try{this.AMap.plugin("AMap.Geocoder",()=>{try{new this.AMap.Geocoder({city:r,radius:500,extensions:"all"}).getLocation(A,(o,l)=>{try{if(o==="complete"&&l.geocodes&&l.geocodes.length>0)clearTimeout(a),n(l.geocodes[0].location);else{const B="高德地理编码失败 - 地址: "+A+", 状态: "+o;console.warn(B,l),clearTimeout(a),n({})}}catch(B){console.warn("地理编码回调处理错误:",B),clearTimeout(a),n({})}})}catch(i){console.warn("地理编码插件加载错误:",i),clearTimeout(a),n({})}})}catch(i){console.warn("地理编码Promise创建错误:",i),clearTimeout(a),n({})}}):(console.warn("地图服务未初始化，返回空对象"),{})}catch(n){return console.warn("地理编码外层异常:",n),{}}}calculateDistance(A,t,r,n){const s=M,a=6371,i=(n-t)*Math.PI/180,o=(r-A)*Math.PI/180,l=Math.sin(i/2)*Math.sin(i/2)+Math.cos(t*Math.PI/180)*Math.cos(n*Math.PI/180)*Math.sin(o/2)*Math.sin(o/2),B=2*Math.atan2(Math[s(48)](l),Math.sqrt(1-l));return a*B}async getBatchAccurateCoordinates(A,t=null){var i;const r=M;if(!this.AMap)throw new Error("地图服务未初始化");if(!Array[r(49)](A)||A.length===0)return[];const n=[],s=A[r(50)](o=>this.getAccurateCoordinates(o,t).catch(l=>({error:l.message,address:o}))),a=await Promise.allSettled(s);for(let o=0;o<a.length;o++){const l=a[o];l.status==="fulfilled"?l.value[r(51)]?n.push({success:!1,address:A[o],error:l.value.error,lng:null,lat:null}):n.push({success:!0,address:A[o],...l.value}):n.push({success:!1,address:A[o],error:((i=l.reason)==null?void 0:i.message)||"未知错误",lng:null,lat:null})}return n.filter(o=>o.success).length,n}}const aA=new Kc;function Tr(){const e=["lastScrollTop","documentElement","autoScrollEnabled","userScrollTimeout","max","getDisplayState","undefined",".answer-area-container","offsetParent","offsetHeight","smartScrollToContent","resetScrollState","isUserScrolling","scrollTop","scrollIntoView"];return Tr=function(){return e},Tr()}const jA=we;function we(e,A){const t=Tr();return we=function(r,n){return r=r-0,t[r]},we(e,A)}class Tc{constructor(){const A=we;this.autoScrollEnabled=!0,this.userScrollTimeout=null,this[A(0)]=0,this.isUserScrolling=!1,this.scrollObserver=null,this.handleUserScroll=null,this.lastUserScrollTime=0,this.scrollVelocity=0,this.consecutiveScrollCount=0}initScrollListener(){typeof window>"u"||(this.handleUserScroll=this.throttle(()=>{const A=we,t=Date.now(),r=window.pageYOffset||document[A(1)].scrollTop,n=Math.abs(r-this.lastScrollTop),s=t-this.lastUserScrollTime;if(this.scrollVelocity=s>0?n/s:0,n>5){this.consecutiveScrollCount++;const a=n>30,i=r<this.lastScrollTop,o=this.scrollVelocity>.5,l=this.consecutiveScrollCount>=2;i||a||o||l&&n>15?(!this.isUserScrolling&&(this.isUserScrolling=!0,this.autoScrollEnabled=!1),this.userScrollTimeout&&clearTimeout(this.userScrollTimeout),this.userScrollTimeout=setTimeout(()=>{const x=A;this.isUserScrolling=!1,this.consecutiveScrollCount=0,!this.checkAllContentCompleted()&&(this[x(2)]=!0,setTimeout(()=>{this.smartScrollToContent()},200))},5e3)):this.isUserScrolling&&n>8&&(this.userScrollTimeout&&clearTimeout(this.userScrollTimeout),this[A(3)]=setTimeout(()=>{this.isUserScrolling=!1,this.consecutiveScrollCount=0,!this.checkAllContentCompleted()&&(this.autoScrollEnabled=!0,setTimeout(()=>{this.smartScrollToContent()},200))},5e3)),this.lastScrollTop=r,this.lastUserScrollTime=t}else this.consecutiveScrollCount=Math[A(4)](0,this.consecutiveScrollCount-1)},80),window.addEventListener("scroll",this.handleUserScroll,{passive:!0}))}cleanupScrollListener(){typeof window>"u"||(this.handleUserScroll&&window.removeEventListener("scroll",this.handleUserScroll),this.userScrollTimeout&&clearTimeout(this.userScrollTimeout))}throttle(A,t){let r;return function(){const n=arguments,s=this;!r&&(A.apply(s,n),r=!0,setTimeout(()=>r=!1,t))}}setContentCompletedChecker(A){this.checkAllContentCompleted=A}setDisplayStateChecker(A){const t=we;this[t(5)]=A}checkAllContentCompleted(){return!1}[jA(5)](){return{selectedDayIndex:-1,isAnyLoading:!1}}smartScrollToContent(){const A=jA;if(!(!this[A(2)]||typeof window===A(6))){if(this.checkAllContentCompleted()){this.autoScrollEnabled=!1;return}try{const t=document.querySelectorAll(A(7));if(t.length===0)return;let r=null,n=!1;n=this.getDisplayState().isAnyLoading;const a=document.querySelector(".global-loading-banner");if(!n&&a&&window.getComputedStyle(a).display!=="none"&&(n=!0),n)for(let i=t.length-1;i>=0;i--){const o=t[i];if(o[A(8)]!==null&&window.getComputedStyle(o).display!=="none"){r=o;break}}if(!n){if(this.checkAllContentCompleted()){this.autoScrollEnabled=!1;return}for(let i=t.length-1;i>=0;i--){const o=t[i];if(o.offsetParent!==null&&window.getComputedStyle(o).display!=="none"){r=o;break}}}if(r){const i=r.getBoundingClientRect(),o=window.pageYOffset||document.documentElement.scrollTop,l=window.innerHeight,B=a&&n?a[A(9)]:0,x=o+i.bottom-l*.7+B;window.scrollTo({top:Math.max(0,x),behavior:"smooth"})}else window.scrollTo({top:document.body.scrollHeight,behavior:"smooth"})}catch{window.scrollTo({top:document.body.scrollHeight,behavior:"smooth"})}}}scrollPageToBottom(){this[jA(10)]()}[jA(11)](){const A=jA;this.autoScrollEnabled=!0,this[A(12)]=!1,typeof window<"u"?this.lastScrollTop=window.pageYOffset||document.documentElement[A(13)]||0:this.lastScrollTop=0,this.userScrollTimeout&&(clearTimeout(this.userScrollTimeout),this.userScrollTimeout=null),this.lastUserScrollTime=Date.now(),this.scrollVelocity=0,this.consecutiveScrollCount=0}getScrollState(){const A=jA;return{autoScrollEnabled:this.autoScrollEnabled,isUserScrolling:this.isUserScrolling,lastScrollTop:this[A(0)]}}setAutoScrollEnabled(A){this.autoScrollEnabled=A}isUserScrollingActive(){return this.isUserScrolling}forceScrollToBottom(){typeof window>"u"||window.scrollTo({top:document.body.scrollHeight,behavior:"smooth"})}scrollToElement(A,t="smooth"){const r=jA;!A||typeof window===r(6)||A[r(14)]({behavior:t,block:"center"})}cleanup(){this.cleanupScrollListener(),this.autoScrollEnabled=!0,this.isUserScrolling=!1,this.lastScrollTop=0,this.userScrollTimeout=null,this.scrollObserver=null,this.handleUserScroll=null,this.lastUserScrollTime=0,this.scrollVelocity=0,this.consecutiveScrollCount=0}}const wA=new Tc,dA=re;class Mc{constructor(){const A=re;this.formDataKey=A(0)}saveFormData(A){const t=re;if(typeof window!==t(1))try{const r={...A,timestamp:Date.now()};localStorage[t(2)](this.formDataKey,JSON.stringify(r))}catch{}}loadFormData(){if(typeof window===re(1))return null;try{const t=localStorage.getItem(this.formDataKey);if(!t)return null;const r=JSON.parse(t),n=7*24*60*60*1e3;return r.timestamp&&Date.now()-r.timestamp>n?(this.clearFormData(),null):r}catch{return this.clearFormData(),null}}clearFormData(){if(!(typeof window>"u"))try{localStorage.removeItem(this.formDataKey)}catch{}}getDefaultFormData(){return{s_address:null,e_address:null,startDate:null,dates:3,plan_mode:"往返",travel_mode:"自驾",s_location:{lng:null,lat:null,province:null,city:null,district:null,address:null,adcode:null},e_location:{lng:null,lat:null,province:null,city:null,district:null,address:null,adcode:null}}}resetFormData(){return this.clearFormData(),this.getDefaultFormData()}validateFormData(A){const t=re,{s_address:r,e_address:n,startDate:s,dates:a}=A,i=[r,n].every(B=>typeof B=="string"&&B.trim().length>=2),o=s!==null,l=a>0&&a<=5;return a>5?{isValid:!1,message:"处于规划耗时，请勿一次性规划超过5天的行程，可以分多次规划"}:i?o?l?{isValid:!0,message:"表单验证通过"}:{isValid:!1,message:"游玩天数必须在1-5天之间"}:{isValid:!1,message:"请选择开始日期"}:{isValid:!1,message:t(3)}}[dA(4)](A,t){const r=dA;return t?{s_address:t.s_address!==void 0?t.s_address:A.s_address,e_address:t.e_address!==void 0?t[r(5)]:A[r(5)],startDate:t.startDate!==void 0?t.startDate:A[r(6)],dates:t.dates!==void 0?t.dates:A.dates,plan_mode:t.plan_mode!==void 0?t.plan_mode:A.plan_mode,travel_mode:t.travel_mode!==void 0?t.travel_mode:A.travel_mode,s_location:t.s_location!==void 0?t.s_location:A.s_location,e_location:t.e_location!==void 0?t.e_location:A.e_location}:A}[dA(7)](A,t){return!!(["s_address","e_address","startDate",dA(8),"plan_mode","travel_mode"].some(s=>A[s]!==t[s])||this.hasLocationChanged(A.s_location,t.s_location)||this.hasLocationChanged(A.e_location,t.e_location))}[dA(9)](A,t){return!A&&!t?!1:!A||!t?!0:["lng","lat","province","city","district","address","adcode"].some(n=>A[n]!==t[n])}getFormDataSummary(A){const{s_address:t,e_address:r,startDate:n,dates:s,plan_mode:a,travel_mode:i,s_location:o,e_location:l}=A;return{route:(t||"未设置")+" → "+(r||"未设置"),duration:s+"天",mode:a+" - "+i,startDate:n||"未设置",startLocationInfo:this.getLocationSummary(o),endLocationInfo:this.getLocationSummary(l),hasCoordinates:this.hasCompleteCoordinates(o,l),isComplete:!!(t&&r&&n&&s>0)}}getLocationSummary(A){const t=dA;if(!A)return"位置信息未设置";const r=[];A.province&&r.push(A.province),A.city&&r.push(A.city),A.district&&r.push(A[t(10)]);const n=r.length>0?r[t(11)](" "):A.address||"地址信息不完整",s=A.lng&&A.lat?"("+A[t(12)]+", "+A.lat+")":"(坐标未获取)";return n+" "+s}hasCompleteCoordinates(A,t){const r=dA;return!!(A!=null&&A.lng&&(A!=null&&A.lat)&&(t!=null&&t.lng)&&(t!=null&&t[r(13)]))}exportFormData(A){try{return JSON.stringify(A,null,2)}catch{return null}}importFormData(A){const t=dA;try{const r=JSON.parse(A),n=this.validateFormData(r);if(n.isValid)return r;throw new Error(n[t(14)])}catch(r){throw r}}setStorageKey(A){this.formDataKey=A}getStorageKey(){return this.formDataKey}[dA(15)](){const A=dA;if(typeof window>"u")return!1;try{const t="__localStorage_test__";return localStorage[A(2)](t,t),localStorage.removeItem(t),!0}catch{return!1}}getStorageSize(){if(!this.isLocalStorageAvailable())return 0;try{const A=localStorage.getItem(this.formDataKey);return A?new Blob([A]).size:0}catch{return 0}}[dA(16)](A=7*24*60*60*1e3){const t=this.loadFormData();return t&&t.timestamp&&Date.now()-t.timestamp>A?(this.clearFormData(),!0):!1}}function re(e,A){const t=Mr();return re=function(r,n){return r=r-0,t[r]},re(e,A)}const HA=new Mc;function Mr(){const e=["topmeans_form_data","undefined","setItem","请输入有效的起点和终点地址（至少2个字符）","mergeFormData","e_address","startDate","hasFormDataChanged","dates","hasLocationChanged","district","join","lng","lat","message","isLocalStorageAvailable","cleanupExpiredData"];return Mr=function(){return e},Mr()}function IA(e,A){const t=Or();return IA=function(r,n){return r=r-0,t[r]},IA(e,A)}const Oc=IA;function Or(){const e=["/api/ai_img2","stringify","json","计划存库失败，请检查网络连接","application/json","abortController","decode","图片保存失败","POST","cleanup"];return Or=function(){return e},Or()}const lA="http://localhost:3999";class _c{constructor(){this.abortController=null}async getHotelUrl(A,t,r,n,s){try{const a=await fetch(lA+"/api/hotel",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({name:""+s+A.replace("*",""),user:t,create_time:r,day:""+n})});if(!a.ok)throw new Error("酒店信息获取失败");const{success:i,url:o}=await a.json();return o}catch(a){throw a}}async getFoodImgUrl(A,t){try{const r=await fetch(lA+"/api/ai_img",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({prompt:"一种食物或一个著名饭店，请根据后续描述来进行写实风格的图片生成，名字："+A+",相关信息："+t})});if(!r.ok)throw new Error("美食图片获取失败");const{success:n,url:s}=await r.json();return s}catch(r){throw r}}async getAIImg(A,t){const r=IA;try{const n=await fetch(lA+r(0),{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({prompt:A+"："+t})});if(!n.ok)throw new Error("美食图片获取失败");const{success:s,url:a}=await n.json();return a}catch(n){throw n}}async getViewUrl(A){try{const t=await fetch(lA+"/api/view",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({name:A})});if(!t.ok)throw new Error("景点信息获取失败");const{success:r,url:n}=await t.json();return n}catch(t){throw t}}async savePlanToDB(A){const t=IA,{content:r,account:n,filename:s}=A;try{let a=await fetch(lA+"/api/save_plan",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON[t(1)]({content:r,user:n,filename:s})});if(!a.ok)throw new Error("保存计划失败，请检查网络连接");return await a[t(2)]()}catch(a){throw a}}async addPlanToUser(A){const t=IA,{account:r,create_time:n,days:s}=A;try{const a=await fetch(lA+"/api/user/add_plan",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({account:r,create_time:n,days:s})});if(!a.ok)throw new Error(t(3));return await a.json()}catch(a){throw a}}async sendMessage(A,t,r,n){const s=IA;this.abortController&&this.abortController.abort(),this.abortController=new AbortController;try{const a=await fetch(lA+"/api/ds",{method:"POST",headers:{"Content-Type":s(4)},body:JSON.stringify({msg:A}),signal:this[s(5)].signal});if(!a.ok)throw new Error("DS API 请求失败!");const i=a.body.getReader(),o=new TextDecoder("utf-8");let l="";for(;;){const{done:B,value:x}=await i.read();if(B)break;const u=o[s(6)](x),h=u.split(`
`).filter(g=>g.trim());for(const g of h)try{if(!g.startsWith("data: "))continue;const d=g.slice(6);if(d==="[DONE]")break;const F=JSON.parse(d),f=F.choices[0].delta.content;f&&(l+=f,t&&t(l,f))}catch{}}return r&&r(l),l}catch(a){if(a.name==="AbortError")return null;throw n&&n(a),a}}cancelCurrentRequest(){this.abortController&&(this.abortController.abort(),this.abortController=null)}async getApiKeys(){try{const A=await fetch(lA+"/api/amap_keys",{method:"GET",credentials:"include"});if(!A.ok)throw new Error("获取API密钥失败，请检查网络连接");return await A.json()}catch(A){throw A}}async getMapScriptUrl(){try{const A=await fetch(lA+"/api/amap",{method:"GET",credentials:"include"});if(!A.ok)throw new Error("地图脚本加载失败，请检查网络连接");return await A.json()}catch{throw new Error("地图脚本加载失败，请检查网络连接")}}async saveMapImage(A){const t=IA,{image:r,user:n,filename:s}=A;try{const a=await fetch(lA+"/api/save_amap_img",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({image:r,user:n,filename:s})});if(!a.ok)throw new Error(t(7));return await a.json()}catch(a){throw a}}async get(A,t={}){try{const r=await fetch(""+lA+A,{method:"GET",credentials:"include",...t});if(!r.ok)throw new Error("GET请求失败: "+r.status);return await r.json()}catch(r){throw r}}async post(A,t,r={}){const n=IA;try{const s=await fetch(""+lA+A,{method:n(8),headers:{"Content-Type":"application/json",...r.headers},body:JSON[n(1)](t),credentials:"include",...r});if(!s.ok)throw new Error("POST请求失败: "+s.status);return await s.json()}catch(s){throw s}}async checkConnection(){try{return(await fetch(lA+"/api/health",{method:"GET",timeout:5e3})).ok}catch{return!1}}[Oc(9)](){this.cancelCurrentRequest()}}const Ae=new _c,gt=bA;function bA(e,A){const t=_r();return bA=function(r,n){return r=r-0,t[r]},bA(e,A)}class Rc{constructor(){this.amapObserver=null,this.amapStyleInterval=null}ensureAmapSuggestStyles(){typeof window>"u"||(this.injectAmapStyles(),this.startAmapStyleMonitor())}injectAmapStyles(){const A=bA,t=document[A(0)]("amap-dark-theme-fix");t&&t.remove();const r=document[A(1)]("style");r.id="amap-dark-theme-fix",r.innerHTML=`
            /* 高德地图提示框强制样式 - 最高优先级 */
            .amap-sug-result,
            .amap-ui-autocomplete,
            div[class*="amap"][class*="sug"],
            div[class*="amap"][class*="auto"] {
                background: var(--vp-c-bg) !important;
                border: 1px solid var(--vp-c-divider) !important;
                border-radius: 8px !important;
                box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1) !important;
                z-index: 99999 !important;
            }

            .amap-sug-result .auto-item,
            .amap-sug-result li,
            .amap-ui-autocomplete .amap-ui-autocomplete-item,
            .amap-ui-autocomplete li,
            div[class*="amap"] .auto-item,
            div[class*="amap"] li {
                background: var(--vp-c-bg) !important;
                color: var(--vp-c-text-1) !important;
                border-bottom: 1px solid var(--vp-c-divider-light) !important;
                padding: 12px 16px !important;
                font-size: 0.875rem !important;
                line-height: 1.4 !important;
            }

            .amap-sug-result .auto-item *,
            .amap-sug-result li *,
            .amap-ui-autocomplete .amap-ui-autocomplete-item *,
            .amap-ui-autocomplete li *,
            div[class*="amap"] .auto-item *,
            div[class*="amap"] li * {
                color: var(--vp-c-text-1) !important;
            }

            /* 深色主题特殊处理 */
            .dark .amap-sug-result,
            .dark .amap-ui-autocomplete,
            .dark div[class*="amap"][class*="sug"],
            .dark div[class*="amap"][class*="auto"] {
                background: var(--vp-c-bg-soft) !important;
                border-color: var(--vp-c-divider) !important;
                box-shadow: 0 4px 16px rgba(0, 0, 0, 0.3) !important;
            }

            .dark .amap-sug-result .auto-item,
            .dark .amap-sug-result li,
            .dark .amap-ui-autocomplete .amap-ui-autocomplete-item,
            .dark .amap-ui-autocomplete li,
            .dark div[class*="amap"] .auto-item,
            .dark div[class*="amap"] li {
                background: var(--vp-c-bg-soft) !important;
                color: #ffffff !important;
                border-bottom-color: var(--vp-c-divider) !important;
            }

            .dark .amap-sug-result .auto-item *,
            .dark .amap-sug-result li *,
            .dark .amap-ui-autocomplete .amap-ui-autocomplete-item *,
            .dark .amap-ui-autocomplete li *,
            .dark div[class*="amap"] .auto-item *,
            .dark div[class*="amap"] li * {
                color: #ffffff !important;
            }

            /* 悬停效果 */
            .amap-sug-result .auto-item:hover,
            .amap-sug-result li:hover,
            .amap-ui-autocomplete .amap-ui-autocomplete-item:hover,
            .amap-ui-autocomplete li:hover {
                background: var(--vp-c-brand-soft) !important;
                color: var(--vp-c-brand-1) !important;
            }

            .dark .amap-sug-result .auto-item:hover,
            .dark .amap-sug-result li:hover,
            .dark .amap-ui-autocomplete .amap-ui-autocomplete-item:hover,
            .dark .amap-ui-autocomplete li:hover {
                background: var(--vp-c-brand-dimm) !important;
                color: #ffffff !important;
            }

            .amap-sug-result .auto-item:hover *,
            .amap-sug-result li:hover *,
            .amap-ui-autocomplete .amap-ui-autocomplete-item:hover *,
            .amap-ui-autocomplete li:hover *,
            .dark .amap-sug-result .auto-item:hover *,
            .dark .amap-sug-result li:hover *,
            .dark .amap-ui-autocomplete .amap-ui-autocomplete-item:hover *,
            .dark .amap-ui-autocomplete li:hover * {
                color: inherit !important;
            }
        `,document[A(2)][A(3)](r)}startAmapStyleMonitor(){this.amapStyleInterval&&clearInterval(this.amapStyleInterval),this.amapStyleInterval=setInterval(()=>{this.forceApplyAmapStyles()},500);const A=new MutationObserver(t=>{let r=!1;t.forEach(n=>{n.addedNodes.forEach(s=>{if(s.nodeType===Node.ELEMENT_NODE){const a=s.className||"";(a.includes("amap")||a.includes("sug")||a.includes("auto"))&&(r=!0)}})}),r&&setTimeout(()=>this.forceApplyAmapStyles(),100)});A.observe(document.body,{childList:!0,subtree:!0}),this.amapObserver=A}forceApplyAmapStyles(){const A=bA;if(!(typeof window>"u"))try{const t=document.documentElement.classList.contains("dark"),r=t?"#ffffff":"var(--vp-c-text-1)",n=t?"var(--vp-c-bg-soft)":"var(--vp-c-bg)";[".amap-sug-result",".amap-ui-autocomplete",'div[class*="amap"][class*="sug"]',A(4),'[class*="amap-sug"]','[class*="amap-auto"]'].forEach(a=>{document.querySelectorAll(a).forEach(o=>{const l=bA;o&&o[l(5)]&&(o.style.setProperty("background",n,"important"),o.style.setProperty(l(6),"1px solid var(--vp-c-divider)",l(7)),o.style.setProperty("border-radius","8px","important"),o.style.setProperty("z-index","99999","important")),o.querySelectorAll('.auto-item, li, .amap-ui-autocomplete-item, [class*="item"]').forEach(x=>{const u=l;x&&x.style&&(x.style.setProperty("background",n,"important"),x[u(5)].setProperty("color",r,"important"),x.style[u(8)]("padding","12px 16px",u(7)),x.querySelectorAll("*").forEach(g=>{g&&g.style&&g.style.setProperty("color",r,"important")}))})})})}catch{}}isDarkMode(){const A=bA;return typeof window>"u"?!1:document[A(9)].classList.contains("dark")}getThemeColors(){const A=bA;if(typeof window===A(10))return{};const t=getComputedStyle(document.documentElement);return{bg:t.getPropertyValue("--vp-c-bg").trim(),bgSoft:t.getPropertyValue("--vp-c-bg-soft")[A(11)](),text1:t.getPropertyValue("--vp-c-text-1").trim(),divider:t.getPropertyValue("--vp-c-divider").trim(),dividerLight:t[A(12)]("--vp-c-divider-light").trim(),brand:t.getPropertyValue("--vp-c-brand").trim(),brandSoft:t.getPropertyValue("--vp-c-brand-soft").trim(),brandDimm:t.getPropertyValue("--vp-c-brand-dimm").trim()}}[gt(13)](A){if(typeof window>"u")return;const t=new MutationObserver(r=>{r.forEach(n=>{const s=bA;if(n.type==="attributes"&&n.attributeName==="class"){const a=document.documentElement.classList[s(14)]("dark");A(a)}})});return t.observe(document.documentElement,{attributes:!0,attributeFilter:["class"]}),t}applyCustomStyles(A,t){const r=gt,n=document.getElementById(A);n&&n[r(15)]();const s=document.createElement("style");s.id=A,s.innerHTML=t,document.head[r(3)](s)}removeCustomStyles(A){const t=gt,r=document.getElementById(A);r&&r[t(15)]()}cleanup(){const A=gt;this.amapObserver&&(this.amapObserver[A(16)](),this.amapObserver=null),this.amapStyleInterval&&(clearInterval(this.amapStyleInterval),this.amapStyleInterval=null),this.removeCustomStyles("amap-dark-theme-fix")}reinitialize(){this.cleanup(),this.ensureAmapSuggestStyles()}getStyleState(){return{hasAmapObserver:!!this.amapObserver,hasStyleInterval:!!this.amapStyleInterval,isDarkMode:this.isDarkMode(),themeColors:this.getThemeColors()}}}function _r(){const e=["getElementById","createElement","head","appendChild",'div[class*="amap"][class*="auto"]',"style","border","important","setProperty","documentElement","undefined","trim","getPropertyValue","watchThemeChange","contains","remove","disconnect"];return _r=function(){return e},_r()}const Rr=new Rc,$n=Ge;function Ge(e,A){const t=Gr();return Ge=function(r,n){return r=r-0,t[r]},Ge(e,A)}function Gr(){const e=["services","map","function","initialized","isInitialized","style"];return Gr=function(){return e},Gr()}class au{constructor(){this.services={map:null,scroll:null,form:null,api:null,style:null},this.initialized=!1}async initialize(){const A=Ge;if(!this.initialized)try{this.services.map=aA,this.services.scroll=wA,this[A(0)].form=HA,this[A(0)].api=Ae,this[A(0)].style=Rr,this.services[A(1)]&&typeof this.services[A(1)].initialize===A(2)&&await this.services.map.initialize(),this.services.style&&typeof this.services.style.initialize=="function"&&await this.services.style.initialize(),this.initialized=!0}catch(t){throw t}}getMapService(){return this.services.map}getScrollManager(){return this.services.scroll}getFormManager(){return this.services.form}getApiService(){return this.services.api}getStyleManager(){return this.services.style}cleanup(){const A=Ge;Object.values(this.services).forEach(t=>{t&&typeof t.cleanup=="function"&&t.cleanup()}),this.services={map:null,scroll:null,form:null,api:null,style:null},this[A(3)]=!1}async reinitialize(){this.cleanup(),await this.initialize()}[$n(4)](){return this.initialized}getServicesStatus(){const A=$n;return{initialized:this.initialized,services:{map:!!this.services.map,scroll:!!this.services.scroll,form:!!this.services.form,api:!!this.services.api,style:!!this.services[A(5)]}}}}function U(e,A){const t=kr();return U=function(r,n){return r=r-0,t[r]},U(e,A)}const dt="http://localhost:3999",Br=void 0,$A=void 0,Gc=void 0,As=void 0,es=void 0,kc=void 0,Vc=void 0,ts=void 0,Nc=void 0;D.setLevel("info");function kr(){const e=["躲避拥堵且不走高速且距离优先","正在规划第","天的景点信息，请稍等...","天的住宿信息，请稍等...","contents","drivingCompleted","foodCompleted","every","initialize","setContentCompletedChecker","shouldContinuePlanning","showBtn","saveFormData","length","expandedSectionType","activeDayDetailIndex","$nextTick","rentCompleted","travel_mode","selectedDayIndex","forEach","forceMapResize","hotelCompleted","costCompleted","plan_mode","resetScrollState","startDate","s_location","mergeFormData","assign","hotel_customized","undefined","parse","rent_requirements","plan_requirements","formData","e_address","dates","clearPlanningContents","timestamp","#map-container-","body","appendChild","getCenter","getZoom","function","setZoom","地图 "," 重新渲染时出现错误:","toggleMapSize","resizeTimeout","maximizedMapIndex","escKeyHandler","removeEventListener","info","food","url",`")

`,"## ","error","计划存库失败，请检查网络连接","saveMapAsImage","utf-8","rent",`**租车建议**
`,"hotel","**酒店信息:**","getHotelUrl","replace","rent_customized","plan_customized","validateFormData","join","lng","padStart","weather","注意，第","，因此今天的起点就是","trim","lat","push","天导航规划失败:","driving","name","split","includes","%PT","last_end","setLoadingState","planningDriving","account","isPlanningFullyCompleted","themeObserver","observe",".custom-amap-suggest","updateThemeStyles","restoreScrollManagerState","continuePlanning","warn","pos","querySelector","hasIncompleteStages","planningRent","天美食规划失败:","天存库失败:","resetAllLoadingStates","天地图失败:"];return kr=function(){return e},kr()}const Pc={data(){return{contents:[],showBtn:!0,loading:!1,errorMessage:"",last_start:"",last_end:"",rent_requirements:"",rent_customized:!1,plan_requirements:"0",plan_customized:!1,hotel_requirements:"",hotel_customized:!1,maximizedMapIndex:-1,originalParent:null,originalNextSibling:null,planOptions:[{value:"0",text:"速度优先(默认值)"},{value:"1",text:"费用优先"},{value:"2",text:"距离优先"},{value:"3",text:"不走高速且避免收费"},{value:"4",text:"躲避拥堵"},{value:"5",text:"不走高速"},{value:"6",text:"躲避拥堵且不走高速"},{value:"7",text:"躲避拥堵且距离优先"},{value:"8",text:U(0)},{value:"9",text:"躲避拥堵且不走高速且费用优先"}],...HA.getDefaultFormData(),selectedDayIndex:-1,activeDayDetailIndex:-1,expandedSectionType:null,loadingStates:{weather:!1,rent:!1,driving:!1,view:!1,food:!1,hotel:!1,cost:!1},showCompletionModal:!1,showFailureModal:!1,failureReason:"",showFloatingButtons:!1}},computed:{selectedPlanText(){const e=this.planOptions.find(A=>A.value===this.plan_requirements);return e?e.text:""},isAnyLoading(){return Object.values(this.loadingStates).some(e=>e)},currentLoadingMessage(){const e=U,A=this.getCurrentProcessingDay(),t={weather:"正在查询天气信息，请稍等...",rent:"正在思考租车方案，请稍等...",driving:"正在规划第"+A+"天的路线，请稍等...",view:e(1)+A+e(2),food:"正在规划第"+A+"天的美食信息，请稍等...",hotel:"正在规划第"+A+e(3),cost:"正在计算费用预算，请稍等..."};for(const[r,n]of Object.entries(this.loadingStates))if(n)return t[r];return"正在处理中，请稍等..."},currentProgressStep(){const e=U;if(this[e(4)].length===0)return 1;if(this.isAnyLoading||!this.showBtn)return 2;const A=this[e(4)].some(r=>{const n=e;return r.weatherCompleted===2||r.rentCompleted===2||r[n(5)]===2||r.viewCompleted===2||r[n(6)]===2||r.hotelCompleted===2||r.costCompleted===2});return this.contents[e(7)](r=>r.weatherCompleted===2||r.rentCompleted===2||r.drivingCompleted===2||r.viewCompleted===2||r.foodCompleted===2||r.hotelCompleted===2||r.costCompleted===2)&&this.contents.length>0?3:A?2:1}},async mounted(){const e=U;if(typeof window>"u")return;try{await aA[e(8)](),aA.setupAutoComplete("start-tipinput","end-tipinput",(t,r)=>{this.s_address=t,this.s_location=r||this.getDefaultFormData().s_location},(t,r)=>{this.e_address=t,this.e_location=r||this.getDefaultFormData().e_location}),Rr.ensureAmapSuggestStyles()}catch(t){this.errorMessage=t.message,D.error("地图初始化失败:",t)}wA.initScrollListener(),wA[e(9)](()=>this.showBtn),wA.setDisplayStateChecker(()=>({selectedDayIndex:this.selectedDayIndex,isAnyLoading:this.isAnyLoading})),this.loadFormData(),this.initFloatingButtons(),this.initThemeObserver(),this.loadPlanningContents()&&(this[e(10)]()?(this.showBtn=!1,this.$nextTick(()=>{setTimeout(()=>{this.handleContentRestoration()},500)})):(this.clearPlanningContents(),this.contents=[],this.selectedDayIndex=-1,this.activeDayDetailIndex=-1,this.expandedSectionType=null,this[e(11)]=!0))},beforeUnmount(){this.maximizedMapIndex!==-1&&this.restoreMapToOriginalPosition(this.maximizedMapIndex),wA.cleanup(),Rr.cleanup(),aA.cleanup(),Ae.cleanup(),this.removeKeyboardListener(),this.handleScroll&&typeof window<"u"&&window.removeEventListener("scroll",this.handleScroll),this.themeObserver&&this.themeObserver.disconnect()},watch:{contents:{handler(e){e.length>0&&wA.getScrollState().autoScrollEnabled&&this.$nextTick(()=>{this.isAnyLoading&&wA.smartScrollToContent()}),e.length>0&&this.savePlanningContents()},deep:!0},s_address:{handler(){this.saveFormData()}},e_address:{handler(){this[U(12)]()}},s_location:{handler(){this.saveFormData()},deep:!0},e_location:{handler(){this.saveFormData()},deep:!0},startDate:{handler(){this.saveFormData()}},dates:{handler(){this.saveFormData()}},plan_mode:{handler(){this.saveFormData()}},travel_mode:{handler(){this.saveFormData()}},selectedDayIndex:{handler(){this.contents.length>0&&this.savePlanningContents()}},activeDayDetailIndex:{handler(){const e=U;this.contents[e(13)]>0&&this.savePlanningContents()}},expandedSectionType:{handler(){this.contents.length>0&&this.savePlanningContents()}},showBtn:{handler(){this.contents.length>0&&this.savePlanningContents()}}},methods:{parseMarkdown(e){return aa.parse(e||"")},getCurrentProcessingDay(){if(!this.contents||this.contents.length===0)return 1;for(let e=this.contents.length-1;e>=0;e--){const A=this.contents[e];if(A.weatherCompleted>0||A.rentCompleted>0||A.drivingCompleted>0||A.viewCompleted>0||A.foodCompleted>0||A.hotelCompleted>0||A.costCompleted>0)return e+1}return 1},selectDay(e){const A=U;e<0||e>=this.contents.length||!this.contents[e]||this.isDayCompleted(this.contents[e])&&(this.activeDayDetailIndex=this.activeDayDetailIndex===e?-1:e,this.activeDayDetailIndex!==-1&&(this[A(14)]=null,this.selectedDayIndex=-1),this.$nextTick(()=>{this.refreshVisibleMaps()}))},handleDetailPanelClick(e){const A=U,t=this[A(15)];t===-1||t>=this.contents.length||t<0||this[A(4)][t]&&(this.expandedSectionType=e,this.selectedDayIndex=t,this.$nextTick(()=>{this.scrollToSection(e,t),this.refreshVisibleMaps()}))},scrollToSection(e,A){let t="";switch(e){case"weather":t=".weather-header";break;case"rent":t=".rent-header";break;case"driving":t=".driving-header";break;case"view":t=".view-header";break;case"food":t=".food-header";break;case"hotel":t=".hotel-header";break}if(t){const r=document.querySelector(t);r&&setTimeout(()=>{wA.scrollToElement(r,"smooth")},100)}},shouldShowSection(e,A){const t=U;if(this.expandedSectionType===null||A>=this[t(4)].length||A<0)return!1;const r=this[t(14)]===e&&(this.selectedDayIndex===-1||this.selectedDayIndex===A);return this[t(16)](()=>{document.querySelectorAll(".answer-area-container").forEach(s=>{const a=s.querySelector(".section-content");a&&(window.getComputedStyle(a).display!=="none"?s.classList.add("expanded"):s.classList.remove("expanded"))})}),r},isSectionAvailable(e,A){const t=U;if(A>=this.contents.length||A<0)return!1;const r=this[t(4)][A];if(!r)return!1;switch(e){case"weather":return A===0&&r.weatherCompleted===2;case"rent":return A===0&&this.travel_mode==="租车"&&r[t(17)]===2;case"driving":return r[t(5)]===2;case"view":return r.viewCompleted===2;case"food":return r.foodCompleted===2;case"hotel":return r.hotelCompleted===2;default:return!1}},shouldShowSectionHeader(e,A){const t=U;if(this.activeDayDetailIndex===-1||A!==this.activeDayDetailIndex||A>=this.contents.length||A<0)return!1;const r=this.contents[A];if(!r)return!1;switch(e){case"weather":return A===0&&r.weatherCompleted!==0;case"rent":return A===0&&this[t(18)]==="租车"&&r.rentCompleted!==0;case"driving":return r.drivingCompleted!==0;case"view":return r.viewCompleted!==0;case"food":return r[t(6)]!==0;case"hotel":return r.hotelCompleted!==0;case"cost":return r.costCompleted!==0&&(A===this[t(4)].length-1||this.contents.every(n=>n&&(n.drivingCompleted===2||n.viewCompleted===2)));default:return!1}},handleSectionHeaderClick(e,A){this[U(14)]===e&&this.selectedDayIndex===A?(this.expandedSectionType=null,this.selectedDayIndex=-1):(this.expandedSectionType=e,this.selectedDayIndex=A,this.activeDayDetailIndex=A,this.$nextTick(()=>{this.refreshVisibleMaps()}))},collapseAll(){const e=U;this.expandedSectionType=null,this[e(19)]=-1,this.activeDayDetailIndex=-1,this[e(16)](()=>{document.querySelectorAll(".answer-area-container").forEach(t=>{t.classList.remove("expanded")}),this.refreshVisibleMaps()})},async temporarilyExpandSection(e,A,t=2e3){const r=U,n=this.expandedSectionType,s=this.selectedDayIndex;return this[r(14)]=e,this.selectedDayIndex=A,await this.$nextTick(),new Promise(a=>{setTimeout(()=>{const i=U;this.expandedSectionType=n,this[i(19)]=s,a()},t)})},refreshVisibleMaps(){const e=U;typeof window>"u"||this.contents[e(20)]((A,t)=>{if("amap"in A&&(this.selectedDayIndex===-1||this.selectedDayIndex===t)){const n=document.querySelector("#map-container-"+t);n&&n.offsetParent!==null&&setTimeout(()=>{this[U(21)](t)},100)}})},isDayCompleted(e){const A=U;return e?e.weatherCompleted===2||e.rentCompleted===2||e.drivingCompleted===2||e.viewCompleted===2||e.foodCompleted===2||e[A(22)]===2||e[A(23)]===2:!1},getDayTitle(e,A){const t=U;return A===0?this.s_address+" → "+this.e_address:A===this.contents.length-1&&this[t(24)]==="往返"?this.e_address+" → "+this.s_address:this.e_address+"游览"},setLoadingState(e,A){this.loadingStates[e]=A},resetAllLoadingStates(){this.loadingStates={weather:!1,rent:!1,driving:!1,view:!1,food:!1,hotel:!1,cost:!1}},scrollPageToBottom(){wA.smartScrollToContent()},resetScrollState(){wA[U(25)]()},saveFormData(){const e=U,A={s_address:this.s_address,e_address:this.e_address,startDate:this[e(26)],dates:this.dates,plan_mode:this.plan_mode,travel_mode:this.travel_mode,s_location:this[e(27)],e_location:this.e_location};HA.saveFormData(A)},loadFormData(){const e=U,A=HA.loadFormData();if(A){const t=HA[e(28)]({s_address:this.s_address,e_address:this.e_address,startDate:this[e(26)],dates:this.dates,plan_mode:this.plan_mode,travel_mode:this.travel_mode,s_location:this[e(27)],e_location:this.e_location},A);Object.assign(this,t)}},clearFormData(){HA.clearFormData()},resetFormData(){const e=U,A=HA.resetFormData();Object[e(29)](this,A),this.clearPlanningContents(),this.contents=[],this[e(19)]=-1,this.activeDayDetailIndex=-1,this.expandedSectionType=null,this.showBtn=!0},savePlanningContents(){const e=U;if(!(typeof window>"u"))try{const A={contents:this.contents,last_start:this.last_start,last_end:this.last_end,selectedDayIndex:this.selectedDayIndex,activeDayDetailIndex:this.activeDayDetailIndex,expandedSectionType:this.expandedSectionType,rent_requirements:this.rent_requirements,rent_customized:this.rent_customized,plan_requirements:this.plan_requirements,plan_customized:this.plan_customized,hotel_requirements:this.hotel_requirements,hotel_customized:this[e(30)],showBtn:this.showBtn,formData:{s_address:this.s_address,e_address:this.e_address,startDate:this.startDate,dates:this.dates,plan_mode:this.plan_mode,travel_mode:this.travel_mode,s_location:this[e(27)],e_location:this.e_location},timestamp:Date.now()};localStorage.setItem("topmeans_planning_contents",JSON.stringify(A))}catch(A){console.warn("保存规划内容失败:",A)}},loadPlanningContents(){const e=U;if(typeof window===e(31))return!1;try{const A=localStorage.getItem("topmeans_planning_contents");if(!A)return!1;const t=JSON[e(32)](A),r=24*60*60*1e3;return t.timestamp&&Date.now()-t.timestamp>r?(this.clearPlanningContents(),!1):(this.contents=t.contents||[],this.last_start=t.last_start||"",this.last_end=t.last_end||"",this.selectedDayIndex=t.selectedDayIndex!==void 0?t.selectedDayIndex:-1,this.activeDayDetailIndex=t.activeDayDetailIndex!==void 0?t.activeDayDetailIndex:-1,this.expandedSectionType=t[e(14)]||null,this.rent_requirements=t[e(33)]||"",this.rent_customized=t.rent_customized||!1,this[e(34)]=t.plan_requirements||"0",this.plan_customized=t.plan_customized||!1,this.hotel_requirements=t.hotel_requirements||"",this.hotel_customized=t.hotel_customized||!1,this.showBtn=t.showBtn!==void 0?t.showBtn:!0,t.formData&&(this.s_address=t[e(35)].s_address||this.s_address,this.e_address=t[e(35)].e_address||this[e(36)],this.startDate=t.formData[e(26)]||this.startDate,this.dates=t.formData[e(37)]||this.dates,this.plan_mode=t.formData.plan_mode||this.plan_mode,this.travel_mode=t.formData.travel_mode||this.travel_mode,this.s_location=t.formData.s_location||this.s_location,this.e_location=t.formData.e_location||this.e_location),!0)}catch(A){return console.warn("加载规划内容失败:",A),this[e(38)](),!1}},clearPlanningContents(){if(!(typeof window>"u"))try{localStorage.removeItem("topmeans_planning_contents")}catch(e){console.warn("清除规划内容失败:",e)}},hasSavedPlanningContents(){const e=U;if(typeof window>"u")return!1;try{const A=localStorage.getItem("topmeans_planning_contents");if(!A)return!1;const t=JSON.parse(A),r=24*60*60*1e3;return t[e(39)]&&Date.now()-t.timestamp>r?!1:t.contents&&t.contents.length>0}catch{return!1}},updateRentRequirements(e){this.rent_requirements=e.target.value},updateHotelRequirements(e){this.hotel_requirements=e.target.value},toggleMapSize(e){this.maximizedMapIndex===e?(this.restoreMapToOriginalPosition(e),this.maximizedMapIndex=-1,this.removeKeyboardListener()):(this.moveMapToBodyForMaximize(e),this.maximizedMapIndex=e,this.addKeyboardListener()),this.$nextTick(()=>{setTimeout(()=>{this.forceMapResize(e)},350)})},moveMapToBodyForMaximize(e){const A=U;if(typeof window>"u")return;const t=document.querySelector(A(40)+e).closest(".map-wrapper");t&&(this.originalParent=t.parentNode,this.originalNextSibling=t.nextSibling,document[A(41)].appendChild(t))},restoreMapToOriginalPosition(e){const A=U;if(typeof window>"u")return;const t=document.querySelector("#map-container-"+e).closest(".map-wrapper");t&&this.originalParent&&(this.originalNextSibling?this.originalParent.insertBefore(t,this.originalNextSibling):this.originalParent[A(42)](t),this.originalParent=null,this.originalNextSibling=null)},forceMapResize(e){const A=U;try{const t=aA.getMapInstance(e);if(!t)return;typeof t.resize=="function"&&t.resize();const r=t[A(43)](),n=t[A(44)]();typeof t.getSize=="function"&&t.getSize(),typeof t.setFitView=="function"&&t.setFitView(),setTimeout(()=>{try{r&&n&&typeof t.setZoomAndCenter=="function"&&t.setZoomAndCenter(n,r),setTimeout(()=>{const s=U;try{if(typeof t.getZoom===s(45)&&typeof t.setZoom=="function"){const a=t.getZoom();t.setZoom(a+.01),setTimeout(()=>{t[s(46)](a)},50)}}catch(a){console.warn(s(47)+e+" 缩放调整时出现错误:",a)}},100)}catch(s){console.warn("地图 "+e+" 中心点设置时出现错误:",s)}},100)}catch(t){console.warn("地图 "+e+A(48),t)}},addKeyboardListener(){const e=U;typeof window<"u"&&(this.escKeyHandler=A=>{const t=U;A.key==="Escape"&&this.maximizedMapIndex!==-1&&this[t(49)](this.maximizedMapIndex)},this.resizeHandler=()=>{const A=U;this.maximizedMapIndex!==-1&&(clearTimeout(this.resizeTimeout),this[A(50)]=setTimeout(()=>{const t=A;this.forceMapResize(this[t(51)])},300))},window.addEventListener("keydown",this[e(52)]),window.addEventListener("resize",this.resizeHandler))},removeKeyboardListener(){const e=U;typeof window<"u"&&(this.escKeyHandler&&(window[e(53)]("keydown",this.escKeyHandler),this.escKeyHandler=null),this.resizeHandler&&(window.removeEventListener("resize",this.resizeHandler),this.resizeHandler=null),this.resizeTimeout&&(clearTimeout(this.resizeTimeout),this.resizeTimeout=null))},handleRentCustomize(e){this.rent_requirements&&this.rent_requirements.trim()&&this.handleActionClick("rent",e)},async drivingPlanning(e,A,t,r,n){try{await this.$nextTick(),await this.$nextTick(),await aA.drivingPlanning(e,A,t,r,n),await new Promise(s=>setTimeout(s,2e3))}catch(s){D.error("路线生成错误",s),this.errorMessage=s.message||"路线生成失败"}finally{this.loading=!1}},async savePlanToDB(e,A,t){const r=U;await this.saveMapAsImage(e,A,t);let n=`# Smart Travel Plan

## `+this.s_address+" 到 "+this.e_address+`

`;if(this.contents[e].weather&&(n+=this.contents[e].weather+`

`),this.contents[e].rent&&(n+=this.contents[e].rent+`

`),this.contents[e].driving&&(n+=this.contents[e].driving+`

`,n+="![路线规划](./map-"+t+"-"+e+`.png)

`),this.contents[e].view)for(let a=0;a<this.contents[e].view.length;a++)n+="## "+this.contents[e].view[a].name+`

`,this.contents[e].view[a].url&&(n+="!["+this.contents[e].view[a].name+"]("+this.contents[e].view[a].url+`)

`),n+=this.contents[e].view[a][r(54)]+`

`;if(this.contents[e][r(55)])for(let a=0;a<this[r(4)][e].food.length;a++)n+="## "+this.contents[e].food[a].name+`

`,this.contents[e].food[a].url&&(n+="!["+this.contents[e].food[a].name+"]("+this.contents[e].food[a].url+`)

`),n+=this[r(4)][e].food[a].info+`

`;if(this.contents[e].hotel)for(let a=0;a<this.contents[e].hotel.length;a++)this.contents[e].hotel[a][r(56)]?n+="## [携程直达："+this.contents[e].hotel[a].name+"]("+this.contents[e].hotel[a].url+' "'+this.contents[e].hotel[a].name+r(57):n+=r(58)+this.contents[e].hotel[a].name+`

`,n+=this.contents[e].hotel[a].info+`

`;let s=await fetch(dt+"/api/save_plan",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({content:n,user:A,filename:"plan-"+t+"-"+e+".md"})});if(!s.ok)throw D[r(59)]("保存计划失败，请检查网络连接",s),new Error("保存计划失败，请检查网络连接");if(s=await fetch(dt+"/api/user/add_plan",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({account:A,create_time:t,days:this.dates})}),!s.ok)throw D.error("计划存库失败，请检查网络连接",s),new Error(r(60))},async saveMapAsImage(e,A,t){const r=U;try{await aA[r(61)](e,A,t,this)}catch(n){throw D.error("保存地图为图片失败:",n),n}},async getHotelUrl(e,A,t,r){try{return await Ae.getHotelUrl(e,A,t,r,"")}catch(n){throw D.error("酒店信息获取失败:",n),n}},async getFoodImgUrl(e,A){try{return await Ae.getFoodImgUrl(e,A)}catch(t){throw D.error("美食图片获取失败:",t),t}},async getViewUrl(e){try{return await Ae.getViewUrl(e)}catch(A){throw D.error("景点信息获取失败:",A),A}},async askDeepSeek(e,A,t){const r=U,n=Ne(),{user:s}=await n.getUserInfo(),a=this.getFormattedDate();try{const i=await fetch(dt+"/api/ds",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({msg:A})});if(!i.ok)throw D.error("DS API 请求失败:"+i.statusText),new Error("DS API 请求失败!");const o=i[r(41)].getReader(),l=new TextDecoder(r(62));let B="";for(;;){const{done:x,value:u}=await o.read();if(x)break;const h=l.decode(u),g=h.split(`
`).filter(d=>d.trim());for(const d of g)try{if(!d.startsWith("data: "))continue;const F=d.slice(6);if(F==="[DONE]")break;const f=JSON.parse(F),w=f.choices[0].delta.content;w&&(B+=w),t==="rent"?this.contents[e][r(63)]=r(64)+B:t==="plan"&&(this.contents[e].plan="**第"+(e+1)+`天的规划**

`+B)}catch(F){D.error("解析数据失败:",F)}}if(B&&t==="hotel"){const x=B.split("住宿推荐");for(let u=0;u<x.length;u++)if(u!==0&&(u>this.contents[e][r(65)].length&&this.contents[e].hotel.push({}),this.contents[e].hotel[u-1].info=`**酒店信息:** 
`,x[u].includes(r(66)))){this.contents[e].hotel[u-1][r(54)]+=x[u].split("**酒店信息:**")[1].trim(),this.contents[e].hotel[u-1].name=x[u].split("@@")[1].split("$$")[0].trim();try{this.contents[e].hotel[u-1].url=await this[r(67)](this[r(4)][e][r(65)][u-1].name,s.account,a,e+1)}catch{}}}}catch(i){throw D.error("DS API 请求失败:",i),i}},async askDS(e,A){let t="";try{const r=await fetch(dt+"/api/ds",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({msg:A})});if(!r.ok)throw D.error("DS API 请求失败!"),new Error("DS API 请求失败!");const n=r.body.getReader(),s=new TextDecoder("utf-8");for(;;){const{done:a,value:i}=await n.read();if(a)break;const o=s.decode(i),l=o.split(`
`).filter(B=>B.trim());for(const B of l)try{if(!B.startsWith("data: "))continue;const x=B.slice(6);if(x==="[DONE]")break;const u=JSON.parse(x),h=u.choices[0].delta.content;h&&(t+=h),this.contents[e].think=t.replace(/%SP/g,"").replace(/%SC/g,"").replace(/%EP/g,"").replace(/%EC/g,"").replace(/%CIR/g,"").replace(/@/g,"").replace(/#/g,"").replace(/\$/g,"").replace(/\^/g,"").replace(/\*/g,"").replace(/%/g,"").replace(/&/g,"").trim()}catch{}}}catch(r){throw D.error("DS API 请求失败:"+r),r}return t},async handleActionClick(e,A){const t=U;let r=!1;const n=Ne(),{user:s}=await n.getUserInfo(),a=this.getFormattedDate();let i=Br;if(i=i[t(68)](/startDate/g,this.startDate).replace(/s_address/g,this.s_address).replace(/e_address/g,this.e_address).replace(/plan_mode/g,this.plan_mode).replace(/travel_mode/g,this.travel_mode).replace(/dates/g,this.dates),e==="rent"){if(this.rent_requirements){this.contents[A].rentCompleted=1;const o=i+As.replace(/customized_rent_prompt/g,this.rent_requirements||"");this.contents[A].rent="",await this.planningRent(A,s,a,o),this.contents[A].rentCompleted=2,this[t(69)]=!0,r=!0}}else if(e==="driving"){if(this.plan_requirements){this.contents[A].drivingCompleted=1;let o="";A>0&&(o="注意，第"+(A-1)+"天路线已完成规划，起点是"+this.last_start+", 终点是"+this.last_end+"，因此今天的起点就是"+this.last_end+"，最终的目的地是"+this.e_address);const l=i+es.replace(/index/g,A+1)[t(68)](/driving_mid_s_e_prompt/g,o).replace(/customized_driving_prompt/g,this.selectedPlanText||"");this.contents[A].driving="";const B=parseInt(this.plan_requirements,10);await this.planningDriving(A,s,a,l,B),this.contents[A].drivingCompleted=2,this[t(70)]=!0,r=!0}}else if(e==="hotel"&&this.hotel_requirements){this.contents[A].hotelCompleted=1;const o=i+ts.replace(/e_address/g,this[t(36)]).replace(/customized_hotel_prompt/g,this.hotel_requirements||"");for(let l=0;l<this.contents[A].hotel.length;l++)this.contents[A].hotel[l].info="",this.contents[A].hotel[l].name="",this.contents[A].hotel[l].url="";await this.planningHotel(A,s,a,o),this.contents[A].hotelCompleted=2,this.hotel_customized=!0,r=!0}r&&this.showBtn&&(await this.savePlanToDB(A,s.account,a),this.savePlanningContents())},validateFormData(){const e=U,A={s_address:this.s_address,e_address:this.e_address,startDate:this.startDate,dates:this.dates,plan_mode:this.plan_mode,travel_mode:this.travel_mode,s_location:this.s_location,e_location:this.e_location},t=HA[e(71)](A);return!t.isValid&&alert(t.message),t.isValid},getDefaultFormData(){return HA.getDefaultFormData()},getLocationSummary(e){const A=U;if(!e)return"未设置";const t=[];return e.province&&t.push(e.province),e.city&&t.push(e.city),e.district&&t.push(e.district),t.length>0?t[A(72)](" "):e.address||"位置信息不完整"},hasCompleteLocation(e){return e&&e[U(73)]&&e.lat||e&&e.city&&e.province},getFormattedDate(){const e=U,A=new Date,t=A.getFullYear(),r=String(A.getMonth()+1).padStart(2,"0"),n=String(A.getDate()).padStart(2,"0"),s=""+t+r+n,a=""+String(A.getHours())[e(74)](2,"0")+String(A.getMinutes()).padStart(2,"0")+String(A.getSeconds()).padStart(2,"0");return""+s+a},async planningWeather(e,A,t,r){const n=U;this.contents[e].weatherCompleted=1,this.setLoadingState("weather",!0),this[n(14)]="weather",this.selectedDayIndex=e,this[n(15)]=e,await this.$nextTick();const s=r+Gc+$A;this.contents[e][n(75)]=await this.askDS(e,s),this.contents[e].weatherCompleted=2,this.setLoadingState("weather",!1),this.expandedSectionType=null,this.selectedDayIndex=-1},async planningRent(e,A,t,r){this.contents[e].rentCompleted=1,this.setLoadingState("rent",!0),this.expandedSectionType="rent",this.selectedDayIndex=e,this.activeDayDetailIndex=e,await this.$nextTick();const n=As.replace(/customized_rent_prompt/g,this.rent_requirements||""),s=r+n+$A;this.contents[e].rent=await this.askDS(e,s),this.contents[e].rentCompleted=2,this.setLoadingState("rent",!1),this.expandedSectionType=null,this.selectedDayIndex=-1},async planningDriving(e,A,t,r,n){const s=U;this.contents[e].drivingCompleted=1,this.setLoadingState("driving",!0),this.expandedSectionType="driving",this.selectedDayIndex=e,this.activeDayDetailIndex=e,await this[s(16)]();let a="";e>0&&e<this.dates-1||e>0&&e===this.dates-1&&this.plan_mode==="单程"?a=s(76)+e+"天路线已完成规划，起点是"+this.last_start+", 终点是"+this.last_end+"，因此今天的起点就是"+this.last_end+"，最终的目的地是"+this.e_address:e===this.dates-1&&this.dates>1&&this.plan_mode==="往返"&&(a="注意，第"+e+"天路线已完成规划，起点是"+this.last_start+", 终点是"+this.last_end+s(77)+this.last_end+"，最终的目的地是"+this.s_address);let i="";for(let Q=0;Q<this.dates;++Q)if(this.contents[Q].circle)for(let b=0;b<this.contents[Q].circle.length;b++)i+=this.contents[Q].circle[b]+",";i&&(i="注意，我已经去过"+i+"这几个地方了，规划环线时不要重复，");const o=es.replace(/index/g,e+1).replace(/driving_mid_s_e_prompt/g,a).replace(/last_circle_prompt/g,i).replace(/customized_driving_prompt/g,this.selectedPlanText||""),l=r+o+$A,B=await this.askDS(e,l);this.contents[e].driving=B.replace(/%SP/g,"").replace(/%SC/g,"").replace(/%EP/g,"").replace(/%EC/g,"").replace(/%CIR/g,"").replace(/\^/g,"").replace(/%/g,"");const x=B.split("^^")[1].split("^^")[0][s(78)](),u=B.split("%SP")[1].split("%SP")[0].trim(),h=B.split("%SC")[1].split("%SC")[0].trim(),g=B.split("%%")[1].split("%%")[0].trim(),d=B.split("%EP")[1].split("%EP")[0].trim(),F=B.split("%EC")[1].split("%EC")[0].trim();this.last_start=x,this.last_end=g;let f={lng:null,lat:null},w={lng:null,lat:null};const v=await aA.getAccurateCoordinates(x,u,h),E=await aA.getAccurateCoordinates(g,d,F);!v.lng||!v.lat?f={lng:null,lat:null}:f={lng:v.lng,lat:v.lat},!E.lng||!E.lat?w={lng:null,lat:null}:w={lng:E.lng,lat:E[s(79)]};let m=[];if(x===g){const Q=B.split("%CIR")[1],b=Q.split(" → ");this[s(4)][e].circle=[];for(let S=1;S<b.length-1;++S){const y=b[S].trim(),_=await aA.getAccurateCoordinates(y,d,F);this.contents[e].circle[s(80)](y),_&&_.lng&&_.lat?m[s(80)]([_.lng,_[s(79)]]):console.warn("环线地点地理编码失败: "+y+"，跳过该地点")}}if(this.contents[e].pos={s_address:x,s_province:u,s_city:h,e_address:g,e_province:d,e_city:F,s_location:{s_lng:f[s(73)],s_lat:f.lat},e_location:{e_lng:w.lng,e_lat:w.lat},circle_locations:m},this.contents[e].amap="",f.lng&&f.lat&&w.lng&&w.lat){await this.$nextTick(),await this.$nextTick();try{await this.drivingPlanning(e,{s_lng:f.lng,s_lat:f.lat},{e_lng:w.lng,e_lat:w.lat},n,m),await new Promise(Q=>setTimeout(Q,1e3))}catch(Q){D[s(59)]("第"+(e+1)+s(81),Q)}}else console.warn("第"+(e+1)+"天地理编码失败，跳过地图规划，但继续其他规划流程");this.contents[e].drivingCompleted=2,this.setLoadingState(s(82),!1),this.expandedSectionType=null,this.selectedDayIndex=-1},async planningView(e,A,t,r){const n=U;this.contents[e].viewCompleted=1,this.setLoadingState("view",!0),this[n(14)]="view",this.selectedDayIndex=e,this.activeDayDetailIndex=e,await this.$nextTick(),this.contents[e].view=[];let s=kc.replace(/e_address/g,this.last_end);if(e>0){let l="";for(let B=0;B<e;++B)for(let x=0;x<this[n(4)][B].view.length;++x)l+=this.contents[B].view[x].name+",";s=s.replace(/last_view_prompt/g,"注意，我已经去过"+l+"这几个地方了，不要重复，")}const a=r+s+$A,i=await this.askDS(e,a),o=i.split("@@@@");for(let l=0;l<o.length;l++)if(l!==0){l>this.contents[e].view.length&&this.contents[e].view.push({}),o[l].includes("^^")?this.contents[e].view[l-1][n(83)]=o[l].split("^^")[1].split("^^")[0].trim():o[l].includes("$$")&&(this.contents[e].view[l-1].name=o[l].split("$$")[1].split("$$")[0].trim()),this.contents[e].view[l-1].info=o[l].replace(/\^\^.*\^\^/g,"").replace(/\$\$.*\$\$/g,"").replace(/%PT/g,"").trim(),this.contents[e].view[l-1].prompt=o[l][n(84)]("%PT")[1][n(84)]("%PT")[0][n(78)]();try{this.contents[e].view[l-1].url=await Ae.getAIImg(this.contents[e].view[l-1].name,this.contents[e].view[l-1].prompt)}catch{this.contents[e].view[l-1].url=""}}this.contents[e].viewCompleted=2,this.setLoadingState("view",!1),this.expandedSectionType=null,this.selectedDayIndex=-1},async planningFood(e,A,t,r){const n=U;this.contents[e][n(6)]=1,this.setLoadingState("food",!0),this.expandedSectionType="food",this.selectedDayIndex=e,this.activeDayDetailIndex=e,await this.$nextTick(),this.contents[e][n(55)]=[];let s=Vc.replace(/e_address/g,this.last_end);if(e>0){let l="";for(let B=0;B<e;++B)for(let x=0;x<this.contents[B].food.length;++x)l+=this.contents[B].food[x].name+",";s=s.replace(/last_food_prompt/g,"注意，我已经吃过"+l+"这几个美食了，不要重复，")}const a=r+s+$A,i=await this.askDS(e,a),o=i.split("@@@@");for(let l=0;l<o.length;l++)if(l!==0){l>this.contents[e].food.length&&this.contents[e].food.push({}),o[l][n(85)]("^^")?this.contents[e].food[l-1].name=o[l].split("^^")[1].split("^^")[0].trim():o[l].includes("$$")&&(this.contents[e].food[l-1].name=o[l][n(84)]("$$")[1].split("$$")[0].trim()),this.contents[e].food[l-1].info=o[l].replace(/\^/g,"").replace(/\$/g,"").replace(/%PT/g,"").trim(),this.contents[e].food[l-1].prompt=o[l].split(n(86))[1].split("%PT")[0].trim();try{this.contents[e].food[l-1].url=await Ae.getAIImg(this.contents[e].food[l-1].name,this[n(4)][e].food[l-1].prompt)}catch{this.contents[e].food[l-1].url=""}}this.contents[e].foodCompleted=2,this.setLoadingState("food",!1),this.expandedSectionType=null,this.selectedDayIndex=-1},async planningHotel(e,A,t,r){const n=U;this[n(4)][e].hotelCompleted=1,this.setLoadingState("hotel",!0),this.expandedSectionType="hotel",this.selectedDayIndex=e,this.activeDayDetailIndex=e,await this.$nextTick(),this.contents[e].hotel=[];const s=ts.replace(/e_address/g,this[n(87)]).replace(/customized_hotel_prompt/g,this.hotel_requirements||""),a=r+s+$A,i=await this.askDS(e,a),o=i.split("@@@@");for(let l=0;l<o.length;l++)if(l!==0){l>this.contents[e].hotel.length&&this.contents[e].hotel.push({}),this.contents[e].hotel[l-1].name=o[l][n(84)]("^^")[1].split("^^")[0].trim(),this[n(4)][e].hotel[l-1].info=o[l][n(68)](/\^\^.*\^\^/g,"").replace(/\$\$.*\$\$/g,"").trim();try{this.contents[e].hotel[l-1].url=await this.getHotelUrl(this.contents[e].hotel[l-1].name,A.account,t,l)}catch{this.contents[e].hotel[l-1].url="https://www.ctrip.com"}}this.contents[e].hotelCompleted=2,this.setLoadingState("hotel",!1),this.expandedSectionType=null,this.selectedDayIndex=-1},async planningCost(e,A,t,r){const n=U;this.contents[e].costCompleted=1,this[n(88)]("cost",!0),this.expandedSectionType="cost",this.selectedDayIndex=e,this[n(15)]=e,await this.$nextTick();const s=r+Nc+$A;this[n(4)][e].cost=await this.askDS(e,s),this.contents[e].costCompleted=2,this.setLoadingState("cost",!1),this.expandedSectionType=null,this.selectedDayIndex=-1},async planningNew(){const e=U,A=Ne();if(!A.checkLoginStatus()){alert("请先登录");return}const{user:t}=await A.getUserInfo();if(this.hasSavedPlanningContents()){if(this.loadPlanningContents()&&this.shouldContinuePlanning()&&confirm(`检测到有未完成的规划内容，是否继续之前的规划？

点击"确定"继续之前的规划
点击"取消"开始新的规划`)){await this.$nextTick(),setTimeout(()=>{this.handleContentRestoration(!1)},500);return}this.clearPlanningContents(),this.contents=[],this.selectedDayIndex=-1,this.activeDayDetailIndex=-1,this.expandedSectionType=null}if(!this.validateFormData())return;if(!this.hasCompleteLocation(this.s_location)){alert("为避免地点位置错误，请根据弹出的地点信息选择起点");return}if(!this.hasCompleteLocation(this.e_location)){alert("为避免地点位置错误，请根据弹出的地点信息选择终点");return}this.showBtn=!1,this.last_start=this.s_address,this.last_end=this.e_address,this.resetScrollState(),this.clearPlanningContents(),this.contents=[];for(let s=0;s<this.dates;s++)this.contents.push({weatherCompleted:0,rentCompleted:0,drivingCompleted:0,viewCompleted:0,hotelCompleted:0,foodCompleted:0,costCompleted:0});const r=this.getFormattedDate();let n=Br;n=n[e(68)](/startDate/g,this.startDate).replace(/s_address/g,this.s_address).replace(/e_address/g,this.e_address).replace(/plan_mode/g,this[e(24)]).replace(/travel_mode/g,this.travel_mode).replace(/dates/g,this.dates);try{await this.planningWeather(0,t,r,n)}catch(s){D.error("天气规划失败："+s),this.planningFinishProc(),this.resetScrollState(),this.contents=[],this.showPlanningFailure("天气信息获取失败，请检查网络连接后重试");return}if(this.travel_mode==="租车")try{await this.planningRent(0,t,r,n)}catch(s){D.error("租车方案规划失败："+s),this.planningFinishProc(),this.resetScrollState(),this.contents=[],this.showPlanningFailure("租车方案规划失败，请检查网络连接后重试");return}for(let s=0;s<this.dates;s++){try{await this[e(89)](s,t,r,n)}catch(a){D.error("路线规划失败："+a),this.planningFinishProc(),this.resetScrollState(),this.contents=[],this.showPlanningFailure("路线规划失败，请检查网络连接后重试");return}if(s!==this.dates-1||s===0||this.plan_mode==="单程"){try{await this.planningView(s,t,r,n)}catch(a){D[e(59)]("景点规划失败："+a),this.planningFinishProc(),this.resetScrollState(),this.contents=[],this.showPlanningFailure("景点推荐规划失败，请检查网络连接后重试");return}try{await this.planningFood(s,t,r,n)}catch(a){D[e(59)]("美食规划失败："+a),this.planningFinishProc(),this.resetScrollState(),this.contents=[],this.showPlanningFailure("美食推荐规划失败，请检查网络连接后重试");return}try{await this.planningHotel(s,t,r,n)}catch(a){D.error("住宿规划失败："+a),this.planningFinishProc(),this.resetScrollState(),this.contents=[],this.showPlanningFailure("住宿推荐规划失败，请检查网络连接后重试");return}}try{await this.savePlanToDB(s,t[e(90)],r)}catch(a){D.error("旅游规划存库失败："+a)}}await this.planningFinishProc(),this.showCompletionModal=!0},async planningFinishProc(){const e=U;this[e(11)]=!0,this.selectedDayIndex=0,this.activeDayDetailIndex=0,this.expandedSectionType=null,await this.$nextTick(),this.refreshVisibleMaps(),this[e(91)]()&&this.clearPlanningContents()},handleCompletionModalConfirm(){this.showCompletionModal=!1,this.clearPlanningContents(),this.$nextTick(()=>{const e=document.querySelector(".day-navigation");if(e){const A=e.offsetTop-100;window.scrollTo({top:Math.max(0,A),behavior:"smooth"})}})},handleFailureModalConfirm(){this.showFailureModal=!1},showPlanningFailure(e){this.failureReason=e,this.showFailureModal=!0},initFloatingButtons(){typeof window>"u"||(this.handleScroll=this.throttle(()=>{const e=window.pageYOffset||document.documentElement.scrollTop,A=document.documentElement.scrollHeight,t=document.documentElement.clientHeight;this.showFloatingButtons=e>200&&A>t+400},100),window.addEventListener("scroll",this.handleScroll,{passive:!0}))},scrollToTop(){window.scrollTo({top:0,behavior:"smooth"})},scrollToBottom(){window.scrollTo({top:document.documentElement.scrollHeight,behavior:"smooth"})},throttle(e,A){let t;return function(...n){const s=()=>{clearTimeout(t),e.apply(this,n)};clearTimeout(t),t=setTimeout(s,A)}},initThemeObserver(){const e=U;typeof window>"u"||(this[e(92)]=new MutationObserver(A=>{A.forEach(t=>{t.type==="attributes"&&t.attributeName==="class"&&this.updateCustomSuggestTheme()})}),this.themeObserver[e(93)](document.documentElement,{attributes:!0,attributeFilter:["class"]}))},updateCustomSuggestTheme(){const e=U;document.querySelectorAll(e(94)).forEach(t=>{aA[e(95)]&&aA.updateThemeStyles(t)})},async handleContentRestoration(e=!0){const A=U;try{await this.restoreMapInstances(),this[A(96)]();let t=!1;e&&(t=this.shouldContinuePlanning(),t&&await this[A(97)]()),(!e||!t)&&(this.restoreCompletedState(),await this.planningFinishProc(),this.showCompletionModal=!0)}catch{this.clearPlanningContents(),this.contents=[],this.selectedDayIndex=-1,this.showBtn=!0,this.resetAllLoadingStates(),alert("恢复规划内容失败，请重新开始规划")}},async restoreMapInstances(){if(!(!this.contents||this.contents.length===0)){await this.$nextTick(),await this.$nextTick();try{await aA.initialize()}catch(e){console.warn("地图服务重新初始化失败:",e)}for(let e=0;e<this.contents.length;e++)if(this.contents[e].drivingCompleted===2)try{await new Promise(r=>setTimeout(r,200*e));const t=document.querySelector("#map-container-"+e);t?(t.innerHTML="",await this.$nextTick(),await this.regenerateMapRoute(e)):console.warn("第"+(e+1)+"天地图容器不存在")}catch(t){console.warn("地图 "+(e+1)+" 恢复失败:",t)}setTimeout(()=>{this.refreshVisibleMaps(),setTimeout(()=>{this.forceRefreshAllMaps()},3e3)},2e3)}},async regenerateMapRoute(e){const A=U;try{const t=this.contents[e];if(!t||!t.driving){console[A(98)]("第"+(e+1)+"天没有路线规划数据");return}if(this.contents[e].pos.s_location&&this.contents[e].pos.e_location&&this.contents[e].pos.s_location.s_lng&&this.contents[e][A(99)].s_location.s_lat&&this.contents[e].pos.e_location.e_lng&&this.contents[e][A(99)].e_location.e_lat){const r=parseInt(this.plan_requirements||"0",10);await this.$nextTick(),await this.$nextTick(),await new Promise(n=>setTimeout(n,500)),await this.drivingPlanning(e,this.contents[e].pos.s_location,this.contents[e].pos.e_location,r,this.contents[e].pos.circle_locations),setTimeout(()=>{this.forceMapResize(e)},1e3)}else console.warn("第"+(e+1)+"天坐标信息无效，无法生成地图")}catch(t){console.error("第"+(e+1)+"天地图路线重新生成失败:",t),setTimeout(()=>{const r=A;try{document[r(100)]("#map-container-"+e)&&this.forceMapResize(e)}catch(n){console.error("第"+(e+1)+"天地图resize失败:",n)}},1e3)}},restoreScrollManagerState(){wA[U(25)]()},isPlanningFullyCompleted(){const e=U;if(!this.contents||this.contents.length===0)return!1;for(let A=0;A<this.contents.length;A++){const t=this[e(4)][A],r=["weatherCompleted","rentCompleted","drivingCompleted","viewCompleted","foodCompleted","hotelCompleted","costCompleted"];for(const n of r)if(t[n]===1)return!1;if(this[e(101)](t,A))return!1}return!0},shouldContinuePlanning(){return!this.isPlanningFullyCompleted()},hasIncompleteStages(e,A){const t=U;return A===0&&e.weatherCompleted===0||A===0&&this.travel_mode==="租车"&&e.rentCompleted===0||e[t(5)]===0||(A!==this[t(37)]-1||A===0||this.plan_mode==="单程")&&(e.viewCompleted===0||e.foodCompleted===0||e.hotelCompleted===0)},async continuePlanning(){const e=U;try{const A=Ne(),{user:t}=await A.getUserInfo(),r=this.getFormattedDate();let n=Br;n=n.replace(/startDate/g,this.startDate).replace(/s_address/g,this.s_address).replace(/e_address/g,this.e_address).replace(/plan_mode/g,this.plan_mode).replace(/travel_mode/g,this.travel_mode).replace(/dates/g,this.dates);for(let s=0;s<this.contents[e(13)];s++){const a=this.contents[s];if(s===0&&a.weatherCompleted!==2)try{await this.planningWeather(s,t,r,n)}catch(o){throw console.warn("第"+(s+1)+"天天气规划失败:",o),o}if(s===0&&this.travel_mode==="租车"&&a.rentCompleted!==2)try{await this[e(102)](s,t,r,n)}catch(o){throw console.warn("第"+(s+1)+"天租车规划失败:",o),o}if(a.drivingCompleted!==2){const o=parseInt(this.plan_requirements,10);try{await this.planningDriving(s,t,r,n,o)}catch(l){throw console.warn("第"+(s+1)+"天路线规划失败:",l),l}}if(s!==this.dates-1||s===0||this.plan_mode==="单程"){if(a.viewCompleted!==2)try{await this.planningView(s,t,r,n)}catch(o){throw console.warn("第"+(s+1)+"天景点规划失败:",o),o}if(a.foodCompleted!==2)try{await this.planningFood(s,t,r,n)}catch(o){throw console.warn("第"+(s+1)+e(103),o),o}if(a.hotelCompleted!==2)try{await this.planningHotel(s,t,r,n)}catch(o){throw console.warn("第"+(s+1)+"天住宿规划失败:",o),o}}try{await this.savePlanToDB(s,t.account,r)}catch(o){console[e(98)]("第"+(s+1)+e(104),o)}}await this.planningFinishProc(),this.showCompletionModal=!0}catch(A){console.error("继续规划失败:",A),this[e(105)](),this.showBtn=!0,this.showPlanningFailure("继续规划失败，请检查网络连接后重试")}},restoreCompletedState(){this.showBtn=!0,this.resetAllLoadingStates(),this.isPlanningFullyCompleted()&&this.clearPlanningContents(),this.contents.length>0&&(this.selectedDayIndex=0,this.$nextTick(()=>{setTimeout(()=>{this.refreshVisibleMaps(),this.contents.forEach((e,A)=>{e.drivingCompleted===2&&setTimeout(()=>{this.forceMapResize(A)},500*(A+1))})},1e3)}))},forceRefreshAllMaps(){this.contents.forEach((e,A)=>{e.drivingCompleted===2&&setTimeout(()=>{const t=U;try{document.querySelector("#map-container-"+A)&&(aA.getMapInstance(A)?this.forceMapResize(A):this.regenerateMapRoute(A))}catch(r){console.error("强制刷新第"+(A+1)+t(106),r)}},1e3*(A+1))})}}},DA=Nr;function Vr(){const e=["disabled","answer-action-input-container","content-wrapper","header-toggle","onKeypress","modal-message","location","modal-footer","progress-step","div","step-number","circle","currentColor","path","M4 15s1-1 4-1 5 2 8 2 4-1 4-1V3s-1 1-4 1-5-2-8-2-4 1-4 1z","请输入目的地城市/景点","startDate","input-decoration","select","plan_mode","button",'<div class="btn-content" data-v-19315b56><svg class="btn-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" data-v-19315b56><path d="M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8" data-v-19315b56></path><path d="M21 3v5h-5" data-v-19315b56></path><path d="M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16" data-v-19315b56></path><path d="M3 21v-5h5" data-v-19315b56></path></svg><span class="btn-text" data-v-19315b56>重置表单</span></div>',"contents","nav-","feature-text","isSectionAvailable","span","activeDayDetailIndex","food","collapseAll","Weather Information","section-header rent-header","think-area","think","rent_requirements","enter","rent","btn-icon","shouldShowSection","answer-action-input","plan_requirements","handleActionClick","drivingCompleted","路线地图","shouldShowSectionHeader","header-title","viewCompleted","foodCompleted","parseMarkdown",`**美食推荐:**

`,"food-","icon-ori","answer-action-btn","hotel","handleCompletionModalConfirm","modal-header","strong","我知道了"];return Vr=function(){return e},Vr()}function Nr(e,A){const t=Vr();return Nr=function(r,n){return r=r-0,t[r]},Nr(e,A)}const Jc={class:"global-loading-banner"},Xc={class:"global-loading-content"},Yc={class:"global-loading-text"},Wc={class:"travel-planning-container"},Zc={class:"travel-form"},zc={class:"form-progress"},qc={class:"location-section"},jc={class:"location-inputs"},$c={class:"form-item location-item"},A0={class:"input-wrapper"},e0=["disabled"],t0={class:"form-item location-item"},r0={class:"input-wrapper"},n0=["disabled"],s0={class:"travel-details-section"},a0={class:"details-grid"},i0={class:"form-item detail-item"},o0={class:"input-wrapper"},l0=["disabled"],B0={class:"form-item detail-item"},c0={class:"input-wrapper"},x0=["disabled"],u0=["value","selected"],g0={class:"form-item detail-item"},d0={class:"input-wrapper"},h0=["disabled"],w0={class:"form-item detail-item"},f0={class:"input-wrapper"},Q0=["disabled"],C0={class:"action-section"},p0={class:"button-group"},U0=["disabled"],F0={class:"loading-state"},m0={class:"day-navigation"},v0=["onClick","disabled"],E0={class:"day-number"},y0={class:"day-title"},H0={class:"day-detail-panel"},I0={class:"panel-header"},b0={class:"panel-title"},S0={class:"panel-subtitle"},L0={class:"panel-body"},D0={class:"panel-features"},K0=["disabled"],T0=["disabled"],M0=["disabled"],O0=[DA(0)],_0=["disabled"],R0=["disabled"],G0={class:"panel-actions"},k0={id:"scroll-area",class:"scroll-container",ref:"scrollContainer"},V0={"data-dynamic-item":"",class:"answer-area-container"},N0=["onClick"],P0={class:"header-toggle"},J0={class:"section-content"},X0=["innerHTML"],Y0={"data-dynamic-item":"",class:"answer-area-container"},W0=["onClick"],Z0={class:"header-toggle"},z0={class:"section-content"},q0=["innerHTML"],j0={class:"answer-action-input-container"},$0=["value","onKeypress"],Ax=["onClick"],ex={"data-dynamic-item":"",class:"answer-area-container"},tx=["onClick"],rx={class:"header-toggle"},nx={class:"section-content"},sx=["innerHTML"],ax={class:DA(1)},ix=["value"],ox=["onClick"],lx={class:"planning-box"},Bx=["onClick"],cx={class:"map-controls"},xx=["onClick","title"],ux={key:0},gx={key:1},dx=["id"],hx={"data-dynamic-item":"",class:"answer-area-container"},wx=["onClick"],fx={class:"header-toggle"},Qx={class:"section-content"},Cx={class:DA(2)},px=["innerHTML"],Ux=["src"],Fx=["innerHTML"],mx={"data-dynamic-item":"",class:"answer-area-container"},vx=["onClick"],Ex={class:DA(3)},yx={class:"section-content"},Hx={class:"content-wrapper"},Ix=["innerHTML"],bx=["src"],Sx=["innerHTML"],Lx={"data-dynamic-item":"",class:"answer-area-container"},Dx=["onClick"],Kx={class:"header-toggle"},Tx={class:"section-content"},Mx={class:"content-wrapper"},Ox=["innerHTML"],_x=["href"],Rx=["innerHTML"],Gx={class:"answer-action-input-container"},kx=["value",DA(4)],Vx=["onClick"],Nx={"data-dynamic-item":"",class:"answer-area-container"},Px=["innerHTML"],Jx={class:"vitepress-divider"},Xx={class:"modal-body"},Yx={class:DA(5)},Wx={class:"modal-subtitle"},Zx={class:DA(6)},zx={class:"location"},qx={class:"modal-footer"},jx={class:"modal-body"},$x={class:"modal-message"},Au={class:"modal-subtitle"},eu={class:DA(7)};function tu(e,A,t,r,n,s){const a=DA;return X(),J(MA,null,[H(c("div",Jc,[c("div",Xc,[A[26]||(A[26]=c("div",{class:"global-spinner"},null,-1)),c("span",Yc,G(e.currentLoadingMessage),1)])],512),[[I,e.isAnyLoading]]),c("div",Wc,[A[84]||(A[84]=CA('<div class="page-header" data-v-19315b56><div class="header-content" data-v-19315b56><h1 class="page-title" data-v-19315b56><svg class="title-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" data-v-19315b56><path d="M21 10c0 7-9 13-9 13s-9-6-9-13a9 9 0 0 1 18 0z" data-v-19315b56></path><circle cx="12" cy="10" r="3" data-v-19315b56></circle></svg> 行旅诗笺 </h1><p class="page-subtitle" data-v-19315b56>为您智能定制完美的旅行体验</p></div><div class="header-decoration" data-v-19315b56><div class="decoration-circle" data-v-19315b56></div><div class="decoration-circle" data-v-19315b56></div><div class="decoration-circle" data-v-19315b56></div></div></div>',1)),c("div",Zc,[c("div",zc,[c("div",{class:nA([a(8),{active:e.currentProgressStep>=1}])},A[27]||(A[27]=[c(a(9),{class:"step-number"},"1",-1),c("span",{class:"step-label"},"基本信息",-1)]),2),c("div",{class:nA(["progress-line",{active:e.currentProgressStep>=2}])},null,2),c("div",{class:nA(["progress-step",{active:e.currentProgressStep>=2}])},A[28]||(A[28]=[c("div",{class:a(10)},"2",-1),c("span",{class:"step-label"},"生成规划",-1)]),2),c(a(9),{class:nA(["progress-line",{active:e.currentProgressStep>=3}])},null,2),c("div",{class:nA(["progress-step",{active:e.currentProgressStep>=3}])},A[29]||(A[29]=[c(a(9),{class:"step-number"},"3",-1),c("span",{class:"step-label"},"完成",-1)]),2)]),c("div",qc,[A[35]||(A[35]=c("h3",{class:"section-title"},[c("svg",{class:"section-icon",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor","stroke-width":"2"},[c("path",{d:"M21 10c0 7-9 13-9 13s-9-6-9-13a9 9 0 0 1 18 0z"}),c(a(11),{cx:"12",cy:"10",r:"3"})]),sA(" 选择出发地与目的地 ")],-1)),c("div",jc,[c("div",$c,[A[31]||(A[31]=c("label",{for:"start-tipinput",class:"form-label"},[c("svg",{class:"label-icon",viewBox:"0 0 24 24",fill:"none",stroke:a(12),"stroke-width":"2"},[c("circle",{cx:"12",cy:"12",r:"10"}),c("circle",{cx:"12",cy:"12",r:"3"})]),sA(" 出发地 ")],-1)),c("div",A0,[H(c("input",{id:"start-tipinput",class:"form-input","onUpdate:modelValue":A[0]||(A[0]=i=>e.s_address=i),type:"text",placeholder:"请输入出发城市/地点",disabled:!e.showBtn,autocomplete:"off"},null,8,e0),[[kt,e.s_address]]),A[30]||(A[30]=c("div",{class:"input-decoration"},null,-1))])]),A[34]||(A[34]=c("div",{class:"route-connector"},[c("svg",{viewBox:"0 0 24 24",fill:"none",stroke:"currentColor","stroke-width":"2"},[c("path",{d:"M5 12h14"}),c(a(13),{d:"m12 5 7 7-7 7"})])],-1)),c(a(9),t0,[A[33]||(A[33]=c("label",{for:"end-tipinput",class:"form-label"},[c("svg",{class:"label-icon",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor","stroke-width":"2"},[c("path",{d:a(14)}),c("path",{d:"M4 22v-7"})]),sA(" 目的地 ")],-1)),c("div",r0,[H(c("input",{id:"end-tipinput",class:"form-input","onUpdate:modelValue":A[1]||(A[1]=i=>e.e_address=i),type:"text",placeholder:a(15),disabled:!e.showBtn,autocomplete:"off"},null,8,n0),[[kt,e.e_address]]),A[32]||(A[32]=c("div",{class:"input-decoration"},null,-1))])])])]),c("div",s0,[A[46]||(A[46]=CA('<h3 class="section-title" data-v-19315b56><svg class="section-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" data-v-19315b56><rect x="3" y="4" width="18" height="18" rx="2" ry="2" data-v-19315b56></rect><line x1="16" y1="2" x2="16" y2="6" data-v-19315b56></line><line x1="8" y1="2" x2="8" y2="6" data-v-19315b56></line><line x1="3" y1="10" x2="21" y2="10" data-v-19315b56></line></svg> 设置旅行参数 </h3>',1)),c(a(9),a0,[c("div",i0,[A[37]||(A[37]=CA('<label for="start-dateinput" class="form-label" data-v-19315b56><svg class="label-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" data-v-19315b56><rect x="3" y="4" width="18" height="18" rx="2" ry="2" data-v-19315b56></rect><line x1="16" y1="2" x2="16" y2="6" data-v-19315b56></line><line x1="8" y1="2" x2="8" y2="6" data-v-19315b56></line><line x1="3" y1="10" x2="21" y2="10" data-v-19315b56></line></svg> 出发日期 </label>',1)),c("div",o0,[H(c("input",{type:"date",class:"form-input date-input","onUpdate:modelValue":A[2]||(A[2]=i=>e[a(16)]=i),disabled:!e.showBtn},null,8,l0),[[kt,e.startDate]]),A[36]||(A[36]=c("div",{class:"input-decoration"},null,-1))])]),c("div",B0,[A[39]||(A[39]=c("label",{for:"datesinput",class:"form-label"},[c("svg",{class:"label-icon",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor","stroke-width":"2"},[c("circle",{cx:"12",cy:"12",r:"10"}),c("polyline",{points:"12,6 12,12 16,14"})]),sA(" 游玩天数 ")],-1)),c("div",c0,[H(c("select",{id:"datesinput",class:"form-input select-input","onUpdate:modelValue":A[3]||(A[3]=i=>e.dates=i),disabled:!e.showBtn},[(X(),J(MA,null,zA(5,i=>c("option",{value:i,key:i,selected:i===3},G(i)+"天",9,u0)),64))],8,x0),[[Pe,e.dates,void 0,{number:!0}]]),A[38]||(A[38]=c(a(9),{class:a(17)},null,-1))])]),c("div",g0,[A[42]||(A[42]=CA('<label for="plan-mode" class="form-label" data-v-19315b56><svg class="label-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" data-v-19315b56><path d="M21 16V8a2 2 0 0 0-1-1.73l-7-4a2 2 0 0 0-2 0l-7 4A2 2 0 0 0 3 8v8a2 2 0 0 0 1 1.73l7 4a2 2 0 0 0 2 0l7-4A2 2 0 0 0 21 16z" data-v-19315b56></path><polyline points="3.27,6.96 12,12.01 20.73,6.96" data-v-19315b56></polyline><line x1="12" y1="22.08" x2="12" y2="12" data-v-19315b56></line></svg> 旅行模式 </label>',1)),c("div",d0,[H(c(a(18),{id:"plan-mode",class:"form-input select-input","onUpdate:modelValue":A[4]||(A[4]=i=>e.plan_mode=i),disabled:!e.showBtn},A[40]||(A[40]=[c("option",{value:"往返"},"往返旅行",-1),c("option",{value:"单程"},"单程旅行",-1)]),8,h0),[[Pe,e[a(19)]]]),A[41]||(A[41]=c("div",{class:"input-decoration"},null,-1))])]),c("div",w0,[A[45]||(A[45]=CA('<label for="travel-mode" class="form-label" data-v-19315b56><svg class="label-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" data-v-19315b56><path d="M7 17m-2 0a2 2 0 1 0 4 0a2 2 0 1 0 -4 0" data-v-19315b56></path><path d="M17 17m-2 0a2 2 0 1 0 4 0a2 2 0 1 0 -4 0" data-v-19315b56></path><path d="M5 17h-2v-6l2-5h9l4 5h1a2 2 0 0 1 2 2v4h-2" data-v-19315b56></path><path d="M9 17v-6h4v6" data-v-19315b56></path><path d="M2 6h15" data-v-19315b56></path></svg> 交通方式 </label>',1)),c("div",f0,[H(c("select",{id:"travel-mode",class:"form-input select-input","onUpdate:modelValue":A[5]||(A[5]=i=>e.travel_mode=i),disabled:!e.showBtn},A[43]||(A[43]=[c("option",{value:"自驾"},"自驾出行",-1),c("option",{value:"租车"},"租车出行",-1)]),8,Q0),[[Pe,e.travel_mode]]),A[44]||(A[44]=c("div",{class:"input-decoration"},null,-1))])])])]),c("div",C0,[c("div",p0,[H(c(a(20),{class:"btn-primary btn-planning",onClick:A[6]||(A[6]=(...i)=>e.planningNew&&e.planningNew(...i))},A[47]||(A[47]=[CA('<div class="btn-content" data-v-19315b56><svg class="btn-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" data-v-19315b56><path d="M4.5 16.5c-1.5 1.5-1.5 4.5 0 6s4.5 1.5 6 0l1-1" data-v-19315b56></path><path d="M15 7l-6.5 6.5a1.5 1.5 0 0 0 0 2.12l.88.88a1.5 1.5 0 0 0 2.12 0L18 10" data-v-19315b56></path><path d="M9 5l8 8" data-v-19315b56></path><path d="M21 3l-6 6" data-v-19315b56></path></svg><span class="btn-text" data-v-19315b56>开始规划</span></div><div class="btn-shine" data-v-19315b56></div>',2)]),512),[[I,e.showBtn]]),H(c("button",{class:"btn-secondary btn-reset",onClick:A[7]||(A[7]=(...i)=>e.resetFormData&&e.resetFormData(...i)),title:"清除所有输入内容",disabled:!e.showBtn},A[48]||(A[48]=[CA(a(21),1)]),8,U0),[[I,e.showBtn]])]),H(c("div",F0,A[49]||(A[49]=[CA('<div class="loading-animation" data-v-19315b56><div class="loading-spinner" data-v-19315b56><svg viewBox="0 0 50 50" data-v-19315b56><circle cx="25" cy="25" r="20" fill="none" stroke="currentColor" stroke-width="4" stroke-linecap="round" stroke-dasharray="31.416" stroke-dashoffset="31.416" data-v-19315b56><animate attributeName="stroke-array" dur="2s" values="0 31.416;15.708 15.708;0 31.416" repeatCount="indefinite" data-v-19315b56></animate><animate attributeName="stroke-dashoffset" dur="2s" values="0;-15.708;-31.416" repeatCount="indefinite" data-v-19315b56></animate></circle></svg></div><div class="loading-text" data-v-19315b56><span class="loading-message" data-v-19315b56>正在为您规划完美旅程...</span><div class="loading-dots" data-v-19315b56><span data-v-19315b56></span><span data-v-19315b56></span><span data-v-19315b56></span></div></div></div>',1)]),512),[[I,!e.showBtn]])])]),H(c("div",m0,[(X(!0),J(MA,null,zA(e[a(22)],(i,o)=>{const l=a;return X(),J("button",{key:l(23)+o,class:nA(["day-nav-btn",{active:e.activeDayDetailIndex===o,completed:e.isDayCompleted(i),disabled:!e.isDayCompleted(i)}]),onClick:B=>e.selectDay(o),disabled:!e.isDayCompleted(i)},[c("span",E0,"第"+G(o+1)+"天",1),c("span",y0,G(e.getDayTitle(i,o)),1)],10,v0)}),128))],512),[[I,e.contents.length>0]]),H(c(a(9),H0,[c("div",I0,[A[50]||(A[50]=c("div",{class:"panel-icon"},"✨",-1)),c("h3",b0,"第"+G(e.activeDayDetailIndex+1)+"天行程详情",1),c("p",S0,G(e.activeDayDetailIndex!==-1&&e.contents[e.activeDayDetailIndex]?e.getDayTitle(e.contents[e.activeDayDetailIndex],e.activeDayDetailIndex):""),1)]),c("div",L0,[c("div",D0,[H(c("button",{class:nA(["feature-button",{available:e.isSectionAvailable("weather",e.activeDayDetailIndex),active:e.expandedSectionType==="weather"}]),disabled:!e.isSectionAvailable("weather",e.activeDayDetailIndex),onClick:A[8]||(A[8]=i=>e.handleDetailPanelClick("weather"))},A[51]||(A[51]=[c("span",{class:"feature-icon"},"🌤️",-1),c("span",{class:a(24)},"天气",-1)]),10,K0),[[I,e.activeDayDetailIndex===0]]),H(c("button",{class:nA(["feature-button",{available:e[a(25)]("rent",e.activeDayDetailIndex),active:e.expandedSectionType==="rent"}]),disabled:!e.isSectionAvailable("rent",e.activeDayDetailIndex),onClick:A[9]||(A[9]=i=>e.handleDetailPanelClick("rent"))},A[52]||(A[52]=[c("span",{class:"feature-icon"},"🚗",-1),c(a(26),{class:"feature-text"},"租车",-1)]),10,T0),[[I,e[a(27)]===0&&e.travel_mode==="租车"]]),c("button",{class:nA(["feature-button",{available:e.isSectionAvailable("driving",e.activeDayDetailIndex),active:e.expandedSectionType==="driving"}]),disabled:!e.isSectionAvailable("driving",e.activeDayDetailIndex),onClick:A[10]||(A[10]=i=>e.handleDetailPanelClick("driving"))},A[53]||(A[53]=[c("span",{class:"feature-icon"},"🗺️",-1),c("span",{class:a(24)},"路线",-1)]),10,M0),c("button",{class:nA(["feature-button",{available:e.isSectionAvailable("view",e[a(27)]),active:e.expandedSectionType==="view"}]),disabled:!e.isSectionAvailable("view",e.activeDayDetailIndex),onClick:A[11]||(A[11]=i=>e.handleDetailPanelClick("view"))},A[54]||(A[54]=[c("span",{class:"feature-icon"},"🏞️",-1),c("span",{class:"feature-text"},"景点",-1)]),10,O0),c("button",{class:nA(["feature-button",{available:e.isSectionAvailable("food",e.activeDayDetailIndex),active:e.expandedSectionType==="food"}]),disabled:!e.isSectionAvailable("food",e[a(27)]),onClick:A[12]||(A[12]=i=>e.handleDetailPanelClick(a(28)))},A[55]||(A[55]=[c("span",{class:"feature-icon"},"🍜",-1),c("span",{class:"feature-text"},"美食",-1)]),10,_0),c("button",{class:nA(["feature-button",{available:e.isSectionAvailable("hotel",e.activeDayDetailIndex),active:e.expandedSectionType==="hotel"}]),disabled:!e.isSectionAvailable("hotel",e.activeDayDetailIndex),onClick:A[13]||(A[13]=i=>e.handleDetailPanelClick("hotel"))},A[56]||(A[56]=[c("span",{class:"feature-icon"},"🏨",-1),c("span",{class:"feature-text"},"住宿",-1)]),10,R0)]),c("div",G0,[H(c("button",{class:"collapse-all-btn",onClick:A[14]||(A[14]=(...i)=>e.collapseAll&&e[a(29)](...i))},A[57]||(A[57]=[c("span",{class:"btn-icon"},"📝",-1),c("span",{class:"btn-text"},"收起全部",-1)]),512),[[I,e.expandedSectionType!==null]])])])],512),[[I,e.activeDayDetailIndex!==-1&&e.contents.length>0&&e.contents[e.activeDayDetailIndex]]]),c("div",k0,[(X(!0),J(MA,null,zA(e.contents,(i,o)=>{const l=a;return X(),J("div",{key:"day-"+o},[H(c("div",V0,[c("div",{class:"section-header weather-header",onClick:B=>e.handleSectionHeaderClick("weather",o)},[A[58]||(A[58]=c("div",{class:"header-icon"},"🌤️",-1)),A[59]||(A[59]=c("div",{class:"header-title"},"天气信息",-1)),A[60]||(A[60]=c("div",{class:"header-subtitle"},l(30),-1)),H(c("div",P0,"▼",512),[[I,e.shouldShowSection("weather",o)]])],8,N0),H(c("div",J0,[H(c("div",{class:"think-area"},G(i.think),513),[[I,i.weatherCompleted===1]]),c("div",{class:"answer-area",innerHTML:e.parseMarkdown(i.weather)},null,8,X0)],512),[[I,e.shouldShowSection("weather",o)]])],512),[[I,e.shouldShowSectionHeader("weather",o)]]),H(c("div",Y0,[c("div",{class:l(31),onClick:B=>e.handleSectionHeaderClick("rent",o)},[A[61]||(A[61]=c("div",{class:"header-icon"},"🚗",-1)),A[62]||(A[62]=c("div",{class:"header-title"},"租车方案",-1)),A[63]||(A[63]=c("div",{class:"header-subtitle"},"Car Rental",-1)),H(c("div",Z0,"▼",512),[[I,e.shouldShowSection("rent",o)]])],8,W0),H(c("div",z0,[H(c(l(9),{class:l(32)},G(i[l(33)]),513),[[I,i.rentCompleted===1]]),c("div",{class:"answer-area",innerHTML:e.parseMarkdown(i.rent)},null,8,q0),H(c("div",j0,[c("input",{value:e[l(34)],type:"text",class:"answer-action-input",placeholder:"请输入您的租车需求，我们可以根据您的需求重新定制一次...",onInput:A[15]||(A[15]=(...B)=>e.updateRentRequirements&&e.updateRentRequirements(...B)),onKeypress:qr(B=>e.handleRentCustomize(o),[l(35)])},null,40,$0)],512),[[I,i.rentCompleted===2&&!e.rent_customized]]),H(c("button",{class:"answer-action-btn",onClick:B=>e.handleActionClick(l(36),o)},A[64]||(A[64]=[c("span",{class:l(37)},"🚗",-1),c("span",{class:"btn-text"},"定制",-1)]),8,Ax),[[I,i.rentCompleted===2&&!e.rent_customized]])],512),[[I,e.shouldShowSection(l(36),o)]])],512),[[I,e.shouldShowSectionHeader("rent",o)]]),H(c("div",ex,[c("div",{class:"section-header driving-header",onClick:B=>e.handleSectionHeaderClick("driving",o)},[A[65]||(A[65]=c("div",{class:"header-icon"},"🗺️",-1)),A[66]||(A[66]=c("div",{class:"header-title"},"路线规划",-1)),A[67]||(A[67]=c("div",{class:"header-subtitle"},"Route Planning",-1)),H(c("div",rx,"▼",512),[[I,e[l(38)]("driving",o)]])],8,tx),H(c("div",nx,[H(c("div",{class:"think-area"},G(i[l(33)]),513),[[I,i.drivingCompleted===1]]),c("div",{class:"answer-area",innerHTML:e.parseMarkdown(i.driving)},null,8,sx),H(c("div",ax,[H(c("select",{class:l(39),"onUpdate:modelValue":A[16]||(A[16]=B=>e.plan_requirements=B)},[(X(!0),J(MA,null,zA(e.planOptions,B=>(X(),J("option",{key:B.value,value:B.value},G(B.text),9,ix))),128))],512),[[Pe,e[l(40)]]])],512),[[I,i.drivingCompleted===2&&!e.plan_customized]]),H(c(l(20),{class:"answer-action-btn",onClick:B=>e[l(41)]("driving",o)},A[68]||(A[68]=[c(l(26),{class:"btn-icon"},"📍",-1),c("span",{class:"btn-text"},"重新规划(一次免费机会)",-1)]),8,ox),[[I,i[l(42)]===2&&!e.plan_customized]])],512),[[I,e.shouldShowSection("driving",o)]])],512),[[I,e.shouldShowSectionHeader("driving",o)]]),H(c("div",lx,[A[69]||(A[69]=c("div",{class:"section-header map-header"},[c(l(9),{class:"header-icon"},"🗺️"),c("div",{class:"header-title"},l(43)),c(l(9),{class:"header-subtitle"},"Route Map")],-1)),c("div",{class:nA(["map-wrapper",{"map-maximized":e.maximizedMapIndex===o}])},[e.maximizedMapIndex===o?(X(),J("div",{key:0,class:"map-overlay",onClick:B=>e.toggleMapSize(o)},null,8,Bx)):Gt("",!0),c("div",cx,[c("button",{class:"map-control-btn",onClick:B=>e.toggleMapSize(o),title:e.maximizedMapIndex===o?"缩小地图 (ESC)":"最大化地图"},[e.maximizedMapIndex===o?(X(),J("span",ux,"✕")):(X(),J(l(26),gx,"🗖"))],8,xx)]),c("div",{id:"map-container-"+o,class:"map-container"},null,8,dx)],2)],512),[[I,e[l(44)]("driving",o)&&"amap"in i&&e.shouldShowSection("driving",o)]]),H(c("div",hx,[c("div",{class:"section-header view-header",onClick:B=>e.handleSectionHeaderClick("view",o)},[A[70]||(A[70]=c("div",{class:"header-icon"},"🏞️",-1)),A[71]||(A[71]=c("div",{class:l(45)},"景点推荐",-1)),A[72]||(A[72]=c("div",{class:"header-subtitle"},"Tourist Attractions",-1)),H(c("div",fx,"▼",512),[[I,e.shouldShowSection("view",o)]])],8,wx),H(c(l(9),Qx,[c("div",Cx,[H(c("div",{class:"think-area"},G(i.think),513),[[I,i[l(46)]===1]]),c("div",{class:"answer-area",innerHTML:this.parseMarkdown(`**景点推荐:**

`)},null,8,px),(X(!0),J(MA,null,zA(i.view,(B,x)=>(X(),J("div",{class:"content-item",key:"view-"+x},[A[73]||(A[73]=c("span",{class:"icon-ori"},"🏞️",-1)),sA(" "+G(B.name)+" ",1),c("img",{src:B.url,alt:"景点图片",class:"view-image"},null,8,Ux),c("div",{class:"answer-area",innerHTML:this.parseMarkdown(B.info)},null,8,Fx)]))),128))])],512),[[I,e.shouldShowSection("view",o)]])],512),[[I,e.shouldShowSectionHeader("view",o)]]),H(c("div",mx,[c("div",{class:"section-header food-header",onClick:B=>e.handleSectionHeaderClick(l(28),o)},[A[74]||(A[74]=c("div",{class:"header-icon"},"🍜",-1)),A[75]||(A[75]=c("div",{class:"header-title"},"美食推荐",-1)),A[76]||(A[76]=c("div",{class:"header-subtitle"},"Local Cuisine",-1)),H(c("div",Ex,"▼",512),[[I,e.shouldShowSection("food",o)]])],8,vx),H(c(l(9),yx,[c("div",Hx,[H(c("div",{class:l(32)},G(i.think),513),[[I,i[l(47)]===1]]),c("div",{class:"answer-area",innerHTML:this[l(48)](l(49))},null,8,Ix),(X(!0),J(MA,null,zA(i.food,(B,x)=>{const u=l;return X(),J("div",{class:"content-item",key:u(50)+x},[A[77]||(A[77]=c("span",{class:u(51)},"🍜",-1)),sA(" "+G(B.name)+" ",1),c("img",{src:B.url,alt:"美食图片",class:"food-image"},null,8,bx),c("div",{class:"answer-area",innerHTML:this.parseMarkdown(B.info)},null,8,Sx)])}),128))])],512),[[I,e.shouldShowSection("food",o)]])],512),[[I,e.shouldShowSectionHeader("food",o)]]),H(c("div",Lx,[c(l(9),{class:"section-header hotel-header",onClick:B=>e.handleSectionHeaderClick("hotel",o)},[A[78]||(A[78]=c(l(9),{class:"header-icon"},"🏨",-1)),A[79]||(A[79]=c("div",{class:"header-title"},"住宿推荐",-1)),A[80]||(A[80]=c("div",{class:"header-subtitle"},"Accommodation",-1)),H(c("div",Kx,"▼",512),[[I,e.shouldShowSection("hotel",o)]])],8,Dx),H(c("div",Tx,[c("div",Mx,[H(c("div",{class:"think-area"},G(i.think),513),[[I,i.hotelCompleted===1]]),c("div",{class:"answer-area",innerHTML:this.parseMarkdown(`**住宿推荐:**

`)},null,8,Ox),(X(!0),J(MA,null,zA(i.hotel,(B,x)=>{const u=l;return X(),J("div",{class:"content-item",key:"hotel-"+x},[c("a",{href:B.url,target:"_blank",class:"hotel-link"},[A[81]||(A[81]=c("span",{class:"icon-ori"},"🏨",-1)),sA(" "+G("携程直达："+B.name),1)],8,_x),c("div",{class:"answer-area",innerHTML:this[u(48)](B.info)},null,8,Rx)])}),128))]),H(c("div",Gx,[c("input",{value:e.hotel_requirements,type:"text",class:"answer-action-input",placeholder:"请输入您的住宿需求，我们可以根据您的需求重新定制一次...",onInput:A[17]||(A[17]=(...B)=>e.updateHotelRequirements&&e.updateHotelRequirements(...B)),onKeypress:qr(B=>e.handleActionClick("hotel",o),[l(35)])},null,40,kx)],512),[[I,i.hotelCompleted===2&&!e.hotel_customized]]),H(c("button",{class:l(52),onClick:B=>e.handleActionClick("hotel",o)},A[82]||(A[82]=[c("span",{class:"btn-icon"},"🏨",-1),c(l(26),{class:"btn-text"},"定制",-1)]),8,Vx),[[I,i.hotelCompleted===2&&!e.hotel_customized]])],512),[[I,e.shouldShowSection("hotel",o)]])],512),[[I,e.shouldShowSectionHeader(l(53),o)]]),H(c("div",Nx,[A[83]||(A[83]=c("div",{class:"section-header cost-header"},[c("div",{class:"header-icon"},"💰"),c(l(9),{class:"header-title"},"费用预算"),c("div",{class:"header-subtitle"},"Budget Planning")],-1)),H(c("div",{class:"think-area"},G(i.think),513),[[I,i.costCompleted===1]]),c("div",{class:"answer-area",innerHTML:e[l(48)](i.cost)},null,8,Px)],512),[[I,e.shouldShowSectionHeader("cost",o)]]),H(c("hr",Jx,null,512),[[I,"cost"in i]])])}),128))],512)]),e.showCompletionModal?(X(),J(a(9),{key:0,class:"completion-modal-overlay",onClick:A[20]||(A[20]=(...i)=>e.handleCompletionModalConfirm&&e[a(54)](...i))},[c("div",{class:"completion-modal",onClick:A[19]||(A[19]=jr(()=>{},["stop"]))},[A[92]||(A[92]=c("div",{class:a(55)},[c("div",{class:"modal-icon"},"✨"),c("h3",{class:"modal-title"},"规划完成！")],-1)),c("div",Xx,[c("p",Yx,[A[85]||(A[85]=sA("您的 ")),c("strong",null,G(e.dates)+"天"+G(e.plan_mode)+"旅行规划",1),A[86]||(A[86]=sA(" 已经生成完毕！"))]),c("p",Wx,[A[87]||(A[87]=sA("从 ")),c(a(26),Zx,G(e.s_address),1),A[88]||(A[88]=sA(" 到 ")),c("span",zx,G(e.e_address),1),A[89]||(A[89]=sA(" 的完美行程等您探索"))]),A[90]||(A[90]=CA('<div class="modal-features" data-v-19315b56><div class="feature-item" data-v-19315b56><span class="feature-icon" data-v-19315b56>📍</span><span data-v-19315b56>详细路线规划</span></div><div class="feature-item" data-v-19315b56><span class="feature-icon" data-v-19315b56>🏞️</span><span data-v-19315b56>精选景点推荐</span></div><div class="feature-item" data-v-19315b56><span class="feature-icon" data-v-19315b56>🏨</span><span data-v-19315b56>优质住宿建议</span></div><div class="feature-item" data-v-19315b56><span class="feature-icon" data-v-19315b56>🍜</span><span data-v-19315b56>地道美食指南</span></div></div>',1))]),c("div",qx,[c(a(20),{class:"modal-confirm-btn",onClick:A[18]||(A[18]=(...i)=>e.handleCompletionModalConfirm&&e.handleCompletionModalConfirm(...i))},A[91]||(A[91]=[c("span",{class:"btn-icon"},"👍",-1)]))])])])):Gt("",!0),e.showFailureModal?(X(),J("div",{key:1,class:"failure-modal-overlay",onClick:A[23]||(A[23]=(...i)=>e.handleFailureModalConfirm&&e.handleFailureModalConfirm(...i))},[c("div",{class:"failure-modal",onClick:A[22]||(A[22]=jr(()=>{},["stop"]))},[A[97]||(A[97]=c("div",{class:"modal-header failure-header"},[c("div",{class:"modal-icon"},"❌"),c("h3",{class:"modal-title"},"规划失败")],-1)),c("div",jx,[c("p",$x,[A[93]||(A[93]=sA("很抱歉，您的 ")),c(a(56),null,G(e.dates)+"天"+G(e.plan_mode)+"旅行规划",1),A[94]||(A[94]=sA(" 生成失败"))]),c("p",Au,G(e.failureReason),1),A[95]||(A[95]=CA('<div class="failure-suggestions" data-v-19315b56><div class="suggestion-item" data-v-19315b56><span class="suggestion-icon" data-v-19315b56>🔄</span><span data-v-19315b56>检查网络连接后重新尝试</span></div><div class="suggestion-item" data-v-19315b56><span class="suggestion-icon" data-v-19315b56>📝</span><span data-v-19315b56>确认起终点地址填写正确</span></div><div class="suggestion-item" data-v-19315b56><span class="suggestion-icon" data-v-19315b56>⏰</span><span data-v-19315b56>稍后再试，避开网络高峰期</span></div><div class="suggestion-item" data-v-19315b56><span class="suggestion-icon" data-v-19315b56>💬</span><span data-v-19315b56>如持续失败请联系客服</span></div></div>',1))]),c("div",eu,[c("button",{class:"modal-failure-btn",onClick:A[21]||(A[21]=(...i)=>e.handleFailureModalConfirm&&e.handleFailureModalConfirm(...i))},A[96]||(A[96]=[c("span",{class:"btn-icon"},"😔",-1),c("span",{class:"btn-text"},a(57),-1)]))])])])):Gt("",!0),c("div",{class:nA(["floating-scroll-buttons",{show:e.showFloatingButtons}])},[c(a(20),{class:"scroll-btn scroll-to-top",onClick:A[24]||(A[24]=(...i)=>e.scrollToTop&&e.scrollToTop(...i)),title:"滚动到顶部"},A[98]||(A[98]=[c("span",{class:"scroll-btn-icon"},"⬆️",-1)])),c("button",{class:"scroll-btn scroll-to-bottom",onClick:A[25]||(A[25]=(...i)=>e.scrollToBottom&&e.scrollToBottom(...i)),title:"滚动到底部"},A[99]||(A[99]=[c("span",{class:"scroll-btn-icon"},"⬇️",-1)]))],2)],64)}const iu=ia(Pc,[["render",tu],["__scopeId","data-v-19315b56"]]);export{iu as default};
