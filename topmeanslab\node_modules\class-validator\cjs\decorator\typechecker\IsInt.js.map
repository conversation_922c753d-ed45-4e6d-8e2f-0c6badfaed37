{"version": 3, "file": "IsInt.js", "sourceRoot": "", "sources": ["../../../../src/decorator/typechecker/IsInt.ts"], "names": [], "mappings": ";;;AACA,qDAAgE;AAEnD,QAAA,MAAM,GAAG,OAAO,CAAC;AAE9B;;GAEG;AACH,SAAgB,KAAK,CAAC,GAAY;IAChC,OAAO,OAAO,GAAG,KAAK,QAAQ,IAAI,MAAM,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC;AAC1D,CAAC;AAFD,sBAEC;AAED;;GAEG;AACH,SAAgB,KAAK,CAAC,iBAAqC;IACzD,OAAO,IAAA,uBAAU,EACf;QACE,IAAI,EAAE,cAAM;QACZ,SAAS,EAAE;YACT,QAAQ,EAAE,CAAC,KAAK,EAAE,IAAI,EAAW,EAAE,CAAC,KAAK,CAAC,KAAK,CAAC;YAChD,cAAc,EAAE,IAAA,yBAAY,EAC1B,UAAU,CAAC,EAAE,CAAC,UAAU,GAAG,qCAAqC,EAChE,iBAAiB,CAClB;SACF;KACF,EACD,iBAAiB,CAClB,CAAC;AACJ,CAAC;AAdD,sBAcC", "sourcesContent": ["import { ValidationOptions } from '../ValidationOptions';\nimport { buildMessage, ValidateBy } from '../common/ValidateBy';\n\nexport const IS_INT = 'isInt';\n\n/**\n * Checks if value is an integer.\n */\nexport function isInt(val: unknown): val is Number {\n  return typeof val === 'number' && Number.isInteger(val);\n}\n\n/**\n * Checks if value is an integer.\n */\nexport function IsInt(validationOptions?: ValidationOptions): PropertyDecorator {\n  return ValidateBy(\n    {\n      name: IS_INT,\n      validator: {\n        validate: (value, args): boolean => isInt(value),\n        defaultMessage: buildMessage(\n          eachPrefix => eachPrefix + '$property must be an integer number',\n          validationOptions\n        ),\n      },\n    },\n    validationOptions\n  );\n}\n"]}