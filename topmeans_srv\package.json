{"name": "topmeans_srv", "version": "1.0.0", "description": "A high-concurrency backend service for handling commercial API calls securely.", "main": "src/server.js", "scripts": {"start": "node src/server.js", "dev": "node src/server.js"}, "dependencies": {"axios": "^1.4.0", "bcryptjs": "^3.0.2", "cheerio": "^1.1.0", "cors": "^2.8.5", "dotenv": "^16.6.1", "express": "^4.18.2", "express-rate-limit": "^6.7.0", "jsonwebtoken": "^9.0.2", "multer": "^1.4.5-lts.2", "mysql2": "^3.14.1", "node-fetch": "^2.7.0", "puppeteer": "^24.8.2", "puppeteer-extra": "^3.3.6", "puppeteer-extra-plugin-stealth": "^2.11.2", "random-useragent": "^0.5.0", "winston": "^3.17.0", "winston-daily-rotate-file": "^5.0.0"}, "devDependencies": {"nodemon": "^2.0.22"}, "author": "Your Name", "license": "MIT"}