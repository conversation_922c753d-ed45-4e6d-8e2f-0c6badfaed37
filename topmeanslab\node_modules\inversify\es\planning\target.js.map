{"version": 3, "file": "target.js", "sourceRoot": "", "sources": ["../../src/planning/target.ts"], "names": [], "mappings": "AAAA,OAAO,KAAK,YAAY,MAAM,4BAA4B,CAAC;AAE3D,OAAO,EAAE,EAAE,EAAE,MAAM,aAAa,CAAC;AACjC,OAAO,EAAE,oBAAoB,EAAE,MAAM,wBAAwB,CAAC;AAC9D,OAAO,EAAE,QAAQ,EAAE,MAAM,YAAY,CAAC;AACtC,OAAO,EAAE,eAAe,EAAE,MAAM,oBAAoB,CAAC;AAErD;IAUE,gBACE,IAA2B,EAC3B,UAA2B,EAC3B,iBAA+C,EAC/C,aAAmC;QAGnC,IAAI,CAAC,EAAE,GAAG,EAAE,EAAE,CAAC;QACf,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;QACjB,IAAI,CAAC,iBAAiB,GAAG,iBAAiB,CAAC;QAC3C,IAAM,aAAa,GAAG,OAAO,UAAU,KAAK,QAAQ,CAAC,CAAC,CAAC,oBAAoB,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC;QACrG,IAAI,CAAC,IAAI,GAAG,IAAI,eAAe,CAAC,aAAa,IAAI,EAAE,CAAC,CAAC;QACrD,IAAI,CAAC,UAAU,GAAG,UAAU,CAAC;QAC7B,IAAI,CAAC,QAAQ,GAAG,IAAI,KAAK,EAAE,CAAC;QAE5B,IAAI,YAAY,GAA+B,IAAI,CAAC;QAGpD,IAAI,OAAO,aAAa,KAAK,QAAQ,EAAE;YACrC,YAAY,GAAG,IAAI,QAAQ,CAAC,YAAY,CAAC,SAAS,EAAE,aAAa,CAAC,CAAC;SACpE;aAAM,IAAI,aAAa,YAAY,QAAQ,EAAE;YAE5C,YAAY,GAAG,aAAa,CAAC;SAC9B;QAGD,IAAI,YAAY,KAAK,IAAI,EAAE;YACzB,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;SAClC;IAEH,CAAC;IAEM,uBAAM,GAAb,UAAc,GAAW;QACvB,KAAgB,UAAa,EAAb,KAAA,IAAI,CAAC,QAAQ,EAAb,cAAa,EAAb,IAAa,EAAE;YAA1B,IAAM,CAAC,SAAA;YACV,IAAI,CAAC,CAAC,GAAG,KAAK,GAAG,EAAE;gBACjB,OAAO,IAAI,CAAC;aACb;SACF;QACD,OAAO,KAAK,CAAC;IACf,CAAC;IAEM,wBAAO,GAAd;QACE,OAAO,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC,gBAAgB,CAAC,CAAC;IACpD,CAAC;IAEM,6BAAY,GAAnB,UAAoB,IAA2C;QAC7D,OAAO,IAAI,CAAC,UAAU,CAAC,YAAY,CAAC,gBAAgB,CAAC,CAAC,IAAI,CAAC,CAAC;IAC9D,CAAC;IAEM,wBAAO,GAAd;QACE,OAAO,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC,SAAS,CAAC,CAAC;IAC7C,CAAC;IAEM,yBAAQ,GAAf;QACE,OAAO,IAAI,CAAC,QAAQ,CAAC,IAAI,CACvB,UAAC,QAAQ,IAAK,OAAA,YAAY,CAAC,mBAAmB,CAAC,KAAK,CAAC,UAAC,GAAG,IAAK,OAAA,QAAQ,CAAC,GAAG,KAAK,GAAG,EAApB,CAAoB,CAAC,EAArE,CAAqE,CACpF,CAAC;IACJ,CAAC;IAEM,2BAAU,GAAjB;QACE,OAAO,IAAI,CAAC,UAAU,CAAC,YAAY,CAAC,YAAY,CAAC,CAAC,IAAI,CAAC,CAAC;IAC1D,CAAC;IAEM,4BAAW,GAAlB;QACE,IAAI,IAAI,CAAC,OAAO,EAAE,EAAE;YAClB,OAAO,IAAI,CAAC,QAAQ,CAAC,MAAM,CACzB,UAAC,CAAC,IAAK,OAAA,CAAC,CAAC,GAAG,KAAK,YAAY,CAAC,SAAS,EAAhC,CAAgC,CACxC,CAAC,CAAC,CAAgC,CAAC;SACrC;QACD,OAAO,IAAI,CAAC;IACd,CAAC;IAEM,8BAAa,GAApB;QACE,IAAI,IAAI,CAAC,QAAQ,EAAE,EAAE;YACnB,OAAO,IAAI,CAAC,QAAQ,CAAC,MAAM,CACzB,UAAC,QAAQ,IAAK,OAAA,YAAY,CAAC,mBAAmB,CAAC,KAAK,CAAC,UAAC,GAAG,IAAK,OAAA,QAAQ,CAAC,GAAG,KAAK,GAAG,EAApB,CAAoB,CAAC,EAArE,CAAqE,CACpF,CAAC;SACH;aAAM;YACL,OAAO,IAAI,CAAC;SACb;IACH,CAAC;IAEM,gCAAe,GAAtB,UAAuB,IAAY;QACjC,OAAO,IAAI,CAAC,UAAU,CAAC,YAAY,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,CAAC;IACvD,CAAC;IAEM,2BAAU,GAAjB,UAAkB,GAAW;QAA7B,iBASC;QARC,OAAO,UAAC,KAAc;YACpB,KAAgB,UAAa,EAAb,KAAA,KAAI,CAAC,QAAQ,EAAb,cAAa,EAAb,IAAa,EAAE;gBAA1B,IAAM,CAAC,SAAA;gBACV,IAAI,CAAC,CAAC,GAAG,KAAK,GAAG,IAAI,CAAC,CAAC,KAAK,KAAK,KAAK,EAAE;oBACtC,OAAO,IAAI,CAAC;iBACb;aACF;YACD,OAAO,KAAK,CAAC;QACf,CAAC,CAAC;IACJ,CAAC;IAEH,aAAC;AAAD,CAAC,AA3GD,IA2GC;AAED,OAAO,EAAE,MAAM,EAAE,CAAC"}