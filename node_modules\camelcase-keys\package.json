{"name": "camelcase-keys", "version": "7.0.2", "description": "Convert object keys to camel case", "license": "MIT", "repository": "sindresorhus/camelcase-keys", "funding": "https://github.com/sponsors/sindresorhus", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com"}, "engines": {"node": ">=12"}, "scripts": {"test": "xo && ava && tsd", "bench": "matcha bench/bench.js"}, "files": ["index.js", "index.d.ts"], "keywords": ["map", "obj", "object", "key", "keys", "value", "values", "val", "iterate", "camelcase", "camel-case", "camel", "case", "dash", "hyphen", "dot", "underscore", "separator", "string", "text", "convert", "pascalcase", "pascal-case", "deep", "recurse", "recursive"], "dependencies": {"camelcase": "^6.3.0", "map-obj": "^4.1.0", "quick-lru": "^5.1.1", "type-fest": "^1.2.1"}, "devDependencies": {"ava": "^2.4.0", "matcha": "^0.7.0", "tsd": "^0.17.0", "typescript": "^4.3.5", "xo": "^0.36.1"}, "xo": {"overrides": [{"files": "bench/bench.js", "rules": {"import/no-unresolved": "off"}}]}}