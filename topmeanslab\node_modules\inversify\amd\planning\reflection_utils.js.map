{"version": 3, "file": "reflection_utils.js", "sourceRoot": "", "sources": ["../../src/planning/reflection_utils.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAyPuD,gGApP9C,+BAAe,OAoP8C;IAhPtE,SAAS,eAAe,CACtB,cAAyC,EAAE,IAAqB;QAEhE,IAAM,eAAe,GAAG,IAAA,+BAAe,EAAC,IAAI,CAAC,CAAC;QAC9C,OAAO,UAAU,CAAC,cAAc,EAAE,eAAe,EAAE,IAAI,EAAE,KAAK,CAAC,CAAC;IAClE,CAAC;IA2OQ,0CAAe;IAzOxB,SAAS,UAAU,CACjB,cAAyC,EACzC,eAAuB,EACvB,IAAqB,EACrB,WAAoB;QAGpB,IAAM,QAAQ,GAAG,cAAc,CAAC,sBAAsB,CAAC,IAAI,CAAC,CAAC;QAG7D,IAAM,kBAAkB,GAAG,QAAQ,CAAC,yBAAyB,CAAC;QAG9D,IAAI,kBAAkB,KAAK,SAAS,EAAE;YACpC,IAAM,GAAG,GAAM,UAAU,CAAC,6BAA6B,SAAI,eAAe,MAAG,CAAC;YAC9E,MAAM,IAAI,KAAK,CAAC,GAAG,CAAC,CAAC;SACtB;QAGD,IAAM,uBAAuB,GAAG,QAAQ,CAAC,qBAAqB,CAAC;QAE/D,IAAM,IAAI,GAAG,MAAM,CAAC,IAAI,CAAC,uBAAuB,CAAC,CAAC;QAClD,IAAM,gCAAgC,GAAG,CAAC,IAAI,CAAC,MAAM,KAAK,CAAC,IAAI,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;QAChF,IAAM,qBAAqB,GAAG,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC;QAExD,IAAM,UAAU,GAAG,CAAC,gCAAgC,IAAI,qBAAqB,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC;QAG3G,IAAM,kBAAkB,GAAG,2BAA2B,CACpD,WAAW,EACX,eAAe,EACf,kBAAkB,EAClB,uBAAuB,EACvB,UAAU,CACX,CAAC;QAGF,IAAM,eAAe,GAAG,sBAAsB,CAAC,cAAc,EAAE,IAAI,EAAE,eAAe,CAAC,CAAC;QAEtF,IAAM,OAAO,mCACR,kBAAkB,SAClB,eAAe,OACnB,CAAC;QAEF,OAAO,OAAO,CAAC;IAEjB,CAAC;IACD,SAAS,0BAA0B,CACjC,KAAa,EACb,WAAoB,EACpB,eAAuB,EACvB,kBAAkD,EAClD,uBAA+C;QAG/C,IAAM,cAAc,GAAG,uBAAuB,CAAC,KAAK,CAAC,QAAQ,EAAE,CAAC,IAAI,EAAE,CAAC;QACvE,IAAM,QAAQ,GAAG,oBAAoB,CAAC,cAAc,CAAC,CAAC;QACtD,IAAM,SAAS,GAAG,QAAQ,CAAC,SAAS,KAAK,IAAI,CAAC;QAI9C,IAAI,iBAAiB,GAAG,kBAAkB,CAAC,KAAK,CAAC,CAAC;QAClD,IAAM,gBAAgB,GAAG,CAAC,QAAQ,CAAC,MAAM,IAAI,QAAQ,CAAC,WAAW,CAAC,CAAC;QACnE,iBAAiB,GAAG,CAAC,gBAAgB,CAAC,CAAC,CAAC,CAAC,CAAC,gBAAgB,CAAC,CAAC,CAAC,CAAC,iBAAiB,CAAC;QAGhF,IAAI,iBAAiB,YAAY,8CAAoB,EAAE;YACrD,iBAAiB,GAAG,iBAAiB,CAAC,MAAM,EAAE,CAAC;SAChD;QAID,IAAI,SAAS,EAAE;YAEb,IAAM,QAAQ,GAAG,iBAAiB,KAAK,MAAM,CAAC;YAC9C,IAAM,UAAU,GAAG,iBAAiB,KAAK,QAAQ,CAAC;YAClD,IAAM,WAAW,GAAG,iBAAiB,KAAK,SAAS,CAAC;YACpD,IAAM,aAAa,GAAG,CAAC,QAAQ,IAAI,UAAU,IAAI,WAAW,CAAC,CAAC;YAE9D,IAAI,CAAC,WAAW,IAAI,aAAa,EAAE;gBACjC,IAAM,GAAG,GAAM,UAAU,CAAC,yBAAyB,kBAAa,KAAK,kBAAa,eAAe,MAAG,CAAC;gBACrG,MAAM,IAAI,KAAK,CAAC,GAAG,CAAC,CAAC;aACtB;YAED,IAAM,MAAM,GAAG,IAAI,eAAM,CAAC,8BAAc,CAAC,mBAAmB,EAAE,QAAQ,CAAC,UAAU,EAAE,iBAAiD,CAAC,CAAC;YACtI,MAAM,CAAC,QAAQ,GAAG,cAAc,CAAC;YACjC,OAAO,MAAM,CAAC;SACf;QAED,OAAO,IAAI,CAAC;IAEd,CAAC;IAED,SAAS,2BAA2B,CAClC,WAAoB,EACpB,eAAuB,EACvB,kBAAkD,EAClD,uBAA+C,EAC/C,UAAkB;QAGlB,IAAM,OAAO,GAAwB,EAAE,CAAC;QACxC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,UAAU,EAAE,CAAC,EAAE,EAAE;YACnC,IAAM,KAAK,GAAG,CAAC,CAAC;YAChB,IAAM,MAAM,GAAG,0BAA0B,CACvC,KAAK,EACL,WAAW,EACX,eAAe,EACf,kBAAkB,EAClB,uBAAuB,CACxB,CAAC;YACF,IAAI,MAAM,KAAK,IAAI,EAAE;gBACnB,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;aACtB;SACF;QAED,OAAO,OAAO,CAAC;IACjB,CAAC;IAED,SAAS,gCAAgC,CACvC,MAAW,EACX,WAAgB,EAChB,YAA6B,EAC7B,SAAiB;QAEjB,IAAM,iBAAiB,GAAG,CAAC,MAAM,IAAI,WAAW,CAAC,CAAC;QAClD,IAAI,iBAAiB,KAAK,SAAS,EAAE;YACnC,IAAM,GAAG,GAAM,UAAU,CAAC,6BAA6B,sBAAiB,MAAM,CAAC,YAAY,CAAC,kBAAa,SAAS,MAAG,CAAC;YACtH,MAAM,IAAI,KAAK,CAAC,GAAG,CAAC,CAAC;SACtB;QACD,OAAO,iBAAiB,CAAC;IAC3B,CAAC;IAED,SAAS,sBAAsB,CAC7B,cAAyC,EACzC,eAAgC,EAChC,eAAuB;QAGvB,IAAM,kBAAkB,GAAG,cAAc,CAAC,qBAAqB,CAAC,eAAe,CAAC,CAAC;QACjF,IAAI,OAAO,GAAwB,EAAE,CAAC;QACtC,IAAM,UAAU,GAAG,MAAM,CAAC,qBAAqB,CAAC,kBAAkB,CAAC,CAAC;QACpE,IAAM,UAAU,GAAwB,MAAM,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAC;QACxE,IAAM,IAAI,GAAwB,UAAU,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC;QAEhE,KAAkB,UAAI,EAAJ,aAAI,EAAJ,kBAAI,EAAJ,IAAI,EAAE;YAAnB,IAAM,GAAG,aAAA;YAGZ,IAAM,cAAc,GAAG,kBAAkB,CAAC,GAAG,CAA0B,CAAC;YAGxE,IAAM,QAAQ,GAAG,oBAAoB,CAAC,cAAc,CAAC,CAAC;YAEtD,IAAM,UAAU,GAAG,QAAQ,CAAC,UAAU,IAAI,GAAG,CAAC;YAG9C,IAAM,iBAAiB,GAAG,gCAAgC,CAAC,QAAQ,CAAC,MAAM,EAAE,QAAQ,CAAC,WAAW,EAAE,GAAG,EAAE,eAAe,CAAC,CAAC;YAGxH,IAAM,MAAM,GAAG,IAAI,eAAM,CAAC,8BAAc,CAAC,aAAa,EAAE,UAAU,EAAE,iBAAiB,CAAC,CAAC;YACvF,MAAM,CAAC,QAAQ,GAAG,cAAc,CAAC;YACjC,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;SACtB;QAGD,IAAM,eAAe,GAAG,MAAM,CAAC,cAAc,CAAC,eAAe,CAAC,SAAS,CAAC,CAAC,WAAW,CAAC;QAErF,IAAI,eAAe,KAAK,MAAM,EAAE;YAE9B,IAAM,WAAW,GAAG,sBAAsB,CAAC,cAAc,EAAE,eAAe,EAAE,eAAe,CAAC,CAAC;YAE7F,OAAO,mCACF,OAAO,SACP,WAAW,OACf,CAAC;SAEH;QAED,OAAO,OAAO,CAAC;IACjB,CAAC;IAED,SAAS,2BAA2B,CAClC,cAAyC,EACzC,IAAqB;QAGrB,IAAM,eAAe,GAAG,MAAM,CAAC,cAAc,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,WAAW,CAAC;QAE1E,IAAI,eAAe,KAAK,MAAM,EAAE;YAG9B,IAAM,mBAAmB,GAAG,IAAA,+BAAe,EAAC,eAAe,CAAC,CAAC;YAE7D,IAAM,OAAO,GAAG,UAAU,CAAC,cAAc,EAAE,mBAAmB,EAAE,eAAe,EAAE,IAAI,CAAC,CAAC;YAGvF,IAAM,QAAQ,GAAG,OAAO,CAAC,GAAG,CAAC,UAAC,CAAC,IAAK,OAAA,CAAC,CAAC,QAAQ,CAAC,MAAM,CAAC,UAAA,CAAC,IAAI,OAAA,CAAC,CAAC,GAAG,KAAK,YAAY,CAAC,aAAa,EAApC,CAAoC,CAAC,EAA5D,CAA4D,CAAC,CAAC;YAIlG,IAAM,cAAc,GAAI,EAAiB,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE,EAAE,QAAQ,CAAC,CAAC,MAAM,CAAC;YAC5E,IAAM,eAAe,GAAG,OAAO,CAAC,MAAM,GAAG,cAAc,CAAC;YAExD,IAAI,eAAe,GAAG,CAAC,EAAE;gBACvB,OAAO,eAAe,CAAC;aACxB;iBAAM;gBACL,OAAO,2BAA2B,CAAC,cAAc,EAAE,eAAe,CAAC,CAAC;aACrE;SAEF;aAAM;YACL,OAAO,CAAC,CAAC;SACV;IAEH,CAAC;IAoByB,kEAA2B;IAlBrD,SAAS,oBAAoB,CAAC,cAAqC;QAGjE,IAAM,iBAAiB,GAAQ,EAAE,CAAC;QAClC,cAAc,CAAC,OAAO,CAAC,UAAC,CAAsB;YAC5C,iBAAiB,CAAC,CAAC,CAAC,GAAG,CAAC,QAAQ,EAAE,CAAC,GAAG,CAAC,CAAC,KAAK,CAAC;QAChD,CAAC,CAAC,CAAC;QAGH,OAAO;YACL,MAAM,EAAE,iBAAiB,CAAC,YAAY,CAAC,UAAU,CAAC;YAClD,WAAW,EAAE,iBAAiB,CAAC,YAAY,CAAC,gBAAgB,CAAC;YAC7D,UAAU,EAAE,iBAAiB,CAAC,YAAY,CAAC,QAAQ,CAAC;YACpD,SAAS,EAAE,iBAAiB,CAAC,YAAY,CAAC,aAAa,CAAC;SACzD,CAAC;IAEJ,CAAC"}