import{_ as $,p as m,v as E,c as y,o as k,ab as T,j as a,e as _,n as L,ao as i,aJ as c,G as U,a as P,aD as M,t as j}from"./framework.B19ydMwb.js";import{V as q}from"./Valicode.C71W2eNO.js";import{_ as S,u as B,E as n}from"./theme.CmWpOUCL.js";const D={class:"login-page"},G={class:"login-container"},O={class:"auth-section"},F={class:"auth-container"},J={class:"auth-tabs"},R={key:0,class:"auth-form"},z={class:"form-group"},H={class:"form-group"},K={class:"form-group"},Q={class:"captcha-container"},W={class:"captcha-wrapper"},X={class:"form-options"},Y={class:"remember-me"},Z=["disabled"],ee={key:0,class:"loading-spinner"},ae={key:1,class:"auth-form"},se={class:"form-group"},te={class:"form-group"},oe={class:"form-group"},re={class:"form-group"},le={class:"captcha-container"},ne={class:"captcha-wrapper"},de=["disabled"],ie={key:0,class:"loading-spinner"},ue={__name:"LoginPage",setup(ce){const V="http://localhost:3999/api";S.setLevel("info");const u=m("login"),v=B(),d=m(!1),x=r=>{C.value=r},t=m({account:"",password:"",valicode:"",remember:!1}),o=m({account:"",password:"",confirmPassword:"",valicode:"",nickname:"",avatar:"/images/default-avatar.jpg",signature:"这个人很懒，什么都没写~"}),l=m(null),C=m(""),A=async()=>{var s,b,g,h,w;if(!t.value.account||!t.value.password){n.error({message:"请输入账号和密码",duration:1e3}),(s=l.value)==null||s.refresh();return}if(t.value.valicode.toLowerCase()!==C.value.toLowerCase()){n.error({message:"验证码错误，请重新输入",duration:1e3}),(b=l.value)==null||b.refresh();return}d.value=!0;let r=0;const e=2;try{const p=await fetch(`${V}/user/login`,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({account:t.value.account,password:t.value.password}),signal:AbortSignal.timeout(8e3)});if(!p.ok)throw new Error(`HTTP ${p.status}`);const f=await p.json();f.success?(t.value.remember?v.setStoredPassword(t.value.password):v.clearStoredPassword(),v.setToken(f.token),v.setUserInfo(f.user),t.value.remember?localStorage.setItem("rememberedAccount",t.value.account):localStorage.removeItem("rememberedAccount"),n.success({message:"登录成功，正在跳转...",duration:500,onClose:()=>{window.location.reload()}})):(n.error({message:f.message||"登录失败",duration:1e3}),(g=l.value)==null||g.refresh())}catch(p){r++,S.error(`登录尝试 ${r} 失败:`,p),r>e?(n.error({message:p.message.includes("timeout")?"请求超时，请检查网络":"服务不稳定，请稍后重试",duration:1e3}),(h=l.value)==null||h.refresh()):(await new Promise(f=>setTimeout(f,1e3)),(w=l.value)==null||w.refresh())}finally{d.value=!1}},I=async()=>{var r,e,s,b,g;if(!o.value.account||!o.value.password){n.error("请输入账号和密码"),(r=l.value)==null||r.refresh();return}if(o.value.password!==o.value.confirmPassword){n.error("两次输入的密码不一致，请重新输入!"),(e=l.value)==null||e.refresh();return}if(o.value.valicode.toLowerCase()!==C.value.toLowerCase()){n.error({message:"验证码错误，请重新输入",duration:1e3}),(s=l.value)==null||s.refresh();return}d.value=!0;try{const w=await(await fetch(`${V}/user/register`,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(o.value)})).json();w.success?(n.success("注册成功，请登录"),u.value="login",t.value.account=o.value.account,o.value={account:"",password:"",confirmPassword:"",valicode:"",nickname:"",avatar:"/images/default-avatar.jpg",signature:"这个人很懒，什么都没写~"}):(n.error(w.message||"注册失败"),(b=l.value)==null||b.refresh())}catch(h){S.error("注册错误:",h),n.error("网络错误，请稍后重试"),(g=l.value)==null||g.refresh()}finally{d.value=!1}},N=r=>{r.preventDefault(),n.info("忘记密码功能暂未开放，请联系管理员")};return E(()=>{if(v.storedPassword&&localStorage.getItem("rememberedAccount")){const r=localStorage.getItem("rememberedAccount");t.value.account=r,t.value.password=v.storedPassword,t.value.remember=!0}}),(r,e)=>(k(),y("div",D,[e[21]||(e[21]=T('<div class="background-decoration" data-v-a2f5cbec><div class="floating-shapes" data-v-a2f5cbec><div class="shape shape-1" data-v-a2f5cbec></div><div class="shape shape-2" data-v-a2f5cbec></div><div class="shape shape-3" data-v-a2f5cbec></div><div class="shape shape-4" data-v-a2f5cbec></div><div class="shape shape-5" data-v-a2f5cbec></div></div></div>',1)),a("div",G,[e[20]||(e[20]=T('<div class="brand-section" data-v-a2f5cbec><div class="brand-content" data-v-a2f5cbec><h1 class="brand-title" data-v-a2f5cbec>旅美</h1><p class="brand-subtitle" data-v-a2f5cbec>一段旅途，一段人生，智慧出行，美好常伴</p><div class="brand-features" data-v-a2f5cbec><div class="feature-item" data-v-a2f5cbec><div class="feature-icon" data-v-a2f5cbec>🗺️</div><span data-v-a2f5cbec>智能路线规划</span></div><div class="feature-item" data-v-a2f5cbec><div class="feature-icon" data-v-a2f5cbec>🏨</div><span data-v-a2f5cbec>酒店推荐</span></div><div class="feature-item" data-v-a2f5cbec><div class="feature-icon" data-v-a2f5cbec>🍽️</div><span data-v-a2f5cbec>美食发现</span></div><div class="feature-item" data-v-a2f5cbec><div class="feature-icon" data-v-a2f5cbec>📸</div><span data-v-a2f5cbec>景点攻略</span></div></div></div></div>',1)),a("div",O,[a("div",F,[e[19]||(e[19]=a("div",{class:"auth-header"},[a("h2",null,"欢迎使用 TopMeansLab"),a("p",null,"请登录您的账户开始旅程规划")],-1)),a("div",J,[a("button",{class:L(["tab-btn",{active:u.value==="login"}]),onClick:e[0]||(e[0]=s=>u.value="login")}," 登录 ",2),a("button",{class:L(["tab-btn",{active:u.value==="register"}]),onClick:e[1]||(e[1]=s=>u.value="register")}," 注册 ",2)]),u.value==="login"?(k(),y("div",R,[a("div",z,[e[10]||(e[10]=a("label",null,"账号",-1)),i(a("input",{"onUpdate:modelValue":e[2]||(e[2]=s=>t.value.account=s),type:"text",placeholder:"请输入手机号或邮箱",class:"form-input",required:""},null,512),[[c,t.value.account]])]),a("div",H,[e[11]||(e[11]=a("label",null,"密码",-1)),i(a("input",{"onUpdate:modelValue":e[3]||(e[3]=s=>t.value.password=s),type:"password",placeholder:"请输入密码",class:"form-input",required:""},null,512),[[c,t.value.password]])]),a("div",K,[e[12]||(e[12]=a("label",null,"验证码",-1)),a("div",Q,[i(a("input",{"onUpdate:modelValue":e[4]||(e[4]=s=>t.value.valicode=s),type:"text",placeholder:"请输入验证码",class:"form-input captcha-input",required:""},null,512),[[c,t.value.valicode]]),a("div",W,[U(q,{ref_key:"valicode",ref:l,onGetCode:x},null,512)])])]),a("div",X,[a("label",Y,[i(a("input",{type:"checkbox","onUpdate:modelValue":e[5]||(e[5]=s=>t.value.remember=s)},null,512),[[M,t.value.remember]]),e[13]||(e[13]=a("span",{class:"checkmark"},null,-1)),e[14]||(e[14]=P(" 记住密码 "))]),a("a",{href:"#",class:"forgot-password",onClick:N},"忘记密码？")]),a("button",{onClick:A,class:"submit-btn",disabled:d.value},[d.value?(k(),y("span",ee)):_("",!0),P(" "+j(d.value?"登录中...":"登录"),1)],8,Z)])):_("",!0),u.value==="register"?(k(),y("div",ae,[a("div",se,[e[15]||(e[15]=a("label",null,"账号",-1)),i(a("input",{"onUpdate:modelValue":e[6]||(e[6]=s=>o.value.account=s),type:"text",placeholder:"请输入你的账号名称",class:"form-input",required:""},null,512),[[c,o.value.account]])]),a("div",te,[e[16]||(e[16]=a("label",null,"密码",-1)),i(a("input",{"onUpdate:modelValue":e[7]||(e[7]=s=>o.value.password=s),type:"password",placeholder:"请输入密码，由6位以上的英文和数字组成",class:"form-input",required:""},null,512),[[c,o.value.password]])]),a("div",oe,[e[17]||(e[17]=a("label",null,"确认密码",-1)),i(a("input",{"onUpdate:modelValue":e[8]||(e[8]=s=>o.value.confirmPassword=s),type:"password",placeholder:"请再次输入密码，确保两次密码一致",class:"form-input",required:""},null,512),[[c,o.value.confirmPassword]])]),a("div",re,[e[18]||(e[18]=a("label",null,"验证码",-1)),a("div",le,[i(a("input",{"onUpdate:modelValue":e[9]||(e[9]=s=>o.value.valicode=s),type:"text",placeholder:"请输入验证码",class:"form-input captcha-input",required:""},null,512),[[c,o.value.valicode]]),a("div",ne,[U(q,{ref_key:"valicode",ref:l,onGetCode:x},null,512)])])]),a("button",{onClick:I,class:"submit-btn",disabled:d.value},[d.value?(k(),y("span",ie)):_("",!0),P(" "+j(d.value?"注册中...":"注册"),1)],8,de)])):_("",!0)])])])]))}},me=$(ue,[["__scopeId","data-v-a2f5cbec"]]);export{me as default};
