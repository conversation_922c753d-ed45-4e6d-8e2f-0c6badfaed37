import { ValidationOptions } from '../ValidationOptions';
export declare const IS_RGB_COLOR = "isRgbColor";
/**
 * Check if the string is a rgb or rgba color.
 * `includePercentValues` defaults to true. If you don't want to allow to set rgb or rgba values with percents, like rgb(5%,5%,5%), or rgba(90%,90%,90%,.3), then set it to false.
 * If given value is not a string, then it returns false.
 */
export declare function isRgbColor(value: unknown, includePercentValues?: boolean): boolean;
/**
 * Check if the string is a rgb or rgba color.
 * `includePercentValues` defaults to true. If you don't want to allow to set rgb or rgba values with percents, like rgb(5%,5%,5%), or rgba(90%,90%,90%,.3), then set it to false.
 * If given value is not a string, then it returns false.
 */
export declare function IsRgbColor(includePercentValues?: boolean, validationOptions?: ValidationOptions): PropertyDecorator;
