# 完整支付功能测试页面

<script setup>
import { ref, onMounted } from 'vue'
import PaymentMethods from './.vitepress/theme/components/Payment/PaymentMethods.vue'
import { useUserStore } from './.vitepress/theme/components/UserCenter/userStore'

const userStore = useUserStore()
const amount = ref(0.01)
const orderInfo = ref({
  subject: '完整测试支付订单',
  serviceType: 'complete_test_service'
})

const testResults = ref([])
const isLoggedIn = ref(false)

const addTestResult = (test, result, details = '') => {
  testResults.value.push({
    test,
    result,
    details,
    timestamp: new Date().toLocaleTimeString()
  })
}

const handlePaymentSuccess = (paymentData) => {
  console.log('支付成功:', paymentData)
  addTestResult('支付成功回调', '✅ 通过', `订单号: ${paymentData.orderId}`)
}

const handlePaymentCancel = () => {
  console.log('支付取消')
  addTestResult('支付取消回调', '✅ 通过', '用户主动取消支付')
}

const testUserAuth = async () => {
  try {
    const response = await fetch(`${import.meta.env.VITE_BACKEND_SRV_URL}/api/user/info`, {
      headers: {
        'Authorization': `Bearer ${userStore.token}`,
        'Content-Type': 'application/json'
      }
    })
    
    if (response.ok) {
      const result = await response.json()
      addTestResult('用户认证测试', '✅ 通过', `用户ID: ${result.user.id}`)
      return true
    } else {
      addTestResult('用户认证测试', '❌ 失败', `状态码: ${response.status}`)
      return false
    }
  } catch (error) {
    addTestResult('用户认证测试', '❌ 失败', error.message)
    return false
  }
}

const testPaymentAPI = async () => {
  try {
    const response = await fetch(`${import.meta.env.VITE_BACKEND_SRV_URL}/api/payment/create`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${userStore.token}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        amount: 0.01,
        subject: 'API测试订单',
        serviceType: 'api_test'
      })
    })
    
    const result = await response.json()
    
    if (response.status === 401) {
      addTestResult('支付API测试', '❌ 失败', '401 认证失败')
    } else if (response.ok && result.success) {
      addTestResult('支付API测试', '✅ 通过', `订单创建成功: ${result.data.outTradeNo}`)
    } else {
      addTestResult('支付API测试', '⚠️ 部分通过', `认证成功但支付宝配置问题: ${result.message}`)
    }
  } catch (error) {
    addTestResult('支付API测试', '❌ 失败', error.message)
  }
}

const runAllTests = async () => {
  testResults.value = []
  addTestResult('开始测试', '🚀 启动', '开始完整支付功能测试')
  
  // 测试用户认证
  const authOk = await testUserAuth()
  
  if (authOk) {
    // 测试支付API
    await testPaymentAPI()
  }
  
  addTestResult('测试完成', '🎉 结束', '所有测试项目已完成')
}

onMounted(() => {
  isLoggedIn.value = userStore.isLoggedIn
  if (isLoggedIn.value) {
    runAllTests()
  }
})
</script>

## 完整支付功能测试

这是一个完整的支付功能测试页面，用于验证从认证到支付的整个流程。

### 用户状态

<div v-if="!isLoggedIn" style="background: #fff2f0; border: 1px solid #ffccc7; padding: 16px; border-radius: 6px; margin: 16px 0;">
  <h4 style="color: #cf1322; margin: 0 0 8px 0;">⚠️ 用户未登录</h4>
  <p style="margin: 0;">请先登录后再进行支付测试。<a href="/">点击这里登录</a></p>
</div>

<div v-else style="background: #f6ffed; border: 1px solid #b7eb8f; padding: 16px; border-radius: 6px; margin: 16px 0;">
  <h4 style="color: #389e0d; margin: 0 0 8px 0;">✅ 用户已登录</h4>
  <p style="margin: 0;">当前用户: {{ userStore.userInfo.nickname }} ({{ userStore.userInfo.account }})</p>
</div>

### 自动化测试结果

<div style="background: #fafafa; border: 1px solid #d9d9d9; border-radius: 6px; padding: 16px; margin: 16px 0;">
  <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 16px;">
    <h4 style="margin: 0;">测试结果</h4>
    <button @click="runAllTests" style="padding: 4px 12px; background: #1677ff; color: white; border: none; border-radius: 4px; cursor: pointer;">
      重新测试
    </button>
  </div>
  
  <div v-if="testResults.length === 0" style="color: #666; font-style: italic;">
    暂无测试结果，请点击"重新测试"按钮开始测试
  </div>
  
  <div v-for="(result, index) in testResults" :key="index" style="margin-bottom: 8px; padding: 8px; background: white; border-radius: 4px; border-left: 3px solid #1677ff;">
    <div style="display: flex; justify-content: space-between; align-items: center;">
      <span><strong>{{ result.test }}</strong>: {{ result.result }}</span>
      <span style="color: #666; font-size: 12px;">{{ result.timestamp }}</span>
    </div>
    <div v-if="result.details" style="color: #666; font-size: 12px; margin-top: 4px;">
      {{ result.details }}
    </div>
  </div>
</div>

### 手动支付测试

<div v-if="isLoggedIn">
  <PaymentMethods
    :amount="amount"
    :order-info="orderInfo"
    @payment-success="handlePaymentSuccess"
    @payment-cancel="handlePaymentCancel"
  />
</div>

### 测试说明

#### 自动化测试项目
1. **用户认证测试**: 验证 JWT token 是否有效
2. **支付API测试**: 验证支付订单创建接口
3. **错误处理测试**: 验证各种错误情况的处理

#### 手动测试项目
1. **支付界面**: 验证支付方式选择界面
2. **二维码显示**: 验证支付二维码生成和显示
3. **状态轮询**: 验证支付状态实时查询
4. **取消支付**: 验证取消支付功能

#### 预期结果
- ✅ **用户认证测试**: 应该返回用户信息
- ⚠️ **支付API测试**: 认证成功，但可能因支付宝配置返回错误
- ✅ **支付界面**: 应该正常显示支付选项
- ✅ **二维码显示**: 应该显示模拟二维码
- ✅ **取消支付**: 应该能正常取消

### 已知问题和解决方案

#### 1. 支付宝配置问题
- **现象**: "Business Failed" 错误
- **原因**: 支付宝密钥配置或沙箱环境问题
- **解决**: 需要配置正确的支付宝应用信息

#### 2. 认证问题（已修复）
- **现象**: 401 Unauthorized 错误
- **原因**: JWT token 字段不匹配
- **解决**: 统一使用 `userId` 字段

#### 3. 二维码生成问题（已修复）
- **现象**: qrcode 模块导入失败
- **原因**: VitePress 动态导入兼容性问题
- **解决**: 使用 SVG 模拟二维码

### 完整测试清单

- [ ] 用户登录功能正常
- [ ] JWT token 正确生成和验证
- [ ] 支付API认证通过
- [ ] 支付界面正常显示
- [ ] 二维码正常生成
- [ ] 支付状态轮询正常
- [ ] 取消支付功能正常
- [ ] 错误处理机制完善

### 生产环境部署清单

- [ ] 配置正确的支付宝应用信息
- [ ] 设置正确的回调地址
- [ ] 启用 HTTPS
- [ ] 配置生产环境域名
- [ ] 测试真实支付流程
