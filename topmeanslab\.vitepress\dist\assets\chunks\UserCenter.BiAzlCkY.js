import{_ as j,p as v,b3 as q,v as A,c as y,o as h,k as $,j as o,e as x,n as T,ao as d,aJ as c,G as U,a as N,aD as E}from"./framework.B19ydMwb.js";import{V as L}from"./Valicode.C71W2eNO.js";import{_ as C,u as B,E as n}from"./theme.CmWpOUCL.js";const F={class:"user-center"},G={key:0,class:"auth-container"},M={class:"auth-box"},O={class:"auth-tabs"},J={key:0,class:"auth-form"},R={class:"form-group"},D={class:"form-group"},z={class:"form-group"},H={class:"form-options"},K={class:"remember-me"},Q={key:1,class:"auth-form"},W={class:"form-group"},X={class:"form-group"},Y={class:"form-group"},Z={class:"form-group"},ee={key:1,class:"profile-container"},oe={__name:"UserCenter",setup(se){const P="http://localhost:3999/api";C.setLevel("info"),v(!1);const i=v("login");v(null);const u=B(),S=q(),V=t=>{k.value=t},s=v({account:"",password:"",valicode:"",remember:!1}),a=v({account:"",password:"",confirmPassword:"",valicode:"",nickname:"",avatar:"/images/default-avatar.jpg",signature:"这个人很懒，什么都没写~"}),l=v(null),k=v(""),_=async()=>{var r,f,g,b,w;if(!s.value.account||!s.value.password){n.error({message:"请输入账号和密码",duration:1e3}),(r=l.value)==null||r.refresh();return}if(s.value.valicode.toLowerCase()!==k.value.toLowerCase()){n.error({message:"验证码错误，请重新输入",duration:1e3}),(f=l.value)==null||f.refresh();return}let t=0;const e=2;try{const m=await fetch(`${P}/user/login`,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({account:s.value.account,password:s.value.password}),signal:AbortSignal.timeout(8e3)});if(!m.ok)throw new Error(`HTTP ${m.status}`);const p=await m.json();if(p.success)s.value.remember?u.setStoredPassword(s.value.password):u.clearStoredPassword(),u.setToken(p.token),u.setUserInfo(p.user),u.setStoredPassword(s.value.remember),s.value.remember?localStorage.setItem("rememberedAccount",s.value.account):localStorage.removeItem("rememberedAccount"),n.success({message:"登录成功，正在跳转...",duration:500,onClose:()=>{S.go("/user-center/profile")}});else{n.error({message:p.message||"登录失败",duration:1e3}),(g=l.value)==null||g.refresh();return}}catch(m){t++,C.error(`登录尝试 ${t} 失败:`,m),t>e?(n.error({message:m.message.includes("timeout")?"请求超时，请检查网络":"服务不稳定，请稍后重试",duration:1e3}),(b=l.value)==null||b.refresh()):(await new Promise(p=>setTimeout(p,1e3)),(w=l.value)==null||w.refresh())}},I=async()=>{var t,e,r,f,g;if(!a.value.account||!a.value.password){n.error("请输入账号和密码"),(t=l.value)==null||t.refresh();return}if(a.value.password!==a.value.confirmPassword){n.error("两次输入的密码不一致，请重新输入!"),(e=l.value)==null||e.refresh();return}if(s.value.valicode.toLowerCase()!==k.value.toLowerCase()){n.error({message:"验证码错误，请重新输入",duration:1e3}),(r=l.value)==null||r.refresh();return}try{const w=await(await fetch(`${P}/user/register`,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(a.value)})).json();w.success?(n.success("注册成功，请登录"),i.value="login",s.value.account=a.value.account,a.value={account:"",password:"",nickname:"",avatar:"/images/default-avatar.jpg",signature:"这个人很懒，什么都没写~"}):(n.error(w.message||"注册失败"),(f=l.value)==null||f.refresh())}catch(b){C.error("注册错误:",b),n.error("网络错误，请稍后重试"),(g=l.value)==null||g.refresh()}};return A(()=>{if(u.checkLoginStatus()&&S.go("/user-center/profile"),u.storedPassword&&localStorage.getItem("rememberedAccount")){const t=localStorage.getItem("rememberedAccount");s.value.account=t,s.value.password=u.storedPassword,s.value.remember=!0}}),(t,e)=>(h(),y("div",F,[$(u).isLoggedIn?(h(),y("div",ee)):(h(),y("div",G,[o("div",M,[o("div",O,[o("button",{class:T(["tab-btn",{active:i.value==="login"}]),onClick:e[0]||(e[0]=r=>i.value="login")}," 登录 ",2),o("button",{class:T(["tab-btn",{active:i.value==="register"}]),onClick:e[1]||(e[1]=r=>i.value="register")}," 注册 ",2)]),i.value==="login"?(h(),y("div",J,[o("div",R,[e[11]||(e[11]=o("label",null,"账号",-1)),d(o("input",{"onUpdate:modelValue":e[2]||(e[2]=r=>s.value.account=r),type:"text",placeholder:"请输入手机号或邮箱",required:""},null,512),[[c,s.value.account]])]),o("div",D,[e[12]||(e[12]=o("label",null,"密码",-1)),d(o("input",{"onUpdate:modelValue":e[3]||(e[3]=r=>s.value.password=r),type:"password",placeholder:"请输入密码",required:""},null,512),[[c,s.value.password]])]),o("div",z,[e[13]||(e[13]=o("label",null,"验证码",-1)),d(o("input",{"onUpdate:modelValue":e[4]||(e[4]=r=>s.value.valicode=r),type:"text",placeholder:"请输入验证码, 并确保大小写正确",required:""},null,512),[[c,s.value.valicode]]),U(L,{ref_key:"valicode",ref:l,onGetCode:V},null,512)]),o("div",H,[o("label",K,[d(o("input",{type:"checkbox","onUpdate:modelValue":e[5]||(e[5]=r=>s.value.remember=r)},null,512),[[E,s.value.remember]]),e[14]||(e[14]=N(" 记住密码 "))]),o("a",{href:"#",class:"forgot-password",onClick:e[6]||(e[6]=(...r)=>t.handleForgotPassword&&t.handleForgotPassword(...r))},"忘记密码？")]),o("button",{onClick:_,class:"submit-btn"},"登录")])):x("",!0),i.value==="register"?(h(),y("div",Q,[o("div",W,[e[15]||(e[15]=o("label",null,"账号",-1)),d(o("input",{"onUpdate:modelValue":e[7]||(e[7]=r=>a.value.account=r),type:"text",placeholder:"请输入你的账号名称",required:""},null,512),[[c,a.value.account]])]),o("div",X,[e[16]||(e[16]=o("label",null,"密码",-1)),d(o("input",{"onUpdate:modelValue":e[8]||(e[8]=r=>a.value.password=r),type:"password",placeholder:"请输入密码, 由6位以上的英文和数组组成",required:""},null,512),[[c,a.value.password]])]),o("div",Y,[e[17]||(e[17]=o("label",null,"确认密码",-1)),d(o("input",{"onUpdate:modelValue":e[9]||(e[9]=r=>a.value.confirmPassword=r),type:"password",placeholder:"请再次输入密码, 确保两次密码一致",required:""},null,512),[[c,a.value.confirmPassword]])]),o("div",Z,[e[18]||(e[18]=o("label",null,"验证码",-1)),d(o("input",{"onUpdate:modelValue":e[10]||(e[10]=r=>s.value.valicode=r),type:"text",placeholder:"请输入验证码, 并确保大小写正确",required:""},null,512),[[c,s.value.valicode]]),U(L,{ref_key:"valicode",ref:l,onGetCode:V},null,512)]),o("button",{onClick:I,class:"submit-btn"},"注册")])):x("",!0)])]))]))}},le=j(oe,[["__scopeId","data-v-e96da37d"]]);export{le as L};
