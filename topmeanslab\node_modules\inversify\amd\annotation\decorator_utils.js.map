{"version": 3, "file": "decorator_utils.js", "sourceRoot": "", "sources": ["../../src/annotation/decorator_utils.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;IAKA,SAAS,2BAA2B,CAAa,MAA0B;QACzE,OAAQ,MAAiC,CAAC,SAAS,KAAK,SAAS,CAAC;IACpE,CAAC;IAgBD,SAAS,uBAAuB,CAAC,aAA0C;QACzE,IAAI,aAAa,KAAK,SAAS,EAAE;YAC/B,MAAM,IAAI,KAAK,CAAC,UAAU,CAAC,2BAA2B,CAAC,CAAC;SACzD;IACH,CAAC;IAGD,SAAS,YAAY,CACnB,gBAAiC,EACjC,aAA0C,EAC1C,cAAsB,EACtB,QAA4C;QAE5C,uBAAuB,CAAC,aAAa,CAAC,CAAC;QACvC,uBAAuB,CAAC,YAAY,CAAC,MAAM,EAAE,gBAAuC,EAAE,cAAc,CAAC,QAAQ,EAAE,EAAE,QAAQ,CAAC,CAAC;IAC7H,CAAC;IA4GkB,oCAAY;IA1G/B,SAAS,WAAW,CAClB,gBAAiC,EACjC,YAA6B,EAC7B,QAA4C;QAE5C,IAAI,2BAA2B,CAAC,gBAAgB,CAAC,EAAE;YACjD,MAAM,IAAI,KAAK,CAAC,UAAU,CAAC,2BAA2B,CAAC,CAAC;SACzD;QACD,uBAAuB,CAAC,YAAY,CAAC,WAAW,EAAE,gBAAgB,CAAC,WAAW,EAAE,YAAY,EAAE,QAAQ,CAAC,CAAC;IAC1G,CAAC;IAiGgC,kCAAW;IA/F5C,SAAS,8BAA8B,CAAC,QAA4C;QAClF,IAAI,SAAS,GAA0B,EAAE,CAAC;QAC1C,IAAI,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,EAAE;YAC3B,SAAS,GAAG,QAAQ,CAAC;YACrB,IAAM,SAAS,GAAG,IAAA,2BAAsB,EAAC,SAAS,CAAC,GAAG,CAAC,UAAA,EAAE,IAAI,OAAA,EAAE,CAAC,GAAG,EAAN,CAAM,CAAC,CAAC,CAAC;YACtE,IAAI,SAAS,KAAK,SAAS,EAAE;gBAC3B,MAAM,IAAI,KAAK,CAAI,UAAU,CAAC,mBAAmB,SAAI,SAAS,CAAC,QAAQ,EAAI,CAAC,CAAC;aAC9E;SACF;aAAM;YACL,SAAS,GAAG,CAAC,QAAQ,CAAC,CAAC;SACxB;QACD,OAAO,SAAS,CAAC;IACnB,CAAC;IAED,SAAS,uBAAuB,CAC9B,WAAmB,EACnB,gBAAiC,EACjC,GAAoB,EACpB,QAA4C;QAE5C,IAAM,SAAS,GAA0B,8BAA8B,CAAC,QAAQ,CAAC,CAAC;QAElF,IAAI,0BAA0B,GAA+D,EAAE,CAAC;QAEhG,IAAI,OAAO,CAAC,cAAc,CAAC,WAAW,EAAE,gBAAgB,CAAC,EAAE;YACzD,0BAA0B,GAAG,OAAO,CAAC,WAAW,CAAC,WAAW,EAAE,gBAAgB,CAAC,CAAC;SACjF;QAED,IAAI,uBAAuB,GAAsC,0BAA0B,CAAC,GAAa,CAAC,CAAC;QAE3G,IAAI,uBAAuB,KAAK,SAAS,EAAE;YACzC,uBAAuB,GAAG,EAAE,CAAC;SAC9B;aAAM;oCACM,CAAC;gBACV,IAAI,SAAS,CAAC,IAAI,CAAC,UAAA,EAAE,IAAI,OAAA,EAAE,CAAC,GAAG,KAAK,CAAC,CAAC,GAAG,EAAhB,CAAgB,CAAC,EAAE;oBAC1C,MAAM,IAAI,KAAK,CAAI,UAAU,CAAC,mBAAmB,SAAI,CAAC,CAAC,GAAG,CAAC,QAAQ,EAAI,CAAC,CAAC;iBAC1E;;YAHH,KAAgB,UAAuB,EAAvB,mDAAuB,EAAvB,qCAAuB,EAAvB,IAAuB;gBAAlC,IAAM,CAAC,gCAAA;wBAAD,CAAC;aAIX;SACF;QAGD,uBAAuB,CAAC,IAAI,OAA5B,uBAAuB,EAAS,SAAS,EAAE;QAC3C,0BAA0B,CAAC,GAAG,CAAC,GAAG,uBAAuB,CAAC;QAC1D,OAAO,CAAC,cAAc,CAAC,WAAW,EAAE,0BAA0B,EAAE,gBAAgB,CAAC,CAAC;IAEpF,CAAC;IAID,SAAS,qBAAqB,CAC5B,QAA4C;QAE5C,OAAO,UACL,MAAuB,EACvB,SAA2B,EAC3B,yBAA+D;YAE/D,IAAI,OAAO,yBAAyB,KAAK,QAAQ,EAAE;gBACjD,YAAY,CAAC,MAAM,EAAE,SAAS,EAAE,yBAAyB,EAAE,QAAQ,CAAC,CAAC;aACtE;iBAAM;gBACL,WAAW,CAAC,MAAM,EAAE,SAA4B,EAAE,QAAQ,CAAC,CAAC;aAC7D;QACH,CAAC,CAAC;IACJ,CAAC;IAgC6C,sDAAqB;IA9BnE,SAAS,SAAS,CAChB,UAAsE,EACtE,MAAuB;QAEvB,OAAO,CAAC,QAAQ,CAAC,UAA8B,EAAE,MAAM,CAAC,CAAC;IAC3D,CAAC;IAED,SAAS,MAAM,CAAC,UAAkB,EAAE,SAA6B;QAC/D,OAAO,UAAU,MAAc,EAAE,GAAW,IAAI,SAAS,CAAC,MAAM,EAAE,GAAG,EAAE,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC;IACxF,CAAC;IAOD,SAAS,QAAQ,CACf,SAAmE,EACnE,MAAW,EACX,wBAA0C;QAE1C,IAAI,OAAO,wBAAwB,KAAK,QAAQ,EAAE;YAChD,SAAS,CAAC,CAAC,MAAM,CAAC,wBAAwB,EAAE,SAA+B,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC;SACxF;aAAM,IAAI,OAAO,wBAAwB,KAAK,QAAQ,EAAE;YACvD,OAAO,CAAC,QAAQ,CAAC,CAAC,SAA4B,CAAC,EAAE,MAAM,EAAE,wBAAwB,CAAC,CAAC;SACpF;aAAM;YACL,SAAS,CAAC,CAAC,SAAS,CAAC,EAAE,MAAM,CAAC,CAAC;SAChC;IACH,CAAC;IAEQ,4BAAQ"}