import{U as e}from"./chunks/UserProfile.CzV3dsgg.js";import{c as t,o as r,G as a}from"./chunks/framework.neMYHtQj.js";import"./chunks/theme.Ch1k4S35.js";import"./chunks/TopmeansMarkdownService.BN2GN_Vw.js";const _=JSON.parse('{"title":"","description":"","frontmatter":{"layout":"page"},"headers":[],"relativePath":"user-center/profile.md","filePath":"user-center/profile.md"}'),o={name:"user-center/profile.md"},f=Object.assign(o,{setup(s){return(c,i)=>(r(),t("div",null,[a(e)]))}});export{_ as __pageData,f as default};
