{"version": 3, "file": "searchPhoneNumbersInText.test.js", "names": ["describe", "it", "NUMBERS", "searchPhoneNumbersInText", "metadata", "number", "should", "equal", "shift", "expectedNumbers", "country", "nationalNumber", "startsAt", "endsAt", "expected", "length"], "sources": ["../source/searchPhoneNumbersInText.test.js"], "sourcesContent": ["import searchPhoneNumbersInText from './searchPhoneNumbersInText.js'\r\nimport metadata from '../metadata.min.json' assert { type: 'json' }\r\n\r\ndescribe('searchPhoneNumbersInText', () => {\r\n\tit('should find phone numbers (with default country)', () => {\r\n\t\tconst NUMBERS = ['+78005553535', '+12133734253']\r\n\t\tfor (const number of searchPhoneNumbersInText('The number is +7 (800) 555-35-35 and not (************* as written in the document.', 'US', metadata)) {\r\n\t\t\tnumber.number.number.should.equal(NUMBERS[0])\r\n\t\t\tNUMBERS.shift()\r\n\t\t}\r\n\t})\r\n\r\n\tit('should find phone numbers', () => {\r\n\t\tconst NUMBERS = ['+78005553535', '+12133734253']\r\n\t\tfor (const number of searchPhoneNumbersInText('The number is +7 (800) 555-35-35 and not (************* as written in the document.', metadata)) {\r\n\t\t\tnumber.number.number.should.equal(NUMBERS[0])\r\n\t\t\tNUMBERS.shift()\r\n\t\t}\r\n\t})\r\n\r\n\tit('should find phone numbers in text', () => {\r\n\t\tconst expectedNumbers = [{\r\n\t\t\tcountry: 'RU',\r\n\t\t\tnationalNumber: '8005553535',\r\n\t\t\tstartsAt: 14,\r\n\t\t\tendsAt: 32\r\n\t\t}, {\r\n\t\t\tcountry: 'US',\r\n\t\t\tnationalNumber: '2133734253',\r\n\t\t\tstartsAt: 41,\r\n\t\t\tendsAt: 55\r\n\t\t}]\r\n\r\n\t\tfor (const number of searchPhoneNumbersInText('The number is +7 (800) 555-35-35 and not (************* as written in the document.', 'US', metadata)) {\r\n\t\t\tconst expected = expectedNumbers.shift()\r\n\t\t\tnumber.startsAt.should.equal(expected.startsAt)\r\n\t\t\tnumber.endsAt.should.equal(expected.endsAt)\r\n\t\t\tnumber.number.nationalNumber.should.equal(expected.nationalNumber)\r\n\t\t\tnumber.number.country.should.equal(expected.country)\r\n\t\t}\r\n\r\n\t\texpectedNumbers.length.should.equal(0)\r\n\t})\r\n})"], "mappings": ";;AAAA;;AACA;;;;;;;;;;AAEAA,QAAQ,CAAC,0BAAD,EAA6B,YAAM;EAC1CC,EAAE,CAAC,kDAAD,EAAqD,YAAM;IAC5D,IAAMC,OAAO,GAAG,CAAC,cAAD,EAAiB,cAAjB,CAAhB;;IACA,qDAAqB,IAAAC,oCAAA,EAAyB,qFAAzB,EAAgH,IAAhH,EAAsHC,uBAAtH,CAArB,wCAAsJ;MAAA,IAA3IC,MAA2I;MACrJA,MAAM,CAACA,MAAP,CAAcA,MAAd,CAAqBC,MAArB,CAA4BC,KAA5B,CAAkCL,OAAO,CAAC,CAAD,CAAzC;MACAA,OAAO,CAACM,KAAR;IACA;EACD,CANC,CAAF;EAQAP,EAAE,CAAC,2BAAD,EAA8B,YAAM;IACrC,IAAMC,OAAO,GAAG,CAAC,cAAD,EAAiB,cAAjB,CAAhB;;IACA,sDAAqB,IAAAC,oCAAA,EAAyB,qFAAzB,EAAgHC,uBAAhH,CAArB,2CAAgJ;MAAA,IAArIC,MAAqI;MAC/IA,MAAM,CAACA,MAAP,CAAcA,MAAd,CAAqBC,MAArB,CAA4BC,KAA5B,CAAkCL,OAAO,CAAC,CAAD,CAAzC;MACAA,OAAO,CAACM,KAAR;IACA;EACD,CANC,CAAF;EAQAP,EAAE,CAAC,mCAAD,EAAsC,YAAM;IAC7C,IAAMQ,eAAe,GAAG,CAAC;MACxBC,OAAO,EAAE,IADe;MAExBC,cAAc,EAAE,YAFQ;MAGxBC,QAAQ,EAAE,EAHc;MAIxBC,MAAM,EAAE;IAJgB,CAAD,EAKrB;MACFH,OAAO,EAAE,IADP;MAEFC,cAAc,EAAE,YAFd;MAGFC,QAAQ,EAAE,EAHR;MAIFC,MAAM,EAAE;IAJN,CALqB,CAAxB;;IAYA,sDAAqB,IAAAV,oCAAA,EAAyB,qFAAzB,EAAgH,IAAhH,EAAsHC,uBAAtH,CAArB,2CAAsJ;MAAA,IAA3IC,MAA2I;MACrJ,IAAMS,QAAQ,GAAGL,eAAe,CAACD,KAAhB,EAAjB;MACAH,MAAM,CAACO,QAAP,CAAgBN,MAAhB,CAAuBC,KAAvB,CAA6BO,QAAQ,CAACF,QAAtC;MACAP,MAAM,CAACQ,MAAP,CAAcP,MAAd,CAAqBC,KAArB,CAA2BO,QAAQ,CAACD,MAApC;MACAR,MAAM,CAACA,MAAP,CAAcM,cAAd,CAA6BL,MAA7B,CAAoCC,KAApC,CAA0CO,QAAQ,CAACH,cAAnD;MACAN,MAAM,CAACA,MAAP,CAAcK,OAAd,CAAsBJ,MAAtB,CAA6BC,KAA7B,CAAmCO,QAAQ,CAACJ,OAA5C;IACA;;IAEDD,eAAe,CAACM,MAAhB,CAAuBT,MAAvB,CAA8BC,KAA9B,CAAoC,CAApC;EACA,CAtBC,CAAF;AAuBA,CAxCO,CAAR"}