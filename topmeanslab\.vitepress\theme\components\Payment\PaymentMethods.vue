<template>
  <div class="payment-methods">
    <el-card class="payment-card">
      <template #header>
        <div class="card-header">
          <span>选择支付方式</span>
        </div>
      </template>

      <el-radio-group v-model="selectedMethod" class="payment-methods-group">
        <el-radio-button
          v-for="method in paymentMethods"
          :key="method.id"
          :label="method.id"
          :disabled="!method.enabled"
        >
          <span class="payment-method-content">
            <span class="payment-icon">{{ method.icon }}</span>
            <span class="payment-name">{{ method.name }}</span>
          </span>
          <div class="payment-description">{{ method.description }}</div>
        </el-radio-button>
      </el-radio-group>

      <div class="payment-amount">
        <span class="amount-label">支付金额：</span>
        <span class="amount-value">{{ formattedAmount }}</span>
      </div>

      <div class="payment-actions">
        <el-button
          type="primary"
          @click="handlePayment"
          :loading="isProcessing"
          :disabled="!selectedMethod"
          size="large"
        >
          {{ isProcessing ? '处理中...' : '确认支付' }}
        </el-button>
        <el-button @click="handleCancel" size="large">
          取消
        </el-button>
      </div>
    </el-card>

    <!-- 支付宝二维码支付弹窗 -->
    <AlipayQRDialog
      :visible="showAlipayDialog"
      :payment-data="currentPaymentData"
      @close="handleAlipayClose"
      @success="handleAlipaySuccess"
      @cancel="handleAlipayCancel"
      @error="handleAlipayError"
    />
  </div>
</template>

<script setup>
import { ref, computed } from 'vue'
import { ElMessage } from 'element-plus'
import paymentService from '../services/paymentService'
import AlipayQRDialog from './AlipayQRDialog.vue'

const props = defineProps({
  amount: {
    type: [Number, String],
    required: true
  },
  orderInfo: {
    type: Object,
    default: () => ({})
  }
})

const emit = defineEmits(['payment-success', 'payment-cancel'])

const selectedMethod = ref('alipay')
const isProcessing = ref(false)
const showAlipayDialog = ref(false)
const currentPaymentData = ref(null)

const paymentMethods = [
  {
    id: 'alipay',
    name: '支付宝',
    icon: '💰',
    description: '使用支付宝扫码支付',
    enabled: true
  },
  {
    id: 'wechat',
    name: '微信支付',
    icon: '💚',
    description: '使用微信扫码支付',
    enabled: false
  },
  {
    id: 'bank',
    name: '银行卡',
    icon: '💳',
    description: '使用银行卡支付',
    enabled: false
  }
]

const formattedAmount = computed(() => {
  return paymentService.formatAmount(props.amount)
})

const handlePayment = async () => {
  if (isProcessing.value) return

  // 验证支付金额
  const validation = paymentService.validateAmount(props.amount)
  if (!validation.valid) {
    ElMessage.error(validation.message)
    return
  }

  isProcessing.value = true

  try {
    if (selectedMethod.value === 'alipay') {
      await handleAlipayPayment()
    } else if (selectedMethod.value === 'wechat') {
      await handleWechatPayment()
    } else if (selectedMethod.value === 'bank') {
      await handleBankPayment()
    }
  } catch (error) {
    console.error('支付处理失败:', error)
    ElMessage.error(error.message || '支付失败，请重试')
  } finally {
    isProcessing.value = false
  }
}

const handleAlipayPayment = async () => {
  try {
    // 创建支付订单
    const result = await paymentService.createPayment({
      amount: props.amount,
      subject: props.orderInfo.subject || 'TopMeansLab 服务购买',
      serviceType: props.orderInfo.serviceType || 'premium_service'
    })

    if (result.success) {
      // 保存支付数据并显示二维码对话框
      currentPaymentData.value = result.data
      showAlipayDialog.value = true

      ElMessage.success('支付订单创建成功，请扫码支付')
    } else {
      throw new Error(result.message)
    }
  } catch (error) {
    console.error('创建支付宝订单失败:', error)
    throw error
  }
}

const handleWechatPayment = async () => {
  ElMessage.warning('微信支付暂未开放')
}

const handleBankPayment = async () => {
  ElMessage.warning('银行卡支付暂未开放')
}

const handleCancel = () => {
  emit('payment-cancel')
}

// 支付宝对话框事件处理
const handleAlipaySuccess = (paymentData) => {
  showAlipayDialog.value = false
  ElMessage.success('支付成功！')

  emit('payment-success', {
    method: 'alipay',
    amount: props.amount,
    orderId: paymentData.outTradeNo,
    tradeNo: paymentData.tradeNo,
    paymentData: paymentData
  })
}

const handleAlipayCancel = () => {
  showAlipayDialog.value = false
  ElMessage.info('支付已取消')
}

const handleAlipayError = (error) => {
  showAlipayDialog.value = false
  ElMessage.error(error.message || '支付失败')
}

const handleAlipayClose = () => {
  showAlipayDialog.value = false
}
</script>

<style scoped>
.payment-methods {
  padding: 20px;
}

.payment-card {
  max-width: 600px;
  margin: 0 auto;
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.card-header {
  text-align: center;
  font-size: 20px;
  font-weight: bold;
  color: #333;
}

.payment-methods-group {
  display: flex;
  flex-direction: column;
  gap: 16px;
  margin: 24px 0;
}

.payment-methods-group :deep(.el-radio-button) {
  width: 100%;
  margin: 0;
}

.payment-methods-group :deep(.el-radio-button__inner) {
  width: 100%;
  padding: 16px 20px;
  border-radius: 8px;
  border: 2px solid #e8e8e8;
  background: #fafafa;
  transition: all 0.3s ease;
  text-align: left;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
}

.payment-methods-group :deep(.el-radio-button__inner:hover) {
  border-color: #1677ff;
  background: #f0f8ff;
}

.payment-methods-group :deep(.el-radio-button.is-active .el-radio-button__inner) {
  border-color: #1677ff;
  background: #e6f4ff;
  color: #1677ff;
}

.payment-methods-group :deep(.el-radio-button.is-disabled .el-radio-button__inner) {
  opacity: 0.5;
  cursor: not-allowed;
}

.payment-method-content {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 4px;
}

.payment-icon {
  font-size: 24px;
}

.payment-name {
  font-size: 16px;
  font-weight: 600;
}

.payment-description {
  font-size: 12px;
  color: #666;
  margin-top: 4px;
}

.payment-amount {
  text-align: center;
  margin: 24px 0;
  padding: 20px;
  background: linear-gradient(135deg, #f0f8ff 0%, #e6f4ff 100%);
  border-radius: 8px;
  border: 1px solid #d6e4ff;
}

.amount-label {
  display: block;
  color: #666;
  font-size: 14px;
  margin-bottom: 8px;
}

.amount-value {
  color: #1677ff;
  font-weight: bold;
  font-size: 28px;
}

.payment-actions {
  display: flex;
  gap: 12px;
  justify-content: center;
  margin-top: 24px;
}

.payment-actions .el-button {
  min-width: 120px;
}
</style>