<template>
  <div class="payment-methods">
    <el-card class="payment-card">
      <template #header>
        <div class="card-header">
          <span>选择支付方式</span>
        </div>
      </template>

      <el-radio-group v-model="selectedMethod" class="payment-methods-group">
        <el-radio-button label="alipay">
          <img :src="'/images/alipay.png?url'" alt="支付宝" class="payment-icon" />
          <span>支付宝</span>
        </el-radio-button>
      </el-radio-group>

      <div class="payment-amount">
        <span class="amount-label">支付金额：</span>
        <span class="amount-value">¥{{ amount }}</span>
      </div>

      <div class="payment-actions">
        <el-button type="primary" @click="handlePayment" :loading="loading">
          确认支付
        </el-button>
      </div>
    </el-card>

    <!-- 支付二维码弹窗 -->
    <el-dialog
      v-model="qrCodeVisible"
      title="扫码支付"
      width="300px"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      :show-close="false"
    >
      <div class="qr-code-container">
        <img :src="qrCodeUrl" alt="支付二维码" class="qr-code" />
        <p class="qr-code-tip">请使用{{ selectedMethod === 'alipay' ? '支付宝' : '微信' }}扫码支付</p>
      </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="cancelPayment">取消支付</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import { ElMessage } from 'element-plus'

const props = defineProps({
  amount: {
    type: Number,
    required: true
  }
})

const emit = defineEmits(['payment-success', 'payment-cancel'])

const selectedMethod = ref('alipay')
const loading = ref(false)
const qrCodeVisible = ref(false)
const qrCodeUrl = ref('')

const handlePayment = async () => {
  loading.value = true
  try {
    // TODO: 调用后端接口获取支付二维码
    // const response = await getPaymentQRCode({
    //   method: selectedMethod.value,
    //   amount: props.amount
    // })
    // qrCodeUrl.value = response.qrCodeUrl
    
    // 模拟获取二维码
    qrCodeUrl.value = '/images/qr-code-demo.png?url'
    qrCodeVisible.value = true
  } catch (error) {
    ElMessage.error('获取支付二维码失败')
  } finally {
    loading.value = false
  }
}

const cancelPayment = () => {
  qrCodeVisible.value = false
  emit('payment-cancel')
}

// 模拟轮询支付状态
const checkPaymentStatus = () => {
  // TODO: 实现支付状态轮询逻辑
  // 这里应该调用后端接口检查支付状态
  // 支付成功后关闭弹窗并触发成功事件
  // qrCodeVisible.value = false
  // emit('payment-success')
}
</script>

<style scoped>
.payment-methods {
  padding: 20px;
}

.payment-card {
  max-width: 600px;
  margin: 0 auto;
}

.card-header {
  text-align: center;
  font-size: 20px;
  font-weight: bold;
}

.payment-methods-group {
  display: flex;
  justify-content: center;
  gap: 20px;
  margin: 20px 0;
}

.payment-icon {
  width: 24px;
  height: 24px;
  margin-right: 8px;
  vertical-align: middle;
}

.payment-amount {
  text-align: center;
  margin: 20px 0;
  font-size: 18px;
}

.amount-label {
  color: #666;
}

.amount-value {
  color: #f56c6c;
  font-weight: bold;
  font-size: 24px;
}

.payment-actions {
  text-align: center;
  margin-top: 20px;
}

.qr-code-container {
  text-align: center;
  padding: 20px 0;
}

.qr-code {
  width: 200px;
  height: 200px;
  margin-bottom: 10px;
}

.qr-code-tip {
  color: #666;
  font-size: 14px;
}
</style> 