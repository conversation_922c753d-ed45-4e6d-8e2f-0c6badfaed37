import{g as M0}from"./theme.Ch1k4S35.js";const Pu={};function B0(u){let e=Pu[u];if(e)return e;e=Pu[u]=[];for(let t=0;t<128;t++){const r=String.fromCharCode(t);e.push(r)}for(let t=0;t<u.length;t++){const r=u.charCodeAt(t);e[r]="%"+("0"+r.toString(16).toUpperCase()).slice(-2)}return e}function J(u,e){typeof e!="string"&&(e=J.defaultChars);const t=B0(e);return u.replace(/(%[a-f0-9]{2})+/gi,function(r){let n="";for(let c=0,i=r.length;c<i;c+=3){const o=parseInt(r.slice(c+1,c+3),16);if(o<128){n+=t[o];continue}if((o&224)===192&&c+3<i){const a=parseInt(r.slice(c+4,c+6),16);if((a&192)===128){const s=o<<6&1984|a&63;s<128?n+="��":n+=String.fromCharCode(s),c+=3;continue}}if((o&240)===224&&c+6<i){const a=parseInt(r.slice(c+4,c+6),16),s=parseInt(r.slice(c+7,c+9),16);if((a&192)===128&&(s&192)===128){const l=o<<12&61440|a<<6&4032|s&63;l<2048||l>=55296&&l<=57343?n+="���":n+=String.fromCharCode(l),c+=6;continue}}if((o&248)===240&&c+9<i){const a=parseInt(r.slice(c+4,c+6),16),s=parseInt(r.slice(c+7,c+9),16),l=parseInt(r.slice(c+10,c+12),16);if((a&192)===128&&(s&192)===128&&(l&192)===128){let f=o<<18&1835008|a<<12&258048|s<<6&4032|l&63;f<65536||f>1114111?n+="����":(f-=65536,n+=String.fromCharCode(55296+(f>>10),56320+(f&1023))),c+=9;continue}}n+="�"}return n})}J.defaultChars=";/?:@&=+$,#";J.componentChars="";const Lu={};function I0(u){let e=Lu[u];if(e)return e;e=Lu[u]=[];for(let t=0;t<128;t++){const r=String.fromCharCode(t);/^[0-9a-z]$/i.test(r)?e.push(r):e.push("%"+("0"+t.toString(16).toUpperCase()).slice(-2))}for(let t=0;t<u.length;t++)e[u.charCodeAt(t)]=u[t];return e}function tu(u,e,t){typeof e!="string"&&(t=e,e=tu.defaultChars),typeof t>"u"&&(t=!0);const r=I0(e);let n="";for(let c=0,i=u.length;c<i;c++){const o=u.charCodeAt(c);if(t&&o===37&&c+2<i&&/^[0-9a-f]{2}$/i.test(u.slice(c+1,c+3))){n+=u.slice(c,c+3),c+=2;continue}if(o<128){n+=r[o];continue}if(o>=55296&&o<=57343){if(o>=55296&&o<=56319&&c+1<i){const a=u.charCodeAt(c+1);if(a>=56320&&a<=57343){n+=encodeURIComponent(u[c]+u[c+1]),c++;continue}}n+="%EF%BF%BD";continue}n+=encodeURIComponent(u[c])}return n}tu.defaultChars=";/?:@&=+$,-_.!~*'()#";tu.componentChars="-_.!~*'()";function Su(u){let e="";return e+=u.protocol||"",e+=u.slashes?"//":"",e+=u.auth?u.auth+"@":"",u.hostname&&u.hostname.indexOf(":")!==-1?e+="["+u.hostname+"]":e+=u.hostname||"",e+=u.port?":"+u.port:"",e+=u.pathname||"",e+=u.search||"",e+=u.hash||"",e}function au(){this.protocol=null,this.slashes=null,this.auth=null,this.port=null,this.hostname=null,this.hash=null,this.search=null,this.pathname=null}const R0=/^([a-z0-9.+-]+:)/i,q0=/:[0-9]*$/,O0=/^(\/\/?(?!\/)[^\?\s]*)(\?[^\s]*)?$/,P0=["<",">",'"',"`"," ","\r",`
`,"	"],L0=["{","}","|","\\","^","`"].concat(P0),N0=["'"].concat(L0),Nu=["%","/","?",";","#"].concat(N0),ju=["/","?","#"],j0=255,$u=/^[+a-z0-9A-Z_-]{0,63}$/,$0=/^([+a-z0-9A-Z_-]{0,63})(.*)$/,Uu={javascript:!0,"javascript:":!0},Hu={http:!0,https:!0,ftp:!0,gopher:!0,file:!0,"http:":!0,"https:":!0,"ftp:":!0,"gopher:":!0,"file:":!0};function Tu(u,e){if(u&&u instanceof au)return u;const t=new au;return t.parse(u,e),t}au.prototype.parse=function(u,e){let t,r,n,c=u;if(c=c.trim(),!e&&u.split("#").length===1){const s=O0.exec(c);if(s)return this.pathname=s[1],s[2]&&(this.search=s[2]),this}let i=R0.exec(c);if(i&&(i=i[0],t=i.toLowerCase(),this.protocol=i,c=c.substr(i.length)),(e||i||c.match(/^\/\/[^@\/]+@[^@\/]+/))&&(n=c.substr(0,2)==="//",n&&!(i&&Uu[i])&&(c=c.substr(2),this.slashes=!0)),!Uu[i]&&(n||i&&!Hu[i])){let s=-1;for(let d=0;d<ju.length;d++)r=c.indexOf(ju[d]),r!==-1&&(s===-1||r<s)&&(s=r);let l,f;s===-1?f=c.lastIndexOf("@"):f=c.lastIndexOf("@",s),f!==-1&&(l=c.slice(0,f),c=c.slice(f+1),this.auth=l),s=-1;for(let d=0;d<Nu.length;d++)r=c.indexOf(Nu[d]),r!==-1&&(s===-1||r<s)&&(s=r);s===-1&&(s=c.length),c[s-1]===":"&&s--;const x=c.slice(0,s);c=c.slice(s),this.parseHost(x),this.hostname=this.hostname||"";const h=this.hostname[0]==="["&&this.hostname[this.hostname.length-1]==="]";if(!h){const d=this.hostname.split(/\./);for(let b=0,p=d.length;b<p;b++){const D=d[b];if(D&&!D.match($u)){let m="";for(let _=0,g=D.length;_<g;_++)D.charCodeAt(_)>127?m+="x":m+=D[_];if(!m.match($u)){const _=d.slice(0,b),g=d.slice(b+1),k=D.match($0);k&&(_.push(k[1]),g.unshift(k[2])),g.length&&(c=g.join(".")+c),this.hostname=_.join(".");break}}}}this.hostname.length>j0&&(this.hostname=""),h&&(this.hostname=this.hostname.substr(1,this.hostname.length-2))}const o=c.indexOf("#");o!==-1&&(this.hash=c.substr(o),c=c.slice(0,o));const a=c.indexOf("?");return a!==-1&&(this.search=c.substr(a),c=c.slice(0,a)),c&&(this.pathname=c),Hu[t]&&this.hostname&&!this.pathname&&(this.pathname=""),this};au.prototype.parseHost=function(u){let e=q0.exec(u);e&&(e=e[0],e!==":"&&(this.port=e.substr(1)),u=u.substr(0,u.length-e.length)),u&&(this.hostname=u)};const U0=Object.freeze(Object.defineProperty({__proto__:null,decode:J,encode:tu,format:Su,parse:Tu},Symbol.toStringTag,{value:"Module"})),n0=/[\0-\uD7FF\uE000-\uFFFF]|[\uD800-\uDBFF][\uDC00-\uDFFF]|[\uD800-\uDBFF](?![\uDC00-\uDFFF])|(?:[^\uD800-\uDBFF]|^)[\uDC00-\uDFFF]/,c0=/[\0-\x1F\x7F-\x9F]/,H0=/[\xAD\u0600-\u0605\u061C\u06DD\u070F\u0890\u0891\u08E2\u180E\u200B-\u200F\u202A-\u202E\u2060-\u2064\u2066-\u206F\uFEFF\uFFF9-\uFFFB]|\uD804[\uDCBD\uDCCD]|\uD80D[\uDC30-\uDC3F]|\uD82F[\uDCA0-\uDCA3]|\uD834[\uDD73-\uDD7A]|\uDB40[\uDC01\uDC20-\uDC7F]/,zu=/[!-#%-\*,-\/:;\?@\[-\]_\{\}\xA1\xA7\xAB\xB6\xB7\xBB\xBF\u037E\u0387\u055A-\u055F\u0589\u058A\u05BE\u05C0\u05C3\u05C6\u05F3\u05F4\u0609\u060A\u060C\u060D\u061B\u061D-\u061F\u066A-\u066D\u06D4\u0700-\u070D\u07F7-\u07F9\u0830-\u083E\u085E\u0964\u0965\u0970\u09FD\u0A76\u0AF0\u0C77\u0C84\u0DF4\u0E4F\u0E5A\u0E5B\u0F04-\u0F12\u0F14\u0F3A-\u0F3D\u0F85\u0FD0-\u0FD4\u0FD9\u0FDA\u104A-\u104F\u10FB\u1360-\u1368\u1400\u166E\u169B\u169C\u16EB-\u16ED\u1735\u1736\u17D4-\u17D6\u17D8-\u17DA\u1800-\u180A\u1944\u1945\u1A1E\u1A1F\u1AA0-\u1AA6\u1AA8-\u1AAD\u1B5A-\u1B60\u1B7D\u1B7E\u1BFC-\u1BFF\u1C3B-\u1C3F\u1C7E\u1C7F\u1CC0-\u1CC7\u1CD3\u2010-\u2027\u2030-\u2043\u2045-\u2051\u2053-\u205E\u207D\u207E\u208D\u208E\u2308-\u230B\u2329\u232A\u2768-\u2775\u27C5\u27C6\u27E6-\u27EF\u2983-\u2998\u29D8-\u29DB\u29FC\u29FD\u2CF9-\u2CFC\u2CFE\u2CFF\u2D70\u2E00-\u2E2E\u2E30-\u2E4F\u2E52-\u2E5D\u3001-\u3003\u3008-\u3011\u3014-\u301F\u3030\u303D\u30A0\u30FB\uA4FE\uA4FF\uA60D-\uA60F\uA673\uA67E\uA6F2-\uA6F7\uA874-\uA877\uA8CE\uA8CF\uA8F8-\uA8FA\uA8FC\uA92E\uA92F\uA95F\uA9C1-\uA9CD\uA9DE\uA9DF\uAA5C-\uAA5F\uAADE\uAADF\uAAF0\uAAF1\uABEB\uFD3E\uFD3F\uFE10-\uFE19\uFE30-\uFE52\uFE54-\uFE61\uFE63\uFE68\uFE6A\uFE6B\uFF01-\uFF03\uFF05-\uFF0A\uFF0C-\uFF0F\uFF1A\uFF1B\uFF1F\uFF20\uFF3B-\uFF3D\uFF3F\uFF5B\uFF5D\uFF5F-\uFF65]|\uD800[\uDD00-\uDD02\uDF9F\uDFD0]|\uD801\uDD6F|\uD802[\uDC57\uDD1F\uDD3F\uDE50-\uDE58\uDE7F\uDEF0-\uDEF6\uDF39-\uDF3F\uDF99-\uDF9C]|\uD803[\uDEAD\uDF55-\uDF59\uDF86-\uDF89]|\uD804[\uDC47-\uDC4D\uDCBB\uDCBC\uDCBE-\uDCC1\uDD40-\uDD43\uDD74\uDD75\uDDC5-\uDDC8\uDDCD\uDDDB\uDDDD-\uDDDF\uDE38-\uDE3D\uDEA9]|\uD805[\uDC4B-\uDC4F\uDC5A\uDC5B\uDC5D\uDCC6\uDDC1-\uDDD7\uDE41-\uDE43\uDE60-\uDE6C\uDEB9\uDF3C-\uDF3E]|\uD806[\uDC3B\uDD44-\uDD46\uDDE2\uDE3F-\uDE46\uDE9A-\uDE9C\uDE9E-\uDEA2\uDF00-\uDF09]|\uD807[\uDC41-\uDC45\uDC70\uDC71\uDEF7\uDEF8\uDF43-\uDF4F\uDFFF]|\uD809[\uDC70-\uDC74]|\uD80B[\uDFF1\uDFF2]|\uD81A[\uDE6E\uDE6F\uDEF5\uDF37-\uDF3B\uDF44]|\uD81B[\uDE97-\uDE9A\uDFE2]|\uD82F\uDC9F|\uD836[\uDE87-\uDE8B]|\uD83A[\uDD5E\uDD5F]/,i0=/[\$\+<->\^`\|~\xA2-\xA6\xA8\xA9\xAC\xAE-\xB1\xB4\xB8\xD7\xF7\u02C2-\u02C5\u02D2-\u02DF\u02E5-\u02EB\u02ED\u02EF-\u02FF\u0375\u0384\u0385\u03F6\u0482\u058D-\u058F\u0606-\u0608\u060B\u060E\u060F\u06DE\u06E9\u06FD\u06FE\u07F6\u07FE\u07FF\u0888\u09F2\u09F3\u09FA\u09FB\u0AF1\u0B70\u0BF3-\u0BFA\u0C7F\u0D4F\u0D79\u0E3F\u0F01-\u0F03\u0F13\u0F15-\u0F17\u0F1A-\u0F1F\u0F34\u0F36\u0F38\u0FBE-\u0FC5\u0FC7-\u0FCC\u0FCE\u0FCF\u0FD5-\u0FD8\u109E\u109F\u1390-\u1399\u166D\u17DB\u1940\u19DE-\u19FF\u1B61-\u1B6A\u1B74-\u1B7C\u1FBD\u1FBF-\u1FC1\u1FCD-\u1FCF\u1FDD-\u1FDF\u1FED-\u1FEF\u1FFD\u1FFE\u2044\u2052\u207A-\u207C\u208A-\u208C\u20A0-\u20C0\u2100\u2101\u2103-\u2106\u2108\u2109\u2114\u2116-\u2118\u211E-\u2123\u2125\u2127\u2129\u212E\u213A\u213B\u2140-\u2144\u214A-\u214D\u214F\u218A\u218B\u2190-\u2307\u230C-\u2328\u232B-\u2426\u2440-\u244A\u249C-\u24E9\u2500-\u2767\u2794-\u27C4\u27C7-\u27E5\u27F0-\u2982\u2999-\u29D7\u29DC-\u29FB\u29FE-\u2B73\u2B76-\u2B95\u2B97-\u2BFF\u2CE5-\u2CEA\u2E50\u2E51\u2E80-\u2E99\u2E9B-\u2EF3\u2F00-\u2FD5\u2FF0-\u2FFF\u3004\u3012\u3013\u3020\u3036\u3037\u303E\u303F\u309B\u309C\u3190\u3191\u3196-\u319F\u31C0-\u31E3\u31EF\u3200-\u321E\u322A-\u3247\u3250\u3260-\u327F\u328A-\u32B0\u32C0-\u33FF\u4DC0-\u4DFF\uA490-\uA4C6\uA700-\uA716\uA720\uA721\uA789\uA78A\uA828-\uA82B\uA836-\uA839\uAA77-\uAA79\uAB5B\uAB6A\uAB6B\uFB29\uFBB2-\uFBC2\uFD40-\uFD4F\uFDCF\uFDFC-\uFDFF\uFE62\uFE64-\uFE66\uFE69\uFF04\uFF0B\uFF1C-\uFF1E\uFF3E\uFF40\uFF5C\uFF5E\uFFE0-\uFFE6\uFFE8-\uFFEE\uFFFC\uFFFD]|\uD800[\uDD37-\uDD3F\uDD79-\uDD89\uDD8C-\uDD8E\uDD90-\uDD9C\uDDA0\uDDD0-\uDDFC]|\uD802[\uDC77\uDC78\uDEC8]|\uD805\uDF3F|\uD807[\uDFD5-\uDFF1]|\uD81A[\uDF3C-\uDF3F\uDF45]|\uD82F\uDC9C|\uD833[\uDF50-\uDFC3]|\uD834[\uDC00-\uDCF5\uDD00-\uDD26\uDD29-\uDD64\uDD6A-\uDD6C\uDD83\uDD84\uDD8C-\uDDA9\uDDAE-\uDDEA\uDE00-\uDE41\uDE45\uDF00-\uDF56]|\uD835[\uDEC1\uDEDB\uDEFB\uDF15\uDF35\uDF4F\uDF6F\uDF89\uDFA9\uDFC3]|\uD836[\uDC00-\uDDFF\uDE37-\uDE3A\uDE6D-\uDE74\uDE76-\uDE83\uDE85\uDE86]|\uD838[\uDD4F\uDEFF]|\uD83B[\uDCAC\uDCB0\uDD2E\uDEF0\uDEF1]|\uD83C[\uDC00-\uDC2B\uDC30-\uDC93\uDCA0-\uDCAE\uDCB1-\uDCBF\uDCC1-\uDCCF\uDCD1-\uDCF5\uDD0D-\uDDAD\uDDE6-\uDE02\uDE10-\uDE3B\uDE40-\uDE48\uDE50\uDE51\uDE60-\uDE65\uDF00-\uDFFF]|\uD83D[\uDC00-\uDED7\uDEDC-\uDEEC\uDEF0-\uDEFC\uDF00-\uDF76\uDF7B-\uDFD9\uDFE0-\uDFEB\uDFF0]|\uD83E[\uDC00-\uDC0B\uDC10-\uDC47\uDC50-\uDC59\uDC60-\uDC87\uDC90-\uDCAD\uDCB0\uDCB1\uDD00-\uDE53\uDE60-\uDE6D\uDE70-\uDE7C\uDE80-\uDE88\uDE90-\uDEBD\uDEBF-\uDEC5\uDECE-\uDEDB\uDEE0-\uDEE8\uDEF0-\uDEF8\uDF00-\uDF92\uDF94-\uDFCA]/,o0=/[ \xA0\u1680\u2000-\u200A\u2028\u2029\u202F\u205F\u3000]/,Z0=Object.freeze(Object.defineProperty({__proto__:null,Any:n0,Cc:c0,Cf:H0,P:zu,S:i0,Z:o0},Symbol.toStringTag,{value:"Module"})),V0=new Uint16Array('ᵁ<Õıʊҝջאٵ۞ޢߖࠏ੊ઑඡ๭༉༦჊ረዡᐕᒝᓃᓟᔥ\0\0\0\0\0\0ᕫᛍᦍᰒᷝ὾⁠↰⊍⏀⏻⑂⠤⤒ⴈ⹈⿎〖㊺㘹㞬㣾㨨㩱㫠㬮ࠀEMabcfglmnoprstu\\bfms¦³¹ÈÏlig耻Æ䃆P耻&䀦cute耻Á䃁reve;䄂Āiyx}rc耻Â䃂;䐐r;쀀𝔄rave耻À䃀pha;䎑acr;䄀d;橓Āgp¡on;䄄f;쀀𝔸plyFunction;恡ing耻Å䃅Ācs¾Ãr;쀀𝒜ign;扔ilde耻Ã䃃ml耻Ä䃄ЀaceforsuåûþėĜĢħĪĀcrêòkslash;或Ŷöø;櫧ed;挆y;䐑ƀcrtąċĔause;戵noullis;愬a;䎒r;쀀𝔅pf;쀀𝔹eve;䋘còēmpeq;扎܀HOacdefhilorsuōőŖƀƞƢƵƷƺǜȕɳɸɾcy;䐧PY耻©䂩ƀcpyŝŢźute;䄆Ā;iŧŨ拒talDifferentialD;慅leys;愭ȀaeioƉƎƔƘron;䄌dil耻Ç䃇rc;䄈nint;戰ot;䄊ĀdnƧƭilla;䂸terDot;䂷òſi;䎧rcleȀDMPTǇǋǑǖot;抙inus;抖lus;投imes;抗oĀcsǢǸkwiseContourIntegral;戲eCurlyĀDQȃȏoubleQuote;思uote;怙ȀlnpuȞȨɇɕonĀ;eȥȦ户;橴ƀgitȯȶȺruent;扡nt;戯ourIntegral;戮ĀfrɌɎ;愂oduct;成nterClockwiseContourIntegral;戳oss;樯cr;쀀𝒞pĀ;Cʄʅ拓ap;才րDJSZacefiosʠʬʰʴʸˋ˗ˡ˦̳ҍĀ;oŹʥtrahd;椑cy;䐂cy;䐅cy;䐏ƀgrsʿ˄ˇger;怡r;憡hv;櫤Āayː˕ron;䄎;䐔lĀ;t˝˞戇a;䎔r;쀀𝔇Āaf˫̧Ācm˰̢riticalȀADGT̖̜̀̆cute;䂴oŴ̋̍;䋙bleAcute;䋝rave;䁠ilde;䋜ond;拄ferentialD;慆Ѱ̽\0\0\0͔͂\0Ѕf;쀀𝔻ƀ;DE͈͉͍䂨ot;惜qual;扐blèCDLRUVͣͲ΂ϏϢϸontourIntegraìȹoɴ͹\0\0ͻ»͉nArrow;懓Āeo·ΤftƀARTΐΖΡrrow;懐ightArrow;懔eåˊngĀLRΫτeftĀARγιrrow;柸ightArrow;柺ightArrow;柹ightĀATϘϞrrow;懒ee;抨pɁϩ\0\0ϯrrow;懑ownArrow;懕erticalBar;戥ǹABLRTaВЪаўѿͼrrowƀ;BUНОТ憓ar;椓pArrow;懵reve;䌑eft˒к\0ц\0ѐightVector;楐eeVector;楞ectorĀ;Bљњ憽ar;楖ightǔѧ\0ѱeeVector;楟ectorĀ;BѺѻ懁ar;楗eeĀ;A҆҇护rrow;憧ĀctҒҗr;쀀𝒟rok;䄐ࠀNTacdfglmopqstuxҽӀӄӋӞӢӧӮӵԡԯԶՒ՝ՠեG;䅊H耻Ð䃐cute耻É䃉ƀaiyӒӗӜron;䄚rc耻Ê䃊;䐭ot;䄖r;쀀𝔈rave耻È䃈ement;戈ĀapӺӾcr;䄒tyɓԆ\0\0ԒmallSquare;旻erySmallSquare;斫ĀgpԦԪon;䄘f;쀀𝔼silon;䎕uĀaiԼՉlĀ;TՂՃ橵ilde;扂librium;懌Āci՗՚r;愰m;橳a;䎗ml耻Ë䃋Āipժկsts;戃onentialE;慇ʀcfiosօֈ֍ֲ׌y;䐤r;쀀𝔉lledɓ֗\0\0֣mallSquare;旼erySmallSquare;斪Ͱֺ\0ֿ\0\0ׄf;쀀𝔽All;戀riertrf;愱cò׋؀JTabcdfgorstר׬ׯ׺؀ؒؖ؛؝أ٬ٲcy;䐃耻>䀾mmaĀ;d׷׸䎓;䏜reve;䄞ƀeiy؇،ؐdil;䄢rc;䄜;䐓ot;䄠r;쀀𝔊;拙pf;쀀𝔾eater̀EFGLSTصلَٖٛ٦qualĀ;Lؾؿ扥ess;招ullEqual;执reater;檢ess;扷lantEqual;橾ilde;扳cr;쀀𝒢;扫ЀAacfiosuڅڋږڛڞڪھۊRDcy;䐪Āctڐڔek;䋇;䁞irc;䄤r;愌lbertSpace;愋ǰگ\0ڲf;愍izontalLine;攀Āctۃۅòکrok;䄦mpńېۘownHumðįqual;扏܀EJOacdfgmnostuۺ۾܃܇܎ܚܞܡܨ݄ݸދޏޕcy;䐕lig;䄲cy;䐁cute耻Í䃍Āiyܓܘrc耻Î䃎;䐘ot;䄰r;愑rave耻Ì䃌ƀ;apܠܯܿĀcgܴܷr;䄪inaryI;慈lieóϝǴ݉\0ݢĀ;eݍݎ戬Āgrݓݘral;戫section;拂isibleĀCTݬݲomma;恣imes;恢ƀgptݿރވon;䄮f;쀀𝕀a;䎙cr;愐ilde;䄨ǫޚ\0ޞcy;䐆l耻Ï䃏ʀcfosuެ޷޼߂ߐĀiyޱ޵rc;䄴;䐙r;쀀𝔍pf;쀀𝕁ǣ߇\0ߌr;쀀𝒥rcy;䐈kcy;䐄΀HJacfosߤߨ߽߬߱ࠂࠈcy;䐥cy;䐌ppa;䎚Āey߶߻dil;䄶;䐚r;쀀𝔎pf;쀀𝕂cr;쀀𝒦րJTaceflmostࠥࠩࠬࡐࡣ঳সে্਷ੇcy;䐉耻<䀼ʀcmnpr࠷࠼ࡁࡄࡍute;䄹bda;䎛g;柪lacetrf;愒r;憞ƀaeyࡗ࡜ࡡron;䄽dil;䄻;䐛Āfsࡨ॰tԀACDFRTUVarࡾࢩࢱࣦ࣠ࣼयज़ΐ४Ānrࢃ࢏gleBracket;柨rowƀ;BR࢙࢚࢞憐ar;懤ightArrow;懆eiling;挈oǵࢷ\0ࣃbleBracket;柦nǔࣈ\0࣒eeVector;楡ectorĀ;Bࣛࣜ懃ar;楙loor;挊ightĀAV࣯ࣵrrow;憔ector;楎Āerँगeƀ;AVउऊऐ抣rrow;憤ector;楚iangleƀ;BEतथऩ抲ar;槏qual;抴pƀDTVषूौownVector;楑eeVector;楠ectorĀ;Bॖॗ憿ar;楘ectorĀ;B॥०憼ar;楒ightáΜs̀EFGLSTॾঋকঝঢভqualGreater;拚ullEqual;扦reater;扶ess;檡lantEqual;橽ilde;扲r;쀀𝔏Ā;eঽা拘ftarrow;懚idot;䄿ƀnpw৔ਖਛgȀLRlr৞৷ਂਐeftĀAR০৬rrow;柵ightArrow;柷ightArrow;柶eftĀarγਊightáοightáϊf;쀀𝕃erĀLRਢਬeftArrow;憙ightArrow;憘ƀchtਾੀੂòࡌ;憰rok;䅁;扪Ѐacefiosuਗ਼੝੠੷੼અઋ઎p;椅y;䐜Ādl੥੯iumSpace;恟lintrf;愳r;쀀𝔐nusPlus;戓pf;쀀𝕄cò੶;䎜ҀJacefostuણધભીଔଙඑ඗ඞcy;䐊cute;䅃ƀaey઴હાron;䅇dil;䅅;䐝ƀgswે૰଎ativeƀMTV૓૟૨ediumSpace;怋hiĀcn૦૘ë૙eryThiî૙tedĀGL૸ଆreaterGreateòٳessLesóੈLine;䀊r;쀀𝔑ȀBnptଢନଷ଺reak;恠BreakingSpace;䂠f;愕ڀ;CDEGHLNPRSTV୕ୖ୪୼஡௫ఄ౞಄ದ೘ൡඅ櫬Āou୛୤ngruent;扢pCap;扭oubleVerticalBar;戦ƀlqxஃஊ஛ement;戉ualĀ;Tஒஓ扠ilde;쀀≂̸ists;戄reater΀;EFGLSTஶஷ஽௉௓௘௥扯qual;扱ullEqual;쀀≧̸reater;쀀≫̸ess;批lantEqual;쀀⩾̸ilde;扵umpń௲௽ownHump;쀀≎̸qual;쀀≏̸eĀfsఊధtTriangleƀ;BEచఛడ拪ar;쀀⧏̸qual;括s̀;EGLSTవశ఼ౄోౘ扮qual;扰reater;扸ess;쀀≪̸lantEqual;쀀⩽̸ilde;扴estedĀGL౨౹reaterGreater;쀀⪢̸essLess;쀀⪡̸recedesƀ;ESಒಓಛ技qual;쀀⪯̸lantEqual;拠ĀeiಫಹverseElement;戌ghtTriangleƀ;BEೋೌ೒拫ar;쀀⧐̸qual;拭ĀquೝഌuareSuĀbp೨೹setĀ;E೰ೳ쀀⊏̸qual;拢ersetĀ;Eഃആ쀀⊐̸qual;拣ƀbcpഓതൎsetĀ;Eഛഞ쀀⊂⃒qual;抈ceedsȀ;ESTലള഻െ抁qual;쀀⪰̸lantEqual;拡ilde;쀀≿̸ersetĀ;E൘൛쀀⊃⃒qual;抉ildeȀ;EFT൮൯൵ൿ扁qual;扄ullEqual;扇ilde;扉erticalBar;戤cr;쀀𝒩ilde耻Ñ䃑;䎝܀Eacdfgmoprstuvලෂ෉෕ෛ෠෧෼ขภยา฿ไlig;䅒cute耻Ó䃓Āiy෎ීrc耻Ô䃔;䐞blac;䅐r;쀀𝔒rave耻Ò䃒ƀaei෮ෲ෶cr;䅌ga;䎩cron;䎟pf;쀀𝕆enCurlyĀDQฎบoubleQuote;怜uote;怘;橔Āclวฬr;쀀𝒪ash耻Ø䃘iŬื฼de耻Õ䃕es;樷ml耻Ö䃖erĀBP๋๠Āar๐๓r;怾acĀek๚๜;揞et;掴arenthesis;揜Ҁacfhilors๿ງຊຏຒດຝະ໼rtialD;戂y;䐟r;쀀𝔓i;䎦;䎠usMinus;䂱Āipຢອncareplanåڝf;愙Ȁ;eio຺ູ໠໤檻cedesȀ;EST່້໏໚扺qual;檯lantEqual;扼ilde;找me;怳Ādp໩໮uct;戏ortionĀ;aȥ໹l;戝Āci༁༆r;쀀𝒫;䎨ȀUfos༑༖༛༟OT耻"䀢r;쀀𝔔pf;愚cr;쀀𝒬؀BEacefhiorsu༾གྷཇའཱིྦྷྪྭ႖ႩႴႾarr;椐G耻®䂮ƀcnrཎནབute;䅔g;柫rĀ;tཛྷཝ憠l;椖ƀaeyཧཬཱron;䅘dil;䅖;䐠Ā;vླྀཹ愜erseĀEUྂྙĀlq྇ྎement;戋uilibrium;懋pEquilibrium;楯r»ཹo;䎡ghtЀACDFTUVa࿁࿫࿳ဢဨၛႇϘĀnr࿆࿒gleBracket;柩rowƀ;BL࿜࿝࿡憒ar;懥eftArrow;懄eiling;按oǵ࿹\0စbleBracket;柧nǔည\0နeeVector;楝ectorĀ;Bဝသ懂ar;楕loor;挋Āerိ၃eƀ;AVဵံြ抢rrow;憦ector;楛iangleƀ;BEၐၑၕ抳ar;槐qual;抵pƀDTVၣၮၸownVector;楏eeVector;楜ectorĀ;Bႂႃ憾ar;楔ectorĀ;B႑႒懀ar;楓Āpuႛ႞f;愝ndImplies;楰ightarrow;懛ĀchႹႼr;愛;憱leDelayed;槴ڀHOacfhimoqstuფჱჷჽᄙᄞᅑᅖᅡᅧᆵᆻᆿĀCcჩხHcy;䐩y;䐨FTcy;䐬cute;䅚ʀ;aeiyᄈᄉᄎᄓᄗ檼ron;䅠dil;䅞rc;䅜;䐡r;쀀𝔖ortȀDLRUᄪᄴᄾᅉownArrow»ОeftArrow»࢚ightArrow»࿝pArrow;憑gma;䎣allCircle;战pf;쀀𝕊ɲᅭ\0\0ᅰt;戚areȀ;ISUᅻᅼᆉᆯ斡ntersection;抓uĀbpᆏᆞsetĀ;Eᆗᆘ抏qual;抑ersetĀ;Eᆨᆩ抐qual;抒nion;抔cr;쀀𝒮ar;拆ȀbcmpᇈᇛሉላĀ;sᇍᇎ拐etĀ;Eᇍᇕqual;抆ĀchᇠህeedsȀ;ESTᇭᇮᇴᇿ扻qual;檰lantEqual;扽ilde;承Tháྌ;我ƀ;esሒሓሣ拑rsetĀ;Eሜም抃qual;抇et»ሓրHRSacfhiorsሾቄ቉ቕ቞ቱቶኟዂወዑORN耻Þ䃞ADE;愢ĀHc቎ቒcy;䐋y;䐦Ābuቚቜ;䀉;䎤ƀaeyብቪቯron;䅤dil;䅢;䐢r;쀀𝔗Āeiቻ኉ǲኀ\0ኇefore;戴a;䎘Ācn኎ኘkSpace;쀀  Space;怉ldeȀ;EFTካኬኲኼ戼qual;扃ullEqual;扅ilde;扈pf;쀀𝕋ipleDot;惛Āctዖዛr;쀀𝒯rok;䅦ૡዷጎጚጦ\0ጬጱ\0\0\0\0\0ጸጽ፷ᎅ\0᏿ᐄᐊᐐĀcrዻጁute耻Ú䃚rĀ;oጇገ憟cir;楉rǣጓ\0጖y;䐎ve;䅬Āiyጞጣrc耻Û䃛;䐣blac;䅰r;쀀𝔘rave耻Ù䃙acr;䅪Ādiፁ፩erĀBPፈ፝Āarፍፐr;䁟acĀekፗፙ;揟et;掵arenthesis;揝onĀ;P፰፱拃lus;抎Āgp፻፿on;䅲f;쀀𝕌ЀADETadps᎕ᎮᎸᏄϨᏒᏗᏳrrowƀ;BDᅐᎠᎤar;椒ownArrow;懅ownArrow;憕quilibrium;楮eeĀ;AᏋᏌ报rrow;憥ownáϳerĀLRᏞᏨeftArrow;憖ightArrow;憗iĀ;lᏹᏺ䏒on;䎥ing;䅮cr;쀀𝒰ilde;䅨ml耻Ü䃜ҀDbcdefosvᐧᐬᐰᐳᐾᒅᒊᒐᒖash;披ar;櫫y;䐒ashĀ;lᐻᐼ抩;櫦Āerᑃᑅ;拁ƀbtyᑌᑐᑺar;怖Ā;iᑏᑕcalȀBLSTᑡᑥᑪᑴar;戣ine;䁼eparator;杘ilde;所ThinSpace;怊r;쀀𝔙pf;쀀𝕍cr;쀀𝒱dash;抪ʀcefosᒧᒬᒱᒶᒼirc;䅴dge;拀r;쀀𝔚pf;쀀𝕎cr;쀀𝒲Ȁfiosᓋᓐᓒᓘr;쀀𝔛;䎞pf;쀀𝕏cr;쀀𝒳ҀAIUacfosuᓱᓵᓹᓽᔄᔏᔔᔚᔠcy;䐯cy;䐇cy;䐮cute耻Ý䃝Āiyᔉᔍrc;䅶;䐫r;쀀𝔜pf;쀀𝕐cr;쀀𝒴ml;䅸ЀHacdefosᔵᔹᔿᕋᕏᕝᕠᕤcy;䐖cute;䅹Āayᕄᕉron;䅽;䐗ot;䅻ǲᕔ\0ᕛoWidtè૙a;䎖r;愨pf;愤cr;쀀𝒵௡ᖃᖊᖐ\0ᖰᖶᖿ\0\0\0\0ᗆᗛᗫᙟ᙭\0ᚕ᚛ᚲᚹ\0ᚾcute耻á䃡reve;䄃̀;Ediuyᖜᖝᖡᖣᖨᖭ戾;쀀∾̳;房rc耻â䃢te肻´̆;䐰lig耻æ䃦Ā;r²ᖺ;쀀𝔞rave耻à䃠ĀepᗊᗖĀfpᗏᗔsym;愵èᗓha;䎱ĀapᗟcĀclᗤᗧr;䄁g;樿ɤᗰ\0\0ᘊʀ;adsvᗺᗻᗿᘁᘇ戧nd;橕;橜lope;橘;橚΀;elmrszᘘᘙᘛᘞᘿᙏᙙ戠;榤e»ᘙsdĀ;aᘥᘦ戡ѡᘰᘲᘴᘶᘸᘺᘼᘾ;榨;榩;榪;榫;榬;榭;榮;榯tĀ;vᙅᙆ戟bĀ;dᙌᙍ抾;榝Āptᙔᙗh;戢»¹arr;捼Āgpᙣᙧon;䄅f;쀀𝕒΀;Eaeiop዁ᙻᙽᚂᚄᚇᚊ;橰cir;橯;扊d;手s;䀧roxĀ;e዁ᚒñᚃing耻å䃥ƀctyᚡᚦᚨr;쀀𝒶;䀪mpĀ;e዁ᚯñʈilde耻ã䃣ml耻ä䃤Āciᛂᛈoninôɲnt;樑ࠀNabcdefiklnoprsu᛭ᛱᜰ᜼ᝃᝈ᝸᝽០៦ᠹᡐᜍ᤽᥈ᥰot;櫭Ācrᛶ᜞kȀcepsᜀᜅᜍᜓong;扌psilon;䏶rime;怵imĀ;e᜚᜛戽q;拍Ŷᜢᜦee;抽edĀ;gᜬᜭ挅e»ᜭrkĀ;t፜᜷brk;掶Āoyᜁᝁ;䐱quo;怞ʀcmprtᝓ᝛ᝡᝤᝨausĀ;eĊĉptyv;榰séᜌnoõēƀahwᝯ᝱ᝳ;䎲;愶een;扬r;쀀𝔟g΀costuvwឍឝឳេ៕៛៞ƀaiuបពរðݠrc;旯p»፱ƀdptឤឨឭot;樀lus;樁imes;樂ɱឹ\0\0ើcup;樆ar;昅riangleĀdu៍្own;施p;斳plus;樄eåᑄåᒭarow;植ƀako៭ᠦᠵĀcn៲ᠣkƀlst៺֫᠂ozenge;槫riangleȀ;dlr᠒᠓᠘᠝斴own;斾eft;旂ight;斸k;搣Ʊᠫ\0ᠳƲᠯ\0ᠱ;斒;斑4;斓ck;斈ĀeoᠾᡍĀ;qᡃᡆ쀀=⃥uiv;쀀≡⃥t;挐Ȁptwxᡙᡞᡧᡬf;쀀𝕓Ā;tᏋᡣom»Ꮜtie;拈؀DHUVbdhmptuvᢅᢖᢪᢻᣗᣛᣬ᣿ᤅᤊᤐᤡȀLRlrᢎᢐᢒᢔ;敗;敔;敖;敓ʀ;DUduᢡᢢᢤᢦᢨ敐;敦;敩;敤;敧ȀLRlrᢳᢵᢷᢹ;敝;敚;敜;教΀;HLRhlrᣊᣋᣍᣏᣑᣓᣕ救;敬;散;敠;敫;敢;敟ox;槉ȀLRlrᣤᣦᣨᣪ;敕;敒;攐;攌ʀ;DUduڽ᣷᣹᣻᣽;敥;敨;攬;攴inus;抟lus;択imes;抠ȀLRlrᤙᤛᤝ᤟;敛;敘;攘;攔΀;HLRhlrᤰᤱᤳᤵᤷ᤻᤹攂;敪;敡;敞;攼;攤;攜Āevģ᥂bar耻¦䂦Ȁceioᥑᥖᥚᥠr;쀀𝒷mi;恏mĀ;e᜚᜜lƀ;bhᥨᥩᥫ䁜;槅sub;柈Ŭᥴ᥾lĀ;e᥹᥺怢t»᥺pƀ;Eeįᦅᦇ;檮Ā;qۜۛೡᦧ\0᧨ᨑᨕᨲ\0ᨷᩐ\0\0᪴\0\0᫁\0\0ᬡᬮ᭍᭒\0᯽\0ᰌƀcpr᦭ᦲ᧝ute;䄇̀;abcdsᦿᧀᧄ᧊᧕᧙戩nd;橄rcup;橉Āau᧏᧒p;橋p;橇ot;橀;쀀∩︀Āeo᧢᧥t;恁îړȀaeiu᧰᧻ᨁᨅǰ᧵\0᧸s;橍on;䄍dil耻ç䃧rc;䄉psĀ;sᨌᨍ橌m;橐ot;䄋ƀdmnᨛᨠᨦil肻¸ƭptyv;榲t脀¢;eᨭᨮ䂢räƲr;쀀𝔠ƀceiᨽᩀᩍy;䑇ckĀ;mᩇᩈ朓ark»ᩈ;䏇r΀;Ecefms᩟᩠ᩢᩫ᪤᪪᪮旋;槃ƀ;elᩩᩪᩭ䋆q;扗eɡᩴ\0\0᪈rrowĀlr᩼᪁eft;憺ight;憻ʀRSacd᪒᪔᪖᪚᪟»ཇ;擈st;抛irc;抚ash;抝nint;樐id;櫯cir;槂ubsĀ;u᪻᪼晣it»᪼ˬ᫇᫔᫺\0ᬊonĀ;eᫍᫎ䀺Ā;qÇÆɭ᫙\0\0᫢aĀ;t᫞᫟䀬;䁀ƀ;fl᫨᫩᫫戁îᅠeĀmx᫱᫶ent»᫩eóɍǧ᫾\0ᬇĀ;dኻᬂot;橭nôɆƀfryᬐᬔᬗ;쀀𝕔oäɔ脀©;sŕᬝr;愗Āaoᬥᬩrr;憵ss;朗Ācuᬲᬷr;쀀𝒸Ābpᬼ᭄Ā;eᭁᭂ櫏;櫑Ā;eᭉᭊ櫐;櫒dot;拯΀delprvw᭠᭬᭷ᮂᮬᯔ᯹arrĀlr᭨᭪;椸;椵ɰ᭲\0\0᭵r;拞c;拟arrĀ;p᭿ᮀ憶;椽̀;bcdosᮏᮐᮖᮡᮥᮨ截rcap;橈Āauᮛᮞp;橆p;橊ot;抍r;橅;쀀∪︀Ȁalrv᮵ᮿᯞᯣrrĀ;mᮼᮽ憷;椼yƀevwᯇᯔᯘqɰᯎ\0\0ᯒreã᭳uã᭵ee;拎edge;拏en耻¤䂤earrowĀlrᯮ᯳eft»ᮀight»ᮽeäᯝĀciᰁᰇoninôǷnt;戱lcty;挭ঀAHabcdefhijlorstuwz᰸᰻᰿ᱝᱩᱵᲊᲞᲬᲷ᳻᳿ᴍᵻᶑᶫᶻ᷆᷍rò΁ar;楥Ȁglrs᱈ᱍ᱒᱔ger;怠eth;愸òᄳhĀ;vᱚᱛ怐»ऊūᱡᱧarow;椏aã̕Āayᱮᱳron;䄏;䐴ƀ;ao̲ᱼᲄĀgrʿᲁr;懊tseq;橷ƀglmᲑᲔᲘ耻°䂰ta;䎴ptyv;榱ĀirᲣᲨsht;楿;쀀𝔡arĀlrᲳᲵ»ࣜ»သʀaegsv᳂͸᳖᳜᳠mƀ;oș᳊᳔ndĀ;ș᳑uit;晦amma;䏝in;拲ƀ;io᳧᳨᳸䃷de脀÷;o᳧ᳰntimes;拇nø᳷cy;䑒cɯᴆ\0\0ᴊrn;挞op;挍ʀlptuwᴘᴝᴢᵉᵕlar;䀤f;쀀𝕕ʀ;emps̋ᴭᴷᴽᵂqĀ;d͒ᴳot;扑inus;戸lus;戔quare;抡blebarwedgåúnƀadhᄮᵝᵧownarrowóᲃarpoonĀlrᵲᵶefôᲴighôᲶŢᵿᶅkaro÷གɯᶊ\0\0ᶎrn;挟op;挌ƀcotᶘᶣᶦĀryᶝᶡ;쀀𝒹;䑕l;槶rok;䄑Ādrᶰᶴot;拱iĀ;fᶺ᠖斿Āah᷀᷃ròЩaòྦangle;榦Āci᷒ᷕy;䑟grarr;柿ऀDacdefglmnopqrstuxḁḉḙḸոḼṉṡṾấắẽỡἪἷὄ὎὚ĀDoḆᴴoôᲉĀcsḎḔute耻é䃩ter;橮ȀaioyḢḧḱḶron;䄛rĀ;cḭḮ扖耻ê䃪lon;払;䑍ot;䄗ĀDrṁṅot;扒;쀀𝔢ƀ;rsṐṑṗ檚ave耻è䃨Ā;dṜṝ檖ot;檘Ȁ;ilsṪṫṲṴ檙nters;揧;愓Ā;dṹṺ檕ot;檗ƀapsẅẉẗcr;䄓tyƀ;svẒẓẕ戅et»ẓpĀ1;ẝẤĳạả;怄;怅怃ĀgsẪẬ;䅋p;怂ĀgpẴẸon;䄙f;쀀𝕖ƀalsỄỎỒrĀ;sỊị拕l;槣us;橱iƀ;lvỚớở䎵on»ớ;䏵ȀcsuvỪỳἋἣĀioữḱrc»Ḯɩỹ\0\0ỻíՈantĀglἂἆtr»ṝess»Ṻƀaeiἒ἖Ἒls;䀽st;扟vĀ;DȵἠD;橸parsl;槥ĀDaἯἳot;打rr;楱ƀcdiἾὁỸr;愯oô͒ĀahὉὋ;䎷耻ð䃰Āmrὓὗl耻ë䃫o;悬ƀcipὡὤὧl;䀡sôծĀeoὬὴctatioîՙnentialåչৡᾒ\0ᾞ\0ᾡᾧ\0\0ῆῌ\0ΐ\0ῦῪ \0 ⁚llingdotseñṄy;䑄male;晀ƀilrᾭᾳ῁lig;耀ﬃɩᾹ\0\0᾽g;耀ﬀig;耀ﬄ;쀀𝔣lig;耀ﬁlig;쀀fjƀaltῙ῜ῡt;晭ig;耀ﬂns;斱of;䆒ǰ΅\0ῳf;쀀𝕗ĀakֿῷĀ;vῼ´拔;櫙artint;樍Āao‌⁕Ācs‑⁒α‚‰‸⁅⁈\0⁐β•‥‧‪‬\0‮耻½䂽;慓耻¼䂼;慕;慙;慛Ƴ‴\0‶;慔;慖ʴ‾⁁\0\0⁃耻¾䂾;慗;慜5;慘ƶ⁌\0⁎;慚;慝8;慞l;恄wn;挢cr;쀀𝒻ࢀEabcdefgijlnorstv₂₉₟₥₰₴⃰⃵⃺⃿℃ℒℸ̗ℾ⅒↞Ā;lٍ₇;檌ƀcmpₐₕ₝ute;䇵maĀ;dₜ᳚䎳;檆reve;䄟Āiy₪₮rc;䄝;䐳ot;䄡Ȁ;lqsؾق₽⃉ƀ;qsؾٌ⃄lanô٥Ȁ;cdl٥⃒⃥⃕c;檩otĀ;o⃜⃝檀Ā;l⃢⃣檂;檄Ā;e⃪⃭쀀⋛︀s;檔r;쀀𝔤Ā;gٳ؛mel;愷cy;䑓Ȁ;Eajٚℌℎℐ;檒;檥;檤ȀEaesℛℝ℩ℴ;扩pĀ;p℣ℤ檊rox»ℤĀ;q℮ℯ檈Ā;q℮ℛim;拧pf;쀀𝕘Āci⅃ⅆr;愊mƀ;el٫ⅎ⅐;檎;檐茀>;cdlqr׮ⅠⅪⅮⅳⅹĀciⅥⅧ;檧r;橺ot;拗Par;榕uest;橼ʀadelsↄⅪ←ٖ↛ǰ↉\0↎proø₞r;楸qĀlqؿ↖lesó₈ií٫Āen↣↭rtneqq;쀀≩︀Å↪ԀAabcefkosy⇄⇇⇱⇵⇺∘∝∯≨≽ròΠȀilmr⇐⇔⇗⇛rsðᒄf»․ilôکĀdr⇠⇤cy;䑊ƀ;cwࣴ⇫⇯ir;楈;憭ar;意irc;䄥ƀalr∁∎∓rtsĀ;u∉∊晥it»∊lip;怦con;抹r;쀀𝔥sĀew∣∩arow;椥arow;椦ʀamopr∺∾≃≞≣rr;懿tht;戻kĀlr≉≓eftarrow;憩ightarrow;憪f;쀀𝕙bar;怕ƀclt≯≴≸r;쀀𝒽asè⇴rok;䄧Ābp⊂⊇ull;恃hen»ᱛૡ⊣\0⊪\0⊸⋅⋎\0⋕⋳\0\0⋸⌢⍧⍢⍿\0⎆⎪⎴cute耻í䃭ƀ;iyݱ⊰⊵rc耻î䃮;䐸Ācx⊼⊿y;䐵cl耻¡䂡ĀfrΟ⋉;쀀𝔦rave耻ì䃬Ȁ;inoܾ⋝⋩⋮Āin⋢⋦nt;樌t;戭fin;槜ta;愩lig;䄳ƀaop⋾⌚⌝ƀcgt⌅⌈⌗r;䄫ƀelpܟ⌏⌓inåގarôܠh;䄱f;抷ed;䆵ʀ;cfotӴ⌬⌱⌽⍁are;愅inĀ;t⌸⌹戞ie;槝doô⌙ʀ;celpݗ⍌⍐⍛⍡al;抺Āgr⍕⍙eróᕣã⍍arhk;樗rod;樼Ȁcgpt⍯⍲⍶⍻y;䑑on;䄯f;쀀𝕚a;䎹uest耻¿䂿Āci⎊⎏r;쀀𝒾nʀ;EdsvӴ⎛⎝⎡ӳ;拹ot;拵Ā;v⎦⎧拴;拳Ā;iݷ⎮lde;䄩ǫ⎸\0⎼cy;䑖l耻ï䃯̀cfmosu⏌⏗⏜⏡⏧⏵Āiy⏑⏕rc;䄵;䐹r;쀀𝔧ath;䈷pf;쀀𝕛ǣ⏬\0⏱r;쀀𝒿rcy;䑘kcy;䑔Ѐacfghjos␋␖␢␧␭␱␵␻ppaĀ;v␓␔䎺;䏰Āey␛␠dil;䄷;䐺r;쀀𝔨reen;䄸cy;䑅cy;䑜pf;쀀𝕜cr;쀀𝓀஀ABEHabcdefghjlmnoprstuv⑰⒁⒆⒍⒑┎┽╚▀♎♞♥♹♽⚚⚲⛘❝❨➋⟀⠁⠒ƀart⑷⑺⑼rò৆òΕail;椛arr;椎Ā;gঔ⒋;檋ar;楢ॣ⒥\0⒪\0⒱\0\0\0\0\0⒵Ⓔ\0ⓆⓈⓍ\0⓹ute;䄺mptyv;榴raîࡌbda;䎻gƀ;dlࢎⓁⓃ;榑åࢎ;檅uo耻«䂫rЀ;bfhlpst࢙ⓞⓦⓩ⓫⓮⓱⓵Ā;f࢝ⓣs;椟s;椝ë≒p;憫l;椹im;楳l;憢ƀ;ae⓿─┄檫il;椙Ā;s┉┊檭;쀀⪭︀ƀabr┕┙┝rr;椌rk;杲Āak┢┬cĀek┨┪;䁻;䁛Āes┱┳;榋lĀdu┹┻;榏;榍Ȁaeuy╆╋╖╘ron;䄾Ādi═╔il;䄼ìࢰâ┩;䐻Ȁcqrs╣╦╭╽a;椶uoĀ;rนᝆĀdu╲╷har;楧shar;楋h;憲ʀ;fgqs▋▌উ◳◿扤tʀahlrt▘▤▷◂◨rrowĀ;t࢙□aé⓶arpoonĀdu▯▴own»њp»०eftarrows;懇ightƀahs◍◖◞rrowĀ;sࣴࢧarpoonó྘quigarro÷⇰hreetimes;拋ƀ;qs▋ও◺lanôবʀ;cdgsব☊☍☝☨c;檨otĀ;o☔☕橿Ā;r☚☛檁;檃Ā;e☢☥쀀⋚︀s;檓ʀadegs☳☹☽♉♋pproøⓆot;拖qĀgq♃♅ôউgtò⒌ôছiíলƀilr♕࣡♚sht;楼;쀀𝔩Ā;Eজ♣;檑š♩♶rĀdu▲♮Ā;l॥♳;楪lk;斄cy;䑙ʀ;achtੈ⚈⚋⚑⚖rò◁orneòᴈard;楫ri;旺Āio⚟⚤dot;䅀ustĀ;a⚬⚭掰che»⚭ȀEaes⚻⚽⛉⛔;扨pĀ;p⛃⛄檉rox»⛄Ā;q⛎⛏檇Ā;q⛎⚻im;拦Ѐabnoptwz⛩⛴⛷✚✯❁❇❐Ānr⛮⛱g;柬r;懽rëࣁgƀlmr⛿✍✔eftĀar০✇ightá৲apsto;柼ightá৽parrowĀlr✥✩efô⓭ight;憬ƀafl✶✹✽r;榅;쀀𝕝us;樭imes;樴š❋❏st;戗áፎƀ;ef❗❘᠀旊nge»❘arĀ;l❤❥䀨t;榓ʀachmt❳❶❼➅➇ròࢨorneòᶌarĀ;d྘➃;業;怎ri;抿̀achiqt➘➝ੀ➢➮➻quo;怹r;쀀𝓁mƀ;egল➪➬;檍;檏Ābu┪➳oĀ;rฟ➹;怚rok;䅂萀<;cdhilqrࠫ⟒☹⟜⟠⟥⟪⟰Āci⟗⟙;檦r;橹reå◲mes;拉arr;楶uest;橻ĀPi⟵⟹ar;榖ƀ;ef⠀भ᠛旃rĀdu⠇⠍shar;楊har;楦Āen⠗⠡rtneqq;쀀≨︀Å⠞܀Dacdefhilnopsu⡀⡅⢂⢎⢓⢠⢥⢨⣚⣢⣤ઃ⣳⤂Dot;戺Ȁclpr⡎⡒⡣⡽r耻¯䂯Āet⡗⡙;時Ā;e⡞⡟朠se»⡟Ā;sျ⡨toȀ;dluျ⡳⡷⡻owîҌefôएðᏑker;斮Āoy⢇⢌mma;権;䐼ash;怔asuredangle»ᘦr;쀀𝔪o;愧ƀcdn⢯⢴⣉ro耻µ䂵Ȁ;acdᑤ⢽⣀⣄sôᚧir;櫰ot肻·Ƶusƀ;bd⣒ᤃ⣓戒Ā;uᴼ⣘;横ţ⣞⣡p;櫛ò−ðઁĀdp⣩⣮els;抧f;쀀𝕞Āct⣸⣽r;쀀𝓂pos»ᖝƀ;lm⤉⤊⤍䎼timap;抸ఀGLRVabcdefghijlmoprstuvw⥂⥓⥾⦉⦘⧚⧩⨕⨚⩘⩝⪃⪕⪤⪨⬄⬇⭄⭿⮮ⰴⱧⱼ⳩Āgt⥇⥋;쀀⋙̸Ā;v⥐௏쀀≫⃒ƀelt⥚⥲⥶ftĀar⥡⥧rrow;懍ightarrow;懎;쀀⋘̸Ā;v⥻ే쀀≪⃒ightarrow;懏ĀDd⦎⦓ash;抯ash;抮ʀbcnpt⦣⦧⦬⦱⧌la»˞ute;䅄g;쀀∠⃒ʀ;Eiop඄⦼⧀⧅⧈;쀀⩰̸d;쀀≋̸s;䅉roø඄urĀ;a⧓⧔普lĀ;s⧓ସǳ⧟\0⧣p肻 ଷmpĀ;e௹ఀʀaeouy⧴⧾⨃⨐⨓ǰ⧹\0⧻;橃on;䅈dil;䅆ngĀ;dൾ⨊ot;쀀⩭̸p;橂;䐽ash;怓΀;Aadqsxஒ⨩⨭⨻⩁⩅⩐rr;懗rĀhr⨳⨶k;椤Ā;oᏲᏰot;쀀≐̸uiöୣĀei⩊⩎ar;椨í஘istĀ;s஠டr;쀀𝔫ȀEest௅⩦⩹⩼ƀ;qs஼⩭௡ƀ;qs஼௅⩴lanô௢ií௪Ā;rஶ⪁»ஷƀAap⪊⪍⪑rò⥱rr;憮ar;櫲ƀ;svྍ⪜ྌĀ;d⪡⪢拼;拺cy;䑚΀AEadest⪷⪺⪾⫂⫅⫶⫹rò⥦;쀀≦̸rr;憚r;急Ȁ;fqs఻⫎⫣⫯tĀar⫔⫙rro÷⫁ightarro÷⪐ƀ;qs఻⪺⫪lanôౕĀ;sౕ⫴»శiíౝĀ;rవ⫾iĀ;eచథiäඐĀpt⬌⬑f;쀀𝕟膀¬;in⬙⬚⬶䂬nȀ;Edvஉ⬤⬨⬮;쀀⋹̸ot;쀀⋵̸ǡஉ⬳⬵;拷;拶iĀ;vಸ⬼ǡಸ⭁⭃;拾;拽ƀaor⭋⭣⭩rȀ;ast୻⭕⭚⭟lleì୻l;쀀⫽⃥;쀀∂̸lint;樔ƀ;ceಒ⭰⭳uåಥĀ;cಘ⭸Ā;eಒ⭽ñಘȀAait⮈⮋⮝⮧rò⦈rrƀ;cw⮔⮕⮙憛;쀀⤳̸;쀀↝̸ghtarrow»⮕riĀ;eೋೖ΀chimpqu⮽⯍⯙⬄୸⯤⯯Ȁ;cerല⯆ഷ⯉uå൅;쀀𝓃ortɭ⬅\0\0⯖ará⭖mĀ;e൮⯟Ā;q൴൳suĀbp⯫⯭å೸åഋƀbcp⯶ⰑⰙȀ;Ees⯿ⰀഢⰄ抄;쀀⫅̸etĀ;eഛⰋqĀ;qണⰀcĀ;eലⰗñസȀ;EesⰢⰣൟⰧ抅;쀀⫆̸etĀ;e൘ⰮqĀ;qൠⰣȀgilrⰽⰿⱅⱇìௗlde耻ñ䃱çృiangleĀlrⱒⱜeftĀ;eచⱚñదightĀ;eೋⱥñ೗Ā;mⱬⱭ䎽ƀ;esⱴⱵⱹ䀣ro;愖p;怇ҀDHadgilrsⲏⲔⲙⲞⲣⲰⲶⳓⳣash;抭arr;椄p;쀀≍⃒ash;抬ĀetⲨⲬ;쀀≥⃒;쀀>⃒nfin;槞ƀAetⲽⳁⳅrr;椂;쀀≤⃒Ā;rⳊⳍ쀀<⃒ie;쀀⊴⃒ĀAtⳘⳜrr;椃rie;쀀⊵⃒im;쀀∼⃒ƀAan⳰⳴ⴂrr;懖rĀhr⳺⳽k;椣Ā;oᏧᏥear;椧ቓ᪕\0\0\0\0\0\0\0\0\0\0\0\0\0ⴭ\0ⴸⵈⵠⵥ⵲ⶄᬇ\0\0ⶍⶫ\0ⷈⷎ\0ⷜ⸙⸫⸾⹃Ācsⴱ᪗ute耻ó䃳ĀiyⴼⵅrĀ;c᪞ⵂ耻ô䃴;䐾ʀabios᪠ⵒⵗǈⵚlac;䅑v;樸old;榼lig;䅓Ācr⵩⵭ir;榿;쀀𝔬ͯ⵹\0\0⵼\0ⶂn;䋛ave耻ò䃲;槁Ābmⶈ෴ar;榵Ȁacitⶕ⶘ⶥⶨrò᪀Āir⶝ⶠr;榾oss;榻nå๒;槀ƀaeiⶱⶵⶹcr;䅍ga;䏉ƀcdnⷀⷅǍron;䎿;榶pf;쀀𝕠ƀaelⷔ⷗ǒr;榷rp;榹΀;adiosvⷪⷫⷮ⸈⸍⸐⸖戨rò᪆Ȁ;efmⷷⷸ⸂⸅橝rĀ;oⷾⷿ愴f»ⷿ耻ª䂪耻º䂺gof;抶r;橖lope;橗;橛ƀclo⸟⸡⸧ò⸁ash耻ø䃸l;折iŬⸯ⸴de耻õ䃵esĀ;aǛ⸺s;樶ml耻ö䃶bar;挽ૡ⹞\0⹽\0⺀⺝\0⺢⺹\0\0⻋ຜ\0⼓\0\0⼫⾼\0⿈rȀ;astЃ⹧⹲຅脀¶;l⹭⹮䂶leìЃɩ⹸\0\0⹻m;櫳;櫽y;䐿rʀcimpt⺋⺏⺓ᡥ⺗nt;䀥od;䀮il;怰enk;怱r;쀀𝔭ƀimo⺨⺰⺴Ā;v⺭⺮䏆;䏕maô੶ne;明ƀ;tv⺿⻀⻈䏀chfork»´;䏖Āau⻏⻟nĀck⻕⻝kĀ;h⇴⻛;愎ö⇴sҀ;abcdemst⻳⻴ᤈ⻹⻽⼄⼆⼊⼎䀫cir;樣ir;樢Āouᵀ⼂;樥;橲n肻±ຝim;樦wo;樧ƀipu⼙⼠⼥ntint;樕f;쀀𝕡nd耻£䂣Ԁ;Eaceinosu່⼿⽁⽄⽇⾁⾉⾒⽾⾶;檳p;檷uå໙Ā;c໎⽌̀;acens່⽙⽟⽦⽨⽾pproø⽃urlyeñ໙ñ໎ƀaes⽯⽶⽺pprox;檹qq;檵im;拨iíໟmeĀ;s⾈ຮ怲ƀEas⽸⾐⽺ð⽵ƀdfp໬⾙⾯ƀals⾠⾥⾪lar;挮ine;挒urf;挓Ā;t໻⾴ï໻rel;抰Āci⿀⿅r;쀀𝓅;䏈ncsp;怈̀fiopsu⿚⋢⿟⿥⿫⿱r;쀀𝔮pf;쀀𝕢rime;恗cr;쀀𝓆ƀaeo⿸〉〓tĀei⿾々rnionóڰnt;樖stĀ;e【】䀿ñἙô༔઀ABHabcdefhilmnoprstux぀けさすムㄎㄫㅇㅢㅲㆎ㈆㈕㈤㈩㉘㉮㉲㊐㊰㊷ƀartぇおがròႳòϝail;検aròᱥar;楤΀cdenqrtとふへみわゔヌĀeuねぱ;쀀∽̱te;䅕iãᅮmptyv;榳gȀ;del࿑らるろ;榒;榥å࿑uo耻»䂻rր;abcfhlpstw࿜ガクシスゼゾダッデナp;極Ā;f࿠ゴs;椠;椳s;椞ë≝ð✮l;楅im;楴l;憣;憝Āaiパフil;椚oĀ;nホボ戶aló༞ƀabrョリヮrò៥rk;杳ĀakンヽcĀekヹ・;䁽;䁝Āes㄂㄄;榌lĀduㄊㄌ;榎;榐Ȁaeuyㄗㄜㄧㄩron;䅙Ādiㄡㄥil;䅗ì࿲âヺ;䑀Ȁclqsㄴㄷㄽㅄa;椷dhar;楩uoĀ;rȎȍh;憳ƀacgㅎㅟངlȀ;ipsླྀㅘㅛႜnåႻarôྩt;断ƀilrㅩဣㅮsht;楽;쀀𝔯ĀaoㅷㆆrĀduㅽㅿ»ѻĀ;l႑ㆄ;楬Ā;vㆋㆌ䏁;䏱ƀgns㆕ㇹㇼht̀ahlrstㆤㆰ㇂㇘㇤㇮rrowĀ;t࿜ㆭaéトarpoonĀduㆻㆿowîㅾp»႒eftĀah㇊㇐rrowó࿪arpoonóՑightarrows;應quigarro÷ニhreetimes;拌g;䋚ingdotseñἲƀahm㈍㈐㈓rò࿪aòՑ;怏oustĀ;a㈞㈟掱che»㈟mid;櫮Ȁabpt㈲㈽㉀㉒Ānr㈷㈺g;柭r;懾rëဃƀafl㉇㉊㉎r;榆;쀀𝕣us;樮imes;樵Āap㉝㉧rĀ;g㉣㉤䀩t;榔olint;樒arò㇣Ȁachq㉻㊀Ⴜ㊅quo;怺r;쀀𝓇Ābu・㊊oĀ;rȔȓƀhir㊗㊛㊠reåㇸmes;拊iȀ;efl㊪ၙᠡ㊫方tri;槎luhar;楨;愞ൡ㋕㋛㋟㌬㌸㍱\0㍺㎤\0\0㏬㏰\0㐨㑈㑚㒭㒱㓊㓱\0㘖\0\0㘳cute;䅛quï➺Ԁ;Eaceinpsyᇭ㋳㋵㋿㌂㌋㌏㌟㌦㌩;檴ǰ㋺\0㋼;檸on;䅡uåᇾĀ;dᇳ㌇il;䅟rc;䅝ƀEas㌖㌘㌛;檶p;檺im;择olint;樓iíሄ;䑁otƀ;be㌴ᵇ㌵担;橦΀Aacmstx㍆㍊㍗㍛㍞㍣㍭rr;懘rĀhr㍐㍒ë∨Ā;oਸ਼਴t耻§䂧i;䀻war;椩mĀin㍩ðnuóñt;朶rĀ;o㍶⁕쀀𝔰Ȁacoy㎂㎆㎑㎠rp;景Āhy㎋㎏cy;䑉;䑈rtɭ㎙\0\0㎜iäᑤaraì⹯耻­䂭Āgm㎨㎴maƀ;fv㎱㎲㎲䏃;䏂Ѐ;deglnprካ㏅㏉㏎㏖㏞㏡㏦ot;橪Ā;q኱ኰĀ;E㏓㏔檞;檠Ā;E㏛㏜檝;檟e;扆lus;樤arr;楲aròᄽȀaeit㏸㐈㐏㐗Āls㏽㐄lsetmé㍪hp;樳parsl;槤Ādlᑣ㐔e;挣Ā;e㐜㐝檪Ā;s㐢㐣檬;쀀⪬︀ƀflp㐮㐳㑂tcy;䑌Ā;b㐸㐹䀯Ā;a㐾㐿槄r;挿f;쀀𝕤aĀdr㑍ЂesĀ;u㑔㑕晠it»㑕ƀcsu㑠㑹㒟Āau㑥㑯pĀ;sᆈ㑫;쀀⊓︀pĀ;sᆴ㑵;쀀⊔︀uĀbp㑿㒏ƀ;esᆗᆜ㒆etĀ;eᆗ㒍ñᆝƀ;esᆨᆭ㒖etĀ;eᆨ㒝ñᆮƀ;afᅻ㒦ְrť㒫ֱ»ᅼaròᅈȀcemt㒹㒾㓂㓅r;쀀𝓈tmîñiì㐕aræᆾĀar㓎㓕rĀ;f㓔ឿ昆Āan㓚㓭ightĀep㓣㓪psiloîỠhé⺯s»⡒ʀbcmnp㓻㕞ሉ㖋㖎Ҁ;Edemnprs㔎㔏㔑㔕㔞㔣㔬㔱㔶抂;櫅ot;檽Ā;dᇚ㔚ot;櫃ult;櫁ĀEe㔨㔪;櫋;把lus;檿arr;楹ƀeiu㔽㕒㕕tƀ;en㔎㕅㕋qĀ;qᇚ㔏eqĀ;q㔫㔨m;櫇Ābp㕚㕜;櫕;櫓c̀;acensᇭ㕬㕲㕹㕻㌦pproø㋺urlyeñᇾñᇳƀaes㖂㖈㌛pproø㌚qñ㌗g;晪ڀ123;Edehlmnps㖩㖬㖯ሜ㖲㖴㗀㗉㗕㗚㗟㗨㗭耻¹䂹耻²䂲耻³䂳;櫆Āos㖹㖼t;檾ub;櫘Ā;dሢ㗅ot;櫄sĀou㗏㗒l;柉b;櫗arr;楻ult;櫂ĀEe㗤㗦;櫌;抋lus;櫀ƀeiu㗴㘉㘌tƀ;enሜ㗼㘂qĀ;qሢ㖲eqĀ;q㗧㗤m;櫈Ābp㘑㘓;櫔;櫖ƀAan㘜㘠㘭rr;懙rĀhr㘦㘨ë∮Ā;oਫ਩war;椪lig耻ß䃟௡㙑㙝㙠ዎ㙳㙹\0㙾㛂\0\0\0\0\0㛛㜃\0㜉㝬\0\0\0㞇ɲ㙖\0\0㙛get;挖;䏄rë๟ƀaey㙦㙫㙰ron;䅥dil;䅣;䑂lrec;挕r;쀀𝔱Ȁeiko㚆㚝㚵㚼ǲ㚋\0㚑eĀ4fኄኁaƀ;sv㚘㚙㚛䎸ym;䏑Ācn㚢㚲kĀas㚨㚮pproø዁im»ኬsðኞĀas㚺㚮ð዁rn耻þ䃾Ǭ̟㛆⋧es膀×;bd㛏㛐㛘䃗Ā;aᤏ㛕r;樱;樰ƀeps㛡㛣㜀á⩍Ȁ;bcf҆㛬㛰㛴ot;挶ir;櫱Ā;o㛹㛼쀀𝕥rk;櫚á㍢rime;怴ƀaip㜏㜒㝤dåቈ΀adempst㜡㝍㝀㝑㝗㝜㝟ngleʀ;dlqr㜰㜱㜶㝀㝂斵own»ᶻeftĀ;e⠀㜾ñम;扜ightĀ;e㊪㝋ñၚot;旬inus;樺lus;樹b;槍ime;樻ezium;揢ƀcht㝲㝽㞁Āry㝷㝻;쀀𝓉;䑆cy;䑛rok;䅧Āio㞋㞎xô᝷headĀlr㞗㞠eftarro÷ࡏightarrow»ཝऀAHabcdfghlmoprstuw㟐㟓㟗㟤㟰㟼㠎㠜㠣㠴㡑㡝㡫㢩㣌㣒㣪㣶ròϭar;楣Ācr㟜㟢ute耻ú䃺òᅐrǣ㟪\0㟭y;䑞ve;䅭Āiy㟵㟺rc耻û䃻;䑃ƀabh㠃㠆㠋ròᎭlac;䅱aòᏃĀir㠓㠘sht;楾;쀀𝔲rave耻ù䃹š㠧㠱rĀlr㠬㠮»ॗ»ႃlk;斀Āct㠹㡍ɯ㠿\0\0㡊rnĀ;e㡅㡆挜r»㡆op;挏ri;旸Āal㡖㡚cr;䅫肻¨͉Āgp㡢㡦on;䅳f;쀀𝕦̀adhlsuᅋ㡸㡽፲㢑㢠ownáᎳarpoonĀlr㢈㢌efô㠭ighô㠯iƀ;hl㢙㢚㢜䏅»ᏺon»㢚parrows;懈ƀcit㢰㣄㣈ɯ㢶\0\0㣁rnĀ;e㢼㢽挝r»㢽op;挎ng;䅯ri;旹cr;쀀𝓊ƀdir㣙㣝㣢ot;拰lde;䅩iĀ;f㜰㣨»᠓Āam㣯㣲rò㢨l耻ü䃼angle;榧ހABDacdeflnoprsz㤜㤟㤩㤭㦵㦸㦽㧟㧤㧨㧳㧹㧽㨁㨠ròϷarĀ;v㤦㤧櫨;櫩asèϡĀnr㤲㤷grt;榜΀eknprst㓣㥆㥋㥒㥝㥤㦖appá␕othinçẖƀhir㓫⻈㥙opô⾵Ā;hᎷ㥢ïㆍĀiu㥩㥭gmá㎳Ābp㥲㦄setneqĀ;q㥽㦀쀀⊊︀;쀀⫋︀setneqĀ;q㦏㦒쀀⊋︀;쀀⫌︀Āhr㦛㦟etá㚜iangleĀlr㦪㦯eft»थight»ၑy;䐲ash»ံƀelr㧄㧒㧗ƀ;beⷪ㧋㧏ar;抻q;扚lip;拮Ābt㧜ᑨaòᑩr;쀀𝔳tré㦮suĀbp㧯㧱»ജ»൙pf;쀀𝕧roð໻tré㦴Ācu㨆㨋r;쀀𝓋Ābp㨐㨘nĀEe㦀㨖»㥾nĀEe㦒㨞»㦐igzag;榚΀cefoprs㨶㨻㩖㩛㩔㩡㩪irc;䅵Ādi㩀㩑Ābg㩅㩉ar;機eĀ;qᗺ㩏;扙erp;愘r;쀀𝔴pf;쀀𝕨Ā;eᑹ㩦atèᑹcr;쀀𝓌ૣណ㪇\0㪋\0㪐㪛\0\0㪝㪨㪫㪯\0\0㫃㫎\0㫘ៜ៟tré៑r;쀀𝔵ĀAa㪔㪗ròσrò৶;䎾ĀAa㪡㪤ròθrò৫að✓is;拻ƀdptឤ㪵㪾Āfl㪺ឩ;쀀𝕩imåឲĀAa㫇㫊ròώròਁĀcq㫒ីr;쀀𝓍Āpt៖㫜ré។Ѐacefiosu㫰㫽㬈㬌㬑㬕㬛㬡cĀuy㫶㫻te耻ý䃽;䑏Āiy㬂㬆rc;䅷;䑋n耻¥䂥r;쀀𝔶cy;䑗pf;쀀𝕪cr;쀀𝓎Ācm㬦㬩y;䑎l耻ÿ䃿Ԁacdefhiosw㭂㭈㭔㭘㭤㭩㭭㭴㭺㮀cute;䅺Āay㭍㭒ron;䅾;䐷ot;䅼Āet㭝㭡træᕟa;䎶r;쀀𝔷cy;䐶grarr;懝pf;쀀𝕫cr;쀀𝓏Ājn㮅㮇;怍j;怌'.split("").map(u=>u.charCodeAt(0))),G0=new Uint16Array("Ȁaglq	\x1Bɭ\0\0p;䀦os;䀧t;䀾t;䀼uot;䀢".split("").map(u=>u.charCodeAt(0)));var pu;const W0=new Map([[0,65533],[128,8364],[130,8218],[131,402],[132,8222],[133,8230],[134,8224],[135,8225],[136,710],[137,8240],[138,352],[139,8249],[140,338],[142,381],[145,8216],[146,8217],[147,8220],[148,8221],[149,8226],[150,8211],[151,8212],[152,732],[153,8482],[154,353],[155,8250],[156,339],[158,382],[159,376]]),J0=(pu=String.fromCodePoint)!==null&&pu!==void 0?pu:function(u){let e="";return u>65535&&(u-=65536,e+=String.fromCharCode(u>>>10&1023|55296),u=56320|u&1023),e+=String.fromCharCode(u),e};function Q0(u){var e;return u>=55296&&u<=57343||u>1114111?65533:(e=W0.get(u))!==null&&e!==void 0?e:u}var w;(function(u){u[u.NUM=35]="NUM",u[u.SEMI=59]="SEMI",u[u.EQUALS=61]="EQUALS",u[u.ZERO=48]="ZERO",u[u.NINE=57]="NINE",u[u.LOWER_A=97]="LOWER_A",u[u.LOWER_F=102]="LOWER_F",u[u.LOWER_X=120]="LOWER_X",u[u.LOWER_Z=122]="LOWER_Z",u[u.UPPER_A=65]="UPPER_A",u[u.UPPER_F=70]="UPPER_F",u[u.UPPER_Z=90]="UPPER_Z"})(w||(w={}));const X0=32;var j;(function(u){u[u.VALUE_LENGTH=49152]="VALUE_LENGTH",u[u.BRANCH_LENGTH=16256]="BRANCH_LENGTH",u[u.JUMP_TABLE=127]="JUMP_TABLE"})(j||(j={}));function yu(u){return u>=w.ZERO&&u<=w.NINE}function K0(u){return u>=w.UPPER_A&&u<=w.UPPER_F||u>=w.LOWER_A&&u<=w.LOWER_F}function Y0(u){return u>=w.UPPER_A&&u<=w.UPPER_Z||u>=w.LOWER_A&&u<=w.LOWER_Z||yu(u)}function ue(u){return u===w.EQUALS||Y0(u)}var F;(function(u){u[u.EntityStart=0]="EntityStart",u[u.NumericStart=1]="NumericStart",u[u.NumericDecimal=2]="NumericDecimal",u[u.NumericHex=3]="NumericHex",u[u.NamedEntity=4]="NamedEntity"})(F||(F={}));var N;(function(u){u[u.Legacy=0]="Legacy",u[u.Strict=1]="Strict",u[u.Attribute=2]="Attribute"})(N||(N={}));class ee{constructor(e,t,r){this.decodeTree=e,this.emitCodePoint=t,this.errors=r,this.state=F.EntityStart,this.consumed=1,this.result=0,this.treeIndex=0,this.excess=1,this.decodeMode=N.Strict}startEntity(e){this.decodeMode=e,this.state=F.EntityStart,this.result=0,this.treeIndex=0,this.excess=1,this.consumed=1}write(e,t){switch(this.state){case F.EntityStart:return e.charCodeAt(t)===w.NUM?(this.state=F.NumericStart,this.consumed+=1,this.stateNumericStart(e,t+1)):(this.state=F.NamedEntity,this.stateNamedEntity(e,t));case F.NumericStart:return this.stateNumericStart(e,t);case F.NumericDecimal:return this.stateNumericDecimal(e,t);case F.NumericHex:return this.stateNumericHex(e,t);case F.NamedEntity:return this.stateNamedEntity(e,t)}}stateNumericStart(e,t){return t>=e.length?-1:(e.charCodeAt(t)|X0)===w.LOWER_X?(this.state=F.NumericHex,this.consumed+=1,this.stateNumericHex(e,t+1)):(this.state=F.NumericDecimal,this.stateNumericDecimal(e,t))}addToNumericResult(e,t,r,n){if(t!==r){const c=r-t;this.result=this.result*Math.pow(n,c)+parseInt(e.substr(t,c),n),this.consumed+=c}}stateNumericHex(e,t){const r=t;for(;t<e.length;){const n=e.charCodeAt(t);if(yu(n)||K0(n))t+=1;else return this.addToNumericResult(e,r,t,16),this.emitNumericEntity(n,3)}return this.addToNumericResult(e,r,t,16),-1}stateNumericDecimal(e,t){const r=t;for(;t<e.length;){const n=e.charCodeAt(t);if(yu(n))t+=1;else return this.addToNumericResult(e,r,t,10),this.emitNumericEntity(n,2)}return this.addToNumericResult(e,r,t,10),-1}emitNumericEntity(e,t){var r;if(this.consumed<=t)return(r=this.errors)===null||r===void 0||r.absenceOfDigitsInNumericCharacterReference(this.consumed),0;if(e===w.SEMI)this.consumed+=1;else if(this.decodeMode===N.Strict)return 0;return this.emitCodePoint(Q0(this.result),this.consumed),this.errors&&(e!==w.SEMI&&this.errors.missingSemicolonAfterCharacterReference(),this.errors.validateNumericCharacterReference(this.result)),this.consumed}stateNamedEntity(e,t){const{decodeTree:r}=this;let n=r[this.treeIndex],c=(n&j.VALUE_LENGTH)>>14;for(;t<e.length;t++,this.excess++){const i=e.charCodeAt(t);if(this.treeIndex=te(r,n,this.treeIndex+Math.max(1,c),i),this.treeIndex<0)return this.result===0||this.decodeMode===N.Attribute&&(c===0||ue(i))?0:this.emitNotTerminatedNamedEntity();if(n=r[this.treeIndex],c=(n&j.VALUE_LENGTH)>>14,c!==0){if(i===w.SEMI)return this.emitNamedEntityData(this.treeIndex,c,this.consumed+this.excess);this.decodeMode!==N.Strict&&(this.result=this.treeIndex,this.consumed+=this.excess,this.excess=0)}}return-1}emitNotTerminatedNamedEntity(){var e;const{result:t,decodeTree:r}=this,n=(r[t]&j.VALUE_LENGTH)>>14;return this.emitNamedEntityData(t,n,this.consumed),(e=this.errors)===null||e===void 0||e.missingSemicolonAfterCharacterReference(),this.consumed}emitNamedEntityData(e,t,r){const{decodeTree:n}=this;return this.emitCodePoint(t===1?n[e]&~j.VALUE_LENGTH:n[e+1],r),t===3&&this.emitCodePoint(n[e+2],r),r}end(){var e;switch(this.state){case F.NamedEntity:return this.result!==0&&(this.decodeMode!==N.Attribute||this.result===this.treeIndex)?this.emitNotTerminatedNamedEntity():0;case F.NumericDecimal:return this.emitNumericEntity(0,2);case F.NumericHex:return this.emitNumericEntity(0,3);case F.NumericStart:return(e=this.errors)===null||e===void 0||e.absenceOfDigitsInNumericCharacterReference(this.consumed),0;case F.EntityStart:return 0}}}function a0(u){let e="";const t=new ee(u,r=>e+=J0(r));return function(n,c){let i=0,o=0;for(;(o=n.indexOf("&",o))>=0;){e+=n.slice(i,o),t.startEntity(c);const s=t.write(n,o+1);if(s<0){i=o+t.end();break}i=o+s,o=s===0?i+1:i}const a=e+n.slice(i);return e="",a}}function te(u,e,t,r){const n=(e&j.BRANCH_LENGTH)>>7,c=e&j.JUMP_TABLE;if(n===0)return c!==0&&r===c?t:-1;if(c){const a=r-c;return a<0||a>=n?-1:u[t+a]-1}let i=t,o=i+n-1;for(;i<=o;){const a=i+o>>>1,s=u[a];if(s<r)i=a+1;else if(s>r)o=a-1;else return u[a+n]}return-1}const re=a0(V0);a0(G0);function s0(u,e=N.Legacy){return re(u,e)}function ne(u){return Object.prototype.toString.call(u)}function Mu(u){return ne(u)==="[object String]"}const ce=Object.prototype.hasOwnProperty;function ie(u,e){return ce.call(u,e)}function fu(u){return Array.prototype.slice.call(arguments,1).forEach(function(t){if(t){if(typeof t!="object")throw new TypeError(t+"must be object");Object.keys(t).forEach(function(r){u[r]=t[r]})}}),u}function l0(u,e,t){return[].concat(u.slice(0,e),t,u.slice(e+1))}function Bu(u){return!(u>=55296&&u<=57343||u>=64976&&u<=65007||(u&65535)===65535||(u&65535)===65534||u>=0&&u<=8||u===11||u>=14&&u<=31||u>=127&&u<=159||u>1114111)}function su(u){if(u>65535){u-=65536;const e=55296+(u>>10),t=56320+(u&1023);return String.fromCharCode(e,t)}return String.fromCharCode(u)}const f0=/\\([!"#$%&'()*+,\-./:;<=>?@[\\\]^_`{|}~])/g,oe=/&([a-z#][a-z0-9]{1,31});/gi,ae=new RegExp(f0.source+"|"+oe.source,"gi"),se=/^#((?:x[a-f0-9]{1,8}|[0-9]{1,8}))$/i;function le(u,e){if(e.charCodeAt(0)===35&&se.test(e)){const r=e[1].toLowerCase()==="x"?parseInt(e.slice(2),16):parseInt(e.slice(1),10);return Bu(r)?su(r):u}const t=s0(u);return t!==u?t:u}function fe(u){return u.indexOf("\\")<0?u:u.replace(f0,"$1")}function Q(u){return u.indexOf("\\")<0&&u.indexOf("&")<0?u:u.replace(ae,function(e,t,r){return t||le(e,r)})}const de=/[&<>"]/,xe=/[&<>"]/g,he={"&":"&amp;","<":"&lt;",">":"&gt;",'"':"&quot;"};function pe(u){return he[u]}function $(u){return de.test(u)?u.replace(xe,pe):u}const be=/[.?*+^$[\]\\(){}|-]/g;function me(u){return u.replace(be,"\\$&")}function A(u){switch(u){case 9:case 32:return!0}return!1}function K(u){if(u>=8192&&u<=8202)return!0;switch(u){case 9:case 10:case 11:case 12:case 13:case 32:case 160:case 5760:case 8239:case 8287:case 12288:return!0}return!1}function Y(u){return zu.test(u)||i0.test(u)}function uu(u){switch(u){case 33:case 34:case 35:case 36:case 37:case 38:case 39:case 40:case 41:case 42:case 43:case 44:case 45:case 46:case 47:case 58:case 59:case 60:case 61:case 62:case 63:case 64:case 91:case 92:case 93:case 94:case 95:case 96:case 123:case 124:case 125:case 126:return!0;default:return!1}}function du(u){return u=u.trim().replace(/\s+/g," "),"ẞ".toLowerCase()==="Ṿ"&&(u=u.replace(/ẞ/g,"ß")),u.toLowerCase().toUpperCase()}const _e={mdurl:U0,ucmicro:Z0},ge=Object.freeze(Object.defineProperty({__proto__:null,arrayReplaceAt:l0,assign:fu,escapeHtml:$,escapeRE:me,fromCodePoint:su,has:ie,isMdAsciiPunct:uu,isPunctChar:Y,isSpace:A,isString:Mu,isValidEntityCode:Bu,isWhiteSpace:K,lib:_e,normalizeReference:du,unescapeAll:Q,unescapeMd:fe},Symbol.toStringTag,{value:"Module"}));function ke(u,e,t){let r,n,c,i;const o=u.posMax,a=u.pos;for(u.pos=e+1,r=1;u.pos<o;){if(c=u.src.charCodeAt(u.pos),c===93&&(r--,r===0)){n=!0;break}if(i=u.pos,u.md.inline.skipToken(u),c===91){if(i===u.pos-1)r++;else if(t)return u.pos=a,-1}}let s=-1;return n&&(s=u.pos),u.pos=a,s}function De(u,e,t){let r,n=e;const c={ok:!1,pos:0,str:""};if(u.charCodeAt(n)===60){for(n++;n<t;){if(r=u.charCodeAt(n),r===10||r===60)return c;if(r===62)return c.pos=n+1,c.str=Q(u.slice(e+1,n)),c.ok=!0,c;if(r===92&&n+1<t){n+=2;continue}n++}return c}let i=0;for(;n<t&&(r=u.charCodeAt(n),!(r===32||r<32||r===127));){if(r===92&&n+1<t){if(u.charCodeAt(n+1)===32)break;n+=2;continue}if(r===40&&(i++,i>32))return c;if(r===41){if(i===0)break;i--}n++}return e===n||i!==0||(c.str=Q(u.slice(e,n)),c.pos=n,c.ok=!0),c}function ye(u,e,t,r){let n,c=e;const i={ok:!1,can_continue:!1,pos:0,str:"",marker:0};if(r)i.str=r.str,i.marker=r.marker;else{if(c>=t)return i;let o=u.charCodeAt(c);if(o!==34&&o!==39&&o!==40)return i;e++,c++,o===40&&(o=41),i.marker=o}for(;c<t;){if(n=u.charCodeAt(c),n===i.marker)return i.pos=c+1,i.str+=Q(u.slice(e,c)),i.ok=!0,i;if(n===40&&i.marker===41)return i;n===92&&c+1<t&&c++,c++}return i.can_continue=!0,i.str+=Q(u.slice(e,c)),i}const Ce=Object.freeze(Object.defineProperty({__proto__:null,parseLinkDestination:De,parseLinkLabel:ke,parseLinkTitle:ye},Symbol.toStringTag,{value:"Module"})),O={};O.code_inline=function(u,e,t,r,n){const c=u[e];return"<code"+n.renderAttrs(c)+">"+$(c.content)+"</code>"};O.code_block=function(u,e,t,r,n){const c=u[e];return"<pre"+n.renderAttrs(c)+"><code>"+$(u[e].content)+`</code></pre>
`};O.fence=function(u,e,t,r,n){const c=u[e],i=c.info?Q(c.info).trim():"";let o="",a="";if(i){const l=i.split(/(\s+)/g);o=l[0],a=l.slice(2).join("")}let s;if(t.highlight?s=t.highlight(c.content,o,a)||$(c.content):s=$(c.content),s.indexOf("<pre")===0)return s+`
`;if(i){const l=c.attrIndex("class"),f=c.attrs?c.attrs.slice():[];l<0?f.push(["class",t.langPrefix+o]):(f[l]=f[l].slice(),f[l][1]+=" "+t.langPrefix+o);const x={attrs:f};return`<pre><code${n.renderAttrs(x)}>${s}</code></pre>
`}return`<pre><code${n.renderAttrs(c)}>${s}</code></pre>
`};O.image=function(u,e,t,r,n){const c=u[e];return c.attrs[c.attrIndex("alt")][1]=n.renderInlineAsText(c.children,t,r),n.renderToken(u,e,t)};O.hardbreak=function(u,e,t){return t.xhtmlOut?`<br />
`:`<br>
`};O.softbreak=function(u,e,t){return t.breaks?t.xhtmlOut?`<br />
`:`<br>
`:`
`};O.text=function(u,e){return $(u[e].content)};O.html_block=function(u,e){return u[e].content};O.html_inline=function(u,e){return u[e].content};function X(){this.rules=fu({},O)}X.prototype.renderAttrs=function(e){let t,r,n;if(!e.attrs)return"";for(n="",t=0,r=e.attrs.length;t<r;t++)n+=" "+$(e.attrs[t][0])+'="'+$(e.attrs[t][1])+'"';return n};X.prototype.renderToken=function(e,t,r){const n=e[t];let c="";if(n.hidden)return"";n.block&&n.nesting!==-1&&t&&e[t-1].hidden&&(c+=`
`),c+=(n.nesting===-1?"</":"<")+n.tag,c+=this.renderAttrs(n),n.nesting===0&&r.xhtmlOut&&(c+=" /");let i=!1;if(n.block&&(i=!0,n.nesting===1&&t+1<e.length)){const o=e[t+1];(o.type==="inline"||o.hidden||o.nesting===-1&&o.tag===n.tag)&&(i=!1)}return c+=i?`>
`:">",c};X.prototype.renderInline=function(u,e,t){let r="";const n=this.rules;for(let c=0,i=u.length;c<i;c++){const o=u[c].type;typeof n[o]<"u"?r+=n[o](u,c,e,t,this):r+=this.renderToken(u,c,e)}return r};X.prototype.renderInlineAsText=function(u,e,t){let r="";for(let n=0,c=u.length;n<c;n++)switch(u[n].type){case"text":r+=u[n].content;break;case"image":r+=this.renderInlineAsText(u[n].children,e,t);break;case"html_inline":case"html_block":r+=u[n].content;break;case"softbreak":case"hardbreak":r+=`
`;break}return r};X.prototype.render=function(u,e,t){let r="";const n=this.rules;for(let c=0,i=u.length;c<i;c++){const o=u[c].type;o==="inline"?r+=this.renderInline(u[c].children,e,t):typeof n[o]<"u"?r+=n[o](u,c,e,t,this):r+=this.renderToken(u,c,e,t)}return r};function v(){this.__rules__=[],this.__cache__=null}v.prototype.__find__=function(u){for(let e=0;e<this.__rules__.length;e++)if(this.__rules__[e].name===u)return e;return-1};v.prototype.__compile__=function(){const u=this,e=[""];u.__rules__.forEach(function(t){t.enabled&&t.alt.forEach(function(r){e.indexOf(r)<0&&e.push(r)})}),u.__cache__={},e.forEach(function(t){u.__cache__[t]=[],u.__rules__.forEach(function(r){r.enabled&&(t&&r.alt.indexOf(t)<0||u.__cache__[t].push(r.fn))})})};v.prototype.at=function(u,e,t){const r=this.__find__(u),n=t||{};if(r===-1)throw new Error("Parser rule not found: "+u);this.__rules__[r].fn=e,this.__rules__[r].alt=n.alt||[],this.__cache__=null};v.prototype.before=function(u,e,t,r){const n=this.__find__(u),c=r||{};if(n===-1)throw new Error("Parser rule not found: "+u);this.__rules__.splice(n,0,{name:e,enabled:!0,fn:t,alt:c.alt||[]}),this.__cache__=null};v.prototype.after=function(u,e,t,r){const n=this.__find__(u),c=r||{};if(n===-1)throw new Error("Parser rule not found: "+u);this.__rules__.splice(n+1,0,{name:e,enabled:!0,fn:t,alt:c.alt||[]}),this.__cache__=null};v.prototype.push=function(u,e,t){const r=t||{};this.__rules__.push({name:u,enabled:!0,fn:e,alt:r.alt||[]}),this.__cache__=null};v.prototype.enable=function(u,e){Array.isArray(u)||(u=[u]);const t=[];return u.forEach(function(r){const n=this.__find__(r);if(n<0){if(e)return;throw new Error("Rules manager: invalid rule name "+r)}this.__rules__[n].enabled=!0,t.push(r)},this),this.__cache__=null,t};v.prototype.enableOnly=function(u,e){Array.isArray(u)||(u=[u]),this.__rules__.forEach(function(t){t.enabled=!1}),this.enable(u,e)};v.prototype.disable=function(u,e){Array.isArray(u)||(u=[u]);const t=[];return u.forEach(function(r){const n=this.__find__(r);if(n<0){if(e)return;throw new Error("Rules manager: invalid rule name "+r)}this.__rules__[n].enabled=!1,t.push(r)},this),this.__cache__=null,t};v.prototype.getRules=function(u){return this.__cache__===null&&this.__compile__(),this.__cache__[u]||[]};function M(u,e,t){this.type=u,this.tag=e,this.attrs=null,this.map=null,this.nesting=t,this.level=0,this.children=null,this.content="",this.markup="",this.info="",this.meta=null,this.block=!1,this.hidden=!1}M.prototype.attrIndex=function(e){if(!this.attrs)return-1;const t=this.attrs;for(let r=0,n=t.length;r<n;r++)if(t[r][0]===e)return r;return-1};M.prototype.attrPush=function(e){this.attrs?this.attrs.push(e):this.attrs=[e]};M.prototype.attrSet=function(e,t){const r=this.attrIndex(e),n=[e,t];r<0?this.attrPush(n):this.attrs[r]=n};M.prototype.attrGet=function(e){const t=this.attrIndex(e);let r=null;return t>=0&&(r=this.attrs[t][1]),r};M.prototype.attrJoin=function(e,t){const r=this.attrIndex(e);r<0?this.attrPush([e,t]):this.attrs[r][1]=this.attrs[r][1]+" "+t};function d0(u,e,t){this.src=u,this.env=t,this.tokens=[],this.inlineMode=!1,this.md=e}d0.prototype.Token=M;const Ee=/\r\n?|\n/g,Ae=/\0/g;function Fe(u){let e;e=u.src.replace(Ee,`
`),e=e.replace(Ae,"�"),u.src=e}function we(u){let e;u.inlineMode?(e=new u.Token("inline","",0),e.content=u.src,e.map=[0,1],e.children=[],u.tokens.push(e)):u.md.block.parse(u.src,u.md,u.env,u.tokens)}function ve(u){const e=u.tokens;for(let t=0,r=e.length;t<r;t++){const n=e[t];n.type==="inline"&&u.md.inline.parse(n.content,u.md,u.env,n.children)}}function Se(u){return/^<a[>\s]/i.test(u)}function Te(u){return/^<\/a\s*>/i.test(u)}function ze(u){const e=u.tokens;if(u.md.options.linkify)for(let t=0,r=e.length;t<r;t++){if(e[t].type!=="inline"||!u.md.linkify.pretest(e[t].content))continue;let n=e[t].children,c=0;for(let i=n.length-1;i>=0;i--){const o=n[i];if(o.type==="link_close"){for(i--;n[i].level!==o.level&&n[i].type!=="link_open";)i--;continue}if(o.type==="html_inline"&&(Se(o.content)&&c>0&&c--,Te(o.content)&&c++),!(c>0)&&o.type==="text"&&u.md.linkify.test(o.content)){const a=o.content;let s=u.md.linkify.match(a);const l=[];let f=o.level,x=0;s.length>0&&s[0].index===0&&i>0&&n[i-1].type==="text_special"&&(s=s.slice(1));for(let h=0;h<s.length;h++){const d=s[h].url,b=u.md.normalizeLink(d);if(!u.md.validateLink(b))continue;let p=s[h].text;s[h].schema?s[h].schema==="mailto:"&&!/^mailto:/i.test(p)?p=u.md.normalizeLinkText("mailto:"+p).replace(/^mailto:/,""):p=u.md.normalizeLinkText(p):p=u.md.normalizeLinkText("http://"+p).replace(/^http:\/\//,"");const D=s[h].index;if(D>x){const k=new u.Token("text","",0);k.content=a.slice(x,D),k.level=f,l.push(k)}const m=new u.Token("link_open","a",1);m.attrs=[["href",b]],m.level=f++,m.markup="linkify",m.info="auto",l.push(m);const _=new u.Token("text","",0);_.content=p,_.level=f,l.push(_);const g=new u.Token("link_close","a",-1);g.level=--f,g.markup="linkify",g.info="auto",l.push(g),x=s[h].lastIndex}if(x<a.length){const h=new u.Token("text","",0);h.content=a.slice(x),h.level=f,l.push(h)}e[t].children=n=l0(n,i,l)}}}}const x0=/\+-|\.\.|\?\?\?\?|!!!!|,,|--/,Me=/\((c|tm|r)\)/i,Be=/\((c|tm|r)\)/ig,Ie={c:"©",r:"®",tm:"™"};function Re(u,e){return Ie[e.toLowerCase()]}function qe(u){let e=0;for(let t=u.length-1;t>=0;t--){const r=u[t];r.type==="text"&&!e&&(r.content=r.content.replace(Be,Re)),r.type==="link_open"&&r.info==="auto"&&e--,r.type==="link_close"&&r.info==="auto"&&e++}}function Oe(u){let e=0;for(let t=u.length-1;t>=0;t--){const r=u[t];r.type==="text"&&!e&&x0.test(r.content)&&(r.content=r.content.replace(/\+-/g,"±").replace(/\.{2,}/g,"…").replace(/([?!])…/g,"$1..").replace(/([?!]){4,}/g,"$1$1$1").replace(/,{2,}/g,",").replace(/(^|[^-])---(?=[^-]|$)/mg,"$1—").replace(/(^|\s)--(?=\s|$)/mg,"$1–").replace(/(^|[^-\s])--(?=[^-\s]|$)/mg,"$1–")),r.type==="link_open"&&r.info==="auto"&&e--,r.type==="link_close"&&r.info==="auto"&&e++}}function Pe(u){let e;if(u.md.options.typographer)for(e=u.tokens.length-1;e>=0;e--)u.tokens[e].type==="inline"&&(Me.test(u.tokens[e].content)&&qe(u.tokens[e].children),x0.test(u.tokens[e].content)&&Oe(u.tokens[e].children))}const Le=/['"]/,Zu=/['"]/g,Vu="’";function cu(u,e,t){return u.slice(0,e)+t+u.slice(e+1)}function Ne(u,e){let t;const r=[];for(let n=0;n<u.length;n++){const c=u[n],i=u[n].level;for(t=r.length-1;t>=0&&!(r[t].level<=i);t--);if(r.length=t+1,c.type!=="text")continue;let o=c.content,a=0,s=o.length;u:for(;a<s;){Zu.lastIndex=a;const l=Zu.exec(o);if(!l)break;let f=!0,x=!0;a=l.index+1;const h=l[0]==="'";let d=32;if(l.index-1>=0)d=o.charCodeAt(l.index-1);else for(t=n-1;t>=0&&!(u[t].type==="softbreak"||u[t].type==="hardbreak");t--)if(u[t].content){d=u[t].content.charCodeAt(u[t].content.length-1);break}let b=32;if(a<s)b=o.charCodeAt(a);else for(t=n+1;t<u.length&&!(u[t].type==="softbreak"||u[t].type==="hardbreak");t++)if(u[t].content){b=u[t].content.charCodeAt(0);break}const p=uu(d)||Y(String.fromCharCode(d)),D=uu(b)||Y(String.fromCharCode(b)),m=K(d),_=K(b);if(_?f=!1:D&&(m||p||(f=!1)),m?x=!1:p&&(_||D||(x=!1)),b===34&&l[0]==='"'&&d>=48&&d<=57&&(x=f=!1),f&&x&&(f=p,x=D),!f&&!x){h&&(c.content=cu(c.content,l.index,Vu));continue}if(x)for(t=r.length-1;t>=0;t--){let g=r[t];if(r[t].level<i)break;if(g.single===h&&r[t].level===i){g=r[t];let k,C;h?(k=e.md.options.quotes[2],C=e.md.options.quotes[3]):(k=e.md.options.quotes[0],C=e.md.options.quotes[1]),c.content=cu(c.content,l.index,C),u[g.token].content=cu(u[g.token].content,g.pos,k),a+=C.length-1,g.token===n&&(a+=k.length-1),o=c.content,s=o.length,r.length=t;continue u}}f?r.push({token:n,pos:l.index,single:h,level:i}):x&&h&&(c.content=cu(c.content,l.index,Vu))}}}function je(u){if(u.md.options.typographer)for(let e=u.tokens.length-1;e>=0;e--)u.tokens[e].type!=="inline"||!Le.test(u.tokens[e].content)||Ne(u.tokens[e].children,u)}function $e(u){let e,t;const r=u.tokens,n=r.length;for(let c=0;c<n;c++){if(r[c].type!=="inline")continue;const i=r[c].children,o=i.length;for(e=0;e<o;e++)i[e].type==="text_special"&&(i[e].type="text");for(e=t=0;e<o;e++)i[e].type==="text"&&e+1<o&&i[e+1].type==="text"?i[e+1].content=i[e].content+i[e+1].content:(e!==t&&(i[t]=i[e]),t++);e!==t&&(i.length=t)}}const bu=[["normalize",Fe],["block",we],["inline",ve],["linkify",ze],["replacements",Pe],["smartquotes",je],["text_join",$e]];function Iu(){this.ruler=new v;for(let u=0;u<bu.length;u++)this.ruler.push(bu[u][0],bu[u][1])}Iu.prototype.process=function(u){const e=this.ruler.getRules("");for(let t=0,r=e.length;t<r;t++)e[t](u)};Iu.prototype.State=d0;function P(u,e,t,r){this.src=u,this.md=e,this.env=t,this.tokens=r,this.bMarks=[],this.eMarks=[],this.tShift=[],this.sCount=[],this.bsCount=[],this.blkIndent=0,this.line=0,this.lineMax=0,this.tight=!1,this.ddIndent=-1,this.listIndent=-1,this.parentType="root",this.level=0;const n=this.src;for(let c=0,i=0,o=0,a=0,s=n.length,l=!1;i<s;i++){const f=n.charCodeAt(i);if(!l)if(A(f)){o++,f===9?a+=4-a%4:a++;continue}else l=!0;(f===10||i===s-1)&&(f!==10&&i++,this.bMarks.push(c),this.eMarks.push(i),this.tShift.push(o),this.sCount.push(a),this.bsCount.push(0),l=!1,o=0,a=0,c=i+1)}this.bMarks.push(n.length),this.eMarks.push(n.length),this.tShift.push(0),this.sCount.push(0),this.bsCount.push(0),this.lineMax=this.bMarks.length-1}P.prototype.push=function(u,e,t){const r=new M(u,e,t);return r.block=!0,t<0&&this.level--,r.level=this.level,t>0&&this.level++,this.tokens.push(r),r};P.prototype.isEmpty=function(e){return this.bMarks[e]+this.tShift[e]>=this.eMarks[e]};P.prototype.skipEmptyLines=function(e){for(let t=this.lineMax;e<t&&!(this.bMarks[e]+this.tShift[e]<this.eMarks[e]);e++);return e};P.prototype.skipSpaces=function(e){for(let t=this.src.length;e<t;e++){const r=this.src.charCodeAt(e);if(!A(r))break}return e};P.prototype.skipSpacesBack=function(e,t){if(e<=t)return e;for(;e>t;)if(!A(this.src.charCodeAt(--e)))return e+1;return e};P.prototype.skipChars=function(e,t){for(let r=this.src.length;e<r&&this.src.charCodeAt(e)===t;e++);return e};P.prototype.skipCharsBack=function(e,t,r){if(e<=r)return e;for(;e>r;)if(t!==this.src.charCodeAt(--e))return e+1;return e};P.prototype.getLines=function(e,t,r,n){if(e>=t)return"";const c=new Array(t-e);for(let i=0,o=e;o<t;o++,i++){let a=0;const s=this.bMarks[o];let l=s,f;for(o+1<t||n?f=this.eMarks[o]+1:f=this.eMarks[o];l<f&&a<r;){const x=this.src.charCodeAt(l);if(A(x))x===9?a+=4-(a+this.bsCount[o])%4:a++;else if(l-s<this.tShift[o])a++;else break;l++}a>r?c[i]=new Array(a-r+1).join(" ")+this.src.slice(l,f):c[i]=this.src.slice(l,f)}return c.join("")};P.prototype.Token=M;const Ue=65536;function mu(u,e){const t=u.bMarks[e]+u.tShift[e],r=u.eMarks[e];return u.src.slice(t,r)}function Gu(u){const e=[],t=u.length;let r=0,n=u.charCodeAt(r),c=!1,i=0,o="";for(;r<t;)n===124&&(c?(o+=u.substring(i,r-1),i=r):(e.push(o+u.substring(i,r)),o="",i=r+1)),c=n===92,r++,n=u.charCodeAt(r);return e.push(o+u.substring(i)),e}function He(u,e,t,r){if(e+2>t)return!1;let n=e+1;if(u.sCount[n]<u.blkIndent||u.sCount[n]-u.blkIndent>=4)return!1;let c=u.bMarks[n]+u.tShift[n];if(c>=u.eMarks[n])return!1;const i=u.src.charCodeAt(c++);if(i!==124&&i!==45&&i!==58||c>=u.eMarks[n])return!1;const o=u.src.charCodeAt(c++);if(o!==124&&o!==45&&o!==58&&!A(o)||i===45&&A(o))return!1;for(;c<u.eMarks[n];){const g=u.src.charCodeAt(c);if(g!==124&&g!==45&&g!==58&&!A(g))return!1;c++}let a=mu(u,e+1),s=a.split("|");const l=[];for(let g=0;g<s.length;g++){const k=s[g].trim();if(!k){if(g===0||g===s.length-1)continue;return!1}if(!/^:?-+:?$/.test(k))return!1;k.charCodeAt(k.length-1)===58?l.push(k.charCodeAt(0)===58?"center":"right"):k.charCodeAt(0)===58?l.push("left"):l.push("")}if(a=mu(u,e).trim(),a.indexOf("|")===-1||u.sCount[e]-u.blkIndent>=4)return!1;s=Gu(a),s.length&&s[0]===""&&s.shift(),s.length&&s[s.length-1]===""&&s.pop();const f=s.length;if(f===0||f!==l.length)return!1;if(r)return!0;const x=u.parentType;u.parentType="table";const h=u.md.block.ruler.getRules("blockquote"),d=u.push("table_open","table",1),b=[e,0];d.map=b;const p=u.push("thead_open","thead",1);p.map=[e,e+1];const D=u.push("tr_open","tr",1);D.map=[e,e+1];for(let g=0;g<s.length;g++){const k=u.push("th_open","th",1);l[g]&&(k.attrs=[["style","text-align:"+l[g]]]);const C=u.push("inline","",0);C.content=s[g].trim(),C.children=[],u.push("th_close","th",-1)}u.push("tr_close","tr",-1),u.push("thead_close","thead",-1);let m,_=0;for(n=e+2;n<t&&!(u.sCount[n]<u.blkIndent);n++){let g=!1;for(let C=0,E=h.length;C<E;C++)if(h[C](u,n,t,!0)){g=!0;break}if(g||(a=mu(u,n).trim(),!a)||u.sCount[n]-u.blkIndent>=4||(s=Gu(a),s.length&&s[0]===""&&s.shift(),s.length&&s[s.length-1]===""&&s.pop(),_+=f-s.length,_>Ue))break;if(n===e+2){const C=u.push("tbody_open","tbody",1);C.map=m=[e+2,0]}const k=u.push("tr_open","tr",1);k.map=[n,n+1];for(let C=0;C<f;C++){const E=u.push("td_open","td",1);l[C]&&(E.attrs=[["style","text-align:"+l[C]]]);const S=u.push("inline","",0);S.content=s[C]?s[C].trim():"",S.children=[],u.push("td_close","td",-1)}u.push("tr_close","tr",-1)}return m&&(u.push("tbody_close","tbody",-1),m[1]=n),u.push("table_close","table",-1),b[1]=n,u.parentType=x,u.line=n,!0}function Ze(u,e,t){if(u.sCount[e]-u.blkIndent<4)return!1;let r=e+1,n=r;for(;r<t;){if(u.isEmpty(r)){r++;continue}if(u.sCount[r]-u.blkIndent>=4){r++,n=r;continue}break}u.line=n;const c=u.push("code_block","code",0);return c.content=u.getLines(e,n,4+u.blkIndent,!1)+`
`,c.map=[e,u.line],!0}function Ve(u,e,t,r){let n=u.bMarks[e]+u.tShift[e],c=u.eMarks[e];if(u.sCount[e]-u.blkIndent>=4||n+3>c)return!1;const i=u.src.charCodeAt(n);if(i!==126&&i!==96)return!1;let o=n;n=u.skipChars(n,i);let a=n-o;if(a<3)return!1;const s=u.src.slice(o,n),l=u.src.slice(n,c);if(i===96&&l.indexOf(String.fromCharCode(i))>=0)return!1;if(r)return!0;let f=e,x=!1;for(;f++,!(f>=t||(n=o=u.bMarks[f]+u.tShift[f],c=u.eMarks[f],n<c&&u.sCount[f]<u.blkIndent));)if(u.src.charCodeAt(n)===i&&!(u.sCount[f]-u.blkIndent>=4)&&(n=u.skipChars(n,i),!(n-o<a)&&(n=u.skipSpaces(n),!(n<c)))){x=!0;break}a=u.sCount[e],u.line=f+(x?1:0);const h=u.push("fence","code",0);return h.info=l,h.content=u.getLines(e+1,f,a,!0),h.markup=s,h.map=[e,u.line],!0}function Ge(u,e,t,r){let n=u.bMarks[e]+u.tShift[e],c=u.eMarks[e];const i=u.lineMax;if(u.sCount[e]-u.blkIndent>=4||u.src.charCodeAt(n)!==62)return!1;if(r)return!0;const o=[],a=[],s=[],l=[],f=u.md.block.ruler.getRules("blockquote"),x=u.parentType;u.parentType="blockquote";let h=!1,d;for(d=e;d<t;d++){const _=u.sCount[d]<u.blkIndent;if(n=u.bMarks[d]+u.tShift[d],c=u.eMarks[d],n>=c)break;if(u.src.charCodeAt(n++)===62&&!_){let k=u.sCount[d]+1,C,E;u.src.charCodeAt(n)===32?(n++,k++,E=!1,C=!0):u.src.charCodeAt(n)===9?(C=!0,(u.bsCount[d]+k)%4===3?(n++,k++,E=!1):E=!0):C=!1;let S=k;for(o.push(u.bMarks[d]),u.bMarks[d]=n;n<c;){const B=u.src.charCodeAt(n);if(A(B))B===9?S+=4-(S+u.bsCount[d]+(E?1:0))%4:S++;else break;n++}h=n>=c,a.push(u.bsCount[d]),u.bsCount[d]=u.sCount[d]+1+(C?1:0),s.push(u.sCount[d]),u.sCount[d]=S-k,l.push(u.tShift[d]),u.tShift[d]=n-u.bMarks[d];continue}if(h)break;let g=!1;for(let k=0,C=f.length;k<C;k++)if(f[k](u,d,t,!0)){g=!0;break}if(g){u.lineMax=d,u.blkIndent!==0&&(o.push(u.bMarks[d]),a.push(u.bsCount[d]),l.push(u.tShift[d]),s.push(u.sCount[d]),u.sCount[d]-=u.blkIndent);break}o.push(u.bMarks[d]),a.push(u.bsCount[d]),l.push(u.tShift[d]),s.push(u.sCount[d]),u.sCount[d]=-1}const b=u.blkIndent;u.blkIndent=0;const p=u.push("blockquote_open","blockquote",1);p.markup=">";const D=[e,0];p.map=D,u.md.block.tokenize(u,e,d);const m=u.push("blockquote_close","blockquote",-1);m.markup=">",u.lineMax=i,u.parentType=x,D[1]=u.line;for(let _=0;_<l.length;_++)u.bMarks[_+e]=o[_],u.tShift[_+e]=l[_],u.sCount[_+e]=s[_],u.bsCount[_+e]=a[_];return u.blkIndent=b,!0}function We(u,e,t,r){const n=u.eMarks[e];if(u.sCount[e]-u.blkIndent>=4)return!1;let c=u.bMarks[e]+u.tShift[e];const i=u.src.charCodeAt(c++);if(i!==42&&i!==45&&i!==95)return!1;let o=1;for(;c<n;){const s=u.src.charCodeAt(c++);if(s!==i&&!A(s))return!1;s===i&&o++}if(o<3)return!1;if(r)return!0;u.line=e+1;const a=u.push("hr","hr",0);return a.map=[e,u.line],a.markup=Array(o+1).join(String.fromCharCode(i)),!0}function Wu(u,e){const t=u.eMarks[e];let r=u.bMarks[e]+u.tShift[e];const n=u.src.charCodeAt(r++);if(n!==42&&n!==45&&n!==43)return-1;if(r<t){const c=u.src.charCodeAt(r);if(!A(c))return-1}return r}function Ju(u,e){const t=u.bMarks[e]+u.tShift[e],r=u.eMarks[e];let n=t;if(n+1>=r)return-1;let c=u.src.charCodeAt(n++);if(c<48||c>57)return-1;for(;;){if(n>=r)return-1;if(c=u.src.charCodeAt(n++),c>=48&&c<=57){if(n-t>=10)return-1;continue}if(c===41||c===46)break;return-1}return n<r&&(c=u.src.charCodeAt(n),!A(c))?-1:n}function Je(u,e){const t=u.level+2;for(let r=e+2,n=u.tokens.length-2;r<n;r++)u.tokens[r].level===t&&u.tokens[r].type==="paragraph_open"&&(u.tokens[r+2].hidden=!0,u.tokens[r].hidden=!0,r+=2)}function Qe(u,e,t,r){let n,c,i,o,a=e,s=!0;if(u.sCount[a]-u.blkIndent>=4||u.listIndent>=0&&u.sCount[a]-u.listIndent>=4&&u.sCount[a]<u.blkIndent)return!1;let l=!1;r&&u.parentType==="paragraph"&&u.sCount[a]>=u.blkIndent&&(l=!0);let f,x,h;if((h=Ju(u,a))>=0){if(f=!0,i=u.bMarks[a]+u.tShift[a],x=Number(u.src.slice(i,h-1)),l&&x!==1)return!1}else if((h=Wu(u,a))>=0)f=!1;else return!1;if(l&&u.skipSpaces(h)>=u.eMarks[a])return!1;if(r)return!0;const d=u.src.charCodeAt(h-1),b=u.tokens.length;f?(o=u.push("ordered_list_open","ol",1),x!==1&&(o.attrs=[["start",x]])):o=u.push("bullet_list_open","ul",1);const p=[a,0];o.map=p,o.markup=String.fromCharCode(d);let D=!1;const m=u.md.block.ruler.getRules("list"),_=u.parentType;for(u.parentType="list";a<t;){c=h,n=u.eMarks[a];const g=u.sCount[a]+h-(u.bMarks[a]+u.tShift[a]);let k=g;for(;c<n;){const Z=u.src.charCodeAt(c);if(Z===9)k+=4-(k+u.bsCount[a])%4;else if(Z===32)k++;else break;c++}const C=c;let E;C>=n?E=1:E=k-g,E>4&&(E=1);const S=g+E;o=u.push("list_item_open","li",1),o.markup=String.fromCharCode(d);const B=[a,0];o.map=B,f&&(o.info=u.src.slice(i,h-1));const I=u.tight,H=u.tShift[a],S0=u.sCount[a],T0=u.listIndent;if(u.listIndent=u.blkIndent,u.blkIndent=S,u.tight=!0,u.tShift[a]=C-u.bMarks[a],u.sCount[a]=k,C>=n&&u.isEmpty(a+1)?u.line=Math.min(u.line+2,t):u.md.block.tokenize(u,a,t,!0),(!u.tight||D)&&(s=!1),D=u.line-a>1&&u.isEmpty(u.line-1),u.blkIndent=u.listIndent,u.listIndent=T0,u.tShift[a]=H,u.sCount[a]=S0,u.tight=I,o=u.push("list_item_close","li",-1),o.markup=String.fromCharCode(d),a=u.line,B[1]=a,a>=t||u.sCount[a]<u.blkIndent||u.sCount[a]-u.blkIndent>=4)break;let Ou=!1;for(let Z=0,z0=m.length;Z<z0;Z++)if(m[Z](u,a,t,!0)){Ou=!0;break}if(Ou)break;if(f){if(h=Ju(u,a),h<0)break;i=u.bMarks[a]+u.tShift[a]}else if(h=Wu(u,a),h<0)break;if(d!==u.src.charCodeAt(h-1))break}return f?o=u.push("ordered_list_close","ol",-1):o=u.push("bullet_list_close","ul",-1),o.markup=String.fromCharCode(d),p[1]=a,u.line=a,u.parentType=_,s&&Je(u,b),!0}function Xe(u,e,t,r){let n=u.bMarks[e]+u.tShift[e],c=u.eMarks[e],i=e+1;if(u.sCount[e]-u.blkIndent>=4||u.src.charCodeAt(n)!==91)return!1;function o(m){const _=u.lineMax;if(m>=_||u.isEmpty(m))return null;let g=!1;if(u.sCount[m]-u.blkIndent>3&&(g=!0),u.sCount[m]<0&&(g=!0),!g){const E=u.md.block.ruler.getRules("reference"),S=u.parentType;u.parentType="reference";let B=!1;for(let I=0,H=E.length;I<H;I++)if(E[I](u,m,_,!0)){B=!0;break}if(u.parentType=S,B)return null}const k=u.bMarks[m]+u.tShift[m],C=u.eMarks[m];return u.src.slice(k,C+1)}let a=u.src.slice(n,c+1);c=a.length;let s=-1;for(n=1;n<c;n++){const m=a.charCodeAt(n);if(m===91)return!1;if(m===93){s=n;break}else if(m===10){const _=o(i);_!==null&&(a+=_,c=a.length,i++)}else if(m===92&&(n++,n<c&&a.charCodeAt(n)===10)){const _=o(i);_!==null&&(a+=_,c=a.length,i++)}}if(s<0||a.charCodeAt(s+1)!==58)return!1;for(n=s+2;n<c;n++){const m=a.charCodeAt(n);if(m===10){const _=o(i);_!==null&&(a+=_,c=a.length,i++)}else if(!A(m))break}const l=u.md.helpers.parseLinkDestination(a,n,c);if(!l.ok)return!1;const f=u.md.normalizeLink(l.str);if(!u.md.validateLink(f))return!1;n=l.pos;const x=n,h=i,d=n;for(;n<c;n++){const m=a.charCodeAt(n);if(m===10){const _=o(i);_!==null&&(a+=_,c=a.length,i++)}else if(!A(m))break}let b=u.md.helpers.parseLinkTitle(a,n,c);for(;b.can_continue;){const m=o(i);if(m===null)break;a+=m,n=c,c=a.length,i++,b=u.md.helpers.parseLinkTitle(a,n,c,b)}let p;for(n<c&&d!==n&&b.ok?(p=b.str,n=b.pos):(p="",n=x,i=h);n<c;){const m=a.charCodeAt(n);if(!A(m))break;n++}if(n<c&&a.charCodeAt(n)!==10&&p)for(p="",n=x,i=h;n<c;){const m=a.charCodeAt(n);if(!A(m))break;n++}if(n<c&&a.charCodeAt(n)!==10)return!1;const D=du(a.slice(1,s));return D?(r||(typeof u.env.references>"u"&&(u.env.references={}),typeof u.env.references[D]>"u"&&(u.env.references[D]={title:p,href:f}),u.line=i),!0):!1}const Ke=["address","article","aside","base","basefont","blockquote","body","caption","center","col","colgroup","dd","details","dialog","dir","div","dl","dt","fieldset","figcaption","figure","footer","form","frame","frameset","h1","h2","h3","h4","h5","h6","head","header","hr","html","iframe","legend","li","link","main","menu","menuitem","nav","noframes","ol","optgroup","option","p","param","search","section","summary","table","tbody","td","tfoot","th","thead","title","tr","track","ul"],Ye="[a-zA-Z_:][a-zA-Z0-9:._-]*",ut="[^\"'=<>`\\x00-\\x20]+",et="'[^']*'",tt='"[^"]*"',rt="(?:"+ut+"|"+et+"|"+tt+")",nt="(?:\\s+"+Ye+"(?:\\s*=\\s*"+rt+")?)",h0="<[A-Za-z][A-Za-z0-9\\-]*"+nt+"*\\s*\\/?>",p0="<\\/[A-Za-z][A-Za-z0-9\\-]*\\s*>",ct="<!---?>|<!--(?:[^-]|-[^-]|--[^>])*-->",it="<[?][\\s\\S]*?[?]>",ot="<![A-Za-z][^>]*>",at="<!\\[CDATA\\[[\\s\\S]*?\\]\\]>",st=new RegExp("^(?:"+h0+"|"+p0+"|"+ct+"|"+it+"|"+ot+"|"+at+")"),lt=new RegExp("^(?:"+h0+"|"+p0+")"),V=[[/^<(script|pre|style|textarea)(?=(\s|>|$))/i,/<\/(script|pre|style|textarea)>/i,!0],[/^<!--/,/-->/,!0],[/^<\?/,/\?>/,!0],[/^<![A-Z]/,/>/,!0],[/^<!\[CDATA\[/,/\]\]>/,!0],[new RegExp("^</?("+Ke.join("|")+")(?=(\\s|/?>|$))","i"),/^$/,!0],[new RegExp(lt.source+"\\s*$"),/^$/,!1]];function ft(u,e,t,r){let n=u.bMarks[e]+u.tShift[e],c=u.eMarks[e];if(u.sCount[e]-u.blkIndent>=4||!u.md.options.html||u.src.charCodeAt(n)!==60)return!1;let i=u.src.slice(n,c),o=0;for(;o<V.length&&!V[o][0].test(i);o++);if(o===V.length)return!1;if(r)return V[o][2];let a=e+1;if(!V[o][1].test(i)){for(;a<t&&!(u.sCount[a]<u.blkIndent);a++)if(n=u.bMarks[a]+u.tShift[a],c=u.eMarks[a],i=u.src.slice(n,c),V[o][1].test(i)){i.length!==0&&a++;break}}u.line=a;const s=u.push("html_block","",0);return s.map=[e,a],s.content=u.getLines(e,a,u.blkIndent,!0),!0}function dt(u,e,t,r){let n=u.bMarks[e]+u.tShift[e],c=u.eMarks[e];if(u.sCount[e]-u.blkIndent>=4)return!1;let i=u.src.charCodeAt(n);if(i!==35||n>=c)return!1;let o=1;for(i=u.src.charCodeAt(++n);i===35&&n<c&&o<=6;)o++,i=u.src.charCodeAt(++n);if(o>6||n<c&&!A(i))return!1;if(r)return!0;c=u.skipSpacesBack(c,n);const a=u.skipCharsBack(c,35,n);a>n&&A(u.src.charCodeAt(a-1))&&(c=a),u.line=e+1;const s=u.push("heading_open","h"+String(o),1);s.markup="########".slice(0,o),s.map=[e,u.line];const l=u.push("inline","",0);l.content=u.src.slice(n,c).trim(),l.map=[e,u.line],l.children=[];const f=u.push("heading_close","h"+String(o),-1);return f.markup="########".slice(0,o),!0}function xt(u,e,t){const r=u.md.block.ruler.getRules("paragraph");if(u.sCount[e]-u.blkIndent>=4)return!1;const n=u.parentType;u.parentType="paragraph";let c=0,i,o=e+1;for(;o<t&&!u.isEmpty(o);o++){if(u.sCount[o]-u.blkIndent>3)continue;if(u.sCount[o]>=u.blkIndent){let h=u.bMarks[o]+u.tShift[o];const d=u.eMarks[o];if(h<d&&(i=u.src.charCodeAt(h),(i===45||i===61)&&(h=u.skipChars(h,i),h=u.skipSpaces(h),h>=d))){c=i===61?1:2;break}}if(u.sCount[o]<0)continue;let x=!1;for(let h=0,d=r.length;h<d;h++)if(r[h](u,o,t,!0)){x=!0;break}if(x)break}if(!c)return!1;const a=u.getLines(e,o,u.blkIndent,!1).trim();u.line=o+1;const s=u.push("heading_open","h"+String(c),1);s.markup=String.fromCharCode(i),s.map=[e,u.line];const l=u.push("inline","",0);l.content=a,l.map=[e,u.line-1],l.children=[];const f=u.push("heading_close","h"+String(c),-1);return f.markup=String.fromCharCode(i),u.parentType=n,!0}function ht(u,e,t){const r=u.md.block.ruler.getRules("paragraph"),n=u.parentType;let c=e+1;for(u.parentType="paragraph";c<t&&!u.isEmpty(c);c++){if(u.sCount[c]-u.blkIndent>3||u.sCount[c]<0)continue;let s=!1;for(let l=0,f=r.length;l<f;l++)if(r[l](u,c,t,!0)){s=!0;break}if(s)break}const i=u.getLines(e,c,u.blkIndent,!1).trim();u.line=c;const o=u.push("paragraph_open","p",1);o.map=[e,u.line];const a=u.push("inline","",0);return a.content=i,a.map=[e,u.line],a.children=[],u.push("paragraph_close","p",-1),u.parentType=n,!0}const iu=[["table",He,["paragraph","reference"]],["code",Ze],["fence",Ve,["paragraph","reference","blockquote","list"]],["blockquote",Ge,["paragraph","reference","blockquote","list"]],["hr",We,["paragraph","reference","blockquote","list"]],["list",Qe,["paragraph","reference","blockquote"]],["reference",Xe],["html_block",ft,["paragraph","reference","blockquote"]],["heading",dt,["paragraph","reference","blockquote"]],["lheading",xt],["paragraph",ht]];function xu(){this.ruler=new v;for(let u=0;u<iu.length;u++)this.ruler.push(iu[u][0],iu[u][1],{alt:(iu[u][2]||[]).slice()})}xu.prototype.tokenize=function(u,e,t){const r=this.ruler.getRules(""),n=r.length,c=u.md.options.maxNesting;let i=e,o=!1;for(;i<t&&(u.line=i=u.skipEmptyLines(i),!(i>=t||u.sCount[i]<u.blkIndent));){if(u.level>=c){u.line=t;break}const a=u.line;let s=!1;for(let l=0;l<n;l++)if(s=r[l](u,i,t,!1),s){if(a>=u.line)throw new Error("block rule didn't increment state.line");break}if(!s)throw new Error("none of the block rules matched");u.tight=!o,u.isEmpty(u.line-1)&&(o=!0),i=u.line,i<t&&u.isEmpty(i)&&(o=!0,i++,u.line=i)}};xu.prototype.parse=function(u,e,t,r){if(!u)return;const n=new this.State(u,e,t,r);this.tokenize(n,n.line,n.lineMax)};xu.prototype.State=P;function ru(u,e,t,r){this.src=u,this.env=t,this.md=e,this.tokens=r,this.tokens_meta=Array(r.length),this.pos=0,this.posMax=this.src.length,this.level=0,this.pending="",this.pendingLevel=0,this.cache={},this.delimiters=[],this._prev_delimiters=[],this.backticks={},this.backticksScanned=!1,this.linkLevel=0}ru.prototype.pushPending=function(){const u=new M("text","",0);return u.content=this.pending,u.level=this.pendingLevel,this.tokens.push(u),this.pending="",u};ru.prototype.push=function(u,e,t){this.pending&&this.pushPending();const r=new M(u,e,t);let n=null;return t<0&&(this.level--,this.delimiters=this._prev_delimiters.pop()),r.level=this.level,t>0&&(this.level++,this._prev_delimiters.push(this.delimiters),this.delimiters=[],n={delimiters:this.delimiters}),this.pendingLevel=this.level,this.tokens.push(r),this.tokens_meta.push(n),r};ru.prototype.scanDelims=function(u,e){const t=this.posMax,r=this.src.charCodeAt(u),n=u>0?this.src.charCodeAt(u-1):32;let c=u;for(;c<t&&this.src.charCodeAt(c)===r;)c++;const i=c-u,o=c<t?this.src.charCodeAt(c):32,a=uu(n)||Y(String.fromCharCode(n)),s=uu(o)||Y(String.fromCharCode(o)),l=K(n),f=K(o),x=!f&&(!s||l||a),h=!l&&(!a||f||s);return{can_open:x&&(e||!h||a),can_close:h&&(e||!x||s),length:i}};ru.prototype.Token=M;function pt(u){switch(u){case 10:case 33:case 35:case 36:case 37:case 38:case 42:case 43:case 45:case 58:case 60:case 61:case 62:case 64:case 91:case 92:case 93:case 94:case 95:case 96:case 123:case 125:case 126:return!0;default:return!1}}function bt(u,e){let t=u.pos;for(;t<u.posMax&&!pt(u.src.charCodeAt(t));)t++;return t===u.pos?!1:(e||(u.pending+=u.src.slice(u.pos,t)),u.pos=t,!0)}const mt=/(?:^|[^a-z0-9.+-])([a-z][a-z0-9.+-]*)$/i;function _t(u,e){if(!u.md.options.linkify||u.linkLevel>0)return!1;const t=u.pos,r=u.posMax;if(t+3>r||u.src.charCodeAt(t)!==58||u.src.charCodeAt(t+1)!==47||u.src.charCodeAt(t+2)!==47)return!1;const n=u.pending.match(mt);if(!n)return!1;const c=n[1],i=u.md.linkify.matchAtStart(u.src.slice(t-c.length));if(!i)return!1;let o=i.url;if(o.length<=c.length)return!1;o=o.replace(/\*+$/,"");const a=u.md.normalizeLink(o);if(!u.md.validateLink(a))return!1;if(!e){u.pending=u.pending.slice(0,-c.length);const s=u.push("link_open","a",1);s.attrs=[["href",a]],s.markup="linkify",s.info="auto";const l=u.push("text","",0);l.content=u.md.normalizeLinkText(o);const f=u.push("link_close","a",-1);f.markup="linkify",f.info="auto"}return u.pos+=o.length-c.length,!0}function gt(u,e){let t=u.pos;if(u.src.charCodeAt(t)!==10)return!1;const r=u.pending.length-1,n=u.posMax;if(!e)if(r>=0&&u.pending.charCodeAt(r)===32)if(r>=1&&u.pending.charCodeAt(r-1)===32){let c=r-1;for(;c>=1&&u.pending.charCodeAt(c-1)===32;)c--;u.pending=u.pending.slice(0,c),u.push("hardbreak","br",0)}else u.pending=u.pending.slice(0,-1),u.push("softbreak","br",0);else u.push("softbreak","br",0);for(t++;t<n&&A(u.src.charCodeAt(t));)t++;return u.pos=t,!0}const Ru=[];for(let u=0;u<256;u++)Ru.push(0);"\\!\"#$%&'()*+,./:;<=>?@[]^_`{|}~-".split("").forEach(function(u){Ru[u.charCodeAt(0)]=1});function kt(u,e){let t=u.pos;const r=u.posMax;if(u.src.charCodeAt(t)!==92||(t++,t>=r))return!1;let n=u.src.charCodeAt(t);if(n===10){for(e||u.push("hardbreak","br",0),t++;t<r&&(n=u.src.charCodeAt(t),!!A(n));)t++;return u.pos=t,!0}let c=u.src[t];if(n>=55296&&n<=56319&&t+1<r){const o=u.src.charCodeAt(t+1);o>=56320&&o<=57343&&(c+=u.src[t+1],t++)}const i="\\"+c;if(!e){const o=u.push("text_special","",0);n<256&&Ru[n]!==0?o.content=c:o.content=i,o.markup=i,o.info="escape"}return u.pos=t+1,!0}function Dt(u,e){let t=u.pos;if(u.src.charCodeAt(t)!==96)return!1;const n=t;t++;const c=u.posMax;for(;t<c&&u.src.charCodeAt(t)===96;)t++;const i=u.src.slice(n,t),o=i.length;if(u.backticksScanned&&(u.backticks[o]||0)<=n)return e||(u.pending+=i),u.pos+=o,!0;let a=t,s;for(;(s=u.src.indexOf("`",a))!==-1;){for(a=s+1;a<c&&u.src.charCodeAt(a)===96;)a++;const l=a-s;if(l===o){if(!e){const f=u.push("code_inline","code",0);f.markup=i,f.content=u.src.slice(t,s).replace(/\n/g," ").replace(/^ (.+) $/,"$1")}return u.pos=a,!0}u.backticks[l]=s}return u.backticksScanned=!0,e||(u.pending+=i),u.pos+=o,!0}function yt(u,e){const t=u.pos,r=u.src.charCodeAt(t);if(e||r!==126)return!1;const n=u.scanDelims(u.pos,!0);let c=n.length;const i=String.fromCharCode(r);if(c<2)return!1;let o;c%2&&(o=u.push("text","",0),o.content=i,c--);for(let a=0;a<c;a+=2)o=u.push("text","",0),o.content=i+i,u.delimiters.push({marker:r,length:0,token:u.tokens.length-1,end:-1,open:n.can_open,close:n.can_close});return u.pos+=n.length,!0}function Qu(u,e){let t;const r=[],n=e.length;for(let c=0;c<n;c++){const i=e[c];if(i.marker!==126||i.end===-1)continue;const o=e[i.end];t=u.tokens[i.token],t.type="s_open",t.tag="s",t.nesting=1,t.markup="~~",t.content="",t=u.tokens[o.token],t.type="s_close",t.tag="s",t.nesting=-1,t.markup="~~",t.content="",u.tokens[o.token-1].type==="text"&&u.tokens[o.token-1].content==="~"&&r.push(o.token-1)}for(;r.length;){const c=r.pop();let i=c+1;for(;i<u.tokens.length&&u.tokens[i].type==="s_close";)i++;i--,c!==i&&(t=u.tokens[i],u.tokens[i]=u.tokens[c],u.tokens[c]=t)}}function Ct(u){const e=u.tokens_meta,t=u.tokens_meta.length;Qu(u,u.delimiters);for(let r=0;r<t;r++)e[r]&&e[r].delimiters&&Qu(u,e[r].delimiters)}const b0={tokenize:yt,postProcess:Ct};function Et(u,e){const t=u.pos,r=u.src.charCodeAt(t);if(e||r!==95&&r!==42)return!1;const n=u.scanDelims(u.pos,r===42);for(let c=0;c<n.length;c++){const i=u.push("text","",0);i.content=String.fromCharCode(r),u.delimiters.push({marker:r,length:n.length,token:u.tokens.length-1,end:-1,open:n.can_open,close:n.can_close})}return u.pos+=n.length,!0}function Xu(u,e){const t=e.length;for(let r=t-1;r>=0;r--){const n=e[r];if(n.marker!==95&&n.marker!==42||n.end===-1)continue;const c=e[n.end],i=r>0&&e[r-1].end===n.end+1&&e[r-1].marker===n.marker&&e[r-1].token===n.token-1&&e[n.end+1].token===c.token+1,o=String.fromCharCode(n.marker),a=u.tokens[n.token];a.type=i?"strong_open":"em_open",a.tag=i?"strong":"em",a.nesting=1,a.markup=i?o+o:o,a.content="";const s=u.tokens[c.token];s.type=i?"strong_close":"em_close",s.tag=i?"strong":"em",s.nesting=-1,s.markup=i?o+o:o,s.content="",i&&(u.tokens[e[r-1].token].content="",u.tokens[e[n.end+1].token].content="",r--)}}function At(u){const e=u.tokens_meta,t=u.tokens_meta.length;Xu(u,u.delimiters);for(let r=0;r<t;r++)e[r]&&e[r].delimiters&&Xu(u,e[r].delimiters)}const m0={tokenize:Et,postProcess:At};function Ft(u,e){let t,r,n,c,i="",o="",a=u.pos,s=!0;if(u.src.charCodeAt(u.pos)!==91)return!1;const l=u.pos,f=u.posMax,x=u.pos+1,h=u.md.helpers.parseLinkLabel(u,u.pos,!0);if(h<0)return!1;let d=h+1;if(d<f&&u.src.charCodeAt(d)===40){for(s=!1,d++;d<f&&(t=u.src.charCodeAt(d),!(!A(t)&&t!==10));d++);if(d>=f)return!1;if(a=d,n=u.md.helpers.parseLinkDestination(u.src,d,u.posMax),n.ok){for(i=u.md.normalizeLink(n.str),u.md.validateLink(i)?d=n.pos:i="",a=d;d<f&&(t=u.src.charCodeAt(d),!(!A(t)&&t!==10));d++);if(n=u.md.helpers.parseLinkTitle(u.src,d,u.posMax),d<f&&a!==d&&n.ok)for(o=n.str,d=n.pos;d<f&&(t=u.src.charCodeAt(d),!(!A(t)&&t!==10));d++);}(d>=f||u.src.charCodeAt(d)!==41)&&(s=!0),d++}if(s){if(typeof u.env.references>"u")return!1;if(d<f&&u.src.charCodeAt(d)===91?(a=d+1,d=u.md.helpers.parseLinkLabel(u,d),d>=0?r=u.src.slice(a,d++):d=h+1):d=h+1,r||(r=u.src.slice(x,h)),c=u.env.references[du(r)],!c)return u.pos=l,!1;i=c.href,o=c.title}if(!e){u.pos=x,u.posMax=h;const b=u.push("link_open","a",1),p=[["href",i]];b.attrs=p,o&&p.push(["title",o]),u.linkLevel++,u.md.inline.tokenize(u),u.linkLevel--,u.push("link_close","a",-1)}return u.pos=d,u.posMax=f,!0}function wt(u,e){let t,r,n,c,i,o,a,s,l="";const f=u.pos,x=u.posMax;if(u.src.charCodeAt(u.pos)!==33||u.src.charCodeAt(u.pos+1)!==91)return!1;const h=u.pos+2,d=u.md.helpers.parseLinkLabel(u,u.pos+1,!1);if(d<0)return!1;if(c=d+1,c<x&&u.src.charCodeAt(c)===40){for(c++;c<x&&(t=u.src.charCodeAt(c),!(!A(t)&&t!==10));c++);if(c>=x)return!1;for(s=c,o=u.md.helpers.parseLinkDestination(u.src,c,u.posMax),o.ok&&(l=u.md.normalizeLink(o.str),u.md.validateLink(l)?c=o.pos:l=""),s=c;c<x&&(t=u.src.charCodeAt(c),!(!A(t)&&t!==10));c++);if(o=u.md.helpers.parseLinkTitle(u.src,c,u.posMax),c<x&&s!==c&&o.ok)for(a=o.str,c=o.pos;c<x&&(t=u.src.charCodeAt(c),!(!A(t)&&t!==10));c++);else a="";if(c>=x||u.src.charCodeAt(c)!==41)return u.pos=f,!1;c++}else{if(typeof u.env.references>"u")return!1;if(c<x&&u.src.charCodeAt(c)===91?(s=c+1,c=u.md.helpers.parseLinkLabel(u,c),c>=0?n=u.src.slice(s,c++):c=d+1):c=d+1,n||(n=u.src.slice(h,d)),i=u.env.references[du(n)],!i)return u.pos=f,!1;l=i.href,a=i.title}if(!e){r=u.src.slice(h,d);const b=[];u.md.inline.parse(r,u.md,u.env,b);const p=u.push("image","img",0),D=[["src",l],["alt",""]];p.attrs=D,p.children=b,p.content=r,a&&D.push(["title",a])}return u.pos=c,u.posMax=x,!0}const vt=/^([a-zA-Z0-9.!#$%&'*+/=?^_`{|}~-]+@[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\.[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)*)$/,St=/^([a-zA-Z][a-zA-Z0-9+.-]{1,31}):([^<>\x00-\x20]*)$/;function Tt(u,e){let t=u.pos;if(u.src.charCodeAt(t)!==60)return!1;const r=u.pos,n=u.posMax;for(;;){if(++t>=n)return!1;const i=u.src.charCodeAt(t);if(i===60)return!1;if(i===62)break}const c=u.src.slice(r+1,t);if(St.test(c)){const i=u.md.normalizeLink(c);if(!u.md.validateLink(i))return!1;if(!e){const o=u.push("link_open","a",1);o.attrs=[["href",i]],o.markup="autolink",o.info="auto";const a=u.push("text","",0);a.content=u.md.normalizeLinkText(c);const s=u.push("link_close","a",-1);s.markup="autolink",s.info="auto"}return u.pos+=c.length+2,!0}if(vt.test(c)){const i=u.md.normalizeLink("mailto:"+c);if(!u.md.validateLink(i))return!1;if(!e){const o=u.push("link_open","a",1);o.attrs=[["href",i]],o.markup="autolink",o.info="auto";const a=u.push("text","",0);a.content=u.md.normalizeLinkText(c);const s=u.push("link_close","a",-1);s.markup="autolink",s.info="auto"}return u.pos+=c.length+2,!0}return!1}function zt(u){return/^<a[>\s]/i.test(u)}function Mt(u){return/^<\/a\s*>/i.test(u)}function Bt(u){const e=u|32;return e>=97&&e<=122}function It(u,e){if(!u.md.options.html)return!1;const t=u.posMax,r=u.pos;if(u.src.charCodeAt(r)!==60||r+2>=t)return!1;const n=u.src.charCodeAt(r+1);if(n!==33&&n!==63&&n!==47&&!Bt(n))return!1;const c=u.src.slice(r).match(st);if(!c)return!1;if(!e){const i=u.push("html_inline","",0);i.content=c[0],zt(i.content)&&u.linkLevel++,Mt(i.content)&&u.linkLevel--}return u.pos+=c[0].length,!0}const Rt=/^&#((?:x[a-f0-9]{1,6}|[0-9]{1,7}));/i,qt=/^&([a-z][a-z0-9]{1,31});/i;function Ot(u,e){const t=u.pos,r=u.posMax;if(u.src.charCodeAt(t)!==38||t+1>=r)return!1;if(u.src.charCodeAt(t+1)===35){const c=u.src.slice(t).match(Rt);if(c){if(!e){const i=c[1][0].toLowerCase()==="x"?parseInt(c[1].slice(1),16):parseInt(c[1],10),o=u.push("text_special","",0);o.content=Bu(i)?su(i):su(65533),o.markup=c[0],o.info="entity"}return u.pos+=c[0].length,!0}}else{const c=u.src.slice(t).match(qt);if(c){const i=s0(c[0]);if(i!==c[0]){if(!e){const o=u.push("text_special","",0);o.content=i,o.markup=c[0],o.info="entity"}return u.pos+=c[0].length,!0}}}return!1}function Ku(u){const e={},t=u.length;if(!t)return;let r=0,n=-2;const c=[];for(let i=0;i<t;i++){const o=u[i];if(c.push(0),(u[r].marker!==o.marker||n!==o.token-1)&&(r=i),n=o.token,o.length=o.length||0,!o.close)continue;e.hasOwnProperty(o.marker)||(e[o.marker]=[-1,-1,-1,-1,-1,-1]);const a=e[o.marker][(o.open?3:0)+o.length%3];let s=r-c[r]-1,l=s;for(;s>a;s-=c[s]+1){const f=u[s];if(f.marker===o.marker&&f.open&&f.end<0){let x=!1;if((f.close||o.open)&&(f.length+o.length)%3===0&&(f.length%3!==0||o.length%3!==0)&&(x=!0),!x){const h=s>0&&!u[s-1].open?c[s-1]+1:0;c[i]=i-s+h,c[s]=h,o.open=!1,f.end=i,f.close=!1,l=-1,n=-2;break}}}l!==-1&&(e[o.marker][(o.open?3:0)+(o.length||0)%3]=l)}}function Pt(u){const e=u.tokens_meta,t=u.tokens_meta.length;Ku(u.delimiters);for(let r=0;r<t;r++)e[r]&&e[r].delimiters&&Ku(e[r].delimiters)}function Lt(u){let e,t,r=0;const n=u.tokens,c=u.tokens.length;for(e=t=0;e<c;e++)n[e].nesting<0&&r--,n[e].level=r,n[e].nesting>0&&r++,n[e].type==="text"&&e+1<c&&n[e+1].type==="text"?n[e+1].content=n[e].content+n[e+1].content:(e!==t&&(n[t]=n[e]),t++);e!==t&&(n.length=t)}const _u=[["text",bt],["linkify",_t],["newline",gt],["escape",kt],["backticks",Dt],["strikethrough",b0.tokenize],["emphasis",m0.tokenize],["link",Ft],["image",wt],["autolink",Tt],["html_inline",It],["entity",Ot]],gu=[["balance_pairs",Pt],["strikethrough",b0.postProcess],["emphasis",m0.postProcess],["fragments_join",Lt]];function nu(){this.ruler=new v;for(let u=0;u<_u.length;u++)this.ruler.push(_u[u][0],_u[u][1]);this.ruler2=new v;for(let u=0;u<gu.length;u++)this.ruler2.push(gu[u][0],gu[u][1])}nu.prototype.skipToken=function(u){const e=u.pos,t=this.ruler.getRules(""),r=t.length,n=u.md.options.maxNesting,c=u.cache;if(typeof c[e]<"u"){u.pos=c[e];return}let i=!1;if(u.level<n){for(let o=0;o<r;o++)if(u.level++,i=t[o](u,!0),u.level--,i){if(e>=u.pos)throw new Error("inline rule didn't increment state.pos");break}}else u.pos=u.posMax;i||u.pos++,c[e]=u.pos};nu.prototype.tokenize=function(u){const e=this.ruler.getRules(""),t=e.length,r=u.posMax,n=u.md.options.maxNesting;for(;u.pos<r;){const c=u.pos;let i=!1;if(u.level<n){for(let o=0;o<t;o++)if(i=e[o](u,!1),i){if(c>=u.pos)throw new Error("inline rule didn't increment state.pos");break}}if(i){if(u.pos>=r)break;continue}u.pending+=u.src[u.pos++]}u.pending&&u.pushPending()};nu.prototype.parse=function(u,e,t,r){const n=new this.State(u,e,t,r);this.tokenize(n);const c=this.ruler2.getRules(""),i=c.length;for(let o=0;o<i;o++)c[o](n)};nu.prototype.State=ru;function Nt(u){const e={};u=u||{},e.src_Any=n0.source,e.src_Cc=c0.source,e.src_Z=o0.source,e.src_P=zu.source,e.src_ZPCc=[e.src_Z,e.src_P,e.src_Cc].join("|"),e.src_ZCc=[e.src_Z,e.src_Cc].join("|");const t="[><｜]";return e.src_pseudo_letter="(?:(?!"+t+"|"+e.src_ZPCc+")"+e.src_Any+")",e.src_ip4="(?:(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\\.){3}(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)",e.src_auth="(?:(?:(?!"+e.src_ZCc+"|[@/\\[\\]()]).)+@)?",e.src_port="(?::(?:6(?:[0-4]\\d{3}|5(?:[0-4]\\d{2}|5(?:[0-2]\\d|3[0-5])))|[1-5]?\\d{1,4}))?",e.src_host_terminator="(?=$|"+t+"|"+e.src_ZPCc+")(?!"+(u["---"]?"-(?!--)|":"-|")+"_|:\\d|\\.-|\\.(?!$|"+e.src_ZPCc+"))",e.src_path="(?:[/?#](?:(?!"+e.src_ZCc+"|"+t+`|[()[\\]{}.,"'?!\\-;]).|\\[(?:(?!`+e.src_ZCc+"|\\]).)*\\]|\\((?:(?!"+e.src_ZCc+"|[)]).)*\\)|\\{(?:(?!"+e.src_ZCc+'|[}]).)*\\}|\\"(?:(?!'+e.src_ZCc+`|["]).)+\\"|\\'(?:(?!`+e.src_ZCc+"|[']).)+\\'|\\'(?="+e.src_pseudo_letter+"|[-])|\\.{2,}[a-zA-Z0-9%/&]|\\.(?!"+e.src_ZCc+"|[.]|$)|"+(u["---"]?"\\-(?!--(?:[^-]|$))(?:-*)|":"\\-+|")+",(?!"+e.src_ZCc+"|$)|;(?!"+e.src_ZCc+"|$)|\\!+(?!"+e.src_ZCc+"|[!]|$)|\\?(?!"+e.src_ZCc+"|[?]|$))+|\\/)?",e.src_email_name='[\\-;:&=\\+\\$,\\.a-zA-Z0-9_][\\-;:&=\\+\\$,\\"\\.a-zA-Z0-9_]*',e.src_xn="xn--[a-z0-9\\-]{1,59}",e.src_domain_root="(?:"+e.src_xn+"|"+e.src_pseudo_letter+"{1,63})",e.src_domain="(?:"+e.src_xn+"|(?:"+e.src_pseudo_letter+")|(?:"+e.src_pseudo_letter+"(?:-|"+e.src_pseudo_letter+"){0,61}"+e.src_pseudo_letter+"))",e.src_host="(?:(?:(?:(?:"+e.src_domain+")\\.)*"+e.src_domain+"))",e.tpl_host_fuzzy="(?:"+e.src_ip4+"|(?:(?:(?:"+e.src_domain+")\\.)+(?:%TLDS%)))",e.tpl_host_no_ip_fuzzy="(?:(?:(?:"+e.src_domain+")\\.)+(?:%TLDS%))",e.src_host_strict=e.src_host+e.src_host_terminator,e.tpl_host_fuzzy_strict=e.tpl_host_fuzzy+e.src_host_terminator,e.src_host_port_strict=e.src_host+e.src_port+e.src_host_terminator,e.tpl_host_port_fuzzy_strict=e.tpl_host_fuzzy+e.src_port+e.src_host_terminator,e.tpl_host_port_no_ip_fuzzy_strict=e.tpl_host_no_ip_fuzzy+e.src_port+e.src_host_terminator,e.tpl_host_fuzzy_test="localhost|www\\.|\\.\\d{1,3}\\.|(?:\\.(?:%TLDS%)(?:"+e.src_ZPCc+"|>|$))",e.tpl_email_fuzzy="(^|"+t+'|"|\\(|'+e.src_ZCc+")("+e.src_email_name+"@"+e.tpl_host_fuzzy_strict+")",e.tpl_link_fuzzy="(^|(?![.:/\\-_@])(?:[$+<=>^`|｜]|"+e.src_ZPCc+"))((?![$+<=>^`|｜])"+e.tpl_host_port_fuzzy_strict+e.src_path+")",e.tpl_link_no_ip_fuzzy="(^|(?![.:/\\-_@])(?:[$+<=>^`|｜]|"+e.src_ZPCc+"))((?![$+<=>^`|｜])"+e.tpl_host_port_no_ip_fuzzy_strict+e.src_path+")",e}function Cu(u){return Array.prototype.slice.call(arguments,1).forEach(function(t){t&&Object.keys(t).forEach(function(r){u[r]=t[r]})}),u}function hu(u){return Object.prototype.toString.call(u)}function jt(u){return hu(u)==="[object String]"}function $t(u){return hu(u)==="[object Object]"}function Ut(u){return hu(u)==="[object RegExp]"}function Yu(u){return hu(u)==="[object Function]"}function Ht(u){return u.replace(/[.?*+^$[\]\\(){}|-]/g,"\\$&")}const _0={fuzzyLink:!0,fuzzyEmail:!0,fuzzyIP:!1};function Zt(u){return Object.keys(u||{}).reduce(function(e,t){return e||_0.hasOwnProperty(t)},!1)}const Vt={"http:":{validate:function(u,e,t){const r=u.slice(e);return t.re.http||(t.re.http=new RegExp("^\\/\\/"+t.re.src_auth+t.re.src_host_port_strict+t.re.src_path,"i")),t.re.http.test(r)?r.match(t.re.http)[0].length:0}},"https:":"http:","ftp:":"http:","//":{validate:function(u,e,t){const r=u.slice(e);return t.re.no_http||(t.re.no_http=new RegExp("^"+t.re.src_auth+"(?:localhost|(?:(?:"+t.re.src_domain+")\\.)+"+t.re.src_domain_root+")"+t.re.src_port+t.re.src_host_terminator+t.re.src_path,"i")),t.re.no_http.test(r)?e>=3&&u[e-3]===":"||e>=3&&u[e-3]==="/"?0:r.match(t.re.no_http)[0].length:0}},"mailto:":{validate:function(u,e,t){const r=u.slice(e);return t.re.mailto||(t.re.mailto=new RegExp("^"+t.re.src_email_name+"@"+t.re.src_host_strict,"i")),t.re.mailto.test(r)?r.match(t.re.mailto)[0].length:0}}},Gt="a[cdefgilmnoqrstuwxz]|b[abdefghijmnorstvwyz]|c[acdfghiklmnoruvwxyz]|d[ejkmoz]|e[cegrstu]|f[ijkmor]|g[abdefghilmnpqrstuwy]|h[kmnrtu]|i[delmnoqrst]|j[emop]|k[eghimnprwyz]|l[abcikrstuvy]|m[acdeghklmnopqrstuvwxyz]|n[acefgilopruz]|om|p[aefghklmnrstwy]|qa|r[eosuw]|s[abcdeghijklmnortuvxyz]|t[cdfghjklmnortvwz]|u[agksyz]|v[aceginu]|w[fs]|y[et]|z[amw]",Wt="biz|com|edu|gov|net|org|pro|web|xxx|aero|asia|coop|info|museum|name|shop|рф".split("|");function Jt(u){u.__index__=-1,u.__text_cache__=""}function Qt(u){return function(e,t){const r=e.slice(t);return u.test(r)?r.match(u)[0].length:0}}function u0(){return function(u,e){e.normalize(u)}}function lu(u){const e=u.re=Nt(u.__opts__),t=u.__tlds__.slice();u.onCompile(),u.__tlds_replaced__||t.push(Gt),t.push(e.src_xn),e.src_tlds=t.join("|");function r(o){return o.replace("%TLDS%",e.src_tlds)}e.email_fuzzy=RegExp(r(e.tpl_email_fuzzy),"i"),e.link_fuzzy=RegExp(r(e.tpl_link_fuzzy),"i"),e.link_no_ip_fuzzy=RegExp(r(e.tpl_link_no_ip_fuzzy),"i"),e.host_fuzzy_test=RegExp(r(e.tpl_host_fuzzy_test),"i");const n=[];u.__compiled__={};function c(o,a){throw new Error('(LinkifyIt) Invalid schema "'+o+'": '+a)}Object.keys(u.__schemas__).forEach(function(o){const a=u.__schemas__[o];if(a===null)return;const s={validate:null,link:null};if(u.__compiled__[o]=s,$t(a)){Ut(a.validate)?s.validate=Qt(a.validate):Yu(a.validate)?s.validate=a.validate:c(o,a),Yu(a.normalize)?s.normalize=a.normalize:a.normalize?c(o,a):s.normalize=u0();return}if(jt(a)){n.push(o);return}c(o,a)}),n.forEach(function(o){u.__compiled__[u.__schemas__[o]]&&(u.__compiled__[o].validate=u.__compiled__[u.__schemas__[o]].validate,u.__compiled__[o].normalize=u.__compiled__[u.__schemas__[o]].normalize)}),u.__compiled__[""]={validate:null,normalize:u0()};const i=Object.keys(u.__compiled__).filter(function(o){return o.length>0&&u.__compiled__[o]}).map(Ht).join("|");u.re.schema_test=RegExp("(^|(?!_)(?:[><｜]|"+e.src_ZPCc+"))("+i+")","i"),u.re.schema_search=RegExp("(^|(?!_)(?:[><｜]|"+e.src_ZPCc+"))("+i+")","ig"),u.re.schema_at_start=RegExp("^"+u.re.schema_search.source,"i"),u.re.pretest=RegExp("("+u.re.schema_test.source+")|("+u.re.host_fuzzy_test.source+")|@","i"),Jt(u)}function Xt(u,e){const t=u.__index__,r=u.__last_index__,n=u.__text_cache__.slice(t,r);this.schema=u.__schema__.toLowerCase(),this.index=t+e,this.lastIndex=r+e,this.raw=n,this.text=n,this.url=n}function Eu(u,e){const t=new Xt(u,e);return u.__compiled__[t.schema].normalize(t,u),t}function T(u,e){if(!(this instanceof T))return new T(u,e);e||Zt(u)&&(e=u,u={}),this.__opts__=Cu({},_0,e),this.__index__=-1,this.__last_index__=-1,this.__schema__="",this.__text_cache__="",this.__schemas__=Cu({},Vt,u),this.__compiled__={},this.__tlds__=Wt,this.__tlds_replaced__=!1,this.re={},lu(this)}T.prototype.add=function(e,t){return this.__schemas__[e]=t,lu(this),this};T.prototype.set=function(e){return this.__opts__=Cu(this.__opts__,e),this};T.prototype.test=function(e){if(this.__text_cache__=e,this.__index__=-1,!e.length)return!1;let t,r,n,c,i,o,a,s,l;if(this.re.schema_test.test(e)){for(a=this.re.schema_search,a.lastIndex=0;(t=a.exec(e))!==null;)if(c=this.testSchemaAt(e,t[2],a.lastIndex),c){this.__schema__=t[2],this.__index__=t.index+t[1].length,this.__last_index__=t.index+t[0].length+c;break}}return this.__opts__.fuzzyLink&&this.__compiled__["http:"]&&(s=e.search(this.re.host_fuzzy_test),s>=0&&(this.__index__<0||s<this.__index__)&&(r=e.match(this.__opts__.fuzzyIP?this.re.link_fuzzy:this.re.link_no_ip_fuzzy))!==null&&(i=r.index+r[1].length,(this.__index__<0||i<this.__index__)&&(this.__schema__="",this.__index__=i,this.__last_index__=r.index+r[0].length))),this.__opts__.fuzzyEmail&&this.__compiled__["mailto:"]&&(l=e.indexOf("@"),l>=0&&(n=e.match(this.re.email_fuzzy))!==null&&(i=n.index+n[1].length,o=n.index+n[0].length,(this.__index__<0||i<this.__index__||i===this.__index__&&o>this.__last_index__)&&(this.__schema__="mailto:",this.__index__=i,this.__last_index__=o))),this.__index__>=0};T.prototype.pretest=function(e){return this.re.pretest.test(e)};T.prototype.testSchemaAt=function(e,t,r){return this.__compiled__[t.toLowerCase()]?this.__compiled__[t.toLowerCase()].validate(e,r,this):0};T.prototype.match=function(e){const t=[];let r=0;this.__index__>=0&&this.__text_cache__===e&&(t.push(Eu(this,r)),r=this.__last_index__);let n=r?e.slice(r):e;for(;this.test(n);)t.push(Eu(this,r)),n=n.slice(this.__last_index__),r+=this.__last_index__;return t.length?t:null};T.prototype.matchAtStart=function(e){if(this.__text_cache__=e,this.__index__=-1,!e.length)return null;const t=this.re.schema_at_start.exec(e);if(!t)return null;const r=this.testSchemaAt(e,t[2],t[0].length);return r?(this.__schema__=t[2],this.__index__=t.index+t[1].length,this.__last_index__=t.index+t[0].length+r,Eu(this,0)):null};T.prototype.tlds=function(e,t){return e=Array.isArray(e)?e:[e],t?(this.__tlds__=this.__tlds__.concat(e).sort().filter(function(r,n,c){return r!==c[n-1]}).reverse(),lu(this),this):(this.__tlds__=e.slice(),this.__tlds_replaced__=!0,lu(this),this)};T.prototype.normalize=function(e){e.schema||(e.url="http://"+e.url),e.schema==="mailto:"&&!/^mailto:/i.test(e.url)&&(e.url="mailto:"+e.url)};T.prototype.onCompile=function(){};const G=2147483647,R=36,qu=1,eu=26,Kt=38,Yt=700,g0=72,k0=128,D0="-",ur=/^xn--/,er=/[^\0-\x7F]/,tr=/[\x2E\u3002\uFF0E\uFF61]/g,rr={overflow:"Overflow: input needs wider integers to process","not-basic":"Illegal input >= 0x80 (not a basic code point)","invalid-input":"Invalid input"},ku=R-qu,q=Math.floor,Du=String.fromCharCode;function L(u){throw new RangeError(rr[u])}function nr(u,e){const t=[];let r=u.length;for(;r--;)t[r]=e(u[r]);return t}function y0(u,e){const t=u.split("@");let r="";t.length>1&&(r=t[0]+"@",u=t[1]),u=u.replace(tr,".");const n=u.split("."),c=nr(n,e).join(".");return r+c}function C0(u){const e=[];let t=0;const r=u.length;for(;t<r;){const n=u.charCodeAt(t++);if(n>=55296&&n<=56319&&t<r){const c=u.charCodeAt(t++);(c&64512)==56320?e.push(((n&1023)<<10)+(c&1023)+65536):(e.push(n),t--)}else e.push(n)}return e}const cr=u=>String.fromCodePoint(...u),ir=function(u){return u>=48&&u<58?26+(u-48):u>=65&&u<91?u-65:u>=97&&u<123?u-97:R},e0=function(u,e){return u+22+75*(u<26)-((e!=0)<<5)},E0=function(u,e,t){let r=0;for(u=t?q(u/Yt):u>>1,u+=q(u/e);u>ku*eu>>1;r+=R)u=q(u/ku);return q(r+(ku+1)*u/(u+Kt))},A0=function(u){const e=[],t=u.length;let r=0,n=k0,c=g0,i=u.lastIndexOf(D0);i<0&&(i=0);for(let o=0;o<i;++o)u.charCodeAt(o)>=128&&L("not-basic"),e.push(u.charCodeAt(o));for(let o=i>0?i+1:0;o<t;){const a=r;for(let l=1,f=R;;f+=R){o>=t&&L("invalid-input");const x=ir(u.charCodeAt(o++));x>=R&&L("invalid-input"),x>q((G-r)/l)&&L("overflow"),r+=x*l;const h=f<=c?qu:f>=c+eu?eu:f-c;if(x<h)break;const d=R-h;l>q(G/d)&&L("overflow"),l*=d}const s=e.length+1;c=E0(r-a,s,a==0),q(r/s)>G-n&&L("overflow"),n+=q(r/s),r%=s,e.splice(r++,0,n)}return String.fromCodePoint(...e)},F0=function(u){const e=[];u=C0(u);const t=u.length;let r=k0,n=0,c=g0;for(const a of u)a<128&&e.push(Du(a));const i=e.length;let o=i;for(i&&e.push(D0);o<t;){let a=G;for(const l of u)l>=r&&l<a&&(a=l);const s=o+1;a-r>q((G-n)/s)&&L("overflow"),n+=(a-r)*s,r=a;for(const l of u)if(l<r&&++n>G&&L("overflow"),l===r){let f=n;for(let x=R;;x+=R){const h=x<=c?qu:x>=c+eu?eu:x-c;if(f<h)break;const d=f-h,b=R-h;e.push(Du(e0(h+d%b,0))),f=q(d/b)}e.push(Du(e0(f,0))),c=E0(n,s,o===i),n=0,++o}++n,++r}return e.join("")},or=function(u){return y0(u,function(e){return ur.test(e)?A0(e.slice(4).toLowerCase()):e})},ar=function(u){return y0(u,function(e){return er.test(e)?"xn--"+F0(e):e})},w0={version:"2.3.1",ucs2:{decode:C0,encode:cr},decode:A0,encode:F0,toASCII:ar,toUnicode:or},sr={options:{html:!1,xhtmlOut:!1,breaks:!1,langPrefix:"language-",linkify:!1,typographer:!1,quotes:"“”‘’",highlight:null,maxNesting:100},components:{core:{},block:{},inline:{}}},lr={options:{html:!1,xhtmlOut:!1,breaks:!1,langPrefix:"language-",linkify:!1,typographer:!1,quotes:"“”‘’",highlight:null,maxNesting:20},components:{core:{rules:["normalize","block","inline","text_join"]},block:{rules:["paragraph"]},inline:{rules:["text"],rules2:["balance_pairs","fragments_join"]}}},fr={options:{html:!0,xhtmlOut:!0,breaks:!1,langPrefix:"language-",linkify:!1,typographer:!1,quotes:"“”‘’",highlight:null,maxNesting:20},components:{core:{rules:["normalize","block","inline","text_join"]},block:{rules:["blockquote","code","fence","heading","hr","html_block","lheading","list","reference","paragraph"]},inline:{rules:["autolink","backticks","emphasis","entity","escape","html_inline","image","link","newline","text"],rules2:["balance_pairs","emphasis","fragments_join"]}}},dr={default:sr,zero:lr,commonmark:fr},xr=/^(vbscript|javascript|file|data):/,hr=/^data:image\/(gif|png|jpeg|webp);/;function pr(u){const e=u.trim().toLowerCase();return xr.test(e)?hr.test(e):!0}const v0=["http:","https:","mailto:"];function br(u){const e=Tu(u,!0);if(e.hostname&&(!e.protocol||v0.indexOf(e.protocol)>=0))try{e.hostname=w0.toASCII(e.hostname)}catch{}return tu(Su(e))}function mr(u){const e=Tu(u,!0);if(e.hostname&&(!e.protocol||v0.indexOf(e.protocol)>=0))try{e.hostname=w0.toUnicode(e.hostname)}catch{}return J(Su(e),J.defaultChars+"%")}function z(u,e){if(!(this instanceof z))return new z(u,e);e||Mu(u)||(e=u||{},u="default"),this.inline=new nu,this.block=new xu,this.core=new Iu,this.renderer=new X,this.linkify=new T,this.validateLink=pr,this.normalizeLink=br,this.normalizeLinkText=mr,this.utils=ge,this.helpers=fu({},Ce),this.options={},this.configure(u),e&&this.set(e)}z.prototype.set=function(u){return fu(this.options,u),this};z.prototype.configure=function(u){const e=this;if(Mu(u)){const t=u;if(u=dr[t],!u)throw new Error('Wrong `markdown-it` preset "'+t+'", check name')}if(!u)throw new Error("Wrong `markdown-it` preset, can't be empty");return u.options&&e.set(u.options),u.components&&Object.keys(u.components).forEach(function(t){u.components[t].rules&&e[t].ruler.enableOnly(u.components[t].rules),u.components[t].rules2&&e[t].ruler2.enableOnly(u.components[t].rules2)}),this};z.prototype.enable=function(u,e){let t=[];Array.isArray(u)||(u=[u]),["core","block","inline"].forEach(function(n){t=t.concat(this[n].ruler.enable(u,!0))},this),t=t.concat(this.inline.ruler2.enable(u,!0));const r=u.filter(function(n){return t.indexOf(n)<0});if(r.length&&!e)throw new Error("MarkdownIt. Failed to enable unknown rule(s): "+r);return this};z.prototype.disable=function(u,e){let t=[];Array.isArray(u)||(u=[u]),["core","block","inline"].forEach(function(n){t=t.concat(this[n].ruler.disable(u,!0))},this),t=t.concat(this.inline.ruler2.disable(u,!0));const r=u.filter(function(n){return t.indexOf(n)<0});if(r.length&&!e)throw new Error("MarkdownIt. Failed to disable unknown rule(s): "+r);return this};z.prototype.use=function(u){const e=[this].concat(Array.prototype.slice.call(arguments,1));return u.apply(u,e),this};z.prototype.parse=function(u,e){if(typeof u!="string")throw new Error("Input data should be a String");const t=new this.core.State(u,this,e);return this.core.process(t),t.tokens};z.prototype.render=function(u,e){return e=e||{},this.renderer.render(this.parse(u,e),this.options,e)};z.prototype.parseInline=function(u,e){const t=new this.core.State(u,this,e);return t.inlineMode=!0,this.core.process(t),t.tokens};z.prototype.renderInline=function(u,e){return e=e||{},this.renderer.render(this.parseInline(u,e),this.options,e)};var U={};U.getAttrs=function(u,e,t){const r=/[^\t\n\f />"'=]/,n=" ",c="=",i=".",o="#",a=[];let s="",l="",f=!0,x=!1;for(let h=e+t.leftDelimiter.length;h<u.length;h++){if(u.slice(h,h+t.rightDelimiter.length)===t.rightDelimiter){s!==""&&a.push([s,l]);break}const d=u.charAt(h);if(d===c&&f){f=!1;continue}if(d===i&&s===""){u.charAt(h+1)===i?(s="css-module",h+=1):s="class",f=!1;continue}if(d===o&&s===""){s="id",f=!1;continue}if(d==='"'&&l===""&&!x){x=!0;continue}if(d==='"'&&x){x=!1;continue}if(d===n&&!x){if(s==="")continue;a.push([s,l]),s="",l="",f=!0;continue}if(!(f&&d.search(r)===-1)){if(f){s+=d;continue}l+=d}}if(t.allowedAttributes&&t.allowedAttributes.length){const h=t.allowedAttributes;return a.filter(function(d){const b=d[0];function p(D){return b===D||D instanceof RegExp&&D.test(b)}return h.some(p)})}return a};U.addAttrs=function(u,e){for(let t=0,r=u.length;t<r;++t){const n=u[t][0];n==="class"?e.attrJoin("class",u[t][1]):n==="css-module"?e.attrJoin("css-module",u[t][1]):e.attrPush(u[t])}return e};U.hasDelimiters=function(u,e){if(!u)throw new Error('Parameter `where` not passed. Should be "start", "end" or "only".');return function(t){const r=e.leftDelimiter.length+1+e.rightDelimiter.length;if(!t||typeof t!="string"||t.length<r)return!1;function n(l){const f=l.charAt(e.leftDelimiter.length)===".",x=l.charAt(e.leftDelimiter.length)==="#";return f||x?l.length>=r+1:l.length>=r}let c,i,o,a;const s=r-e.rightDelimiter.length;switch(u){case"start":o=t.slice(0,e.leftDelimiter.length),c=o===e.leftDelimiter?0:-1,i=c===-1?-1:t.indexOf(e.rightDelimiter,s),a=t.charAt(i+e.rightDelimiter.length),a&&e.rightDelimiter.indexOf(a)!==-1&&(i=-1);break;case"end":c=t.lastIndexOf(e.leftDelimiter),i=c===-1?-1:t.indexOf(e.rightDelimiter,c+s),i=i===t.length-e.rightDelimiter.length?i:-1;break;case"only":o=t.slice(0,e.leftDelimiter.length),c=o===e.leftDelimiter?0:-1,o=t.slice(t.length-e.rightDelimiter.length),i=o===e.rightDelimiter?t.length-e.rightDelimiter.length:-1;break;default:throw new Error(`Unexpected case ${u}, expected 'start', 'end' or 'only'`)}return c!==-1&&i!==-1&&n(t.substring(c,i+e.rightDelimiter.length))}};U.removeDelimiter=function(u,e){const t=Au(e.leftDelimiter),r=Au(e.rightDelimiter),n=new RegExp("[ \\n]?"+t+"[^"+t+r+"]+"+r+"$"),c=u.search(n);return c!==-1?u.slice(0,c):u};function Au(u){return u.replace(/[-/\\^$*+?.()|[\]{}]/g,"\\$&")}U.escapeRegExp=Au;U.getMatchingOpeningToken=function(u,e){if(u[e].type==="softbreak")return!1;if(u[e].nesting===0)return u[e];const t=u[e].level,r=u[e].type.replace("_close","_open");for(;e>=0;--e)if(u[e].type===r&&u[e].level===t)return u[e];return!1};const _r=/[&<>"]/,gr=/[&<>"]/g,kr={"&":"&amp;","<":"&lt;",">":"&gt;",'"':"&quot;"};function Dr(u){return kr[u]}U.escapeHtml=function(u){return _r.test(u)?u.replace(gr,Dr):u};const y=U;var yr=u=>{const e=new RegExp("^ {0,3}[-*_]{3,} ?"+y.escapeRegExp(u.leftDelimiter)+"[^"+y.escapeRegExp(u.rightDelimiter)+"]");return[{name:"fenced code blocks",tests:[{shift:0,block:!0,info:y.hasDelimiters("end",u)}],transform:(t,r)=>{const n=t[r],c=n.info.lastIndexOf(u.leftDelimiter),i=y.getAttrs(n.info,c,u);y.addAttrs(i,n),n.info=y.removeDelimiter(n.info,u)}},{name:"inline nesting 0",tests:[{shift:0,type:"inline",children:[{shift:-1,type:t=>t==="image"||t==="code_inline"},{shift:0,type:"text",content:y.hasDelimiters("start",u)}]}],transform:(t,r,n)=>{const c=t[r].children[n],i=c.content.indexOf(u.rightDelimiter),o=t[r].children[n-1],a=y.getAttrs(c.content,0,u);y.addAttrs(a,o),c.content.length===i+u.rightDelimiter.length?t[r].children.splice(n,1):c.content=c.content.slice(i+u.rightDelimiter.length)}},{name:"tables",tests:[{shift:0,type:"table_close"},{shift:1,type:"paragraph_open"},{shift:2,type:"inline",content:y.hasDelimiters("only",u)}],transform:(t,r)=>{const n=t[r+2],c=y.getMatchingOpeningToken(t,r),i=y.getAttrs(n.content,0,u);y.addAttrs(i,c),t.splice(r+1,3)}},{name:"tables thead metadata",tests:[{shift:0,type:"tr_close"},{shift:1,type:"thead_close"},{shift:2,type:"tbody_open"}],transform:(t,r)=>{const n=y.getMatchingOpeningToken(t,r),c=t[r-1];let i=0,o=r;for(;--o;){if(t[o]===n){t[o-1].meta=Object.assign({},t[o+2].meta,{colsnum:i});break}i+=(t[o].level===c.level&&t[o].type===c.type)>>0}t[r+2].meta=Object.assign({},t[r+2].meta,{colsnum:i})}},{name:"tables tbody calculate",tests:[{shift:0,type:"tbody_close",hidden:!1}],transform:(t,r)=>{let n=r-2;for(;n>0&&t[--n].type!=="tbody_open";);const c=t[n].meta.colsnum>>0;if(c<2)return;const i=t[r].level+2;for(let o=n;o<r;o++){if(t[o].level>i)continue;const a=t[o],s=a.hidden?0:a.attrGet("rowspan")>>0,l=a.hidden?0:a.attrGet("colspan")>>0;if(s>1){let f=c-(l>0?l:1);for(let x=o,h=s;h>1;x++)t[x].type=="tr_open"&&(t[x].meta=Object.assign({},t[x].meta),t[x].meta&&t[x].meta.colsnum&&(f-=1),t[x].meta.colsnum=f,h--)}if(a.type=="tr_open"&&a.meta&&a.meta.colsnum){const f=a.meta.colsnum;for(let x=o,h=0;x<r;x++){if(t[x].type=="td_open")h+=1;else if(t[x].type=="tr_close")break;h>f&&(t[x].hidden||Fu(t[x]))}}if(l>1){const f=[];let x=o+3,h=c;for(let p=o;p>n;p--)if(t[p].type=="tr_open"){h=t[p].meta&&t[p].meta.colsnum||h;break}else t[p].type==="td_open"&&f.unshift(p);for(let p=o+2;p<r;p++)if(t[p].type=="tr_close"){x=p;break}else t[p].type=="td_open"&&f.push(p);const d=f.indexOf(o);let b=h-d;b=b>l?l:b,l>b&&a.attrSet("colspan",b+"");for(let p=f.slice(h+1-c-b)[0];p<x;p++)t[p].hidden||Fu(t[p])}}}},{name:"inline attributes",tests:[{shift:0,type:"inline",children:[{shift:-1,nesting:-1},{shift:0,type:"text",content:y.hasDelimiters("start",u)}]}],transform:(t,r,n)=>{const c=t[r].children[n],i=c.content,o=y.getAttrs(i,0,u),a=y.getMatchingOpeningToken(t[r].children,n-1);y.addAttrs(o,a),c.content=i.slice(i.indexOf(u.rightDelimiter)+u.rightDelimiter.length)}},{name:"list softbreak",tests:[{shift:-2,type:"list_item_open"},{shift:0,type:"inline",children:[{position:-2,type:"softbreak"},{position:-1,type:"text",content:y.hasDelimiters("only",u)}]}],transform:(t,r,n)=>{const i=t[r].children[n].content,o=y.getAttrs(i,0,u);let a=r-2;for(;t[a-1]&&t[a-1].type!=="ordered_list_open"&&t[a-1].type!=="bullet_list_open";)a--;y.addAttrs(o,t[a-1]),t[r].children=t[r].children.slice(0,-2)}},{name:"list double softbreak",tests:[{shift:0,type:t=>t==="bullet_list_close"||t==="ordered_list_close"},{shift:1,type:"paragraph_open"},{shift:2,type:"inline",content:y.hasDelimiters("only",u),children:t=>t.length===1},{shift:3,type:"paragraph_close"}],transform:(t,r)=>{const c=t[r+2].content,i=y.getAttrs(c,0,u),o=y.getMatchingOpeningToken(t,r);y.addAttrs(i,o),t.splice(r+1,3)}},{name:"list item end",tests:[{shift:-2,type:"list_item_open"},{shift:0,type:"inline",children:[{position:-1,type:"text",content:y.hasDelimiters("end",u)}]}],transform:(t,r,n)=>{const c=t[r].children[n],i=c.content,o=y.getAttrs(i,i.lastIndexOf(u.leftDelimiter),u);y.addAttrs(o,t[r-2]);const a=i.slice(0,i.lastIndexOf(u.leftDelimiter));c.content=t0(a)!==" "?a:a.slice(0,-1)}},{name:`
{.a} softbreak then curly in start`,tests:[{shift:0,type:"inline",children:[{position:-2,type:"softbreak"},{position:-1,type:"text",content:y.hasDelimiters("only",u)}]}],transform:(t,r,n)=>{const c=t[r].children[n],i=y.getAttrs(c.content,0,u);let o=r+1;for(;t[o+1]&&t[o+1].nesting===-1;)o++;const a=y.getMatchingOpeningToken(t,o);y.addAttrs(i,a),t[r].children=t[r].children.slice(0,-2)}},{name:"horizontal rule",tests:[{shift:0,type:"paragraph_open"},{shift:1,type:"inline",children:t=>t.length===1,content:t=>t.match(e)!==null},{shift:2,type:"paragraph_close"}],transform:(t,r)=>{const n=t[r];n.type="hr",n.tag="hr",n.nesting=0;const c=t[r+1].content,i=c.lastIndexOf(u.leftDelimiter),o=y.getAttrs(c,i,u);y.addAttrs(o,n),n.markup=c,t.splice(r+1,2)}},{name:"end of block",tests:[{shift:0,type:"inline",children:[{position:-1,content:y.hasDelimiters("end",u),type:t=>t!=="code_inline"&&t!=="math_inline"}]}],transform:(t,r,n)=>{const c=t[r].children[n],i=c.content,o=y.getAttrs(i,i.lastIndexOf(u.leftDelimiter),u);let a=r+1;do if(t[a]&&t[a].nesting===-1)break;while(a++<t.length);const s=y.getMatchingOpeningToken(t,a);y.addAttrs(o,s);const l=i.slice(0,i.lastIndexOf(u.leftDelimiter));c.content=t0(l)!==" "?l:l.slice(0,-1)}}]};function t0(u){return u.slice(-1)[0]}function Fu(u){u.hidden=!0,u.children&&u.children.forEach(e=>(e.content="",Fu(e),void 0))}const Cr=yr,Er={leftDelimiter:"{",rightDelimiter:"}",allowedAttributes:[]};var Ar=function(e,t){let r=Object.assign({},Er);r=Object.assign(r,t);const n=Cr(r);function c(i){const o=i.tokens;for(let a=0;a<o.length;a++)for(let s=0;s<n.length;s++){const l=n[s];let f=null;if(l.tests.every(h=>{const d=wu(o,a,h);return d.j!==null&&(f=d.j),d.match}))try{l.transform(o,a,f),(l.name==="inline attributes"||l.name==="inline nesting 0")&&s--}catch(h){console.error(`markdown-it-attrs: Error in pattern '${l.name}': ${h.message}`),console.error(h.stack)}}}e.core.ruler.before("linkify","curly_attributes",c)};function wu(u,e,t){const r={match:!1,j:null},n=t.shift!==void 0?e+t.shift:t.position;if(t.shift!==void 0&&n<0)return r;const c=vr(u,n);if(c===void 0)return r;for(const i of Object.keys(t))if(!(i==="shift"||i==="position")){if(c[i]===void 0)return r;if(i==="children"&&Fr(t.children)){if(c.children.length===0)return r;let o;const a=t.children,s=c.children;if(a.every(l=>l.position!==void 0)){if(o=a.every(l=>wu(s,l.position,l).match),o){const l=Sr(a).position;r.j=l>=0?l:s.length+l}}else for(let l=0;l<s.length;l++)if(o=a.every(f=>wu(s,l,f).match),o){r.j=l;break}if(o===!1)return r;continue}switch(typeof t[i]){case"boolean":case"number":case"string":if(c[i]!==t[i])return r;break;case"function":if(!t[i](c[i]))return r;break;case"object":if(wr(t[i])){if(t[i].every(a=>a(c[i]))===!1)return r;break}default:throw new Error(`Unknown type of pattern test (key: ${i}). Test should be of type boolean, number, string, function or array of functions.`)}}return r.match=!0,r}function Fr(u){return Array.isArray(u)&&u.length&&u.every(e=>typeof e=="object")}function wr(u){return Array.isArray(u)&&u.length&&u.every(e=>typeof e=="function")}function vr(u,e){return e>=0?u[e]:u[u.length+e]}function Sr(u){return u.slice(-1)[0]||{}}const Tr=M0(Ar);function r0(u,e,t){function r(x){return x.trim().split(" ",2)[0]===e}function n(x,h,d,b,p){return x[h].nesting===1&&x[h].attrJoin("class",e),p.renderToken(x,h,d,b,p)}t=t||{};const c=3,i=t.marker||":",o=i.charCodeAt(0),a=i.length,s=t.validate||r,l=t.render||n;function f(x,h,d,b){let p,D=!1,m=x.bMarks[h]+x.tShift[h],_=x.eMarks[h];if(o!==x.src.charCodeAt(m))return!1;for(p=m+1;p<=_&&i[(p-m)%a]===x.src[p];p++);const g=Math.floor((p-m)/a);if(g<c)return!1;p-=(p-m)%a;const k=x.src.slice(m,p),C=x.src.slice(p,_);if(!s(C,k))return!1;if(b)return!0;let E=h;for(;E++,!(E>=d||(m=x.bMarks[E]+x.tShift[E],_=x.eMarks[E],m<_&&x.sCount[E]<x.blkIndent));)if(o===x.src.charCodeAt(m)&&!(x.sCount[E]-x.blkIndent>=4)){for(p=m+1;p<=_&&i[(p-m)%a]===x.src[p];p++);if(!(Math.floor((p-m)/a)<g)&&(p-=(p-m)%a,p=x.skipSpaces(p),!(p<_))){D=!0;break}}const S=x.parentType,B=x.lineMax;x.parentType="container",x.lineMax=E;const I=x.push("container_"+e+"_open","div",1);I.markup=k,I.block=!0,I.info=C,I.map=[h,E],x.md.block.tokenize(x,h+1,E);const H=x.push("container_"+e+"_close","div",-1);return H.markup=x.src.slice(m,p),H.block=!0,x.parentType=S,x.lineMax=B,x.line=E+(D?1:0),!0}u.block.ruler.before("fence","container_"+e,f,{alt:["paragraph","reference","blockquote","list"]}),u.renderer.rules["container_"+e+"_open"]=l,u.renderer.rules["container_"+e+"_close"]=l}const ou=W;function W(u,e){const t=vu();return W=function(r,n){return r=r-0,t[r]},W(u,e)}function vu(){const u=["setupPlugins","trim","use",`</div>
`,"replace","setupCustomRules","font-size: 1.15rem !important; font-weight: 600 !important; color: var(--vp-c-text-1) !important; margin: 20px 0 14px 0 !important; line-height: 1.3 !important; position: relative !important; padding-left: 12px !important;","renderer","rel","attrs","rules","table_close","style","renderToken","margin: 16px 0 !important; padding-left: 24px !important;",'<div class="image-caption" style="margin-top: 8px; font-size: 0.875rem; color: var(--vp-c-text-2); font-style: italic;">'];return vu=function(){return u},vu()}class zr{constructor(){this.md=null,this.initialize()}initialize(){this.md=new z({html:!0,linkify:!0,breaks:!0,typographer:!0,quotes:`""''`}),this.setupPlugins(),this.setupCustomRules()}[ou(0)](){const e=ou;this.md.use(Tr,{allowedAttributes:["class","id","style","target","rel"]}),this.setupCustomEmoji(),this.md.use(r0,"tip",{validate:function(t){return t[W(1)]().match(/^tip\s+(.*)$/)},render:function(t,r){const n=t[r].info.trim().match(/^tip\s+(.*)$/);return t[r].nesting===1?`<div class="tip-container" style="background: linear-gradient(135deg, #e8f5e8, #f0f9f0) !important; border-left: 4px solid #52c41a !important; border-radius: 12px !important; padding: 20px !important; margin: 20px 0 !important; box-shadow: 0 8px 32px rgba(82, 196, 26, 0.15) !important; position: relative !important; overflow: hidden !important;">
<div class="tip-title" style="font-weight: 700 !important; color: #389e0d !important; margin-bottom: 12px !important; display: flex !important; align-items: center !important; font-size: 1.1rem !important;"><span style="margin-right: 8px; font-size: 1.2em;">💡</span>`+(n&&n[1]?n[1]:"提示")+`</div>
`:`</div>
`}}),this.md[e(2)](r0,"warning",{validate:function(t){return t.trim().match(/^warning\s+(.*)$/)},render:function(t,r){const n=e,c=t[r].info.trim().match(/^warning\s+(.*)$/);return t[r].nesting===1?`<div class="warning-container" style="background: linear-gradient(135deg, #fff7e6, #fefcf6) !important; border-left: 4px solid #fa8c16 !important; border-radius: 12px !important; padding: 20px !important; margin: 20px 0 !important; box-shadow: 0 8px 32px rgba(250, 140, 22, 0.15) !important; position: relative !important; overflow: hidden !important;">
<div class="warning-title" style="font-weight: 700 !important; color: #d46b08 !important; margin-bottom: 12px !important; display: flex !important; align-items: center !important; font-size: 1.1rem !important;"><span style="margin-right: 8px; font-size: 1.2em;">⚠️</span>`+(c&&c[1]?c[1]:"警告")+n(3):`</div>
`}})}setupCustomEmoji(){const e={":sunny:":"☀️",":sun:":"☀️",":cloud:":"☁️",":partly_sunny:":"⛅",":rain:":"🌧️",":thermometer:":"🌡️",":droplet:":"💧",":wind:":"🌬️",":umbrella:":"☂️",":sunglasses:":"🕶️",":backpack:":"🎒",":car:":"🚗",":hotel:":"🏨",":restaurant:":"🍽️",":mountain:":"🏔️",":camera:":"📷",":map:":"🗺️",":location:":"📍",":calendar:":"📅",":clock:":"🕐",":money:":"💰",":credit_card:":"💳",":warning:":"⚠️",":info:":"ℹ️",":bulb:":"💡",":fire:":"🔥",":star:":"⭐",":heart:":"❤️",":check:":"✅",":x:":"❌"};this.md.core.ruler.after("normalize","emoji",t=>{const r=W;let n=t.src;for(const[c,i]of Object.entries(e))n=n[r(4)](new RegExp(c.replace(/[.*+?^${}()|[\]\\]/g,"\\$&"),"g"),i);t.src=n})}[ou(5)](){const e=ou,t=this.md.renderer.rules.heading_open||function(l,f,x,h,d){return d.renderToken(l,f,x)};this.md.renderer.rules.heading_open=function(l,f,x,h,d){const b=W,p=l[f],D=p.tag,m={h1:"font-size: 2.2rem !important; font-weight: 700 !important; color: var(--vp-c-text-1) !important; margin: 32px 0 20px 0 !important; line-height: 1.2 !important; border-bottom: 2px solid var(--vp-c-divider-light) !important; padding-bottom: 12px !important; position: relative !important; text-rendering: optimizeLegibility !important; -webkit-font-smoothing: antialiased !important;",h2:"font-size: 1.6rem !important; font-weight: 600 !important; color: var(--vp-c-text-1) !important; margin: 28px 0 18px 0 !important; line-height: 1.3 !important; border-bottom: 1px solid var(--vp-c-divider) !important; padding-bottom: 8px !important; position: relative !important; padding-left: 20px !important;",h3:"font-size: 1.35rem !important; font-weight: 600 !important; color: var(--vp-c-brand-1) !important; margin: 24px 0 16px 0 !important; line-height: 1.3 !important; position: relative !important; padding-left: 16px !important;",h4:b(6),h5:"font-size: 1.05rem !important; font-weight: 600 !important; color: var(--vp-c-text-1) !important; margin: 18px 0 12px 0 !important; line-height: 1.3 !important;",h6:"font-size: 0.9rem !important; font-weight: 600 !important; color: var(--vp-c-text-2) !important; margin: 16px 0 10px 0 !important; line-height: 1.3 !important; text-transform: uppercase !important; letter-spacing: 0.1em !important;"},_=m[D]||"";return _&&p.attrPush(["style",_]),t(l,f,x,h,d)};const r=this.md.renderer.rules.link_open||function(l,f,x,h,d){return d.renderToken(l,f,x)};this.md[e(7)].rules.link_open=function(l,f,x,h,d){const b=e,p=l[f].attrIndex("target");p<0?l[f].attrPush(["target","_blank"]):l[f].attrs[p][1]="_blank";const D=l[f].attrIndex(b(8));return D<0?l[f].attrPush(["rel","noopener noreferrer"]):l[f][b(9)][D][1]="noopener noreferrer",r(l,f,x,h,d)},this.md[e(7)][e(10)].table_open=function(){return`<div class="table-container" style="margin: 20px 0; overflow-x: auto; border-radius: 12px; box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12); backdrop-filter: blur(10px); border: 1px solid var(--vp-c-divider-light);"><table class="markdown-table" style="width: 100% !important; border-collapse: collapse !important; background: linear-gradient(135deg, var(--vp-c-bg), var(--vp-c-bg-soft)) !important; font-size: 0.9rem !important; margin: 0 !important; border-radius: 12px !important; overflow: hidden !important;">
`},this.md.renderer.rules[e(11)]=function(){return`</table></div>
`};const n=this.md[e(7)].rules.th_open||function(l,f,x,h,d){return d.renderToken(l,f,x)};this.md.renderer.rules.th_open=function(l,f,x,h,d){const b=e;return l[f].attrPush([b(12),"padding: 14px 18px !important; text-align: left !important; border-bottom: 3px solid var(--vp-c-brand) !important; background: linear-gradient(135deg, var(--vp-c-brand-soft), var(--vp-c-brand)) !important; font-weight: 700 !important; color: white !important; text-shadow: 0 1px 2px rgba(0,0,0,0.2) !important; position: relative !important;"]),n(l,f,x,h,d)};const c=this.md.renderer.rules.td_open||function(l,f,x,h,d){return d[e(13)](l,f,x)};this.md.renderer.rules.td_open=function(l,f,x,h,d){return l[f].attrPush(["style","padding: 12px 18px !important; text-align: left !important; border-bottom: 1px solid var(--vp-c-divider-light) !important; color: var(--vp-c-text-1) !important; transition: all 0.3s ease !important; position: relative !important;"]),c(l,f,x,h,d)},this.md.renderer.rules.code_block,this.md.renderer.rules.code_block=function(l,f,x,h,d){const b=l[f],p=b.info?' class="language-'+b.info+'"':"";return`<div class="code-container" style="margin: 20px 0 !important; border-radius: 12px !important; overflow: hidden !important; box-shadow: 0 8px 32px rgba(0, 0, 0, 0.15) !important; border: 1px solid var(--vp-c-divider-light) !important; background: linear-gradient(135deg, var(--vp-c-bg-alt), var(--vp-c-bg-mute)) !important;">
                <div class="code-header" style="background: linear-gradient(135deg, var(--vp-c-brand-soft), var(--vp-c-brand)) !important; padding: 8px 16px !important; font-size: 0.75rem !important; color: white !important; font-weight: 600 !important; text-transform: uppercase !important; letter-spacing: 0.1em !important;">`+(b.info||"text")+`</div>
                <pre`+p+` style="background: transparent !important; padding: 20px !important; margin: 0 !important; overflow-x: auto !important; font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', monospace !important; font-size: 0.875rem !important; line-height: 1.6 !important; color: var(--vp-c-text-1) !important; border-left: 4px solid var(--vp-c-brand) !important;"><code>`+b.content+`</code></pre>
            </div>
`};const i=this.md.renderer.rules.paragraph_open||function(l,f,x,h,d){return d.renderToken(l,f,x)};this.md.renderer.rules.paragraph_open=function(l,f,x,h,d){return l[f].attrPush(["style","margin: 12px 0 !important; line-height: 1.7 !important; color: var(--vp-c-text-1) !important;"]),i(l,f,x,h,d)},this.md.renderer.rules.code_inline=function(l,f){return`<code class="inline-code" style="background: var(--vp-c-bg-soft) !important; color: var(--vp-c-brand-1) !important; padding: 2px 6px !important; border-radius: 4px !important; font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', monospace !important; font-size: 0.875em !important; font-weight: 500 !important; border: 1px solid var(--vp-c-divider-light) !important;">`+l[f].content+"</code>"};const o=this.md.renderer.rules.bullet_list_open||function(l,f,x,h,d){return d.renderToken(l,f,x)};this.md.renderer.rules.bullet_list_open=function(l,f,x,h,d){return l[f].attrPush(["style","margin: 16px 0 !important; padding-left: 24px !important;"]),o(l,f,x,h,d)};const a=this.md.renderer.rules.ordered_list_open||function(l,f,x,h,d){return d.renderToken(l,f,x)};this.md.renderer.rules.ordered_list_open=function(l,f,x,h,d){const b=e;return l[f].attrPush(["style",b(14)]),a(l,f,x,h,d)};const s=this.md.renderer.rules.list_item_open||function(l,f,x,h,d){return d.renderToken(l,f,x)};this.md.renderer.rules.list_item_open=function(l,f,x,h,d){return l[f].attrPush(["style","margin: 8px 0 !important; line-height: 1.6 !important;"]),s(l,f,x,h,d)},this.md.renderer[e(10)].image=function(l,f){const x=e,h=l[f],d=h.attrs[h.attrIndex("src")][1],b=h.content,p=h.attrs&&h.attrs[h.attrIndex("title")]?h[x(9)][h.attrIndex("title")][1]:"";return`<div class="image-container" style="margin: 20px 0; text-align: center;">
                <img src="`+d+'" alt="'+b+'" title="'+p+`" class="markdown-image" loading="lazy" style="max-width: 100%; height: auto; border-radius: 8px; box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1); transition: all 0.3s ease;">
                `+(p?x(15)+p+"</div>":"")+`
            </div>`}}parse(e){if(!e||typeof e!="string")return"";try{return this.md.render(e)}catch(t){return console.error("Markdown parsing error:",t),"<pre>"+e+"</pre>"}}parseInline(e){if(!e||typeof e!="string")return"";try{return this.md.renderInline(e)}catch(t){return console.error("Inline markdown parsing error:",t),e}}getInstance(){return this.md}use(e,t){return this.md&&this.md.use(e,t),this}cleanup(){this.md=null}}const Br=new zr;export{Br as m};
